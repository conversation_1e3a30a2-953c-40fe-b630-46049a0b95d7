import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { BarChart3, TrendingUp, Loader2, Award, Target } from 'lucide-react';
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts';
import { getLoanStatistics, getLoanSettings, LoanSubmissionByDay, LoanSettings } from '@/api/loanApplications';
import { format } from 'date-fns';

export function PersonalFinanceTrackerDashboard() {
  const [submissionsByDay, setSubmissionsByDay] = useState<LoanSubmissionByDay[]>([]);
  const [loading, setLoading] = useState(true);
  const [settings, setSettings] = useState<LoanSettings | null>(null);
  const [loadingSettings, setLoadingSettings] = useState(true);
  const [weeklyStats, setWeeklyStats] = useState({
    weeklySubmissions: 0,
    weeklyFunded: 0,
    weekStart: new Date(),
    weekEnd: new Date()
  });

  // Load data on initial render
  useEffect(() => {
    fetchSubmissionsData();
    fetchSettings();
  }, []);

  // Fetch submissions data
  const fetchSubmissionsData = async () => {
    setLoading(true);
    try {
      // Get data for the last 7 days
      const response = await getLoanStatistics('7days');

      if (response.success) {
        setSubmissionsByDay(response.data.submissionsByDay);
        calculateWeeklyStats(response.data.submissionsByDay);
      }
    } catch (error) {
      console.error('Error fetching loan submissions data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Fetch loan settings
  const fetchSettings = async () => {
    setLoadingSettings(true);
    try {
      const response = await getLoanSettings();
      if (response.success) {
        setSettings(response.data);
      }
    } catch (error) {
      console.error('Error fetching loan settings:', error);
    } finally {
      setLoadingSettings(false);
    }
  };

  // Calculate weekly statistics
  const calculateWeeklyStats = (data: LoanSubmissionByDay[]) => {
    try {
      // Calculate current week's start (Monday) and end (Sunday)
      const today = new Date();
      const dayOfWeek = today.getDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
      const daysToSubtract = dayOfWeek === 0 ? 6 : dayOfWeek - 1;

      const weekStart = new Date(today);
      weekStart.setDate(today.getDate() - daysToSubtract);
      weekStart.setHours(0, 0, 0, 0);

      const weekEnd = new Date(weekStart);
      weekEnd.setDate(weekStart.getDate() + 6);
      weekEnd.setHours(23, 59, 59, 999);

      // Filter data for current week
      const weekData = data.filter(day => {
        const date = new Date(day.date);
        return date >= weekStart && date <= weekEnd;
      });

      // Calculate weekly totals
      const weeklySubmissions = weekData.reduce(
        (sum, day) => sum + (day.total || 0), 0
      );

      const weeklyFunded = weekData.reduce(
        (sum, day) => sum + ((day.approved || 0) + (day.approvedPaid || 0)), 0
      );

      setWeeklyStats({
        weeklySubmissions,
        weeklyFunded,
        weekStart,
        weekEnd
      });
    } catch (error) {
      console.error('Error calculating weekly statistics:', error);
    }
  };

  return (
    <Card className="overflow-hidden">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div className="flex items-center space-x-2">
          <div className="p-1.5 bg-gray-100 dark:bg-gray-800 rounded-full">
            <TrendingUp className="h-5 w-5 text-gray-600 dark:text-gray-400" />
          </div>
          <CardTitle className="text-base font-medium">Personal Finance Tracker (Week to Date)</CardTitle>
        </div>
        <Button variant="outline" size="sm" asChild>
          <Link to="/pf/tracker" className="flex items-center gap-1">
            <BarChart3 className="h-4 w-4" />
            <span>View Details</span>
          </Link>
        </Button>
      </CardHeader>
      <CardContent>
        {loading || loadingSettings ? (
          <div className="flex flex-col justify-center items-center h-60 bg-gray-50/50 dark:bg-gray-900/20 rounded-lg border border-dashed border-gray-200 dark:border-gray-800">
            <Loader2 className="h-8 w-8 animate-spin text-gray-500 dark:text-gray-400 mb-2" />
            <p className="text-sm text-muted-foreground">Loading loan data...</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left side - Stats */}
            <div className="space-y-6">
              {/* Stats and Goals - 2x2 Grid */}
              <div className="grid grid-cols-2 gap-4">
                {/* Total Submissions */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base font-medium">Total Submissions</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center">
                      <div className="text-4xl font-bold">{weeklyStats.weeklySubmissions}</div>
                    </div>
                  </CardContent>
                </Card>

                {/* Total Funded */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base font-medium">Total Funded</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center">
                      <div className="text-4xl font-bold">{weeklyStats.weeklyFunded}</div>
                    </div>
                  </CardContent>
                </Card>

                {/* Submission Goal */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base font-medium">Submission Goal</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center">
                      {settings && (
                        <>
                          <div className="flex justify-center items-baseline gap-2">
                            <span className="text-3xl font-bold">{weeklyStats.weeklySubmissions}</span>
                            <span className="text-lg text-muted-foreground">/ {settings.weeklySubmissionGoal}</span>
                          </div>
                          <div className="mt-2 text-sm text-center">
                            <span className={`font-medium ${weeklyStats.weeklySubmissions >= settings.weeklySubmissionGoal ? 'text-green-600 dark:text-green-400' : 'text-amber-600 dark:text-amber-400'}`}>
                              {weeklyStats.weeklySubmissions >= settings.weeklySubmissionGoal
                                ? 'Goal Reached!'
                                : `${weeklyStats.weeklySubmissions === 0 ? 0 : Math.round((weeklyStats.weeklySubmissions / settings.weeklySubmissionGoal) * 100)}% Complete`}
                            </span>
                          </div>
                          <div className="w-full mt-3">
                            <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                              <div
                                className="h-full rounded-full transition-all duration-500 ease-out"
                                style={{
                                  width: `${settings.weeklySubmissionGoal === 0 ? 0 : Math.min(100, (weeklyStats.weeklySubmissions / settings.weeklySubmissionGoal) * 100)}%`,
                                  backgroundColor: weeklyStats.weeklySubmissions >= settings.weeklySubmissionGoal ? '#0daa5d' : '#6366f1'
                                }}
                              />
                            </div>
                          </div>
                        </>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Funded Goal */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base font-medium">Funded Goal</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center">
                      {settings && (
                        <>
                          <div className="flex justify-center items-baseline gap-2">
                            <span className="text-3xl font-bold">{weeklyStats.weeklyFunded}</span>
                            <span className="text-lg text-muted-foreground">/ {settings.weeklyFundedGoal}</span>
                          </div>
                          <div className="mt-2 text-sm text-center">
                            <span className={`font-medium ${weeklyStats.weeklyFunded >= settings.weeklyFundedGoal ? 'text-green-600 dark:text-green-400' : 'text-amber-600 dark:text-amber-400'}`}>
                              {weeklyStats.weeklyFunded >= settings.weeklyFundedGoal
                                ? 'Goal Reached!'
                                : `${weeklyStats.weeklyFunded === 0 ? 0 : Math.round((weeklyStats.weeklyFunded / settings.weeklyFundedGoal) * 100)}% Complete`}
                            </span>
                          </div>
                          <div className="w-full mt-3">
                            <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                              <div
                                className="h-full rounded-full transition-all duration-500 ease-out"
                                style={{
                                  width: `${settings.weeklyFundedGoal === 0 ? 0 : Math.min(100, (weeklyStats.weeklyFunded / settings.weeklyFundedGoal) * 100)}%`,
                                  backgroundColor: weeklyStats.weeklyFunded >= settings.weeklyFundedGoal ? '#0daa5d' : '#22c55e'
                                }}
                              />
                            </div>
                          </div>
                        </>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Right side - Chart */}
            <div className="h-[350px]">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart
                  data={submissionsByDay.map(item => ({
                    ...item,
                    combinedApproved: (item.approved || 0) + (item.approvedPaid || 0)
                  }))}
                  margin={{ top: 10, right: 30, left: 20, bottom: 30 }}
                >
                  <defs>
                    <linearGradient id="colorTotalDash" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="0%" stopColor="#6366f1" stopOpacity={0.5}/>
                      <stop offset="100%" stopColor="#6366f1" stopOpacity={0.1}/>
                    </linearGradient>
                    <linearGradient id="colorApprovedDash" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="0%" stopColor="#22c55e" stopOpacity={0.5}/>
                      <stop offset="100%" stopColor="#22c55e" stopOpacity={0.1}/>
                    </linearGradient>
                  </defs>
                  <XAxis
                    dataKey="date"
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12, fill: '#888' }}
                    tickFormatter={(date) => format(new Date(date), 'MMM dd')}
                    tickMargin={10}
                    height={40}
                    stroke="#444"
                    minTickGap={5}
                  />
                  <YAxis
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12, fill: '#888' }}
                    tickMargin={10}
                    stroke="#444"
                    width={40}
                  />
                  <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="#333" opacity={0.4} />
                  <Tooltip
                    content={({ active, payload, label }) => {
                      if (active && payload && payload.length) {
                        return (
                          <div style={{
                            backgroundColor: '#1e1e1e',
                            border: '1px solid #333',
                            borderRadius: '4px',
                            padding: '8px 12px',
                            boxShadow: '0 2px 5px rgba(0,0,0,0.5)'
                          }}>
                            <p style={{ color: '#e0e0e0', margin: '0 0 8px 0', fontWeight: 'bold' }}>
                              {format(new Date(label), 'MMM dd, yyyy')}
                            </p>
                            {payload.map((entry) => (
                              <div key={entry.name} style={{ display: 'flex', alignItems: 'center', marginBottom: '4px' }}>
                                <div style={{
                                  width: '10px',
                                  height: '10px',
                                  backgroundColor: entry.dataKey === 'total' ? '#6366f1' : '#22c55e',
                                  marginRight: '8px',
                                  borderRadius: '50%'
                                }} />
                                <p style={{ color: '#e0e0e0', margin: 0 }}>
                                  {entry.name}: <span style={{ fontWeight: 'bold' }}>{entry.value}</span>
                                </p>
                              </div>
                            ))}
                          </div>
                        );
                      }
                      return null;
                    }}
                    cursor={{ stroke: '#666', strokeWidth: 1 }}
                  />
                  <Area
                    type="monotone"
                    dataKey="total"
                    name="Total Submitted"
                    stroke="#6366f1"
                    strokeWidth={2}
                    fill="url(#colorTotalDash)"
                    activeDot={{ r: 6, strokeWidth: 0 }}
                  />
                  <Area
                    type="monotone"
                    dataKey="combinedApproved"
                    name="Total Approved"
                    stroke="#22c55e"
                    strokeWidth={2}
                    fill="url(#colorApprovedDash)"
                    activeDot={{ r: 6, strokeWidth: 0 }}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
