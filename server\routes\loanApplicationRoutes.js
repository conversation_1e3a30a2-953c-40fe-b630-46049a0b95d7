const express = require('express');
const router = express.Router();
const LoanApplication = require('../models/LoanApplication');
const LoanSettings = require('../models/LoanSettings');
const User = require('../models/User');
const { requireUser } = require('./middleware/auth');
const mongoose = require('mongoose');

// Get active users for loan applications (accessible to all users)
router.get('/active-users', requireUser, async (req, res) => {
  try {
    // Get all active users
    const users = await User.find({ isActive: true })
      .select('_id fullName username email role')
      .sort({ fullName: 1 });

    return res.status(200).json(users);
  } catch (error) {
    console.error('Error fetching active users for loan applications:', error);
    return res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

// Get all loan applications with pagination and filtering
router.get('/', requireUser, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;

    console.log(`Loan Applications API - Received request with limit=${req.query.limit}, parsed as ${limit}`);

    // Ensure limit is a valid number between 1 and 100
    const validLimit = isNaN(limit) ? 10 : Math.min(Math.max(limit, 1), 100);
    const skip = (page - 1) * validLimit;

    // Build filter object
    const filter = {};

    // Date range filter
    if (req.query.startDate && req.query.endDate) {
      filter.submittedDate = {
        $gte: new Date(req.query.startDate),
        $lte: new Date(req.query.endDate),
      };
    }

    // Status filter
    if (req.query.status && req.query.status !== 'all') {
      filter.status = req.query.status;
    }

    // User filter (submitted by)
    if (req.query.submittedBy) {
      filter.submittedBy = new mongoose.Types.ObjectId(req.query.submittedBy);
    }

    // Search by customer name or loan ID
    if (req.query.search) {
      const searchRegex = new RegExp(req.query.search, 'i');
      filter.$or = [
        { customerName: searchRegex },
        { loanId: searchRegex },
        { customerId: searchRegex },
      ];
    }

    // Get total count for pagination
    const total = await LoanApplication.countDocuments(filter);

    // Get applications with populated user data
    console.log(`Loan Applications API - Fetching applications with limit=${validLimit}`);

    const applications = await LoanApplication.find(filter)
      .sort({ submittedDate: -1 })
      .skip(skip)
      .limit(validLimit)
      .populate('createdBy', 'fullName username')
      .populate('submittedBy', 'fullName username');

    return res.status(200).json({
      success: true,
      data: applications,
      pagination: {
        total,
        page,
        limit: validLimit,
        pages: Math.ceil(total / validLimit),
      },
    });
  } catch (error) {
    console.error('Error fetching loan applications:', error);
    return res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

// Get loan application by ID
router.get('/:id', requireUser, async (req, res) => {
  try {
    const application = await LoanApplication.findById(req.params.id)
      .populate('createdBy', 'fullName username')
      .populate('submittedBy', 'fullName username');

    if (!application) {
      return res.status(404).json({
        success: false,
        error: 'Loan application not found',
      });
    }

    return res.status(200).json({
      success: true,
      data: application,
    });
  } catch (error) {
    console.error('Error fetching loan application:', error);
    return res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

// Create new loan application
router.post('/', requireUser, async (req, res) => {
  try {
    const {
      loanId,
      loanAmount,
      customerId,
      customerName,
      status,
      submittedDate,
      submittedBy,
      notes,
    } = req.body;

    // Create new application
    const application = new LoanApplication({
      loanId,
      loanAmount,
      customerId,
      customerName,
      status,
      submittedDate: new Date(submittedDate),
      createdBy: req.user._id,
      submittedBy: submittedBy || req.user._id, // Default to current user if not specified
      notes,
    });

    await application.save();

    // Populate user data before returning
    const populatedApplication = await LoanApplication.findById(application._id)
      .populate('createdBy', 'fullName username')
      .populate('submittedBy', 'fullName username');

    return res.status(201).json({
      success: true,
      data: populatedApplication,
    });
  } catch (error) {
    console.error('Error creating loan application:', error);
    return res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

// Update loan application (support both PUT and PATCH methods)
router.put('/:id', requireUser, async (req, res) => {
  updateLoanApplication(req, res);
});

router.patch('/:id', requireUser, async (req, res) => {
  updateLoanApplication(req, res);
});

// Helper function for updating loan applications
async function updateLoanApplication(req, res) {
  try {
    const application = await LoanApplication.findById(req.params.id);

    if (!application) {
      return res.status(404).json({
        success: false,
        error: 'Loan application not found',
      });
    }

    // Check permissions
    const isAdminOrManager = ['admin', 'manager'].includes(req.user.role);
    const isCreator = application.createdBy.toString() === req.user._id.toString();
    const isStatusOnlyUpdate = Object.keys(req.body).length === 2 && req.body.status && req.body.notes !== undefined;

    // Allow employees to update status and notes of any loan application
    // But only allow admin/manager or creator to update other fields
    if (!isAdminOrManager && !isCreator && !isStatusOnlyUpdate) {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to update this loan application',
      });
    }

    // Update fields
    const {
      loanId,
      loanAmount,
      customerId,
      customerName,
      status,
      submittedDate,
      submittedBy,
      notes,
    } = req.body;

    // If it's a status-only update by a non-creator employee, only update status and notes
    const isEmployeeStatusUpdate = !isAdminOrManager && !isCreator && isStatusOnlyUpdate;

    if (!isEmployeeStatusUpdate) {
      // Full update for admin/manager or creator
      application.loanId = loanId || application.loanId;
      application.loanAmount = loanAmount || application.loanAmount;
      application.customerId = customerId || application.customerId;
      application.customerName = customerName || application.customerName;

      if (submittedDate) {
        application.submittedDate = new Date(submittedDate);
      }

      // Only admin/manager can change who submitted the application
      if (isAdminOrManager && submittedBy) {
        application.submittedBy = submittedBy;
      }
    }

    // Always allow status and notes updates
    application.status = status || application.status;
    application.notes = notes !== undefined ? notes : application.notes;

    // Prevent employees from setting approvedPaid status (admin/manager only)
    if (!isAdminOrManager && application.status === 'approvedPaid') {
      return res.status(403).json({
        success: false,
        error: 'Only admin or manager can set Approved & Paid status',
      });
    }

    await application.save();

    // Populate user data before returning
    const populatedApplication = await LoanApplication.findById(application._id)
      .populate('createdBy', 'fullName username')
      .populate('submittedBy', 'fullName username');

    return res.status(200).json({
      success: true,
      data: populatedApplication,
    });
  } catch (error) {
    console.error('Error updating loan application:', error);
    return res.status(500).json({
      success: false,
      error: error.message,
    });
  }
}

// Delete loan application (admin/manager only)
router.delete('/:id', requireUser, async (req, res) => {
  try {
    const application = await LoanApplication.findById(req.params.id);

    if (!application) {
      return res.status(404).json({
        success: false,
        error: 'Loan application not found',
      });
    }

    // Check permissions - only admin/manager can delete
    const isAdminOrManager = ['admin', 'manager'].includes(req.user.role);

    if (!isAdminOrManager) {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to delete loan applications',
      });
    }

    await application.deleteOne();

    return res.status(200).json({
      success: true,
      message: 'Loan application deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting loan application:', error);
    return res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

// Get loan application statistics
router.get('/stats/summary', requireUser, async (req, res) => {
  try {
    // Get date range from query params or default to last 7 days
    let endDate = new Date();
    let startDate;

    const period = req.query.period || '7days';

    switch (period) {
      case '7days':
        startDate = new Date(endDate);
        startDate.setDate(endDate.getDate() - 7);
        break;
      case '14days':
        startDate = new Date(endDate);
        startDate.setDate(endDate.getDate() - 14);
        break;
      case '1month':
        startDate = new Date(endDate);
        startDate.setMonth(endDate.getMonth() - 1);
        break;
      case '1year':
        startDate = new Date(endDate);
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
      case 'all':
        // Get all data by setting a very old start date
        startDate = new Date(2000, 0, 1);
        break;
      case 'custom':
        try {
          if (req.query.startDate) {
            startDate = new Date(req.query.startDate);
            // Validate the date
            if (isNaN(startDate.getTime())) {
              console.error('Invalid start date:', req.query.startDate);
              startDate = new Date(endDate);
              startDate.setDate(endDate.getDate() - 30); // Default to 30 days for custom
            }
          } else {
            startDate = new Date(endDate);
            startDate.setDate(endDate.getDate() - 30); // Default to 30 days for custom
          }

          if (req.query.endDate) {
            const tempEndDate = new Date(req.query.endDate);
            // Validate the date
            if (!isNaN(tempEndDate.getTime())) {
              endDate = tempEndDate;
            } else {
              console.error('Invalid end date:', req.query.endDate);
            }
          }
        } catch (error) {
          console.error('Error parsing date range:', error);
          startDate = new Date(endDate);
          startDate.setDate(endDate.getDate() - 30); // Default to 30 days for custom
        }
        break;
      default:
        startDate = new Date(endDate);
        startDate.setDate(endDate.getDate() - 7);
    }

    // Filter by user if specified
    const userFilter = req.query.userId ? { submittedBy: new mongoose.Types.ObjectId(req.query.userId) } : {};

    // Get total counts by status
    const statusCounts = await LoanApplication.aggregate([
      {
        $match: {
          submittedDate: { $gte: startDate, $lte: endDate },
          ...userFilter,
        },
      },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalAmount: { $sum: '$loanAmount' },
        },
      },
    ]);

    // Get submissions by day
    const submissionsByDay = await LoanApplication.aggregate([
      {
        $match: {
          submittedDate: { $gte: startDate, $lte: endDate },
          ...userFilter,
        },
      },
      {
        $group: {
          _id: {
            year: { $year: '$submittedDate' },
            month: { $month: '$submittedDate' },
            day: { $dayOfMonth: '$submittedDate' },
          },
          count: { $sum: 1 },
          awaitingDecision: {
            $sum: {
              $cond: [{ $eq: ['$status', 'awaitingDecision'] }, 1, 0],
            },
          },
          approved: {
            $sum: {
              $cond: [{ $eq: ['$status', 'approved'] }, 1, 0],
            },
          },
          approvedPaid: {
            $sum: {
              $cond: [{ $eq: ['$status', 'approvedPaid'] }, 1, 0],
            },
          },
          declined: {
            $sum: {
              $cond: [{ $eq: ['$status', 'declined'] }, 1, 0],
            },
          },
          cancelled: {
            $sum: {
              $cond: [{ $eq: ['$status', 'cancelled'] }, 1, 0],
            },
          },
          totalAmount: { $sum: '$loanAmount' },
        },
      },
      {
        $sort: {
          '_id.year': 1,
          '_id.month': 1,
          '_id.day': 1,
        },
      },
    ]);

    // Format the submissions by day for the chart
    const formattedSubmissionsByDay = submissionsByDay.map((day) => {
      const date = new Date(day._id.year, day._id.month - 1, day._id.day);
      return {
        date: date.toISOString().split('T')[0],
        total: day.count,
        awaitingDecision: day.awaitingDecision,
        approved: day.approved,
        approvedPaid: day.approvedPaid,
        // Add combined approved value
        combinedApproved: (day.approved || 0) + (day.approvedPaid || 0),
        declined: day.declined,
        cancelled: day.cancelled,
        totalAmount: day.totalAmount,
      };
    });

    // Get submissions by user
    // First, get all users who have submitted applications in the time period
    const activeUsers = await LoanApplication.aggregate([
      {
        $match: {
          submittedDate: { $gte: startDate, $lte: endDate },
        },
      },
      {
        $group: {
          _id: '$submittedBy',
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'user',
        },
      },
      {
        $unwind: '$user',
      },
      {
        $project: {
          userId: '$_id',
          fullName: '$user.fullName',
          username: '$user.username',
        },
      },
    ]);

    // Get submissions by user
    const submissionsByUser = await LoanApplication.aggregate([
      {
        $match: {
          submittedDate: { $gte: startDate, $lte: endDate },
        },
      },
      {
        $group: {
          _id: '$submittedBy',
          count: { $sum: 1 },
          awaitingDecision: {
            $sum: {
              $cond: [{ $eq: ['$status', 'awaitingDecision'] }, 1, 0],
            },
          },
          approved: {
            $sum: {
              $cond: [{ $eq: ['$status', 'approved'] }, 1, 0],
            },
          },
          approvedPaid: {
            $sum: {
              $cond: [{ $eq: ['$status', 'approvedPaid'] }, 1, 0],
            },
          },
          declined: {
            $sum: {
              $cond: [{ $eq: ['$status', 'declined'] }, 1, 0],
            },
          },
          cancelled: {
            $sum: {
              $cond: [{ $eq: ['$status', 'cancelled'] }, 1, 0],
            },
          },
          totalAmount: { $sum: '$loanAmount' },
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'user',
        },
      },
      {
        $unwind: '$user',
      },
      {
        $project: {
          userId: '$_id',
          fullName: '$user.fullName',
          username: '$user.username',
          count: 1,
          awaitingDecision: 1,
          approved: 1,
          approvedPaid: 1,
          // Add combined approved value
          combinedApproved: { $add: [{ $ifNull: ['$approved', 0] }, { $ifNull: ['$approvedPaid', 0] }] },
          declined: 1,
          cancelled: 1,
          totalAmount: 1,
        },
      },
      {
        $sort: { count: -1 },
      },
      // Ensure all users are included even if they have no submissions in this period
      {
        $group: {
          _id: '$userId',
          doc: { $first: '$$ROOT' },
        },
      },
      {
        $replaceRoot: {
          newRoot: '$doc',
        },
      },
    ]);

    return res.status(200).json({
      success: true,
      data: {
        statusCounts: statusCounts.reduce((acc, curr) => {
          acc[curr._id] = {
            count: curr.count,
            totalAmount: curr.totalAmount,
          };
          return acc;
        }, {}),
        submissionsByDay: formattedSubmissionsByDay,
        submissionsByUser: submissionsByUser,
        dateRange: {
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
          period,
        },
      },
    });
  } catch (error) {
    console.error('Error fetching loan application statistics:', error);
    return res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

// Get loan settings
router.get('/settings/bonus', requireUser, async (req, res) => {
  try {
    // Check permissions - only admin/manager can view settings
    const isAdminOrManager = ['admin', 'manager'].includes(req.user.role);

    if (!isAdminOrManager) {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to view loan settings',
      });
    }

    // Get the latest settings
    const settings = await LoanSettings.findOne()
      .sort({ updatedAt: -1 })
      .populate('updatedBy', 'fullName username');

    // If no settings exist, create default settings
    if (!settings) {
      const defaultSettings = new LoanSettings({
        submissionBonus: 0,
        fundedBonus: 0,
        weeklySubmissionGoal: 10,
        weeklyFundedGoal: 5,
        updatedBy: req.user._id,
      });

      await defaultSettings.save();

      const populatedSettings = await LoanSettings.findById(defaultSettings._id)
        .populate('updatedBy', 'fullName username');

      return res.status(200).json({
        success: true,
        data: populatedSettings,
      });
    }

    return res.status(200).json({
      success: true,
      data: settings,
    });
  } catch (error) {
    console.error('Error fetching loan settings:', error);
    return res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

// Update loan settings
router.put('/settings/bonus', requireUser, async (req, res) => {
  try {
    // Check permissions - only admin/manager can update settings
    const isAdminOrManager = ['admin', 'manager'].includes(req.user.role);

    if (!isAdminOrManager) {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to update loan settings',
      });
    }

    const { submissionBonus, fundedBonus, weeklySubmissionGoal, weeklyFundedGoal } = req.body;

    // Create new settings document (to keep history)
    const settings = new LoanSettings({
      submissionBonus,
      fundedBonus,
      weeklySubmissionGoal,
      weeklyFundedGoal,
      updatedBy: req.user._id,
    });

    await settings.save();

    // Populate user data before returning
    const populatedSettings = await LoanSettings.findById(settings._id)
      .populate('updatedBy', 'fullName username');

    return res.status(200).json({
      success: true,
      data: populatedSettings,
    });
  } catch (error) {
    console.error('Error updating loan settings:', error);
    return res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

module.exports = router;
