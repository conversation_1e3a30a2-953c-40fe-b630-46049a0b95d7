import { useState, useEffect } from 'react';

interface AuctionCountdownProps {
  endDate: string | Date | null;
}

export function AuctionCountdown({ endDate }: AuctionCountdownProps) {
  const [timeLeft, setTimeLeft] = useState<{
    days: number;
    hours: number;
    minutes: number;
    seconds: number;
    total: number;
  }>({ days: 0, hours: 0, minutes: 0, seconds: 0, total: 0 });

  useEffect(() => {
    // Return early if no end date
    if (!endDate) return;

    const calculateTimeLeft = () => {
      const end = new Date(endDate).getTime();
      const now = new Date().getTime();
      const difference = end - now;

      if (difference <= 0) {
        return {
          days: 0,
          hours: 0,
          minutes: 0,
          seconds: 0,
          total: 0
        };
      }

      return {
        days: Math.floor(difference / (1000 * 60 * 60 * 24)),
        hours: Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
        minutes: Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60)),
        seconds: Math.floor((difference % (1000 * 60)) / 1000),
        total: difference
      };
    };

    setTimeLeft(calculateTimeLeft());

    const timer = setInterval(() => {
      setTimeLeft(calculateTimeLeft());
    }, 1000);

    return () => clearInterval(timer);
  }, [endDate]);

  if (!endDate) return <span>No end date</span>;

  const formatDate = (date: string | Date) => {
    const d = new Date(date);
    const days = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
    const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];

    return `${days[d.getDay()]} ${d.getDate()} ${months[d.getMonth()]}, ${d.getHours()}:${d.getMinutes().toString().padStart(2, '0')}${d.getHours() >= 12 ? 'pm' : 'am'}`;
  };

  // Format remaining time
  const getTimeLeftString = () => {
    if (timeLeft.total <= 0) {
      return "Auction ended";
    }

    if (timeLeft.days > 0) {
      return `${timeLeft.days} days left`;
    }

    if (timeLeft.hours > 0) {
      return `${timeLeft.hours} hrs ${timeLeft.minutes} mins`;
    }

    return `${timeLeft.minutes} min ${timeLeft.seconds} sec`;
  };

  // Determine if the auction is ending soon (< 1 hour)
  const isEndingSoon = timeLeft.total > 0 && timeLeft.days === 0 && timeLeft.hours === 0;

  return (
    <div className="flex flex-col">
      <div className="text-sm">{formatDate(endDate)}</div>
      <div className={`text-xs font-medium ${isEndingSoon ? 'text-red-500' : 'text-muted-foreground'}`}>
        {getTimeLeftString()}
      </div>
    </div>
  );
}