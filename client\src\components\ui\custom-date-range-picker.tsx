import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { CalendarIcon } from 'lucide-react';
import { DateRange } from 'react-day-picker';

export interface DateRangeType {
  startDate: Date | undefined;
  endDate: Date | undefined;
  label?: string;
}

interface CustomDateRangePickerProps {
  onSelectDateRange: (range: DateRangeType) => void;
  currentDateRange: DateRangeType;
  currentPreset: string;
}

export const CustomDateRangePicker: React.FC<CustomDateRangePickerProps> = ({
  onSelectDateRange,
  currentDateRange,
  currentPreset
}) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: currentDateRange.startDate,
    to: currentDateRange.endDate,
  });

  const handleDateRangeSelect = (range: DateRange | undefined) => {
    setDateRange(range);
    
    if (range?.from && range?.to) {
      // Set end date to end of day
      const endDate = new Date(range.to);
      endDate.setHours(23, 59, 59, 999);
      
      onSelectDateRange({
        startDate: range.from,
        endDate: endDate,
        label: 'Custom Range'
      });
      setIsOpen(false);
    }
  };

  const isCustomSelected = currentPreset === 'Custom Range';

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant={isCustomSelected ? 'default' : 'outline'}
          size="sm"
          className="text-xs"
        >
          <CalendarIcon className="h-3 w-3 mr-1" />
          Custom
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <div className="p-4">
          <div className="mb-4">
            <h4 className="text-sm font-medium mb-2">Select Date Range</h4>
            <p className="text-xs text-muted-foreground">
              Choose a start and end date for your custom range
            </p>
          </div>
          <Calendar
            mode="range"
            defaultMonth={dateRange?.from}
            selected={dateRange}
            onSelect={handleDateRangeSelect}
            numberOfMonths={2}
            className="rounded-md border"
          />
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default CustomDateRangePicker;
