#!/usr/bin/env node

/**
 * Test Script for Virtual Display Screenshot Service
 * 
 * This script tests the virtual display implementation by taking a screenshot
 * of a simple webpage and verifying the output.
 */

const os = require('os');
const fs = require('fs');
const path = require('path');

// Mock the updateStatus function and screenshotStatus for testing
let testLogs = [];
global.screenshotStatus = {
  logs: []
};

function updateStatus(update) {
  if (update.message) {
    const logEntry = {
      time: new Date(),
      message: update.message
    };
    testLogs.push(logEntry);
    console.log(`[${logEntry.time.toISOString()}] ${update.message}`);
  }
}

// Mock DashboardSettings for testing
const DashboardSettings = {
  findOne: () => null
};

// Override the require for the screenshot service
const Module = require('module');
const originalRequire = Module.prototype.require;

Module.prototype.require = function(id) {
  if (id === '../models/DashboardSettings') {
    return DashboardSettings;
  }
  return originalRequire.apply(this, arguments);
};

// Now require the screenshot service
const { captureScreenshot } = require('./services/screenshotService');

async function testVirtualDisplay() {
  console.log('='.repeat(60));
  console.log('Virtual Display Screenshot Service Test');
  console.log('='.repeat(60));
  console.log(`Platform: ${os.platform()}`);
  console.log(`Architecture: ${os.arch()}`);
  console.log(`Node.js Version: ${process.version}`);
  console.log('='.repeat(60));

  try {
    console.log('\n🚀 Starting virtual display test...\n');

    // Test URL - using a simple webpage that should render consistently
    const testUrl = 'data:text/html,<html><head><title>Test Page</title><style>body{font-family:Arial;padding:50px;background:#f0f0f0;}</style></head><body><h1>Virtual Display Test</h1><p>This is a test page for the virtual display screenshot service.</p><div style="width:200px;height:100px;background:linear-gradient(45deg,#ff6b6b,#4ecdc4);margin:20px 0;"></div><p>Current time: ' + new Date().toISOString() + '</p></body></html>';

    // Take a screenshot
    const startTime = Date.now();
    const base64Image = await captureScreenshot(testUrl, 'VirtualDisplayTest');
    const endTime = Date.now();

    console.log(`\n✅ Screenshot completed in ${endTime - startTime}ms`);

    // Verify the screenshot
    if (base64Image && base64Image.length > 0) {
      const imageBuffer = Buffer.from(base64Image, 'base64');
      console.log(`📊 Screenshot size: ${imageBuffer.length} bytes`);

      // Save the test screenshot
      const testDir = path.join(__dirname, 'test-output');
      if (!fs.existsSync(testDir)) {
        fs.mkdirSync(testDir, { recursive: true });
      }

      const outputPath = path.join(testDir, `virtual-display-test-${Date.now()}.png`);
      fs.writeFileSync(outputPath, imageBuffer);
      console.log(`💾 Test screenshot saved to: ${outputPath}`);

      // Basic validation
      if (imageBuffer.length < 1000) {
        console.log('⚠️  Warning: Screenshot seems unusually small');
      } else if (imageBuffer.length > 10000) {
        console.log('✅ Screenshot size looks good');
      }

      // Check if it's a valid PNG
      const pngHeader = imageBuffer.slice(0, 8);
      const expectedPngHeader = Buffer.from([0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A]);
      if (pngHeader.equals(expectedPngHeader)) {
        console.log('✅ Valid PNG format detected');
      } else {
        console.log('❌ Invalid PNG format');
      }

    } else {
      console.log('❌ No screenshot data returned');
      return false;
    }

    console.log('\n📋 Test Summary:');
    console.log(`   Platform: ${os.platform()}`);
    console.log(`   Duration: ${endTime - startTime}ms`);
    console.log(`   Image Size: ${Buffer.from(base64Image, 'base64').length} bytes`);
    console.log(`   Log Entries: ${testLogs.length}`);

    console.log('\n📝 Virtual Display Logs:');
    testLogs.forEach((log, index) => {
      console.log(`   ${index + 1}. ${log.message}`);
    });

    console.log('\n🎉 Virtual display test completed successfully!');
    return true;

  } catch (error) {
    console.error('\n❌ Virtual display test failed:');
    console.error(`   Error: ${error.message}`);
    console.error(`   Stack: ${error.stack}`);

    console.log('\n📝 Error Logs:');
    testLogs.forEach((log, index) => {
      console.log(`   ${index + 1}. ${log.message}`);
    });

    return false;
  }
}

async function testPlatformDetection() {
  console.log('\n🔍 Testing platform detection...');
  
  const platform = os.platform();
  console.log(`   Detected platform: ${platform}`);
  
  switch (platform) {
    case 'linux':
      console.log('   Expected behavior: Xvfb virtual display (if available)');
      break;
    case 'win32':
      console.log('   Expected behavior: Enhanced Windows headless mode');
      break;
    case 'darwin':
      console.log('   Expected behavior: Enhanced macOS headless mode');
      break;
    default:
      console.log('   Expected behavior: Generic enhanced headless mode');
  }
}

async function checkDependencies() {
  console.log('\n🔧 Checking dependencies...');
  
  try {
    const puppeteer = require('puppeteer');
    console.log('   ✅ Puppeteer available');
  } catch (error) {
    console.log('   ❌ Puppeteer not available');
    return false;
  }

  try {
    const sharp = require('sharp');
    console.log('   ✅ Sharp available');
  } catch (error) {
    console.log('   ❌ Sharp not available');
    return false;
  }

  if (os.platform() === 'linux') {
    const { spawn } = require('child_process');
    try {
      const xvfbCheck = spawn('which', ['Xvfb'], { stdio: 'pipe' });
      await new Promise((resolve, reject) => {
        xvfbCheck.on('exit', (code) => {
          if (code === 0) {
            console.log('   ✅ Xvfb available');
            resolve();
          } else {
            console.log('   ⚠️  Xvfb not found (will use fallback mode)');
            resolve();
          }
        });
        xvfbCheck.on('error', () => {
          console.log('   ⚠️  Cannot check Xvfb availability');
          resolve();
        });
      });
    } catch (error) {
      console.log('   ⚠️  Cannot check Xvfb availability');
    }
  }

  return true;
}

// Main test execution
async function main() {
  try {
    await testPlatformDetection();
    
    const depsOk = await checkDependencies();
    if (!depsOk) {
      console.log('\n❌ Dependency check failed. Cannot proceed with test.');
      process.exit(1);
    }

    const success = await testVirtualDisplay();
    
    if (success) {
      console.log('\n🎊 All tests passed! Virtual display implementation is working correctly.');
      process.exit(0);
    } else {
      console.log('\n💥 Test failed. Please check the error messages above.');
      process.exit(1);
    }
  } catch (error) {
    console.error('\n💥 Unexpected error during testing:');
    console.error(error);
    process.exit(1);
  }
}

// Run the test if this script is executed directly
if (require.main === module) {
  main();
}

module.exports = {
  testVirtualDisplay,
  testPlatformDetection,
  checkDependencies
};
