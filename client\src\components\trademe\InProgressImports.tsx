import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, AlertCircle, Clock, ArrowRightCircle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { getInProgressImports } from '@/api/tradeMeImport';
import { formatDistanceToNow } from 'date-fns';

const InProgressImports: React.FC = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [inProgressImports, setInProgressImports] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadInProgressImports = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const result = await getInProgressImports();
        if (result.success) {
          setInProgressImports(result.inProgressImports);
        } else {
          setError(result.error || 'Failed to load in-progress imports');
        }
      } catch (error: any) {
        console.error('Error loading in-progress imports:', error);
        setError(error.message || 'Failed to load in-progress imports');
      } finally {
        setIsLoading(false);
      }
    };

    loadInProgressImports();
  }, []);

  const handleResumeImport = (importLogId: string) => {
    navigate(`/trademe/import/wizard/${importLogId}`);
  };

  if (isLoading) {
    return (
      <Card className="mt-6">
        <CardHeader>
          <CardTitle className="text-lg">In-Progress Imports</CardTitle>
          <CardDescription>Resume your previous import sessions</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center items-center py-4">
            <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="mt-6">
        <CardHeader>
          <CardTitle className="text-lg">In-Progress Imports</CardTitle>
          <CardDescription>Resume your previous import sessions</CardDescription>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  if (inProgressImports.length === 0) {
    return null; // Don't show anything if there are no in-progress imports
  }

  return (
    <Card className="mt-6">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg">In-Progress Imports</CardTitle>
        <CardDescription>Resume your previous import sessions</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {inProgressImports.map((importItem) => (
            <div
              key={importItem.importLogId}
              className="border rounded-md p-4 hover:bg-muted/30 transition-colors cursor-pointer"
              onClick={() => handleResumeImport(importItem.importLogId)}
            >
              <div className="flex justify-between items-start">
                <div>
                  <div className="flex items-center gap-2">
                    <h3 className="font-medium">
                      {importItem.importLog?.name || `Import ${importItem.importLogId.substring(0, 8)}...`}
                    </h3>
                    {importItem.progress.importType && (
                      <Badge variant="outline" className={importItem.progress.importType === 'sold' ? 'bg-amber-500/10 text-amber-600' : 'bg-green-500/10 text-green-600'}>
                        {importItem.progress.importType === 'sold' ? 'Sold Items' : 'Active Listings'}
                      </Badge>
                    )}
                  </div>
                  <div className="flex items-center text-sm text-muted-foreground mt-1">
                    <Clock className="h-3 w-3 mr-1" />
                    <span>
                      Started {formatDistanceToNow(new Date(importItem.timestamp), { addSuffix: true })}
                    </span>
                  </div>
                  <div className="mt-2 flex flex-wrap items-center gap-2">
                    <Badge variant="outline" className="bg-green-500/10">
                      {importItem.progress.completedItems.length} completed
                    </Badge>
                    <Badge variant="outline" className="bg-yellow-500/10">
                      {importItem.progress.skippedItems.length} skipped
                    </Badge>
                    <Badge variant="outline" className="bg-blue-500/10">
                      Item {importItem.progress.currentItemIndex + 1} of {importItem.progress.totalItems || importItem.importLog?.stats?.itemsProcessed || '?'}
                    </Badge>
                    <Badge variant="outline" className="bg-purple-500/10">
                      {Math.round(((importItem.progress.completedItems.length + importItem.progress.skippedItems.length) /
                      (importItem.progress.totalItems || importItem.importLog?.stats?.itemsProcessed || 100)) * 100)}% complete
                    </Badge>
                  </div>
                </div>
                <Button
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleResumeImport(importItem.importLogId);
                  }}
                  className="ml-4"
                >
                  <ArrowRightCircle className="h-4 w-4 mr-2" />
                  Resume
                </Button>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default InProgressImports;
