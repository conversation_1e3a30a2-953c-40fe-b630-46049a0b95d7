import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/hooks/useToast';
import { useAuth } from '@/contexts/AuthContext';
import { getAudits, completeFollowup, Audit } from '@/api/buyPawnAudits';
import { PaginatedResponse } from '@/api/buyPawnAudits';
import { AuditNavigation } from '@/components/buys/audits/AuditNavigation';
import { AuditFlaggingActions } from '@/components/buys/audits/AuditFlaggingActions';

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Loader2, Search, Flag, CheckCircle, AlertCircle } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { format } from 'date-fns';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';

export function FlaggedAudits() {
  const { toast } = useToast();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [audits, setAudits] = useState<Audit[]>([]);
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    limit: 10,
    pages: 1,
  });

  // Filter states
  const [searchTerm, setSearchTerm] = useState('');

  // Follow-up dialog states
  const [selectedAudit, setSelectedAudit] = useState<Audit | null>(null);
  const [followupResponse, setFollowupResponse] = useState('');
  const [dialogOpen, setDialogOpen] = useState(false);

  // Load flagged audits
  const loadAudits = async (page = 1) => {
    setIsLoading(true);
    try {
      const filters: any = {
        page,
        limit: pagination.limit,
        sort: 'createdAt',
        sortDirection: 'desc',
        flaggedForFollowup: true,
      };

      // Apply search filter
      if (searchTerm) filters.search = searchTerm;

      const result: PaginatedResponse<Audit> = await getAudits(filters);

      if (result.success) {
        setAudits(result.data);
        setPagination(result.pagination);
      } else {
        toast({
          title: 'Error',
          description: result.error || 'Failed to load flagged audits.',
          variant: 'destructive',
        });
      }
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'An unexpected error occurred.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    loadAudits();
  }, [searchTerm]);

  // Format date
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'MMM d, yyyy');
  };

  // Handle submitting the follow-up
  const handleSubmitFollowup = async () => {
    if (!selectedAudit) return;

    if (!followupResponse.trim()) {
      toast({
        title: 'Missing Information',
        description: 'Please enter a follow-up response.',
        variant: 'destructive',
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const result = await markAuditFollowedUp(selectedAudit._id, followupResponse);

      if (result.success) {
        toast({
          title: 'Follow-up Recorded',
          description: 'The follow-up has been recorded successfully.',
        });

        // Update the audit in the list
        setAudits(audits.map(audit =>
          audit._id === selectedAudit._id ? { ...audit, followedUp: true, followupResponse } : audit
        ));

        // Close the dialog
        setDialogOpen(false);
        setSelectedAudit(null);
        setFollowupResponse('');

        // Reload audits
        loadAudits(pagination.page);
      } else {
        toast({
          title: 'Error',
          description: result.error || 'Failed to record follow-up.',
          variant: 'destructive',
        });
      }
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'An unexpected error occurred.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle opening the follow-up dialog
  const handleOpenFollowup = (audit: Audit) => {
    setSelectedAudit(audit);
    setFollowupResponse('');
    setDialogOpen(true);
  };

  // Get compliance badge
  const getComplianceBadge = (compliance: string) => {
    switch (compliance) {
      case 'compliant':
        return <Badge className="bg-green-500">Compliant</Badge>;
      case 'minor_non_compliant':
        return <Badge className="bg-yellow-500">Minor Non-Compliant</Badge>;
      case 'major_non_compliant':
        return <Badge className="bg-red-500">Major Non-Compliant</Badge>;
      default:
        return <Badge variant="outline">{compliance}</Badge>;
    }
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'draft':
        return <Badge variant="outline">Draft</Badge>;
      case 'completed':
        return <Badge variant="success">Completed</Badge>;
      case 'flagged':
        return <Badge variant="destructive">Flagged</Badge>;
      case 'resolved':
        return <Badge variant="secondary">Resolved</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col gap-4 mb-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold">Flagged Audits</h1>
            <p className="text-muted-foreground">Manage audits that require follow-up</p>
          </div>
        </div>

        <AuditNavigation />
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Flagged Audits</CardTitle>
          <CardDescription>
            Audits that have been flagged for follow-up with employees
          </CardDescription>

          <div className="flex flex-col sm:flex-row gap-4 mt-4">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by Transaction ID"
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Button variant="outline" onClick={() => loadAudits(1)}>
              Refresh
            </Button>
          </div>
        </CardHeader>

        <CardContent>
          {isLoading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : audits.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No flagged audits found. All issues have been resolved!
            </div>
          ) : (
            <>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Transaction ID</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Employee</TableHead>
                      <TableHead>Compliance</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Follow-up</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {audits.map((audit) => (
                      <TableRow key={audit._id}>
                        <TableCell className="font-medium">
                          <Button
                            variant="link"
                            className="p-0 h-auto font-medium"
                            onClick={() => navigate(`/buys/audits/${audit._id}`)}
                          >
                            {audit.transactionId}
                          </Button>
                        </TableCell>
                        <TableCell className="capitalize">{audit.auditType}</TableCell>
                        <TableCell>{audit.employeeName}</TableCell>
                        <TableCell>{getComplianceBadge(audit.overallCompliance)}</TableCell>
                        <TableCell>{getStatusBadge(audit.status)}</TableCell>
                        <TableCell>
                          <AuditFlaggingActions
                            audit={audit}
                            onUpdate={() => loadAudits(pagination.page)}
                            size="sm"
                            variant="ghost"
                          />
                        </TableCell>
                        <TableCell>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => navigate(`/buys/audits/${audit._id}`)}
                          >
                            View Details
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination */}
              {pagination.pages > 1 && (
                <Pagination className="mt-4">
                  <PaginationContent>
                    <PaginationItem>
                      <PaginationPrevious
                        onClick={() => loadAudits(Math.max(1, pagination.page - 1))}
                        disabled={pagination.page === 1}
                      />
                    </PaginationItem>

                    {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                      const pageNumber = i + 1;
                      return (
                        <PaginationItem key={pageNumber}>
                          <PaginationLink
                            onClick={() => loadAudits(pageNumber)}
                            isActive={pagination.page === pageNumber}
                          >
                            {pageNumber}
                          </PaginationLink>
                        </PaginationItem>
                      );
                    })}

                    {pagination.pages > 5 && (
                      <>
                        <PaginationItem>
                          <PaginationEllipsis />
                        </PaginationItem>
                        <PaginationItem>
                          <PaginationLink
                            onClick={() => loadAudits(pagination.pages)}
                            isActive={pagination.page === pagination.pages}
                          >
                            {pagination.pages}
                          </PaginationLink>
                        </PaginationItem>
                      </>
                    )}

                    <PaginationItem>
                      <PaginationNext
                        onClick={() => loadAudits(Math.min(pagination.pages, pagination.page + 1))}
                        disabled={pagination.page === pagination.pages}
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Follow-up Dialog */}
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Record Follow-up</DialogTitle>
            <DialogDescription>
              Record the follow-up conversation with the employee.
            </DialogDescription>
          </DialogHeader>

          {selectedAudit && (
            <div className="space-y-4">
              <div>
                <p className="text-sm font-medium">Transaction ID</p>
                <p className="text-sm">{selectedAudit.transactionId}</p>
              </div>

              <div>
                <p className="text-sm font-medium">Employee</p>
                <p className="text-sm">{selectedAudit.employeeName}</p>
              </div>

              <div>
                <p className="text-sm font-medium">Flag Reason</p>
                <p className="text-sm">{selectedAudit.flagReason || 'No reason provided'}</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="followupResponse">Follow-up Response</Label>
                <Textarea
                  id="followupResponse"
                  placeholder="Enter details of the follow-up conversation"
                  value={followupResponse}
                  onChange={(e) => setFollowupResponse(e.target.value)}
                  className="min-h-[100px]"
                />
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSubmitFollowup} disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                'Save Follow-up'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
