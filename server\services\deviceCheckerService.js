const axios = require('axios');
const DeviceCheckerSettings = require('../models/DeviceCheckerSettings');
const DeviceChecker = require('../models/DeviceChecker');

/**
 * Mask an API key, preserving hyphens and showing only the last 4 characters
 * @param {string} apiKey - The API key to mask
 * @returns {string} The masked API key
 */
function maskApiKey(apiKey) {
  if (!apiKey) return '';

  // If the key already contains 'X', it's already masked
  if (apiKey.includes('X')) return apiKey;

  // Get the last 4 characters
  const last4 = apiKey.slice(-4);

  // Replace all characters except hyphens with 'X'
  let maskedPart = '';
  for (let i = 0; i < apiKey.length - 4; i++) {
    maskedPart += apiKey[i] === '-' ? '-' : 'X';
  }

  return maskedPart + last4;
}

/**
 * Get device checker settings
 * @returns {Promise<Object>} Device checker settings
 */
async function getSettings() {
  try {
    const settings = await DeviceCheckerSettings.getSettings();
    return {
      success: true,
      settings: {
        apiType: settings.apiType,
        apiTitle: settings.apiTitle,
        username: settings.username,
        url: settings.url,
        apiKey: settings.apiKey ? maskApiKey(settings.apiKey) : '', // Mask with X but show last 4 chars
        enabledServices: settings.enabledServices || [],
        accountBalance: settings.accountBalance,
        lastUpdated: settings.lastUpdated
      }
    };
  } catch (error) {
    console.error('Error fetching device checker settings:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Update device checker settings
 * @param {Object} settingsData - The settings data to update
 * @returns {Promise<Object>} Updated settings
 */
async function updateSettings(settingsData) {
  try {
    const settings = await DeviceCheckerSettings.getSettings();

    // Update fields if provided
    if (settingsData.apiType) settings.apiType = settingsData.apiType;
    if (settingsData.apiTitle) settings.apiTitle = settingsData.apiTitle;
    if (settingsData.username) settings.username = settingsData.username;
    if (settingsData.url) settings.url = settingsData.url;
    if (settingsData.apiKey) {
      console.log('Updating API key:', settingsData.apiKey.substring(0, 3) + '...' + settingsData.apiKey.slice(-4));
      settings.apiKey = settingsData.apiKey;
    }
    if (settingsData.enabledServices) settings.enabledServices = settingsData.enabledServices;

    settings.lastUpdated = new Date();
    await settings.save();

    return {
      success: true,
      settings: {
        apiType: settings.apiType,
        apiTitle: settings.apiTitle,
        username: settings.username,
        url: settings.url,
        apiKey: settings.apiKey ? maskApiKey(settings.apiKey) : '', // Mask with X but show last 4 chars
        enabledServices: settings.enabledServices || [],
        accountBalance: settings.accountBalance,
        lastUpdated: settings.lastUpdated
      }
    };
  } catch (error) {
    console.error('Error updating device checker settings:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Verify connection to the API
 * @returns {Promise<Object>} Connection verification result
 */
async function verifyConnection() {
  try {
    const settings = await DeviceCheckerSettings.getSettings();

    if (!settings.apiKey || !settings.url) {
      return {
        success: false,
        error: 'API credentials are not configured'
      };
    }

    // Check account balance as a way to verify connection
    const balanceResult = await getAccountBalance();

    console.log('Verify connection balance result:', balanceResult);

    if (!balanceResult.success) {
      return {
        success: false,
        error: balanceResult.error
      };
    }

    return {
      success: true,
      message: 'Connection verified successfully',
      balance: balanceResult.balance
    };
  } catch (error) {
    console.error('Error verifying API connection:', error);
    // Log more detailed error information
    if (error.response) {
      console.error('Error response data:', error.response.data);
      console.error('Error response status:', error.response.status);
      console.error('Error response headers:', error.response.headers);
    } else if (error.request) {
      console.error('Error request:', error.request);
    }
    return {
      success: false,
      error: error.response?.data?.message || error.message
    };
  }
}

/**
 * Get account balance from the API
 * @returns {Promise<Object>} Account balance
 */
async function getAccountBalance() {
  try {
    const settings = await DeviceCheckerSettings.getSettings();

    if (!settings.apiKey || !settings.url) {
      return {
        success: false,
        error: 'API credentials are not configured'
      };
    }

    // Create URLSearchParams for the POST request (similar to form data)
    const params = new URLSearchParams();
    params.append('accountinfo', 'balance');
    params.append('key', settings.apiKey);

    const response = await axios.post(settings.url, params, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json'
      },
      timeout: 60000 // 60 seconds timeout, matching the PHP example
    });

    // Log the response for debugging
    console.log('Balance API Response:', JSON.stringify(response.data));

    // Check if we have a valid response
    if (response.data) {
      // Handle successful response
      if (response.data.success === true) {
        // Try to extract balance from different possible locations in the response
        let balance = 0;
        if (response.data.object && response.data.object.account_balance) {
          balance = parseFloat(response.data.object.account_balance) || 0;
        } else if (response.data.object && response.data.object.available_balance) {
          balance = parseFloat(response.data.object.available_balance) || 0;
        } else if (response.data.balance) {
          balance = parseFloat(response.data.balance) || 0;
        }

        // Update the balance in settings
        settings.accountBalance = balance;
        settings.lastUpdated = new Date();
        await settings.save();

        return {
          success: true,
          balance: settings.accountBalance
        };
      } else {
        return {
          success: false,
          error: response.data.error || response.data.message || 'Failed to retrieve account balance'
        };
      }
    } else {
      return {
        success: false,
        error: 'Invalid response from API'
      };
    }
  } catch (error) {
    console.error('Error getting account balance:', error);
    // Log more detailed error information
    if (error.response) {
      console.error('Error response data:', error.response.data);
      console.error('Error response status:', error.response.status);
      console.error('Error response headers:', error.response.headers);
    } else if (error.request) {
      console.error('Error request:', error.request);
    }
    return {
      success: false,
      error: error.response?.data?.message || error.message
    };
  }
}

/**
 * Get available services from the local database
 * @returns {Promise<Object>} Available services from the database
 */
async function getLocalServices() {
  try {
    const settings = await DeviceCheckerSettings.getSettings();

    // Return the services stored in the database
    return {
      success: true,
      services: settings.enabledServices || []
    };
  } catch (error) {
    console.error('Error getting local services:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Refresh available services from the external API
 * @returns {Promise<Object>} Updated services from the API
 */
async function refreshAvailableServices() {
  try {
    const settings = await DeviceCheckerSettings.getSettings();

    if (!settings.apiKey || !settings.url) {
      return {
        success: false,
        error: 'API credentials are not configured'
      };
    }

    // Create URLSearchParams for the POST request (similar to form data)
    const params = new URLSearchParams();
    params.append('accountinfo', 'servicelist');
    params.append('key', settings.apiKey);

    const response = await axios.post(settings.url, params, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json'
      },
      timeout: 60000 // 60 seconds timeout, matching the PHP example
    });

    // Log the response for debugging
    console.log('Services API Response:', JSON.stringify(response.data));

    // Check if we have a valid response
    if (response.data) {
      // Handle successful response
      if (response.data.success === true) {
        // Try to extract services from the response
        const services = response.data.object || {};

        // Log a sample service to see its structure
        const sampleServiceId = Object.keys(services)[0];
        if (sampleServiceId) {
          console.log('Sample service structure:', JSON.stringify(services[sampleServiceId], null, 2));
        }

        // Update enabled services in settings
        const currentEnabledServices = settings.enabledServices || [];
        const currentEnabledMap = {};

        currentEnabledServices.forEach(service => {
          currentEnabledMap[service.serviceId] = service.enabled;
        });

        // Map services and preserve enabled status and custom names
        const mappedServices = Object.entries(services).map(([id, service]) => {
          // Find existing service to preserve custom name and enabled status
          const existingService = currentEnabledServices.find(s => s.serviceId === id);

          return {
            serviceId: id,
            // Preserve custom name if it exists
            name: existingService ? existingService.name : service.name,
            cost: parseFloat(service.price) || 0,
            description: service.description || '',
            enabled: currentEnabledMap[id] !== undefined ? currentEnabledMap[id] : true
          };
        });

        // Update settings
        settings.enabledServices = mappedServices;
        settings.lastUpdated = new Date();
        await settings.save();

        return {
          success: true,
          services: mappedServices
        };
      } else {
        return {
          success: false,
          error: response.data.error || response.data.message || 'Failed to retrieve available services'
        };
      }
    } else {
      return {
        success: false,
        error: 'Invalid response from API'
      };
    }
  } catch (error) {
    console.error('Error refreshing available services:', error);
    // Log more detailed error information
    if (error.response) {
      console.error('Error response data:', error.response.data);
      console.error('Error response status:', error.response.status);
      console.error('Error response headers:', error.response.headers);
    } else if (error.request) {
      console.error('Error request:', error.request);
    }
    return {
      success: false,
      error: error.response?.data?.message || error.message
    };
  }
}

/**
 * Get available services (alias for getLocalServices for backward compatibility)
 * @returns {Promise<Object>} Available services from the database
 */
async function getAvailableServices() {
  return getLocalServices();
}

/**
 * Lookup device information
 * @param {Object} data - The lookup data
 * @param {Object} user - The user performing the lookup
 * @returns {Promise<Object>} Lookup result
 */
async function lookupDevice(data, user) {
  try {
    const { deviceIdentifier, serviceId, checkAgain = false } = data;

    if (!deviceIdentifier || !serviceId) {
      return {
        success: false,
        error: 'Device identifier and service are required'
      };
    }

    // Check if this device has been looked up before
    if (!checkAgain) {
      const existingLookup = await DeviceChecker.findOne({
        deviceIdentifier: deviceIdentifier,
        serviceId: serviceId
      }).sort({ createdAt: -1 });

      if (existingLookup) {
        return {
          success: true,
          result: existingLookup.result,
          isExisting: true,
          lookupId: existingLookup._id,
          createdAt: existingLookup.createdAt,
          username: existingLookup.username
        };
      }
    }

    // Get settings
    const settings = await DeviceCheckerSettings.getSettings();

    if (!settings.apiKey || !settings.url) {
      return {
        success: false,
        error: 'API credentials are not configured'
      };
    }

    // Find the service
    const service = settings.enabledServices.find(s => s.serviceId === serviceId);

    if (!service) {
      return {
        success: false,
        error: 'Selected service not found'
      };
    }

    if (!service.enabled) {
      return {
        success: false,
        error: 'Selected service is not enabled'
      };
    }

    // Create URLSearchParams for the POST request (similar to form data)
    const params = new URLSearchParams();
    params.append('key', settings.apiKey);
    params.append('service', serviceId);
    params.append('imei', deviceIdentifier);

    // Make API request
    const response = await axios.post(settings.url, params, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json'
      },
      timeout: 60000 // 60 seconds timeout, matching the PHP example
    });

    // Log the response for debugging
    console.log('Device Lookup API Response:', JSON.stringify(response.data));

    // Check if we have a valid response
    if (response.data) {
      // Create a record of the lookup
      const deviceChecker = new DeviceChecker({
        deviceIdentifier,
        serviceId,
        serviceName: service.name,
        cost: service.cost,
        result: response.data,
        status: response.data.success === true ? 'success' : 'error',
        userId: user._id,
        username: user.username || user.email
      });

      await deviceChecker.save();

      // Update account balance if successful
      if (response.data.success === true) {
        await getAccountBalance();
      }

      return {
        success: true,
        result: response.data,
        lookupId: deviceChecker._id,
        createdAt: deviceChecker.createdAt
      };
    } else {
      return {
        success: false,
        error: 'Invalid response from API'
      };
    }
  } catch (error) {
    console.error('Error looking up device:', error);
    // Log more detailed error information
    if (error.response) {
      console.error('Error response data:', error.response.data);
      console.error('Error response status:', error.response.status);
      console.error('Error response headers:', error.response.headers);
    } else if (error.request) {
      console.error('Error request:', error.request);
    }
    return {
      success: false,
      error: error.response?.data?.message || error.message
    };
  }
}

/**
 * Get lookup history
 * @param {Object} query - Query parameters
 * @returns {Promise<Object>} Lookup history
 */
async function getLookupHistory(query = {}) {
  try {
    const { page = 1, limit = 10, userId } = query;

    const queryFilter = {};
    if (userId) {
      queryFilter.userId = userId;
    }

    const total = await DeviceChecker.countDocuments(queryFilter);
    const lookups = await DeviceChecker.find(queryFilter)
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(parseInt(limit))
      .populate('userId', 'username email');

    return {
      success: true,
      lookups,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(total / limit)
      }
    };
  } catch (error) {
    console.error('Error getting lookup history:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

module.exports = {
  getSettings,
  updateSettings,
  verifyConnection,
  getAccountBalance,
  getAvailableServices,
  getLocalServices,
  refreshAvailableServices,
  lookupDevice,
  getLookupHistory
};
