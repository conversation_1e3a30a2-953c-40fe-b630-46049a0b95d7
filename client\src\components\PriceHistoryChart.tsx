import { useState, useRef, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogTitle, DialogHeader, DialogDescription } from '@/components/ui/dialog';
import { Maximize, ZoomIn, ZoomOut, RefreshCw } from 'lucide-react';
import {
  Line,
  LineChart,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  ReferenceArea,
  ReferenceLine
} from 'recharts';

interface HistoricalPrice {
  metal: string;
  date: string;
  price: number;
}

interface PriceHistoryChartProps {
  metal: string;
  data: HistoricalPrice[];
  loading: boolean;
  color?: string;
}

const DEFAULT_COLORS = {
  Gold: '#fbbf24',
  Silver: '#94a3b8',
  Platinum: '#e5e7eb',
  Palladium: '#fb923c'
};

const TIME_PERIODS = [
  { label: '1W', days: 7 },
  { label: '1M', days: 30 },
  { label: '2M', days: 60 },
  { label: '3M', days: 90 },
  { label: '6M', days: 180 },
  { label: '1Y', days: 365 }
];

export function PriceHistoryChart({ metal, data, loading, color }: PriceHistoryChartProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [refAreaLeft, setRefAreaLeft] = useState('');
  const [refAreaRight, setRefAreaRight] = useState('');
  const [leftIndex, setLeftIndex] = useState(0);
  const [rightIndex, setRightIndex] = useState<number>(0);
  const [datesToShow, setDatesToShow] = useState<HistoricalPrice[]>([]);
  const [activePeriod, setActivePeriod] = useState('1Y');
  const chartRef = useRef<any>(null);

  const chartColor = color || DEFAULT_COLORS[metal as keyof typeof DEFAULT_COLORS] || '#8884d8';
  const currentPrice = data.length > 0 ? data[data.length - 1].price : 0;

  // Initialize chart data
  useEffect(() => {
    if (data.length > 0) {
      setDatesToShow(data);
      setLeftIndex(0);
      setRightIndex(data.length - 1);
    }
  }, [data]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  const formatPrice = (value: number) => {
    return `$${value.toFixed(2)}`;
  };

  const zoomOut = () => {
    setDatesToShow(data);
    setLeftIndex(0);
    setRightIndex(data.length - 1);
    setActivePeriod('1Y');
  };

  const zoomIn = () => {
    if (datesToShow.length <= 10) return;

    const newLeftIndex = Math.floor(leftIndex + (rightIndex - leftIndex) * 0.25);
    const newRightIndex = Math.floor(rightIndex - (rightIndex - leftIndex) * 0.25);

    setLeftIndex(newLeftIndex);
    setRightIndex(newRightIndex);
    setDatesToShow(data.slice(newLeftIndex, newRightIndex + 1));
    setActivePeriod('');
  };

  const handleMouseDown = (e: any) => {
    if (!isExpanded || !e || !e.activeLabel) return;
    setRefAreaLeft(e.activeLabel);
  };

  const handleMouseMove = (e: any) => {
    if (!isExpanded || !refAreaLeft || !e || !e.activeLabel) return;
    setRefAreaRight(e.activeLabel);
  };

  const handleMouseUp = () => {
    if (!isExpanded || !refAreaLeft || !refAreaRight) {
      setRefAreaLeft('');
      setRefAreaRight('');
      return;
    }

    // Convert strings to dates for comparison
    let left = new Date(refAreaLeft);
    let right = new Date(refAreaRight);

    // Swap if needed
    if (left > right) {
      [left, right] = [right, left];
    }

    // Find indices of dates
    const newLeftIndex = data.findIndex(item => new Date(item.date) >= left);
    let newRightIndex = data.findIndex(item => new Date(item.date) > right);
    if (newRightIndex === -1) newRightIndex = data.length - 1;
    else newRightIndex--;

    // Only zoom if selection is meaningful
    if (newRightIndex - newLeftIndex > 5) {
      setLeftIndex(newLeftIndex);
      setRightIndex(newRightIndex);
      setDatesToShow(data.slice(newLeftIndex, newRightIndex + 1));
      setActivePeriod('');
    }

    setRefAreaLeft('');
    setRefAreaRight('');
  };

  const handleWheel = (e: React.WheelEvent<HTMLDivElement>) => {
    if (!isExpanded || data.length <= 10) return;
    e.preventDefault();

    const delta = e.deltaY > 0 ? 1 : -1; // 1 for zoom out, -1 for zoom in
    const range = rightIndex - leftIndex;
    const step = Math.max(1, Math.floor(range * 0.1)); // 10% step

    if (delta < 0) {
      // Zoom in
      const newLeftIndex = Math.min(rightIndex - 10, leftIndex + step);
      const newRightIndex = Math.max(newLeftIndex + 9, rightIndex - step);

      setLeftIndex(newLeftIndex);
      setRightIndex(newRightIndex);
      setDatesToShow(data.slice(newLeftIndex, newRightIndex + 1));
      setActivePeriod('');
    } else {
      // Zoom out
      const newLeftIndex = Math.max(0, leftIndex - step);
      const newRightIndex = Math.min(data.length - 1, rightIndex + step);

      setLeftIndex(newLeftIndex);
      setRightIndex(newRightIndex);
      setDatesToShow(data.slice(newLeftIndex, newRightIndex + 1));

      // Reset to full view if we're close to showing all data
      if (newLeftIndex <= 5 && newRightIndex >= data.length - 6) {
        zoomOut();
      }

      setActivePeriod('');
    }
  };

  const handlePeriodClick = (days: number, label: string) => {
    if (data.length === 0) return;

    setActivePeriod(label);

    if (days >= data.length) {
      zoomOut();
      return;
    }

    const newLeftIndex = Math.max(0, data.length - days);
    const newRightIndex = data.length - 1;

    setLeftIndex(newLeftIndex);
    setRightIndex(newRightIndex);
    setDatesToShow(data.slice(newLeftIndex, newRightIndex + 1));
  };

  const renderChart = (height: number = 400) => (
    <div
      className="w-full chart-container"
      style={{ height: `${height}px` }}
      onWheel={handleWheel}
    >
      {loading ? (
        <div className="flex h-full items-center justify-center">Loading chart data...</div>
      ) : data?.length === 0 ? (
        <div className="flex h-full items-center justify-center">No historical data available</div>
      ) : (
        <ResponsiveContainer width="100%" height="100%">
          <LineChart
            ref={chartRef}
            data={datesToShow}
            margin={{ top: 20, right: 30, left: 25, bottom: 30 }}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            className="no-select"
          >
            <CartesianGrid strokeDasharray="3 3" stroke="var(--chart-grid)" />
            <XAxis
              dataKey="date"
              tickFormatter={formatDate}
              stroke="var(--chart-axis)"
              tick={{ fill: 'var(--chart-text)' }}
              allowDataOverflow
              minTickGap={30}
              padding={{ left: 10, right: 10 }}
              className="no-select"
            />
            <YAxis
              tickFormatter={formatPrice}
              stroke="var(--chart-axis)"
              tick={{ fill: 'var(--chart-text)' }}
              allowDataOverflow
              domain={['auto', 'auto']}
              padding={{ top: 20, bottom: 20 }}
              className="no-select"
            />
            <Tooltip
              formatter={(value: number) => [`$${value.toFixed(2)}`, '']}
              labelFormatter={formatDate}
              contentStyle={{
                backgroundColor: 'var(--tooltip-bg)',
                border: '1px solid var(--tooltip-border)',
                color: 'var(--tooltip-text)',
                fontSize: '12px',
                userSelect: 'none'
              }}
              // className prop is not supported by Recharts Tooltip
              wrapperClassName="no-select"
            />
            <Line
              type="monotone"
              dataKey="price"
              stroke={chartColor}
              dot={false}
              activeDot={{ r: 6 }}
              strokeWidth={2}
              animationDuration={300}
            />

            {/* Current price reference line */}
            {datesToShow.length > 0 && (
              <ReferenceLine
                y={datesToShow[datesToShow.length - 1].price}
                stroke={chartColor}
                strokeDasharray="3 3"
                label={{
                  value: 'Current',
                  position: 'insideBottomRight',
                  fill: 'var(--chart-text)',
                  className: 'no-select'
                }}
              />
            )}

            {/* Selection area for zooming */}
            {refAreaLeft && refAreaRight && (
              <ReferenceArea
                x1={refAreaLeft}
                x2={refAreaRight}
                strokeOpacity={0.3}
                fill={chartColor}
                fillOpacity={0.3}
              />
            )}
          </LineChart>
        </ResponsiveContainer>
      )}
    </div>
  );

  // Check if data is recent (within the last 24 hours)
  const isDataRecent = () => {
    if (data.length === 0) return false;

    const latestDataDate = new Date(data[data.length - 1].date);
    const now = new Date();
    const diffInHours = (now.getTime() - latestDataDate.getTime()) / (1000 * 60 * 60);

    return diffInHours <= 24;
  };

  return (
    <>
      <style>
        {`
          .no-select,
          .chart-container,
          .chart-container * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
            cursor: default !important;
          }

          .chart-container {
            cursor: crosshair !important;
          }

          .chart-container .recharts-tooltip-wrapper {
            pointer-events: none !important;
          }
        `}
      </style>
      <Card className="w-full">
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle className="text-lg">
            {metal} <span className="text-sm font-normal text-muted-foreground">(NZD/troy ounce)</span>
            {currentPrice > 0 && (
              <span className="text-sm font-semibold ml-2">${currentPrice.toFixed(2)}</span>
            )}
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="icon"
              onClick={() => setIsExpanded(true)}
              title="Expand chart"
            >
              <Maximize className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {!isDataRecent() && data.length > 0 && (
            <div className="mb-2 p-2 bg-yellow-500/10 border border-yellow-500/30 rounded text-sm text-yellow-500">
              <RefreshCw className="h-4 w-4 inline-block mr-1" />
              Data may not be up to date. Last update: {formatDate(data[data.length - 1].date)}
            </div>
          )}
          {renderChart()}
        </CardContent>
      </Card>

      <Dialog open={isExpanded} onOpenChange={setIsExpanded}>
        <DialogContent className="sm:max-w-[900px] max-h-[90vh]">
          <DialogHeader>
            <DialogTitle>
              {metal} Price History <span className="text-sm font-normal text-muted-foreground">(NZD/troy ounce)</span>
              {currentPrice > 0 && (
                <span className="text-sm font-semibold ml-2">${currentPrice.toFixed(2)}</span>
              )}
            </DialogTitle>
            <DialogDescription>
              Drag to zoom on a specific time range. Use the mouse wheel to zoom in and out.
            </DialogDescription>
          </DialogHeader>

          {!isDataRecent() && data.length > 0 && (
            <div className="mb-4 p-2 bg-yellow-500/10 border border-yellow-500/30 rounded text-sm text-yellow-500">
              <RefreshCw className="h-4 w-4 inline-block mr-1" />
              Data may not be up to date. Last update: {formatDate(data[data.length - 1].date)}
            </div>
          )}

          <div className="flex flex-wrap justify-between items-center mb-2 gap-2">
            <div className="flex flex-wrap gap-1">
              {TIME_PERIODS.map(period => (
                <Button
                  key={period.label}
                  variant={activePeriod === period.label ? "default" : "outline"}
                  size="sm"
                  onClick={() => handlePeriodClick(period.days, period.label)}
                >
                  {period.label}
                </Button>
              ))}
            </div>

            <div className="flex items-center gap-1">
              <Button variant="outline" size="sm" onClick={zoomIn}>
                <ZoomIn className="h-4 w-4 mr-1" />
                Zoom In
              </Button>
              <Button variant="outline" size="sm" onClick={zoomOut}>
                <ZoomOut className="h-4 w-4 mr-1" />
                Reset
              </Button>
            </div>
          </div>

          {renderChart(500)}
        </DialogContent>
      </Dialog>
    </>
  );
}