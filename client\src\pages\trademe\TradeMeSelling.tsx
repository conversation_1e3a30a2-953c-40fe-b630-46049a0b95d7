import { useEffect, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { getItems as getListings, TradeMeItem as TradeItem } from '@/api/tradeMeItems';
import { useToast } from '@/hooks/useToast';
import { TradeMeListingsLayout } from '@/components/trademe/TradeMeListingsLayout';
import { TradeMeListingCard } from '@/components/trademe/TradeMeListingCard';

export function TradeMeSelling() {

  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const { toast } = useToast();

  // State for listings and loading
  const [listings, setListings] = useState<TradeItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [syncing, setSyncing] = useState(false);
  const [totalItems, setTotalItems] = useState(0);
  const [page, setPage] = useState(1);
  // hasMore and page are kept for future pagination implementation
  const [hasMore, setHasMore] = useState(true);

  // State for filters and search
  const [searchTerm, setSearchTerm] = useState('');
  const [filter, setFilter] = useState(searchParams.get('filter') || 'all');
  const [sortBy, setSortBy] = useState(searchParams.get('sort') || 'default');

  // Only fetch on initial load
  useEffect(() => {
    fetchListings(1, true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Fetch listings from the API
  const fetchListings = async (pageToFetch = page, reset = false) => {
    try {
      // Set loading state only if we're resetting the list
      if (reset) {
        setLoading(true);
      }

      console.log('Fetching listings with params:', {
        status: 'active',
        page: pageToFetch,
        filter,
        sort: sortBy,
        search: searchTerm
      });

      const data = await getListings({
        status: 'active',
        page: pageToFetch,
        limit: 20,
        sync: false,
        filter: filter !== 'all' ? filter : undefined,
        sort: sortBy !== 'default' ? sortBy : undefined,
        search: searchTerm
      });

      // Check if data.items exists (from the server response)
      if (reset) {
        setListings(data.items || data.listings || []);
      } else {
        setListings(prev => [...prev, ...(data.items || data.listings || [])]);
      }

      // Log the data structure for debugging
      console.log('API Response:', data);

      setTotalItems(data.pagination?.total || 0);
      setHasMore(data.pagination?.page < data.pagination?.pages);
      setPage(pageToFetch);
    } catch (error: any) {
      console.error('Failed to fetch listings:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to fetch listings',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Note: We might need a loadMore function in the future if pagination is implemented
  // Currently the TradeMeListingsLayout doesn't use this functionality

  // Handle search input
  const handleSearch = (term: string) => {
    // Skip if the search term is the same
    if (term === searchTerm) return;

    console.log('Search term changed to:', term);
    setSearchTerm(term);
    setPage(1);
    fetchListings(1, true);
  };

  // Handle filter change
  const handleFilter = (value: string) => {
    console.log('Filter changed to:', value);
    setFilter(value);
    setSearchParams(prev => {
      prev.set('filter', value);
      return prev;
    });
    // Trigger a new fetch when filter changes
    setPage(1);
    fetchListings(1, true);
  };

  // Handle sort change
  const handleSort = (value: string) => {
    console.log('Sort changed to:', value);
    setSortBy(value);
    setSearchParams(prev => {
      prev.set('sort', value);
      return prev;
    });
    // Trigger a new fetch when sort changes
    setPage(1);
    fetchListings(1, true);
  };

  // Sync with TradeMe
  const handleSync = async () => {
    try {
      setSyncing(true);
      toast({
        title: 'Syncing',
        description: 'Syncing with TradeMe...',
      });

      const data = await getListings({
        status: 'active',
        page: 1,
        limit: 20,
        sync: true
      });

      setListings(data.items || data.listings || []);
      setTotalItems(data.pagination?.total || 0);
      setPage(1);
      setHasMore((data.pagination?.page || 0) < (data.pagination?.pages || 0));

      toast({
        title: 'Sync Complete',
        description: `Successfully synced ${data.pagination?.total || 0} listings from TradeMe.`,
      });
    } catch (error: any) {
      console.error('Failed to sync with TradeMe:', error);
      toast({
        title: 'Sync Failed',
        description: error.message || 'Failed to sync with TradeMe',
        variant: 'destructive',
      });
    } finally {
      setSyncing(false);
    }
  };

  // Handle edit listing
  const handleEditListing = (id: string) => {
    navigate(`/trademe/listing/edit/${id}`);
  };

  // Handle withdraw listing
  const handleWithdrawListing = async (id: string, reason: string): Promise<void> => {
    // This function will be implemented later
    console.log(`Withdrawing listing ${id} with reason: ${reason}`);

    // For now, just show a toast notification
    toast({
      title: 'Not Implemented',
      description: 'Withdraw functionality will be implemented soon.',
      variant: 'default',
    });

    // In the future, this will call the API to withdraw the listing
    // await withdrawListing(id, reason);

    // Refresh listings after withdrawal
    // fetchListings(1, true);
  };

  // No longer need the action button as it's in the top navigation

  return (
    <TradeMeListingsLayout
      title="Items I'm Selling"
      type="selling"
      listings={listings}
      totalItems={totalItems}
      loading={loading}
      syncing={syncing}
      onSync={handleSync}
      onSearch={handleSearch}
      onFilter={handleFilter}
      onSort={handleSort}
    >
      <div className="grid gap-6">
        {(!listings || listings.length === 0) && !loading ? (
          <div className="text-center py-12">
            <h3 className="mt-4 text-lg font-medium">No active listings found</h3>
            <p className="text-muted-foreground mt-2">
              You don't have any active TradeMe listings at the moment.
            </p>
          </div>
        ) : (
          listings.map((listing) => (
            <TradeMeListingCard
              key={listing._id}
              listing={listing}
              type="selling"
              onEdit={handleEditListing}
              onWithdraw={handleWithdrawListing}
            />
          ))
        )}
      </div>
    </TradeMeListingsLayout>
  );
}
