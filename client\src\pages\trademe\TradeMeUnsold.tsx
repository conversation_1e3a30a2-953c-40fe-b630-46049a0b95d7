import { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { getItems as getListings, TradeMeItem as TradeItem } from '@/api/tradeMeItems';
import { useToast } from '@/hooks/useToast';
import { TradeMeListingsLayout } from '@/components/trademe/TradeMeListingsLayout';
import { TradeMeListingCard } from '@/components/trademe/TradeMeListingCard';

export function TradeMeUnsold() {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const { toast } = useToast();

  // State for listings and loading
  const [listings, setListings] = useState<TradeItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [syncing, setSyncing] = useState(false);
  const [totalItems, setTotalItems] = useState(0);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  // State for filters and search
  const [searchTerm, setSearchTerm] = useState('');
  const [filter, setFilter] = useState(searchParams.get('filter') || 'all');
  const [sortBy, setSortBy] = useState(searchParams.get('sort') || 'default');
  const [timeFilter, setTimeFilter] = useState<number | null>(null);

  useEffect(() => {
    fetchListings(1, true);
  }, []);

  // Fetch listings from the API
  const fetchListings = async (pageToFetch = page, reset = false) => {
    try {
      // Set loading state only if we're resetting the list
      if (reset) {
        setLoading(true);
      }

      // Build query parameters
      const queryParams: any = {
        status: 'unsold',
        page: pageToFetch,
        limit: 20,
        sync: false,
        filter: filter !== 'all' ? filter : undefined,
        sort: sortBy !== 'default' ? sortBy : undefined,
        search: searchTerm
      };

      // Add time filter if set
      if (timeFilter) {
        queryParams.days = timeFilter;
      }

      console.log('Fetching unsold listings with params:', queryParams);

      const data = await getListings(queryParams);

      if (reset) {
        setListings(data.listings || []);
      } else {
        setListings(prev => [...prev, ...(data.listings || [])]);
      }

      setTotalItems(data.pagination?.total || 0);
      setHasMore(data.pagination?.page < data.pagination?.pages);
      setPage(pageToFetch);
    } catch (error: any) {
      console.error('Failed to fetch listings:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to fetch listings',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Load more listings when scrolling
  const loadMore = async () => {
    if (hasMore) {
      await fetchListings(page + 1);
    }
  };

  // Handle search input
  const handleSearch = (term: string) => {
    // Skip if the search term is the same
    if (term === searchTerm) return;

    console.log('Search term changed to:', term);
    setSearchTerm(term);
    setPage(1);
    fetchListings(1, true);
  };

  // Handle filter change
  const handleFilter = (value: string) => {
    console.log('Filter changed to:', value);
    setFilter(value);
    setSearchParams(prev => {
      prev.set('filter', value);
      return prev;
    });
    // Trigger a new fetch when filter changes
    setPage(1);
    fetchListings(1, true);
  };

  // Handle sort change
  const handleSort = (value: string) => {
    console.log('Sort changed to:', value);
    setSortBy(value);
    setSearchParams(prev => {
      prev.set('sort', value);
      return prev;
    });
    // Trigger a new fetch when sort changes
    setPage(1);
    fetchListings(1, true);
  };

  // Handle time filter change
  const handleTimeFilter = (days: number) => {
    console.log('Time filter changed to:', days);
    setTimeFilter(days === 0 ? null : days);
    // Trigger a new fetch when time filter changes
    setPage(1);
    fetchListings(1, true);
  };

  // Sync with TradeMe
  const handleSync = async () => {
    try {
      setSyncing(true);
      toast({
        title: 'Syncing',
        description: 'Syncing with TradeMe...',
      });

      const data = await getListings({
        status: 'unsold',
        page: 1,
        limit: 20,
        sync: true
      });

      setListings(data.listings);
      setTotalItems(data.pagination.total);
      setPage(1);
      setHasMore(data.pagination.page < data.pagination.pages);

      toast({
        title: 'Sync Complete',
        description: `Successfully synced ${data.pagination.total} unsold listings from TradeMe.`,
      });
    } catch (error: any) {
      console.error('Failed to sync with TradeMe:', error);
      toast({
        title: 'Sync Failed',
        description: error.message || 'Failed to sync with TradeMe',
        variant: 'destructive',
      });
    } finally {
      setSyncing(false);
    }
  };

  // Handle relist action
  const handleRelist = (listingId: string) => {
    navigate(`/trademe/listing/relist/${listingId}`);
  };

  return (
    <TradeMeListingsLayout
      title="Unsold Items"
      type="unsold"
      listings={listings}
      totalItems={totalItems}
      loading={loading}
      syncing={syncing}
      onSync={handleSync}
      onSearch={handleSearch}
      onFilter={handleFilter}
      onSort={handleSort}
      onTimeFilter={handleTimeFilter}
    >
      <div className="grid gap-6">
        {(!listings || listings.length === 0) && !loading ? (
          <div className="text-center py-12">
            <h3 className="mt-4 text-lg font-medium">No unsold items found</h3>
            <p className="text-muted-foreground mt-2">
              You don't have any unsold TradeMe listings at the moment.
            </p>
          </div>
        ) : (
          listings.map((listing) => (
            <TradeMeListingCard
              key={listing._id}
              listing={listing}
              type="unsold"
              onRelist={() => handleRelist(listing._id)}
            />
          ))
        )}
      </div>
    </TradeMeListingsLayout>
  );
}
