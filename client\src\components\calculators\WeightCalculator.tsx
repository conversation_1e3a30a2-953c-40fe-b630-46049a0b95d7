import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { MetalPrice } from '@/api/goldPricing';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { InfoIcon } from 'lucide-react';
import { format } from 'date-fns';

interface WeightCalculatorProps {
  prices: MetalPrice[];
}

// Define price type options
type PriceType = 'minBuyPrice' | 'maxBuyPrice' | 'spotPrice' | 'meltPrice';

interface PriceTypeOption {
  value: PriceType;
  label: string;
}

const priceTypeOptions: PriceTypeOption[] = [
  { value: 'minBuyPrice', label: 'Minimum Buy Price' },
  { value: 'maxBuyPrice', label: 'Maximum Buy Price' },
  { value: 'spotPrice', label: 'Spot Price' },
  { value: 'meltPrice', label: 'Melt Price' },
];

export function WeightCalculator({ prices }: WeightCalculatorProps) {
  const [weight, setWeight] = useState<string>('');
  const [selectedPurity, setSelectedPurity] = useState<string>('');
  const [selectedPriceType, setSelectedPriceType] = useState<PriceType>('minBuyPrice');
  const [calculatedPrice, setCalculatedPrice] = useState<number | null>(null);
  const [selectedPrice, setSelectedPrice] = useState<MetalPrice | null>(null);

  // Update selected price when purity changes
  useEffect(() => {
    if (!selectedPurity) return;

    const [metal, purity] = selectedPurity.split('-');
    const price = prices.find(p => p.metal === metal && p.purity === purity);

    if (price) {
      setSelectedPrice(price);
    }
  }, [selectedPurity, prices]);

  // Calculate price when weight, selection, or price type changes
  useEffect(() => {
    if (!selectedPrice || !weight) {
      setCalculatedPrice(null);
      return;
    }

    const weightNum = parseFloat(weight);
    if (isNaN(weightNum) || weightNum <= 0) {
      setCalculatedPrice(null);
      return;
    }

    // Use the selected price type for calculation
    setCalculatedPrice(weightNum * selectedPrice[selectedPriceType]);
  }, [weight, selectedPrice, selectedPriceType]);

  // Format date nicely
  const formatDateTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return format(date, 'PPpp'); // e.g., "Apr 29, 2023, 3:25 PM"
    } catch (e) {
      return 'Unknown';
    }
  };

  // Get the label for the current selected price type
  const getCurrentPriceTypeLabel = () => {
    return priceTypeOptions.find(option => option.value === selectedPriceType)?.label || 'Price';
  };

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <label className="text-sm font-medium">Metal and Purity</label>
        <Select value={selectedPurity} onValueChange={setSelectedPurity}>
          <SelectTrigger>
            <SelectValue placeholder="Select metal and purity" />
          </SelectTrigger>
          <SelectContent>
            {prices.map((price) => (
              <SelectItem key={`${price.metal}-${price.purity}`} value={`${price.metal}-${price.purity}`}>
                {price.metal} - {price.purity}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <label className="text-sm font-medium">Price Type</label>
        <Select value={selectedPriceType} onValueChange={(value: PriceType) => setSelectedPriceType(value)}>
          <SelectTrigger>
            <SelectValue placeholder="Select price type" />
          </SelectTrigger>
          <SelectContent>
            {priceTypeOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <label className="text-sm font-medium">Weight (grams)</label>
        <Input
          type="number"
          value={weight}
          onChange={(e) => setWeight(e.target.value)}
          placeholder="Enter weight in grams"
          min="0.01"
          step="0.01"
        />
      </div>

      {calculatedPrice !== null && (
        <div className="pt-4 border-t">
          <div className="flex flex-col space-y-2">
            <span className="text-lg font-medium">Calculated Value ({getCurrentPriceTypeLabel()}):</span>
            <span className="text-2xl font-bold">
              ${calculatedPrice.toFixed(2)}
            </span>

            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" size="sm" className="w-[150px] flex items-center gap-2">
                  <InfoIcon className="h-4 w-4" />
                  <span>More info</span>
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-[300px] p-4">
                <div className="space-y-2">
                  <h4 className="font-semibold">{selectedPrice?.metal} - {selectedPrice?.purity}</h4>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <span className="text-muted-foreground">Min Buy Price:</span>
                    <span>${selectedPrice?.minBuyPrice.toFixed(2)}/g</span>

                    <span className="text-muted-foreground">Max Buy Price:</span>
                    <span>${selectedPrice?.maxBuyPrice.toFixed(2)}/g</span>

                    <span className="text-muted-foreground">Spot Price:</span>
                    <span>${selectedPrice?.spotPrice.toFixed(2)}/g</span>

                    <span className="text-muted-foreground">Melt Price:</span>
                    <span>${selectedPrice?.meltPrice.toFixed(2)}/g</span>

                    <span className="text-muted-foreground">Last Price Update:</span>
                    <span>{formatDateTime(selectedPrice?.lastUpdated || '')}</span>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>
        </div>
      )}
    </div>
  );
}