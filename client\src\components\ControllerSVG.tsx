import React from 'react';

interface ControllerSVGProps {
  controllerType: string;
  className?: string;
}

const ControllerSVG: React.FC<ControllerSVGProps> = ({ controllerType, className = 'w-full h-auto' }) => {
  // Determine the SVG path based on controller type
  const getSVGPath = () => {
    switch (controllerType.toLowerCase()) {
      case 'dualsense':
      case 'ps5':
        return '/img/controllers/dualsense.svg';
      case 'dualsenseedge':
      case 'ps5edge':
        return '/img/controllers/dualsenseEdge.svg';
      case 'dualshock':
      case 'ps4':
        return '/img/controllers/dualshock.svg';
      default:
        // Default to DualSense if no match
        return '/img/controllers/dualsense.svg';
    }
  };

  // Determine controller name for display
  const getControllerName = () => {
    switch (controllerType.toLowerCase()) {
      case 'dualsense':
      case 'ps5':
        return 'DualSense (PS5)';
      case 'dualsenseedge':
      case 'ps5edge':
        return 'DualSense Edge (PS5)';
      case 'dualshock':
      case 'ps4':
        return 'DualShock 4 (PS4)';
      default:
        return controllerType || 'Unknown Controller';
    }
  };

  return (
    <div className="controller-svg-container">
      <img 
        src={getSVGPath()} 
        alt={`${getControllerName()} Controller`} 
        className={className} 
      />
      <div className="text-center text-sm text-muted-foreground mt-2">
        {getControllerName()}
      </div>
    </div>
  );
};

export default ControllerSVG;
