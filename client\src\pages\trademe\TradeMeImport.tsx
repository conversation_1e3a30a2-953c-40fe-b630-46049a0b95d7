import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Loader2, AlertCircle, CheckCircle2, ShoppingBag, Archive } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { importListings, importSoldListings } from '@/api/tradeMeImport';
import { toast } from 'sonner';
import InProgressImports from '@/components/trademe/InProgressImports';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

const TradeMeImport: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [isImporting, setIsImporting] = useState(false);
  const [maxPages, setMaxPages] = useState(5);
  const [importResult, setImportResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'active' | 'sold'>('active');

  // Check for tab parameter in URL
  useEffect(() => {
    const tabParam = searchParams.get('tab');
    if (tabParam === 'sold') {
      setActiveTab('sold');
    }
  }, [searchParams]);

  // Check if user has permission to access this page
  const hasPermission = user?.role === 'admin' || user?.role === 'manager';

  const handleImport = async () => {
    try {
      setIsImporting(true);
      setError(null);
      setImportResult(null);

      // Call the appropriate import function based on the active tab
      const result = activeTab === 'active'
        ? await importListings(maxPages)
        : await importSoldListings(maxPages);

      setImportResult(result);

      if (result.success) {
        toast.success(`${activeTab === 'active' ? 'Active' : 'Sold'} listings import completed successfully`);
        // Redirect to the import wizard if there's an import log ID
        if (result.importLogId) {
          navigate(`/trademe/import/wizard/${result.importLogId}`);
        }
      } else {
        setError(result.error || 'Import failed');
        toast.error(`Import failed: ${result.error}`);
      }
    } catch (error: any) {
      console.error(`Error importing TradeMe ${activeTab} listings:`, error);
      setError(error.message || `Failed to import TradeMe ${activeTab} listings`);
      toast.error(`Import failed: ${error.message}`);
    } finally {
      setIsImporting(false);
    }
  };

  const handleViewImportLog = () => {
    if (importResult?.importLogId) {
      navigate(`/trademe/import/logs/${importResult.importLogId}`);
    }
  };

  if (!hasPermission) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>TradeMe Import</CardTitle>
          <CardDescription>Import your TradeMe listings</CardDescription>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Access Denied</AlertTitle>
            <AlertDescription>
              You do not have permission to access this page.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>TradeMe Import</CardTitle>
        <CardDescription>Import your TradeMe listings</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <Tabs defaultValue="active" onValueChange={(value) => setActiveTab(value as 'active' | 'sold')}>
            <TabsList className="grid w-full grid-cols-2 mb-4">
              <TabsTrigger value="active" className="flex items-center">
                <ShoppingBag className="mr-2 h-4 w-4" />
                Active Listings
              </TabsTrigger>
              <TabsTrigger value="sold" className="flex items-center">
                <Archive className="mr-2 h-4 w-4" />
                Sold Listings
              </TabsTrigger>
            </TabsList>

            <TabsContent value="active" className="space-y-4">
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">
                  This tool will import your active TradeMe listings into the system. You will need to add additional information like stock codes, buyer, cost, location, and relisting settings for each item.
                </p>
              </div>
            </TabsContent>

            <TabsContent value="sold" className="space-y-4">
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">
                  This tool will import your sold TradeMe listings into the system. You will need to add additional information like stock codes, buyer, and cost for each item.
                </p>
              </div>
            </TabsContent>
          </Tabs>

          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="maxPages">Maximum Pages to Import</Label>
                <Input
                  id="maxPages"
                  type="number"
                  min={1}
                  max={20}
                  value={maxPages}
                  onChange={(e) => setMaxPages(parseInt(e.target.value))}
                  disabled={isImporting}
                />
                <p className="text-xs text-muted-foreground">
                  Each page contains up to 50 listings. Increase this value if you have many listings.
                </p>
              </div>
            </div>

            <Button
              onClick={handleImport}
              disabled={isImporting}
              className="w-full sm:w-auto"
            >
              {isImporting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isImporting ? 'Importing...' : `Start Import (${activeTab === 'active' ? 'Active' : 'Sold'} Listings)`}
            </Button>
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Import Failed</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {importResult && importResult.success && (
            <Alert variant="success">
              <CheckCircle2 className="h-4 w-4" />
              <AlertTitle>Import Completed</AlertTitle>
              <AlertDescription>
                <div className="space-y-2">
                  <p>Successfully imported TradeMe listings.</p>
                  <ul className="list-disc pl-5 text-sm">
                    <li>Items Processed: {importResult.stats?.itemsProcessed || 0}</li>
                    <li>New Items: {importResult.stats?.newItems || 0}</li>
                    <li>Duplicate Items: {importResult.stats?.duplicateItems || 0}</li>
                    <li>Failed Items: {importResult.stats?.failedItems || 0}</li>
                  </ul>
                  <div className="flex space-x-2 mt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleViewImportLog}
                    >
                      View Import Log
                    </Button>
                    <Button
                      variant="default"
                      size="sm"
                      onClick={() => navigate('/trademe/import/items')}
                    >
                      Manage Imported Items
                    </Button>
                  </div>
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* Display in-progress imports */}
          <InProgressImports />
        </div>
      </CardContent>
    </Card>
  );
};

export default TradeMeImport;
