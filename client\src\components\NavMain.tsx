import { ChevronRight, LucideIcon } from "lucide-react";
import { Link, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar";

export type NavItem = {
  name: string;
  path: string;
  icon: LucideIcon | React.ComponentType<{ className?: string }>;
  isActive?: boolean;
  items?: NavSubItem[];
}

export type NavSubItem = {
  name: string;
  path: string;
  external?: boolean;
  disabled?: boolean;
  isActive?: boolean;
}

export function NavMain({ items }: { items: NavItem[] }) {
  const location = useLocation();

  const isSubItemActive = (item: NavSubItem) => {
    if (item.external) return false;
    return location.pathname.startsWith(item.path);
  };

  const isMainItemActive = (item: NavItem) => {
    if (item.path === "/" && location.pathname !== "/") return false;
    if (item.path === "#" && item.items) {
      return item.items.some(subItem => isSubItemActive(subItem));
    }
    return location.pathname.startsWith(item.path);
  };

  return (
    <SidebarGroup>
      <SidebarGroupLabel>Menu</SidebarGroupLabel>
      <SidebarMenu>
        {items.map((item) => (
          <Collapsible
            key={item.name}
            asChild
            defaultOpen={isMainItemActive(item)}
            className="group/collapsible"
          >
            <SidebarMenuItem>
              <CollapsibleTrigger asChild>
                {item.items ? (
                  <SidebarMenuButton className={isMainItemActive(item) ? 'font-medium text-primary' : ''}>
                    <item.icon className="size-4" />
                    <span>{item.name}</span>
                    <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                  </SidebarMenuButton>
                ) : (
                  <SidebarMenuButton asChild>
                    <Link
                      to={item.path}
                      className={isMainItemActive(item) ? 'font-medium text-primary' : ''}
                    >
                      <item.icon className="size-4" />
                      <span>{item.name}</span>
                    </Link>
                  </SidebarMenuButton>
                )}
              </CollapsibleTrigger>
              {item.items && (
                <CollapsibleContent>
                  <SidebarMenuSub>
                    {item.items.map((subItem) => (
                      <SidebarMenuSubItem key={subItem.name}>
                        <SidebarMenuSubButton asChild>
                          {subItem.external ? (
                            <a
                              href={subItem.disabled ? "#" : subItem.path}
                              target="_blank"
                              rel="noopener noreferrer"
                              aria-disabled={subItem.disabled ? "true" : undefined}
                              className={subItem.disabled ? 'opacity-50 cursor-not-allowed' : ''}
                              onClick={(e) => subItem.disabled && e.preventDefault()}
                            >
                              <span>{subItem.name}</span>
                            </a>
                          ) : (
                            <Link
                              to={subItem.disabled ? "#" : subItem.path}
                              aria-disabled={subItem.disabled ? "true" : undefined}
                              className={cn(
                                subItem.disabled ? 'opacity-50 cursor-not-allowed' : '',
                                isSubItemActive(subItem) ? 'font-medium text-primary bg-primary/10' : ''
                              )}
                              onClick={(e) => subItem.disabled && e.preventDefault()}
                            >
                              <span>{subItem.name}</span>
                            </Link>
                          )}
                        </SidebarMenuSubButton>
                      </SidebarMenuSubItem>
                    ))}
                  </SidebarMenuSub>
                </CollapsibleContent>
              )}
            </SidebarMenuItem>
          </Collapsible>
        ))}
      </SidebarMenu>
    </SidebarGroup>
  );
}
