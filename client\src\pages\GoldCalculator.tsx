import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { getMetalPrices, getMetalPriceSettings, MetalPrice, MetalPriceSettings, getHistoricalPrices } from '@/api/goldPricing';
import { RefreshCw, Settings } from "lucide-react";
import { useToast } from '@/hooks/useToast';
import { MetalPriceTable } from '@/components/MetalPriceTable';
import { WeightCalculator } from '@/components/calculators/WeightCalculator';
import { MhjCalculator } from '@/components/calculators/MhjCalculator';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { PriceHistoryChart } from '@/components/PriceHistoryChart';

interface HistoricalPrice {
  metal: string;
  date: string;
  price: number;
}

export function GoldCalculator() {
  const [prices, setPrices] = useState<MetalPrice[]>([]);
  const [settings, setSettings] = useState<MetalPriceSettings | null>(null);
  const [nextUpdate, setNextUpdate] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [historyLoading, setHistoryLoading] = useState(true);
  const [historicalData, setHistoricalData] = useState<{
    Gold: HistoricalPrice[],
    Silver: HistoricalPrice[],
    Platinum: HistoricalPrice[],
    Palladium: HistoricalPrice[]
  }>({
    Gold: [],
    Silver: [],
    Platinum: [],
    Palladium: []
  });
  const { toast } = useToast();
  const { user } = useAuth();
  const isAdminOrManager = user?.role === 'admin' || user?.role === 'manager';

  useEffect(() => {
    const fetchPrices = async () => {
      try {
        const data = await getMetalPrices();
        setPrices(data.prices);
        setNextUpdate(data.nextUpdateAt);

        const settingsData = await getMetalPriceSettings();
        setSettings(settingsData);
      } catch (error: any) {
        toast({
          title: "Error fetching metal prices",
          description: error.message || 'Unknown error',
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    fetchPrices();
  }, [toast]);

  useEffect(() => {
    const fetchHistoricalData = async () => {
      setHistoryLoading(true);
      try {
        // Fetch historical data for all 4 metals (limited to 365 days)
        const goldData = await getHistoricalPrices('Gold', 365);
        const silverData = await getHistoricalPrices('Silver', 365);
        const platinumData = await getHistoricalPrices('Platinum', 365);
        const palladiumData = await getHistoricalPrices('Palladium', 365);

        setHistoricalData({
          Gold: goldData,
          Silver: silverData,
          Platinum: platinumData,
          Palladium: palladiumData
        });

        // Check for warnings in any of the responses
        const warnings = [];
        if ('warning' in goldData) warnings.push(`Gold: ${goldData.warning}`);
        if ('warning' in silverData) warnings.push(`Silver: ${silverData.warning}`);
        if ('warning' in platinumData) warnings.push(`Platinum: ${platinumData.warning}`);
        if ('warning' in palladiumData) warnings.push(`Palladium: ${palladiumData.warning}`);

        if (warnings.length > 0) {
          toast({
            title: "Historical data may not be up to date",
            description: "Using the most recent available data from the database.",
            variant: "default"
          });
        }
      } catch (error: any) {
        toast({
          title: "Error fetching historical data",
          description: error.message || 'Unknown error',
          variant: "destructive"
        });
      } finally {
        setHistoryLoading(false);
      }
    };

    fetchHistoricalData();
  }, [toast]);

  return (
    <div className="space-y-6">
      {/* Fix: Remove jsx and global boolean attributes, use className instead */}
      <style>
        {`
          :root {
            --chart-grid: rgba(102, 102, 102, 0.2);
            --chart-axis: #888888;
            --chart-text: #888888;
            --tooltip-bg: #1f2937;
            --tooltip-border: #374151;
            --tooltip-text: #f3f4f6;
          }

          html.light-theme {
            --chart-grid: rgba(102, 102, 102, 0.2);
            --chart-axis: #555555;
            --chart-text: #555555;
            --tooltip-bg: #ffffff;
            --tooltip-border: #e5e7eb;
            --tooltip-text: #1f2937;
          }
        `}
      </style>

      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Gold Calculator</h1>
        <div className="flex items-center gap-2">
          {isAdminOrManager && (
            <Button variant="outline" asChild>
              <Link to="/settings?tab=gold" className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Settings
              </Link>
            </Button>
          )}
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
            disabled={loading}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? "animate-spin" : ""}`} />
            {loading ? "Refreshing..." : "Refresh Prices"}
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-10">
        {/* Current Prices Card - LEFT (7 columns) */}
        <Card className="md:col-span-7">
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>Current Prices</CardTitle>
              {nextUpdate && (
                <div className="text-sm text-muted-foreground">
                  Next update: {new Date(nextUpdate).toLocaleString()}
                </div>
              )}
            </div>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="p-4 text-center">Loading prices...</div>
            ) : (
              <MetalPriceTable prices={prices} />
            )}
          </CardContent>
        </Card>

        {/* Calculators Section - RIGHT (3 columns) */}
        <div className="space-y-6 md:col-span-3">
          {/* Weight Calculator */}
          <Card>
            <CardHeader>
              <CardTitle>Weight Calculator</CardTitle>
            </CardHeader>
            <CardContent>
              <WeightCalculator prices={prices} />
            </CardContent>
          </Card>

          {/* MHJ Calculator */}
          <Card>
            <CardHeader>
              <CardTitle>MHJ Calculator</CardTitle>
            </CardHeader>
            <CardContent>
              {settings ? (
                <MhjCalculator settings={settings} />
              ) : (
                <div className="p-4 text-center">Loading settings...</div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Metal Price History Charts */}
      <div className="grid gap-6 grid-cols-1 md:grid-cols-2">
        <PriceHistoryChart
          metal="Gold"
          data={historicalData.Gold}
          loading={historyLoading}
          color="#fbbf24"
        />
        <PriceHistoryChart
          metal="Silver"
          data={historicalData.Silver}
          loading={historyLoading}
          color="#94a3b8"
        />
        <PriceHistoryChart
          metal="Platinum"
          data={historicalData.Platinum}
          loading={historyLoading}
          color="#e5e7eb"
        />
        <PriceHistoryChart
          metal="Palladium"
          data={historicalData.Palladium}
          loading={historyLoading}
          color="#fb923c"
        />
      </div>

      {/* Jewellery Information Section */}
      <div className="mt-6">
        <h2 className="text-2xl font-bold text-center mb-6">Jewellery Reference Guide</h2>

        <div className="grid gap-6 grid-cols-1 md:grid-cols-2">
          {/* Left Column - Jewellery Checks and Tips */}
          <Card className="h-full">
            <CardHeader>
              <CardTitle>Important Jewellery Checks and Tips</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h3 className="text-lg font-medium mb-2">How to Check Gold Purity</h3>
                <p className="mb-2 text-muted-foreground">Checking the purity of gold is a crucial skill when evaluating jewellery. Here are some methods for assessing gold purity:</p>
                <ul className="list-disc pl-5 space-y-1 text-muted-foreground">
                  <li><span className="font-medium text-foreground">Visual Inspection:</span> Look for karat markings, hallmarks, and examine the color and luster of the item.</li>
                  <li><span className="font-medium text-foreground">Magnet Test:</span> Use a strong magnet to check if the item is attracted to it (copper, gold, silver, aluminum, are also non-magnetic metals).</li>
                  <li><span className="font-medium text-foreground">Acid Testing:</span> Apply nitric acid to a small scratch on the item and observe the reaction.</li>
                  <li><span className="font-medium text-foreground">Electric Gold Tester:</span> Utilize an electric gold tester for quick and accurate assessments.</li>
                  <li><span className="font-medium text-foreground">Valuation or Appraisal:</span> Ask the customer for the valuation, or to get the item appraised.</li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Use Your Magnet</h3>
                <ul className="list-disc pl-5 space-y-1 text-muted-foreground">
                  <li>If the piece sticks to the magnet, it's likely filled or plated, even if it has a hallmark.</li>
                  <li>If it doesn't stick, it doesn't necessarily mean it's genuine; consider all the steps below as part of your testing process.</li>
                  <li>Some metals, like copper or brass, are commonly used as fillers or in gold plating and won't attract a magnet. Even if the piece doesn't stick to the magnet, it could still be filled with these metals, so be sure to perform other tests to confirm authenticity.</li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Check for Discoloration</h3>
                <ul className="list-disc pl-5 space-y-1 text-muted-foreground">
                  <li>Look for signs of noticeable discoloration or wear on the gold.</li>
                  <li>If the gold appears to be wearing off or showing a different metal underneath (usually a greenish tinge around the edges), it is likely gold plated.</li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Inspect for Join Marks</h3>
                <ul className="list-disc pl-5 space-y-1 text-muted-foreground">
                  <li>Real gold items usually have every link soldered closed with no visible joins.</li>
                  <li>If you notice unsoldered links, be suspicious.</li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Be Cautious with Heavy Chains</h3>
                <ul className="list-disc pl-5 space-y-1 text-muted-foreground">
                  <li>Lightweight counterfeit pieces are far less common.</li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Use an Eye Loop</h3>
                <ul className="list-disc pl-5 space-y-1 text-muted-foreground">
                  <li>Check for hallmarks on the piece.</li>
                  <li>Examine the entire item for multiple hallmarks.</li>
                  <li>Some pieces may have a hallmark of 375 on one side (indicating 9ct gold) and "silver-filled" on the other side, indicating it's a 9ct gold plated silver chain.</li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Seek a Second Opinion</h3>
                <ul className="list-disc pl-5 space-y-1 text-muted-foreground">
                  <li>If you are unsure about the authenticity of a piece, consult with an experienced member of your team for a second opinion.</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* Right Column - Hallmarks Information */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Common Hallmark Guide</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="mb-4 text-muted-foreground">The following reference table will help you recognize some common hallmarks or markings found on jewellery pieces from around the world.</p>

                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="bg-muted">
                        <th className="border px-4 py-2 text-left">Marking</th>
                        <th className="border px-4 py-2 text-left">What it Means</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td className="border px-4 py-2">333</td>
                        <td className="border px-4 py-2">8ct gold</td>
                      </tr>
                      <tr>
                        <td className="border px-4 py-2">375</td>
                        <td className="border px-4 py-2">9ct gold</td>
                      </tr>
                      <tr>
                        <td className="border px-4 py-2">416 or 417</td>
                        <td className="border px-4 py-2">10ct gold</td>
                      </tr>
                      <tr>
                        <td className="border px-4 py-2">500</td>
                        <td className="border px-4 py-2">12ct gold</td>
                      </tr>
                      <tr>
                        <td className="border px-4 py-2">583 or 585</td>
                        <td className="border px-4 py-2">14ct gold</td>
                      </tr>
                      <tr>
                        <td className="border px-4 py-2">750</td>
                        <td className="border px-4 py-2">18ct gold</td>
                      </tr>
                      <tr>
                        <td className="border px-4 py-2">833</td>
                        <td className="border px-4 py-2">20ct gold</td>
                      </tr>
                      <tr>
                        <td className="border px-4 py-2">916 or 917</td>
                        <td className="border px-4 py-2">22ct gold</td>
                      </tr>
                      <tr>
                        <td className="border px-4 py-2">900 or 999</td>
                        <td className="border px-4 py-2">24ct gold</td>
                      </tr>
                      <tr>
                        <td className="border px-4 py-2">850 or 900</td>
                        <td className="border px-4 py-2">Platinum</td>
                      </tr>
                      <tr>
                        <td className="border px-4 py-2">925</td>
                        <td className="border px-4 py-2">Sterling Silver</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>What to Watch Out For</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="mb-4 text-muted-foreground">Some symbols and letters indicate that the piece is not solid gold. The table below outlines additional common markings that confirm gold is electroplated, filled, or rolled.</p>

                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="bg-muted">
                        <th className="border px-4 py-2 text-left">Marking</th>
                        <th className="border px-4 py-2 text-left">What it Means</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td className="border px-4 py-2">EPNS</td>
                        <td className="border px-4 py-2">Electroplated Nickel Silver - This is not gold, the piece is silver plated</td>
                      </tr>
                      <tr>
                        <td className="border px-4 py-2">EPBM</td>
                        <td className="border px-4 py-2">Electroplated Britannia Metal - This is not gold, the piece is metal</td>
                      </tr>
                      <tr>
                        <td className="border px-4 py-2">GE/ GP/ GEP</td>
                        <td className="border px-4 py-2">Gold Electroplate - This is not gold, the piece is metal made to look like gold</td>
                      </tr>
                      <tr>
                        <td className="border px-4 py-2">HE/ HGP</td>
                        <td className="border px-4 py-2">Heavy Gold Electroplate - This is not gold, the piece is metal made to look like gold</td>
                      </tr>
                      <tr>
                        <td className="border px-4 py-2">GF</td>
                        <td className="border px-4 py-2">Gold Filled - This is not solid gold, it has an outer layer of gold</td>
                      </tr>
                      <tr>
                        <td className="border px-4 py-2">RGP</td>
                        <td className="border px-4 py-2">Rolled Gold Plate - This is not solid gold, it has an outer layer of gold</td>
                      </tr>
                      <tr>
                        <td className="border px-4 py-2">SF or silver filled</td>
                        <td className="border px-4 py-2">Silver Filled - This is not solid gold, it has an outer layer of gold</td>
                      </tr>
                      <tr>
                        <td className="border px-4 py-2">1/20 or copper filled</td>
                        <td className="border px-4 py-2">Brass, Copper or Silver Filled - This is not solid gold, it has an outer layer of gold</td>
                      </tr>
                    </tbody>
                  </table>
                </div>

                <div className="mt-4 p-3 bg-muted/50 rounded-lg">
                  <p className="font-medium">Note: Make sure you search for multiple hallmarks. For example, a piece of gold jewellery with a 9ct hallmark and a 925 stamp indicates that the piece is silver-filled, despite the 9ct marking.</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}

export default GoldCalculator;
