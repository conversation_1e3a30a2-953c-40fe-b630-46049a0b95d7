import { useState, useEffect } from 'react';
import { getEmailTemplates, createEmailTemplate, updateEmailTemplate, deleteEmailTemplate, EmailTemplate } from '@/api/emailTemplates';
import { useToast } from '@/hooks/useToast';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Copy, Edit, Plus, Trash2, ClipboardCopy } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

export function EmailTemplateManager() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [templates, setTemplates] = useState<EmailTemplate[]>([]);
  const [filteredTemplates, setFilteredTemplates] = useState<EmailTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeCategory, setActiveCategory] = useState<string>("all");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<EmailTemplate | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    subject: '',
    body: '',
    category: 'Other'
  });

  useEffect(() => {
    fetchTemplates();
  }, []);

  useEffect(() => {
    // First filter by category
    let filtered = activeCategory === 'all'
      ? [...templates]
      : templates.filter(template => template.category === activeCategory);

    // Then sort alphabetically by title
    filtered.sort((a, b) => a.title.localeCompare(b.title));

    setFilteredTemplates(filtered);
  }, [templates, activeCategory]);

  const fetchTemplates = async () => {
    try {
      setLoading(true);
      const response = await getEmailTemplates();
      if (response.success) {
        setTemplates(response.templates || []);
      }
    } catch (error) {
      console.error('Failed to fetch templates:', error);
      toast({
        title: "Error",
        description: "Failed to load email templates",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      subject: '',
      body: '',
      category: 'Other'
    });
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSelectChange = (value: string) => {
    setFormData(prev => ({
      ...prev,
      category: value
    }));
  };

  const handleAddTemplate = async () => {
    try {
      if (!formData.title.trim() || !formData.subject.trim() || !formData.body.trim()) {
        toast({
          title: "Error",
          description: "Title, subject, and body are required",
          variant: "destructive"
        });
        return;
      }

      setIsSubmitting(true);
      const result = await createEmailTemplate({
        title: formData.title,
        subject: formData.subject,
        body: formData.body,
        category: formData.category
      });

      if (result.success) {
        setTemplates(prev => [...prev, result.template]);
        toast({
          title: "Success",
          description: "Email template created successfully"
        });
        setIsAddDialogOpen(false);
        resetForm();
      }
    } catch (error) {
      console.error('Failed to create template:', error);
      toast({
        title: "Error",
        description: "Failed to create email template",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUpdateTemplate = async () => {
    if (!selectedTemplate) return;

    try {
      if (!formData.title.trim() || !formData.subject.trim() || !formData.body.trim()) {
        toast({
          title: "Error",
          description: "Title, subject, and body are required",
          variant: "destructive"
        });
        return;
      }

      setIsSubmitting(true);
      const result = await updateEmailTemplate(selectedTemplate._id, {
        title: formData.title,
        subject: formData.subject,
        body: formData.body,
        category: formData.category
      });

      if (result.success) {
        setTemplates(prev =>
          prev.map(template =>
            template._id === selectedTemplate._id ? result.template : template
          )
        );
        toast({
          title: "Success",
          description: "Email template updated successfully"
        });
        setIsEditDialogOpen(false);
      }
    } catch (error) {
      console.error('Failed to update template:', error);
      toast({
        title: "Error",
        description: "Failed to update email template",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteTemplate = async () => {
    if (!selectedTemplate) return;

    try {
      setIsSubmitting(true);
      const result = await deleteEmailTemplate(selectedTemplate._id);

      if (result.success) {
        setTemplates(prev => prev.filter(template => template._id !== selectedTemplate._id));
        toast({
          title: "Success",
          description: "Email template deleted successfully"
        });
        setIsDeleteDialogOpen(false);
      }
    } catch (error) {
      console.error('Failed to delete template:', error);
      toast({
        title: "Error",
        description: "Failed to delete email template",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditClick = (template: EmailTemplate) => {
    setSelectedTemplate(template);
    setFormData({
      title: template.title,
      subject: template.subject,
      body: template.body,
      category: template.category
    });
    setIsEditDialogOpen(true);
  };

  const handleDeleteClick = (template: EmailTemplate) => {
    setSelectedTemplate(template);
    setIsDeleteDialogOpen(true);
  };

  const handleDuplicateClick = (template: EmailTemplate) => {
    setFormData({
      title: `${template.title} (Copy)`,
      subject: template.subject,
      body: template.body,
      category: template.category
    });
    setIsAddDialogOpen(true);
  };

  const copyToClipboard = (text: string, type: 'subject' | 'body') => {
    navigator.clipboard.writeText(text).then(() => {
      toast({
        title: "Copied!",
        description: `${type === 'subject' ? 'Subject' : 'Message body'} copied to clipboard`,
      });
    }).catch(err => {
      console.error('Failed to copy text: ', err);
      toast({
        title: "Error",
        description: "Failed to copy to clipboard",
        variant: "destructive"
      });
    });
  };

  const canEditTemplate = (template: EmailTemplate) => {
    if (!user) return false;
    if (['admin', 'manager'].includes(user.role)) return true;
    return template.createdBy._id === user._id;
  };

  const getCategoryColor = (category: string): string => {
    switch (category) {
      case 'Trademe':
        return '#f59e0b'; // amber-500 (yellow/orange)
      case 'Personal Finance':
        return '#8b5cf6'; // violet-500 (purple)
      case 'Buys/WIW':
        return '#0ea5e9'; // sky-500 (blue)
      case 'Other':
        return '#22c55e'; // green-500 (green)
      default:
        return '#6b7280'; // gray-500
    }
  };

  return (
    <div className="space-y-6">
      <div className="bg-card rounded-lg p-6 border shadow-sm">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold">Email Templates</h1>
            <p className="text-muted-foreground mt-1">
              Craft consistent, professional emails with ease.
            </p>
          </div>
          <Button
            onClick={() => { resetForm(); setIsAddDialogOpen(true); }}
            size="lg"
            className="gap-2"
          >
            <Plus className="h-5 w-5" /> Create New Template
          </Button>
        </div>

        <Tabs defaultValue="all" value={activeCategory} onValueChange={setActiveCategory}>
          <TabsList className="w-full grid grid-cols-5">
            <TabsTrigger value="all" className="flex items-center gap-2">
              <span className="hidden sm:inline">All Templates</span>
              <span className="sm:hidden">All</span>
            </TabsTrigger>
            <TabsTrigger value="Trademe" className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full" style={{ backgroundColor: getCategoryColor('Trademe') }}></div>
              <span className="hidden sm:inline">TradeMe</span>
              <span className="sm:hidden">TM</span>
            </TabsTrigger>
            <TabsTrigger value="Personal Finance" className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full" style={{ backgroundColor: getCategoryColor('Personal Finance') }}></div>
              <span className="hidden sm:inline">Personal Finance</span>
              <span className="sm:hidden">PF</span>
            </TabsTrigger>
            <TabsTrigger value="Buys/WIW" className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full" style={{ backgroundColor: getCategoryColor('Buys/WIW') }}></div>
              <span className="hidden sm:inline">Buys/WIW</span>
              <span className="sm:hidden">Buys</span>
            </TabsTrigger>
            <TabsTrigger value="Other" className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full" style={{ backgroundColor: getCategoryColor('Other') }}></div>
              <span className="hidden sm:inline">Other</span>
              <span className="sm:hidden">Other</span>
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {loading ? (
        <div className="space-y-6">
          {[1, 2, 3].map(i => (
            <Card key={i} className="w-full">
              <CardHeader>
                <Skeleton className="h-6 w-1/3" />
                <Skeleton className="h-4 w-1/4 mt-2" />
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Skeleton className="h-5 w-1/6 mb-2" />
                  <Skeleton className="h-4 w-full" />
                </div>
                <div>
                  <Skeleton className="h-5 w-1/6 mb-2" />
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-full mt-1" />
                  <Skeleton className="h-4 w-3/4 mt-1" />
                </div>
              </CardContent>
              <CardFooter className="flex justify-between border-t pt-4">
                <Skeleton className="h-4 w-1/4" />
                <div className="flex gap-2">
                  <Skeleton className="h-9 w-24" />
                  <Skeleton className="h-9 w-24" />
                  <Skeleton className="h-9 w-24" />
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : filteredTemplates.length === 0 ? (
        <div className="text-center py-10">
          <p className="text-muted-foreground">No email templates found. Create one to get started.</p>
        </div>
      ) : (
        <div className="space-y-6">
          {filteredTemplates.map(template => (
            <Card key={template._id} className="w-full overflow-hidden border-l-4"
              style={{ borderLeftColor: getCategoryColor(template.category) }}>
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-xl">{template.title}</CardTitle>
                    <CardDescription className="flex items-center mt-1">
                      <span className="inline-block text-xs px-2 py-0.5 rounded-full bg-muted mr-2">
                        {template.category}
                      </span>
                      <span className="text-xs">
                        Created by {template.createdBy.username || template.createdBy.email} • {new Date(template.createdAt).toLocaleDateString()}
                        {template.updatedAt !== template.createdAt &&
                          ` • Updated: ${new Date(template.updatedAt).toLocaleDateString()}`}
                      </span>
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="bg-muted/30 p-3 rounded-md">
                  <h4 className="text-sm font-semibold mb-1">Subject:</h4>
                  <p className="text-sm">{template.subject}</p>
                </div>
                <div className="bg-muted/30 p-3 rounded-md">
                  <h4 className="text-sm font-semibold mb-1">Body:</h4>
                  <p className="text-sm whitespace-pre-wrap">{template.body}</p>
                </div>
              </CardContent>
              <CardFooter className="flex flex-wrap justify-end gap-2 border-t pt-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(template.subject, 'subject')}
                >
                  <ClipboardCopy className="h-4 w-4 mr-2" />
                  Copy Subject
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(template.body, 'body')}
                >
                  <ClipboardCopy className="h-4 w-4 mr-2" />
                  Copy Body
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleDuplicateClick(template)}
                >
                  <Copy className="h-4 w-4 mr-2" />
                  Duplicate
                </Button>
                {canEditTemplate(template) && (
                  <>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEditClick(template)}
                    >
                      <Edit className="h-4 w-4 mr-2" />
                      Edit
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => handleDeleteClick(template)}
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete
                    </Button>
                  </>
                )}
              </CardFooter>
            </Card>
          ))}
        </div>
      )}

      {/* Add Template Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-xl">Add Email Template</DialogTitle>
            <DialogDescription>
              Create a new email template. All fields are required.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-6 py-4">
            <div className="space-y-2">
              <label htmlFor="title" className="text-sm font-medium">
                Template Title
              </label>
              <Input
                id="title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                placeholder="Template title (for your reference)"
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="category" className="text-sm font-medium">
                Category
              </label>
              <Select
                value={formData.category}
                onValueChange={handleSelectChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Trademe">TradeMe</SelectItem>
                  <SelectItem value="Personal Finance">Personal Finance</SelectItem>
                  <SelectItem value="Buys/WIW">Buys/WIW</SelectItem>
                  <SelectItem value="Other">Other</SelectItem>
                </SelectContent>
              </Select>
              <div className="flex mt-1 items-center">
                <div className="w-4 h-4 rounded-full mr-2" style={{ backgroundColor: getCategoryColor(formData.category) }}></div>
                <span className="text-xs text-muted-foreground">Templates are color-coded by category</span>
              </div>
            </div>

            <div className="space-y-2">
              <label htmlFor="subject" className="text-sm font-medium">
                Email Subject
              </label>
              <Input
                id="subject"
                name="subject"
                value={formData.subject}
                onChange={handleInputChange}
                placeholder="Email subject line"
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="body" className="text-sm font-medium">
                Email Body
              </label>
              <Textarea
                id="body"
                name="body"
                value={formData.body}
                onChange={handleInputChange}
                placeholder="Email body content"
                className="min-h-[200px] font-mono text-sm"
              />
              <p className="text-xs text-muted-foreground">Use plain text formatting. Line breaks will be preserved.</p>
            </div>
          </div>
          <DialogFooter className="gap-2">
            <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddTemplate} disabled={isSubmitting}>
              {isSubmitting ? 'Saving...' : 'Save Template'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Template Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-xl">Edit Email Template</DialogTitle>
            <DialogDescription>
              Update the email template details.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-6 py-4">
            <div className="space-y-2">
              <label htmlFor="edit-title" className="text-sm font-medium">
                Template Title
              </label>
              <Input
                id="edit-title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                placeholder="Template title (for your reference)"
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="edit-category" className="text-sm font-medium">
                Category
              </label>
              <Select
                value={formData.category}
                onValueChange={handleSelectChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Trademe">TradeMe</SelectItem>
                  <SelectItem value="Personal Finance">Personal Finance</SelectItem>
                  <SelectItem value="Buys/WIW">Buys/WIW</SelectItem>
                  <SelectItem value="Other">Other</SelectItem>
                </SelectContent>
              </Select>
              <div className="flex mt-1 items-center">
                <div className="w-4 h-4 rounded-full mr-2" style={{ backgroundColor: getCategoryColor(formData.category) }}></div>
                <span className="text-xs text-muted-foreground">Templates are color-coded by category</span>
              </div>
            </div>

            <div className="space-y-2">
              <label htmlFor="edit-subject" className="text-sm font-medium">
                Email Subject
              </label>
              <Input
                id="edit-subject"
                name="subject"
                value={formData.subject}
                onChange={handleInputChange}
                placeholder="Email subject line"
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="edit-body" className="text-sm font-medium">
                Email Body
              </label>
              <Textarea
                id="edit-body"
                name="body"
                value={formData.body}
                onChange={handleInputChange}
                placeholder="Email body content"
                className="min-h-[200px] font-mono text-sm"
              />
              <p className="text-xs text-muted-foreground">Use plain text formatting. Line breaks will be preserved.</p>
            </div>
          </div>
          <DialogFooter className="gap-2">
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateTemplate} disabled={isSubmitting}>
              {isSubmitting ? 'Updating...' : 'Update Template'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Email Template</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this email template? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>

          {selectedTemplate && (
            <div className="my-4 p-3 border rounded-md bg-muted/30">
              <div className="mb-2">
                <span className="font-medium">Title:</span> {selectedTemplate.title}
              </div>
              <div className="mb-2">
                <span className="font-medium">Category:</span>
                <span className="inline-flex items-center ml-1">
                  <div
                    className="w-3 h-3 rounded-full mr-1"
                    style={{ backgroundColor: getCategoryColor(selectedTemplate.category) }}
                  ></div>
                  {selectedTemplate.category}
                </span>
              </div>
              <div className="mb-2">
                <span className="font-medium">Subject:</span> {selectedTemplate.subject}
              </div>
            </div>
          )}

          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteTemplate}
              disabled={isSubmitting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isSubmitting ? 'Deleting...' : 'Delete Template'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
