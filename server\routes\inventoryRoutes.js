const express = require('express');
const router = express.Router();
const inventoryService = require('../services/inventoryService');
const { requireUser } = require('./middleware/auth');
const mongoose = require('mongoose');
const Inventory = require('../models/Inventory');



// Get all inventory items
router.get('/', requireUser, async (req, res) => {
  try {
    const result = await inventoryService.getAllItems();

    if (result.success) {
      return res.status(200).json({
        success: true,
        data: result.items,
      });
    } else {
      console.error(`Failed to retrieve inventory items: ${result.error}`);
      return res.status(400).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error('Error in get inventory route:', error);
    return res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

// Create a new inventory item
router.post('/', requireUser, async (req, res) => {
  try {
    console.log(`POST /api/inventory request received from user: ${req.user._id}`);

    const itemData = req.body;
    console.log('Creating inventory item with data:', itemData);

    // Make sure we're handling all the new fields
    const inventoryItem = {
      name: itemData.name,
      category: itemData.category,
      brand: itemData.brand || '',
      model: itemData.model || '',
      modelNumber: itemData.modelNumber || '',
      releaseYear: itemData.releaseYear || null,
      lastRRP: itemData.lastRRP || 0,
      currentPrice: itemData.currentPrice,
      lastUpdated: new Date()
    };

    const result = await inventoryService.createItem(inventoryItem);

    if (result.success) {
      console.log(`Successfully created inventory item: ${result.item._id}`);
      return res.status(201).json({
        success: true,
        data: result.item,
      });
    } else {
      console.error(`Failed to create inventory item: ${result.error}`);
      return res.status(400).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error('Error in create inventory item route:', error);
    return res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

// Seed inventory items - requires admin role
router.post('/seed', requireUser, async (req, res) => {
  try {

    // Check if user is admin
    if (req.user.role !== 'admin') {
      console.warn(`Unauthorized seed attempt by non-admin user: ${req.user._id}`);
      return res.status(403).json({
        success: false,
        error: 'Only admin users can seed inventory',
      });
    }

    console.log('Starting inventory seeding process...');
    const result = await inventoryService.seedInventory();

    if (result.success) {
      console.log(`Inventory seeding completed successfully: ${result.message}`);
      return res.status(200).json({
        success: true,
        message: result.message,
        count: result.count,
      });
    } else {
      console.error(`Inventory seeding failed: ${result.error}`);
      return res.status(400).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error('Error in seed inventory route:', error);
    return res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

// Get a single inventory item by ID
router.get('/:id', requireUser, async (req, res) => {
  try {

    const itemId = req.params.id;

    // Validate itemId is a valid MongoDB ObjectId
    if (!mongoose.Types.ObjectId.isValid(itemId)) {
      console.error(`Invalid inventory item ID format: ${itemId}`);
      return res.status(400).json({
        success: false,
        error: 'Invalid item ID format',
      });
    }

    // Find the item in the database
    const item = await Inventory.findById(itemId).populate('location', 'name');

    if (!item) {
      console.error(`Inventory item with ID ${itemId} not found`);
      return res.status(404).json({
        success: false,
        error: 'Inventory item not found',
      });
    }

    return res.status(200).json({
      success: true,
      data: {
        ...item.toObject(),
        location: item.location?.name,
        locationId: item.location?._id || '',
      },
    });

  } catch (error) {
    console.error(`Error retrieving inventory item:`, error);
    return res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

// Update an inventory item
router.put('/:id', requireUser, async (req, res) => {
  try {
    console.log(`PUT /api/inventory/${req.params.id} request received from user: ${req.user._id}`);

    // Check if user has permission (admin or manager)
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      console.warn(`Unauthorized update attempt by user: ${req.user._id}`);
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to update inventory items',
      });
    }

    const itemId = req.params.id;
    const updateData = req.body;
    const userId = req.user._id; // Pass the user ID for audit logging

    console.log(`Updating inventory item ${itemId} with data:`, updateData);

    const result = await inventoryService.updateItem(itemId, updateData, userId);

    if (result.success) {
      console.log(`Successfully updated inventory item: ${itemId}`);
      return res.status(200).json({
        success: true,
        data: result.item,
      });
    } else {
      console.error(`Failed to update inventory item: ${result.error}`);

      // Determine appropriate status code
      const statusCode = result.error === 'Inventory item not found' ? 404 : 400;

      return res.status(statusCode).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error(`Error in update inventory item route:`, error);
    return res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

// Delete an inventory item
router.delete('/:id', requireUser, async (req, res) => {
  try {
    console.log(`DELETE /api/inventory/${req.params.id} request received from user: ${req.user._id}`);

    // Check if user has permission (admin or manager)
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      console.warn(`Unauthorized delete attempt by user: ${req.user._id}`);
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to delete inventory items',
      });
    }

    const itemId = req.params.id;
    const userId = req.user._id;

    if (!mongoose.Types.ObjectId.isValid(itemId)) {
      console.error(`Invalid inventory item ID format: ${itemId}`);
      return res.status(400).json({
        success: false,
        error: 'Invalid item ID format',
      });
    }

    console.log(`Deleting inventory item: ${itemId}`);
    const result = await inventoryService.deleteItem(itemId, userId);

    if (result.success) {
      console.log(`Successfully deleted inventory item: ${itemId}`);
      return res.status(200).json({
        success: true,
        message: result.message,
      });
    } else {
      console.error(`Failed to delete inventory item: ${result.error}`);

      // Determine appropriate status code
      const statusCode = result.error === 'Inventory item not found' ? 404 : 400;

      return res.status(statusCode).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error(`Error in delete inventory item route:`, error);
    return res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

module.exports = router;