import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { GoldSettings } from '@/components/GoldSettings';
import { TradeMeSettings } from '@/components/trademe/TradeMeSettings';
import { DeviceCheckerSettings } from '@/components/DeviceCheckerSettings';
import { LoanSettings } from '@/components/LoanSettings';
import { HappyOrNotSettings } from '@/components/HappyOrNotSettings';
import { useAuth } from '@/contexts/AuthContext';
import { AlertTriangle } from 'lucide-react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/useToast';
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Plus, Trash2, RefreshCw } from "lucide-react";
import { Progress } from "@/components/ui/progress";
import { UserManagementTable } from '@/components/UserManagementTable';
import { getAllUsers, UserListItem } from '@/api/user';
import { getDashboardSettings, updateDashboardSettings, forceDashboardUpdate, getScreenshotStatus, DashboardSettings, ScreenshotStatus } from "@/api/dashboardSettings";

export function Settings() {
  const [searchParams] = useSearchParams();
  const tabParam = searchParams.get('tab');
  const validTabs = ['dashboard', 'users', 'gold', 'trademe', 'deviceLookupTool', 'loanTracker', 'happyOrNot'];
  const defaultTab = validTabs.includes(tabParam) ? tabParam : 'dashboard';

  const [activeTab, setActiveTab] = useState(defaultTab);
  const { user } = useAuth();
  const navigate = useNavigate();
  const isAdminOrManager = user?.role === 'admin' || user?.role === 'manager';
  const { toast } = useToast();
  const [isUpdating, setIsUpdating] = useState(false);

  // Dashboard Settings State
  const [dashboardSettings, setDashboardSettings] = useState<DashboardSettings>({
    pbiLinks: [],
    showPowerBIImages: true,
    screenshotInterval: 5,
    screenshotSchedule: {
      enabled: true,
      startTime: '08:00',
      endTime: '17:30'
    }
  });

  // Add these states
  const [users, setUsers] = useState<UserListItem[]>([]);
  const [loadingUsers, setLoadingUsers] = useState(false);
  const [updateProgress, setUpdateProgress] = useState(0);
  const [screenshotStatus, setScreenshotStatus] = useState<ScreenshotStatus | null>(null);
  const [statusPolling, setStatusPolling] = useState<NodeJS.Timeout | null>(null);
  const [successToastShown, setSuccessToastShown] = useState(false);

  // Fetch dashboard settings
  useEffect(() => {
    if (activeTab === 'dashboard') {
      getDashboardSettings()
        .then(settings => setDashboardSettings(settings))
        .catch(error => {
          console.error('Error fetching dashboard settings:', error);
          toast({
            title: "Error",
            description: "Failed to fetch dashboard settings",
            variant: "destructive",
          });
        });
    }
  }, [activeTab]);

  // Function to start polling for screenshot status
  const startStatusPolling = () => {
    // Clear any existing polling
    if (statusPolling) {
      clearInterval(statusPolling);
    }

    // Immediately fetch status
    fetchScreenshotStatus();

    // Set up polling every 1 second for more responsive updates
    const intervalId = setInterval(fetchScreenshotStatus, 1000);
    setStatusPolling(intervalId);
  };

  // Function to fetch screenshot status
  const fetchScreenshotStatus = async () => {
    try {
      const status = await getScreenshotStatus();

      // Log the status for debugging
      console.log('Screenshot status:', JSON.stringify(status, null, 2));
      console.log('Current message:', status.logs.length > 0 ? status.logs[status.logs.length - 1].message : 'No logs');

      // Check if status has changed before updating
      const hasChanged = !screenshotStatus ||
        JSON.stringify(status) !== JSON.stringify(screenshotStatus);

      if (hasChanged) {
        console.log('Status changed, updating UI...');
        // Update the status state
        setScreenshotStatus(status);

        // Update progress based on status
        if (status.totalLinks > 0) {
          // Calculate progress percentage based on processed links
          const progressPercent = Math.floor((status.processedLinks / status.totalLinks) * 100);

          // If process is complete, set to 100%
          if (!status.inProgress && status.currentStep === 'completed') {
            setUpdateProgress(100);
          } else {
            // Otherwise use the calculated percentage
            setUpdateProgress(progressPercent);
          }
        }
      }

      // If process is complete, stop polling
      if (!status.inProgress && (status.currentStep === 'completed' || status.currentStep === 'error')) {
        if (statusPolling) {
          console.log('Screenshot process complete, stopping polling');
          clearInterval(statusPolling);
          setStatusPolling(null);

          // Dismiss the progress toast first
          if (toastRef.current) {
            try {
              toastRef.current.dismiss();
            } catch (error) {
              console.error('Error dismissing toast:', error);
            }
          }

          // Show final success toast if not already shown
          if (status.currentStep === 'completed' && !successToastShown) {
            setSuccessToastShown(true);

            // Show a separate success toast
            setTimeout(() => {
              toast({
                title: "Success",
                description: "Dashboard screenshots updated successfully",
                variant: "default",
                duration: 5000, // Show for 5 seconds
              });
            }, 500); // Shorter delay to show success toast
          }
          // Show error toast if there was an error
          else if (status.currentStep === 'error' && !successToastShown) {
            setSuccessToastShown(true); // Use the same flag to prevent multiple toasts

            // Show a separate error toast
            setTimeout(() => {
              toast({
                title: "Error",
                description: status.error || "An error occurred during the screenshot process",
                variant: "destructive",
                duration: 5000, // Show for 5 seconds
              });
            }, 500); // Shorter delay to show error toast
          }
        }
      }
    } catch (error) {
      console.error('Error fetching screenshot status:', error);
    }
  };

  // Clean up polling on unmount
  useEffect(() => {
    return () => {
      if (statusPolling) {
        clearInterval(statusPolling);
      }
    };
  }, [statusPolling]);

  // Add this function
  const fetchUsers = async () => {
    setLoadingUsers(true);
    try {
      const data = await getAllUsers();
      setUsers(data);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch users",
        variant: "destructive",
      });
    } finally {
      setLoadingUsers(false);
    }
  };

  // Add this useEffect
  useEffect(() => {
    if (activeTab === 'users') {
      fetchUsers();
    }
  }, [activeTab]);

  // Save dashboard settings
  const handleSaveDashboardSettings = async () => {
    try {
      await updateDashboardSettings(dashboardSettings);
      toast({
        title: "Success",
        description: "Settings saved successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save settings",
        variant: "destructive",
      });
    }
  };

  // Create a ref to store the toast object
  const toastRef = useRef<{ id: string; update: Function; dismiss: Function } | null>(null);

  // Function to update the toast with current status
  const updateStatusToast = () => {
    if (!screenshotStatus) return;

    // Get the latest log message
    const latestLog = screenshotStatus.logs.length > 0
      ? screenshotStatus.logs[screenshotStatus.logs.length - 1].message
      : 'Processing...';

    // Format the message to show
    let statusMessage = latestLog;

    // If completed, show the success message
    if (screenshotStatus.currentStep === 'completed') {
      statusMessage = 'Successfully updated screenshots in database';
    }

    // Calculate progress
    const progressValue = screenshotStatus.totalLinks > 0
      ? Math.floor((screenshotStatus.processedLinks / screenshotStatus.totalLinks) * 100)
      : 0;

    // Get a unique key based on the latest log to force re-render
    const latestLogTime = screenshotStatus.logs.length > 0
      ? new Date(screenshotStatus.logs[screenshotStatus.logs.length - 1].time).getTime()
      : Date.now();

    // Create toast content with a key to force re-render
    const toastContent = (
      <div className="w-full space-y-2" key={`toast-${latestLogTime}-${screenshotStatus.processedLinks}`}>
        <div className="flex items-center gap-2 text-sm font-medium">
          {screenshotStatus.inProgress || !screenshotStatus.endTime ? (
            <Loader2 className="h-4 w-4 animate-spin text-primary" />
          ) : null}
          <span>{statusMessage}</span>
        </div>
        <div className="text-xs text-muted-foreground">
          {screenshotStatus.processedLinks}/{screenshotStatus.totalLinks} dashboards processed
        </div>
        {screenshotStatus.currentLink && screenshotStatus.inProgress && (
          <div className="text-xs text-muted-foreground">
            Current dashboard: {screenshotStatus.currentLink}
          </div>
        )}
        {screenshotStatus.currentStep && (
          <div className="text-xs text-muted-foreground">
            Status: {screenshotStatus.currentStep.replace(/_/g, ' ')}
          </div>
        )}
        <Progress value={progressValue} className="h-2" />
      </div>
    );

    // If we already have a toast, update it
    if (toastRef.current) {
      try {
        toastRef.current.update({
          title: "Dashboard Update",
          description: toastContent,
          duration: !screenshotStatus.inProgress ? 3000 : 100000, // Close automatically when complete
        });
      } catch (error) {
        console.error('Error updating toast:', error);
        // If update fails, create a new toast
        toastRef.current = toast({
          title: "Dashboard Update",
          description: toastContent,
          duration: !screenshotStatus.inProgress ? 3000 : 100000, // Close automatically when complete
        });
      }
    }
    // Otherwise create a new toast
    else {
      toastRef.current = toast({
        title: "Dashboard Update",
        description: toastContent,
        duration: !screenshotStatus.inProgress ? 3000 : 100000, // Close automatically when complete
      });
    }

    // We'll handle the success toast in the useEffect to avoid showing it multiple times
  };

  // Effect to update the toast whenever screenshotStatus changes
  useEffect(() => {
    if (screenshotStatus) {
      updateStatusToast();

      // If process is complete, clean up
      if (!screenshotStatus.inProgress &&
          (screenshotStatus.currentStep === 'completed' || screenshotStatus.currentStep === 'error')) {
        console.log('Process complete, stopping polling and cleaning up');

        // Stop polling
        if (statusPolling) {
          clearInterval(statusPolling);
          setStatusPolling(null);
        }

        setIsUpdating(false);

        // Dismiss the progress toast
        if (toastRef.current) {
          try {
            toastRef.current.dismiss();
          } catch (error) {
            console.error('Error dismissing toast:', error);
          }
          toastRef.current = null;
        }

        // Clear the status after a delay to prevent further updates
        setTimeout(() => {
          setScreenshotStatus(null);
        }, 1000);
      }
    }
  }, [screenshotStatus, statusPolling]);

  const handleForceUpdate = async () => {
    setIsUpdating(true);
    setUpdateProgress(0);
    setSuccessToastShown(false); // Reset success toast flag
    toastRef.current = null; // Reset toast ref

    try {
      // Step 1: Trigger the update
      const response = await forceDashboardUpdate();

      if (!response.success) {
        throw new Error('Failed to trigger dashboard update');
      }

      const totalLinks = response.totalLinks || 0;
      if (totalLinks === 0) {
        throw new Error('No dashboards to update');
      }

      // Create initial toast
      toastRef.current = toast({
        title: "Dashboard Update",
        description: (
          <div className="w-full space-y-2">
            <div className="flex items-center gap-2 text-sm">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Starting PowerBI dashboard update...</span>
            </div>
            <Progress value={0} className="h-2" />
          </div>
        ),
        duration: 100000, // Long duration for the initial toast
      });

      // Start polling for status updates
      startStatusPolling();

    } catch (error) {
      // Show error toast
      toast({
        title: "Error",
        description: error.message || "Failed to trigger dashboard update",
        variant: "destructive",
      });

      // Stop polling
      if (statusPolling) {
        clearInterval(statusPolling);
        setStatusPolling(null);
      }

      setIsUpdating(false);
    }
  };

  // Update URL when tab changes
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    navigate(`/settings?tab=${value}`, { replace: true });
  };

  // Listen for query param changes
  useEffect(() => {
    if (tabParam && validTabs.includes(tabParam)) {
      setActiveTab(tabParam);
    }
  }, [tabParam]);

  return (
    <div className="space-y-6">
      <div className="border-b">
        <div className="flex h-16 items-center px-4">
          <h1 className="text-2xl font-bold">Settings</h1>
        </div>
      </div>

      {!isAdminOrManager && (
        <Card className="border-yellow-500/50 bg-yellow-500/10">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 text-yellow-500">
              <AlertTriangle className="h-5 w-5" />
              <p>Only administrators and managers can view and modify settings.</p>
            </div>
          </CardContent>
        </Card>
      )}

      {isAdminOrManager && (
        <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
          <TabsList className="grid w-full grid-cols-7">
            <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
            <TabsTrigger value="users">User Management</TabsTrigger>
            <TabsTrigger value="gold">Gold Calculator</TabsTrigger>
            <TabsTrigger value="trademe">Trademe</TabsTrigger>
            <TabsTrigger value="deviceLookupTool">Device Lookup Tool</TabsTrigger>
            <TabsTrigger value="loanTracker">Loan Tracker</TabsTrigger>
            <TabsTrigger value="happyOrNot">Happy or Not</TabsTrigger>
          </TabsList>

          <TabsContent value="dashboard" className="mt-6 space-y-6">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-xl">PowerBI Dashboard Settings</CardTitle>
                <CardDescription>
                  Configure PowerBI dashboard display and capture settings
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-5 pt-0">
                {/* Dashboard Visibility Section */}
                <div className="space-y-4">
                  <h3 className="text-base font-medium">Dashboard Visibility</h3>
                  <div className="border rounded-lg overflow-hidden">
                    <div className="p-3">
                      <div className="flex items-center justify-between">
                        <div>
                          <Label htmlFor="showPowerBIImages" className="font-medium">Show PowerBI Images</Label>
                          <p className="text-xs text-muted-foreground mt-0.5">
                            Toggle visibility of PowerBI images on the dashboard
                          </p>
                        </div>
                        <Switch
                          id="showPowerBIImages"
                          checked={dashboardSettings.showPowerBIImages}
                          onCheckedChange={(checked) => {
                            setDashboardSettings({
                              ...dashboardSettings,
                              showPowerBIImages: checked
                            });
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <Separator className="my-4" />

                {/* Screenshot Schedule Section */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-base font-medium">Screenshot Settings</h3>
                    <Button
                      onClick={handleForceUpdate}
                      disabled={isUpdating}
                      variant="outline"
                      size="sm"
                    >
                      {isUpdating ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Updating...
                        </>
                      ) : (
                        <>
                          <RefreshCw className="mr-2 h-4 w-4" />
                          Update Now
                        </>
                      )}
                    </Button>
                  </div>

                  <div className="border rounded-lg overflow-hidden">
                    <div className="p-3 border-b">
                      <div className="flex items-center justify-between">
                        <div>
                          <Label htmlFor="screenshotScheduleEnabled" className="font-medium">Automatic Screenshots</Label>
                          <p className="text-xs text-muted-foreground mt-0.5">
                            Enable or disable automatic screenshot capture
                          </p>
                        </div>
                        <Switch
                          id="screenshotScheduleEnabled"
                          checked={dashboardSettings.screenshotSchedule.enabled}
                          onCheckedChange={(checked) => {
                            setDashboardSettings({
                              ...dashboardSettings,
                              screenshotSchedule: {
                                ...dashboardSettings.screenshotSchedule,
                                enabled: checked
                              }
                            });
                          }}
                        />
                      </div>
                    </div>

                    <div className="p-3">
                      <div className="grid gap-4 sm:grid-cols-3">
                        <div className="space-y-1">
                          <Label htmlFor="interval" className="text-xs">Capture Interval (min)</Label>
                          <Input
                            id="interval"
                            type="number"
                            value={dashboardSettings.screenshotInterval}
                            onChange={(e) => setDashboardSettings({
                              ...dashboardSettings,
                              screenshotInterval: parseInt(e.target.value)
                            })}
                            min={1}
                            className="h-8"
                          />
                        </div>
                        <div className="space-y-1">
                          <Label htmlFor="startTime" className="text-xs">Start Time</Label>
                          <Input
                            id="startTime"
                            type="time"
                            value={dashboardSettings.screenshotSchedule.startTime}
                            onChange={(e) => setDashboardSettings({
                              ...dashboardSettings,
                              screenshotSchedule: {
                                ...dashboardSettings.screenshotSchedule,
                                startTime: e.target.value
                              }
                            })}
                            className="h-8"
                          />
                        </div>
                        <div className="space-y-1">
                          <Label htmlFor="endTime" className="text-xs">End Time</Label>
                          <Input
                            id="endTime"
                            type="time"
                            value={dashboardSettings.screenshotSchedule.endTime}
                            onChange={(e) => setDashboardSettings({
                              ...dashboardSettings,
                              screenshotSchedule: {
                                ...dashboardSettings.screenshotSchedule,
                                endTime: e.target.value
                              }
                            })}
                            className="h-8"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <Separator className="my-4" />

                {/* Dashboard Links Section */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-base font-medium">PowerBI Dashboard Links</h3>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setDashboardSettings({
                          ...dashboardSettings,
                          pbiLinks: [
                            ...dashboardSettings.pbiLinks,
                            { name: '', url: '', enabled: true }
                          ]
                        });
                      }}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add Link
                    </Button>
                  </div>

                  <div className="border rounded-lg overflow-hidden">
                    {dashboardSettings.pbiLinks.length === 0 ? (
                      <div className="p-4 text-center text-muted-foreground">
                        No dashboard links added. Click "Add Link" to create one.
                      </div>
                    ) : (
                      <div className="divide-y">
                        {dashboardSettings.pbiLinks.map((link, index) => (
                          <div key={index} className="p-3 hover:bg-muted/30 transition-colors">
                            <div className="flex items-center justify-between mb-2">
                              <div className="font-medium truncate max-w-[70%]">
                                {link.name || <span className="text-muted-foreground italic">Unnamed Dashboard</span>}
                              </div>
                              <div className="flex items-center gap-2">
                                <div className="flex items-center gap-1.5">
                                  <Switch
                                    id={`enabled-${index}`}
                                    checked={link.enabled}
                                    onCheckedChange={(checked) => {
                                      const newLinks = [...dashboardSettings.pbiLinks];
                                      newLinks[index].enabled = checked;
                                      setDashboardSettings({ ...dashboardSettings, pbiLinks: newLinks });
                                    }}
                                    className="scale-75 origin-left"
                                  />
                                  <Label htmlFor={`enabled-${index}`} className="text-xs">Active</Label>
                                </div>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-7 w-7 text-destructive hover:text-destructive/90 hover:bg-destructive/10"
                                  onClick={() => {
                                    const newLinks = dashboardSettings.pbiLinks.filter((_, i) => i !== index);
                                    setDashboardSettings({ ...dashboardSettings, pbiLinks: newLinks });
                                  }}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                            <div className="grid gap-3 sm:grid-cols-2">
                              <div className="space-y-1">
                                <Label htmlFor={`name-${index}`} className="text-xs">Dashboard Name</Label>
                                <Input
                                  id={`name-${index}`}
                                  value={link.name}
                                  onChange={(e) => {
                                    const newLinks = [...dashboardSettings.pbiLinks];
                                    newLinks[index].name = e.target.value;
                                    setDashboardSettings({ ...dashboardSettings, pbiLinks: newLinks });
                                  }}
                                  placeholder="Enter dashboard name"
                                  className="h-8"
                                />
                              </div>
                              <div className="space-y-1">
                                <Label htmlFor={`url-${index}`} className="text-xs">PowerBI URL</Label>
                                <Input
                                  id={`url-${index}`}
                                  value={link.url}
                                  onChange={(e) => {
                                    const newLinks = [...dashboardSettings.pbiLinks];
                                    newLinks[index].url = e.target.value;
                                    setDashboardSettings({ ...dashboardSettings, pbiLinks: newLinks });
                                  }}
                                  placeholder="Enter PowerBI URL"
                                  className="h-8"
                                />
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="flex justify-end space-x-4">
              <Button onClick={handleSaveDashboardSettings}>
                Save Changes
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="users" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>User Management</CardTitle>
                <CardDescription>Manage user accounts and permissions</CardDescription>
              </CardHeader>
              <CardContent>
                {loadingUsers ? (
                  <div className="flex justify-center items-center h-32">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                ) : (
                  <UserManagementTable users={users} onUserUpdated={fetchUsers} currentUserRole={user?.role} />
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="gold" className="mt-6">
            <GoldSettings onSettingsUpdated={() => {}} />
          </TabsContent>

          <TabsContent value="trademe" className="mt-6">
            <TradeMeSettings />
          </TabsContent>

          <TabsContent value="deviceLookupTool" className="mt-6">
            <DeviceCheckerSettings />
          </TabsContent>

          <TabsContent value="loanTracker" className="mt-6">
            <LoanSettings />
          </TabsContent>

          <TabsContent value="happyOrNot" className="mt-6">
            <HappyOrNotSettings />
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}

export default Settings;
