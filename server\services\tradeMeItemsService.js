/**
 * TradeMe Items Service
 *
 * Handles operations related to TradeMe items, including:
 * - Creating and updating items
 * - Listing and relisting items on TradeMe
 * - Managing questions and answers
 * - Tracking item history and audits
 * - Syncing with TradeMe API
 */

const TradeMeItems = require('../models/TradeMeItems');
const TradeMeItemAudit = require('../models/TradeMeItemAudit');
const tradeMeService = require('./tradeMeService');
const { downloadAndSaveImage, isLocalImage } = require('./imageService');
const fs = require('fs');
const path = require('path');
const mongoose = require('mongoose');
const User = require('../models/User');
const Location = require('../models/Location');

/**
 * Create a new TradeMe item
 * @param {Object} itemData - Data for the new item
 * @param {Object} user - User creating the item
 * @returns {Promise<Object>} Result of the creation
 */
async function createItem(itemData, user) {
  try {
    // Create the item
    const newItem = new TradeMeItems({
      ...itemData,
      createdBy: user._id,
      status: 'draft' // Always start as draft
    });

    // Save the item
    await newItem.save();

    // Create audit entry
    await createAuditEntry({
      itemId: newItem._id,
      action: 'create',
      details: 'Item created',
      newValues: itemData,
      performedBy: user._id
    });

    return {
      success: true,
      item: newItem
    };
  } catch (error) {
    console.error('Error creating TradeMe item:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Update an existing TradeMe item
 * @param {string} itemId - ID of the item to update
 * @param {Object} updateData - Data to update
 * @param {Object} user - User performing the update
 * @returns {Promise<Object>} Result of the update
 */
async function updateItem(itemId, updateData, user) {
  try {
    // Find the item
    const item = await TradeMeItems.findById(itemId);

    if (!item) {
      return {
        success: false,
        error: 'Item not found'
      };
    }

    // Check if we're updating status
    const isStatusChange = updateData.status && updateData.status !== item.status;
    const isSoldStatusChange = updateData.soldStatus && updateData.soldStatus !== item.soldStatus;

    // Store previous values for audit
    const previousValues = {
      title: item.title,
      description: item.description,
      currentPrice: item.currentPrice,
      buyNowPrice: item.buyNowPrice,
      reservePrice: item.reservePrice,
      status: item.status,
      soldStatus: item.soldStatus,
      // Add other fields as needed
    };

    // Update the item
    Object.assign(item, updateData);

    // If updating sold status, set the updatedBy and updatedAt
    if (isSoldStatusChange) {
      item.soldStatusUpdatedBy = user._id;
      item.soldStatusUpdatedAt = new Date();
    }

    // Save the item
    await item.save();

    // Create appropriate audit entries
    if (isStatusChange) {
      await createAuditEntry({
        itemId: item._id,
        trademeListingId: item.currentListingId,
        action: 'status_change',
        details: `Status changed from ${previousValues.status} to ${item.status}`,
        previousValues: { status: previousValues.status },
        newValues: { status: item.status },
        performedBy: user._id
      });
    }

    if (isSoldStatusChange) {
      await createAuditEntry({
        itemId: item._id,
        trademeListingId: item.currentListingId,
        action: 'sold_status_change',
        details: `Sold status changed from ${previousValues.soldStatus || 'none'} to ${item.soldStatus || 'none'}`,
        previousValues: { soldStatus: previousValues.soldStatus },
        newValues: { soldStatus: item.soldStatus },
        performedBy: user._id
      });
    }

    // General update audit
    await createAuditEntry({
      itemId: item._id,
      trademeListingId: item.currentListingId,
      action: 'update',
      details: 'Item updated',
      previousValues,
      newValues: updateData,
      performedBy: user._id
    });

    return {
      success: true,
      item
    };
  } catch (error) {
    console.error('Error updating TradeMe item:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Get TradeMe items with filtering and pagination
 * @param {Object} options - Query options
 * @returns {Promise<Object>} Result with items and pagination info
 */
async function getItems(options = {}) {
  try {
    // Log the start of the operation
    console.log('TradeMe Items Service - Getting items with filters');

    const {
      status,
      soldStatus,
      search = '',
      page = 1,
      limit = 20,
      sort = 'createdAt',
      sortDirection = 'desc',
      buyer,
      isFaulty,
      days,
      locationId
    } = options;

    // Build query
    const query = {};

    // Filter by status
    if (status) {
      query.status = status;
    }

    // Filter by sold status
    if (soldStatus) {
      query.soldStatus = soldStatus;
    }

    // Filter by buyer
    if (buyer) {
      // Handle the case where buyer might be a string like 'Imported'
      if (buyer === 'Imported') {
        query.buyer = 'Imported';
      } else {
        try {
          // Try to convert to ObjectId
          query.buyer = mongoose.Types.ObjectId(buyer);
        } catch (err) {
          // If conversion fails, use the original value
          query.buyer = buyer;
        }
      }
    }

    // Filter by faulty flag
    if (isFaulty !== undefined) {
      query.isFaulty = isFaulty;
    }

    // Filter by location
    if (locationId) {
      query.locationId = locationId;
    }

    // Filter by date range
    if (days) {
      const daysAgo = new Date();
      daysAgo.setDate(daysAgo.getDate() - parseInt(days));
      query.createdAt = { $gte: daysAgo };
    }

    // Search in title and description
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { stockCode: { $regex: search, $options: 'i' } },
        { currentListingId: { $regex: search, $options: 'i' } }
      ];
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Determine sort field and direction
    const sortOptions = {};
    sortOptions[sort] = sortDirection === 'asc' ? 1 : -1;

    // Build the query
    let itemsQuery;
    try {
      // Create a modified query that handles 'Imported' string values
      const modifiedQuery = { ...query };

      // Handle fields that might contain 'Imported' string
      const userReferenceFields = ['createdBy', 'buyer', 'soldStatusUpdatedBy', 'importedBy', 'removedBy'];

      userReferenceFields.forEach(field => {
        if (modifiedQuery[field]) {
          // If the field is 'Imported', use $in to match either the string or an ObjectId
          if (modifiedQuery[field] === 'Imported') {
            modifiedQuery[field] = { $in: ['Imported'] };
          }
        }
      });

      itemsQuery = TradeMeItems.find(modifiedQuery)
        .sort(sortOptions)
        .skip(skip)
        .limit(limit)
        .lean(); // Use lean to get plain JavaScript objects
    } catch (queryError) {
      console.error('TradeMe Items Service - Error building query:', queryError);
      throw queryError;
    }

    // Execute query without population first
    const rawItems = await itemsQuery.lean();

    // Process items to replace 'Imported' strings with null before population
    // This prevents the ObjectId casting error
    for (const item of rawItems) {
      // Process main fields
      const userReferenceFields = ['createdBy', 'buyer', 'soldStatusUpdatedBy', 'importedBy', 'removedBy'];
      userReferenceFields.forEach(field => {
        if (item[field] === 'Imported') {
          // Set to null temporarily to avoid population errors
          item[field] = null;
        }
      });

      // Process listingHistory.createdBy
      if (item.listingHistory && item.listingHistory.length > 0) {
        item.listingHistory.forEach(listing => {
          if (listing.createdBy === 'Imported') {
            listing.createdBy = null;
          }
        });
      }

      // Process questions.answeredBy
      if (item.questions && item.questions.length > 0) {
        item.questions.forEach(question => {
          if (question.answeredBy === 'Imported') {
            question.answeredBy = null;
          }
        });
      }
    }

    // Now populate the fields that are not 'Imported'

    // Create a new array to store the populated items
    const items = [];

    // Populate each item individually
    for (const item of rawItems) {
      // Populate locationId
      if (item.locationId) {
        try {
          const location = await Location.findById(item.locationId).lean();
          if (location) {
            item.locationId = location;
          }
        } catch (err) {
          // Silently handle location population errors
        }
      }

      // Populate user references that are not null (were not 'Imported')
      const userReferenceFields = ['createdBy', 'buyer', 'soldStatusUpdatedBy', 'importedBy', 'removedBy'];
      for (const field of userReferenceFields) {
        if (item[field] && item[field] !== null) {
          try {
            const user = await User.findById(item[field]).select('username fullName').lean();
            if (user) {
              item[field] = user;
            }
          } catch (err) {
            // Silently handle user population errors
          }
        }
      }

      items.push(item);
    }

    // Post-process items to handle null values that were originally 'Imported'
    items.forEach(item => {
      // Convert null values back to 'Imported' objects
      const userReferenceFields = ['createdBy', 'buyer', 'soldStatusUpdatedBy', 'importedBy', 'removedBy'];

      userReferenceFields.forEach(field => {
        // If the field is null, it was originally 'Imported'
        if (item[field] === null) {
          item[field] = { username: 'Imported', fullName: 'Imported' };
        }
      });

      // Handle questions.answeredBy
      if (item.questions && item.questions.length > 0) {
        item.questions.forEach(question => {
          if (question.answeredBy === null) {
            question.answeredBy = { username: 'Imported', fullName: 'Imported' };
          }
        });
      }

      // Handle listingHistory.createdBy
      if (item.listingHistory && item.listingHistory.length > 0) {
        item.listingHistory.forEach(listing => {
          if (listing.createdBy === null) {
            listing.createdBy = { username: 'Imported', fullName: 'Imported' };
          }
        });
      }
    });

    // Get total count for pagination
    // Create a modified query that handles 'Imported' string values
    const countQuery = { ...query };

    // Handle fields that might contain 'Imported' string
    const userReferenceFields = ['createdBy', 'buyer', 'soldStatusUpdatedBy', 'importedBy', 'removedBy'];

    userReferenceFields.forEach(field => {
      if (countQuery[field]) {
        // If the field is 'Imported', use $in to match either the string or an ObjectId
        if (countQuery[field] === 'Imported') {
          countQuery[field] = { $in: ['Imported'] };
        }
      }
    });

    let total = 0;
    try {
      total = await TradeMeItems.countDocuments(countQuery);
    } catch (countError) {
      console.error('TradeMe Items Service - Error in countDocuments:', countError);
      // Set total to 0 if count fails
      total = 0;
    }

    return {
      success: true,
      items,
      pagination: {
        total,
        page: parseInt(page),
        pages: Math.ceil(total / limit),
        limit: parseInt(limit)
      }
    };
  } catch (error) {
    console.error('Error getting TradeMe items:', error.message);

    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Get a single TradeMe item by ID
 * @param {string} itemId - ID of the item to retrieve
 * @returns {Promise<Object>} Result with the item
 */
async function getItemById(itemId) {
  try {
    // Create the query
    const itemQuery = TradeMeItems.findById(itemId)
      .lean(); // Use lean to get plain JavaScript objects

    // Execute the query without population first
    const rawItem = await itemQuery;

    if (!rawItem) {
      return {
        success: false,
        error: 'Item not found'
      };
    }

    // Process item to replace 'Imported' strings with null before population
    // This prevents the ObjectId casting error
    const userReferenceFields = ['createdBy', 'buyer', 'soldStatusUpdatedBy', 'importedBy', 'removedBy'];
    userReferenceFields.forEach(field => {
      if (rawItem[field] === 'Imported') {
        // Set to null temporarily to avoid population errors
        rawItem[field] = null;
      }
    });

    // Process listingHistory.createdBy
    if (rawItem.listingHistory && rawItem.listingHistory.length > 0) {
      rawItem.listingHistory.forEach(listing => {
        if (listing.createdBy === 'Imported') {
          listing.createdBy = null;
        }
      });
    }

    // Process questions.answeredBy
    if (rawItem.questions && rawItem.questions.length > 0) {
      rawItem.questions.forEach(question => {
        if (question.answeredBy === 'Imported') {
          question.answeredBy = null;
        }
      });
    }

    // Populate locationId
    if (rawItem.locationId) {
      try {
        const location = await Location.findById(rawItem.locationId).lean();
        if (location) {
          rawItem.locationId = location;
        }
      } catch (err) {
        console.log(`Error populating locationId for item ${rawItem._id}:`, err.message);
      }
    }

    // Populate user references that are not null (were not 'Imported')
    for (const field of userReferenceFields) {
      if (rawItem[field] && rawItem[field] !== null) {
        try {
          const user = await User.findById(rawItem[field]).select('username fullName').lean();
          if (user) {
            rawItem[field] = user;
          }
        } catch (err) {
          console.log(`Error populating ${field} for item ${rawItem._id}:`, err.message);
        }
      }
    }

    const item = rawItem;

    // Post-process item to handle null values that were originally 'Imported'
    if (item) {
      // Convert null values back to 'Imported' objects
      const userReferenceFields = ['createdBy', 'buyer', 'soldStatusUpdatedBy', 'importedBy', 'removedBy'];

      userReferenceFields.forEach(field => {
        // If the field is null, it was originally 'Imported'
        if (item[field] === null) {
          item[field] = { username: 'Imported', fullName: 'Imported' };
        }
      });

      // Handle questions.answeredBy
      if (item.questions && item.questions.length > 0) {
        item.questions.forEach(question => {
          if (question.answeredBy === null) {
            question.answeredBy = { username: 'Imported', fullName: 'Imported' };
          }
        });
      }

      // Handle listingHistory.createdBy
      if (item.listingHistory && item.listingHistory.length > 0) {
        item.listingHistory.forEach(listing => {
          if (listing.createdBy === null) {
            listing.createdBy = { username: 'Imported', fullName: 'Imported' };
          }
        });
      }
    }

    if (!item) {
      return {
        success: false,
        error: 'Item not found'
      };
    }

    return {
      success: true,
      item
    };
  } catch (error) {
    console.error('Error getting TradeMe item:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Get a single TradeMe item by TradeMe listing ID
 * @param {string} listingId - TradeMe listing ID
 * @returns {Promise<Object>} Result with the item
 */
async function getItemByListingId(listingId) {
  try {
    // Create the query
    const itemQuery = TradeMeItems.findOne({ currentListingId: listingId })
      .lean(); // Use lean to get plain JavaScript objects

    // Execute the query without population first
    const rawItem = await itemQuery;

    if (!rawItem) {
      return {
        success: false,
        error: 'Item not found'
      };
    }

    // Process item to replace 'Imported' strings with null before population
    // This prevents the ObjectId casting error
    const userReferenceFields = ['createdBy', 'buyer', 'soldStatusUpdatedBy', 'importedBy', 'removedBy'];
    userReferenceFields.forEach(field => {
      if (rawItem[field] === 'Imported') {
        // Set to null temporarily to avoid population errors
        rawItem[field] = null;
      }
    });

    // Process listingHistory.createdBy
    if (rawItem.listingHistory && rawItem.listingHistory.length > 0) {
      rawItem.listingHistory.forEach(listing => {
        if (listing.createdBy === 'Imported') {
          listing.createdBy = null;
        }
      });
    }

    // Process questions.answeredBy
    if (rawItem.questions && rawItem.questions.length > 0) {
      rawItem.questions.forEach(question => {
        if (question.answeredBy === 'Imported') {
          question.answeredBy = null;
        }
      });
    }

    // Populate locationId
    if (rawItem.locationId) {
      try {
        const location = await Location.findById(rawItem.locationId).lean();
        if (location) {
          rawItem.locationId = location;
        }
      } catch (err) {
        // Silently handle location population errors
      }
    }

    // Populate user references that are not null (were not 'Imported')
    for (const field of userReferenceFields) {
      if (rawItem[field] && rawItem[field] !== null) {
        try {
          const user = await User.findById(rawItem[field]).select('username fullName').lean();
          if (user) {
            rawItem[field] = user;
          }
        } catch (err) {
          // Silently handle user population errors
        }
      }
    }

    const item = rawItem;

    // Post-process item to handle null values that were originally 'Imported'
    if (item) {
      // Convert null values back to 'Imported' objects
      const userReferenceFields = ['createdBy', 'buyer', 'soldStatusUpdatedBy', 'importedBy', 'removedBy'];

      userReferenceFields.forEach(field => {
        // If the field is null, it was originally 'Imported'
        if (item[field] === null) {
          item[field] = { username: 'Imported', fullName: 'Imported' };
        }
      });

      // Handle questions.answeredBy
      if (item.questions && item.questions.length > 0) {
        item.questions.forEach(question => {
          if (question.answeredBy === null) {
            question.answeredBy = { username: 'Imported', fullName: 'Imported' };
          }
        });
      }

      // Handle listingHistory.createdBy
      if (item.listingHistory && item.listingHistory.length > 0) {
        item.listingHistory.forEach(listing => {
          if (listing.createdBy === null) {
            listing.createdBy = { username: 'Imported', fullName: 'Imported' };
          }
        });
      }
    }

    if (!item) {
      return {
        success: false,
        error: 'Item not found'
      };
    }

    return {
      success: true,
      item
    };
  } catch (error) {
    console.error('Error getting TradeMe item by listing ID:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * List an item on TradeMe
 * @param {string} itemId - ID of the item to list
 * @param {Object} user - User performing the listing
 * @returns {Promise<Object>} Result of the listing operation
 */
async function listItemOnTradeMe(itemId, user) {
  try {
    // Find the item
    const item = await TradeMeItems.findById(itemId);

    if (!item) {
      return {
        success: false,
        error: 'Item not found'
      };
    }

    // Check if item is in draft or queued status
    if (item.status !== 'draft' && item.status !== 'queued') {
      return {
        success: false,
        error: `Cannot list item with status ${item.status}`
      };
    }

    // Prepare listing data for TradeMe API
    const listingData = {
      title: item.title,
      description: item.description,
      price: item.currentPrice,
      buyNowPrice: item.buyNowPrice,
      reservePrice: item.reservePrice,
      category: item.category,
      locationId: item.locationId,
      duration: item.duration,
      isNew: item.isNew,
      sku: item.stockCode,
      shippingOptions: item.shippingOptions,
      paymentOptions: item.paymentOptions,
      // Add other fields as needed
    };

    // Call TradeMe service to create the listing
    const result = await tradeMeService.createListing(listingData, user);

    if (!result.success) {
      return result;
    }

    // Update the item with the new listing information
    const listingHistoryEntry = {
      trademeListingId: result.listingId,
      startDate: new Date(),
      endDate: new Date(result.endDate),
      startPrice: item.currentPrice,
      buyNowPrice: item.buyNowPrice,
      reservePrice: item.reservePrice,
      status: 'active',
      createdBy: user._id
    };

    // Add to listing history
    item.listingHistory.push(listingHistoryEntry);

    // Update item status and current listing ID
    item.status = 'active';
    item.currentListingId = result.listingId;

    // Save the item
    await item.save();

    // Create audit entry
    await createAuditEntry({
      itemId: item._id,
      trademeListingId: result.listingId,
      action: 'list',
      details: `Item listed on TradeMe with ID ${result.listingId}`,
      newValues: {
        trademeListingId: result.listingId,
        status: 'active',
        endDate: result.endDate
      },
      performedBy: user._id
    });

    return {
      success: true,
      item,
      listingId: result.listingId,
      message: 'Item successfully listed on TradeMe'
    };
  } catch (error) {
    console.error('Error listing item on TradeMe:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Relist an item on TradeMe
 * @param {string} itemId - ID of the item to relist
 * @param {Object} user - User performing the relisting
 * @param {Object} options - Relisting options
 * @returns {Promise<Object>} Result of the relisting operation
 */
async function relistItemOnTradeMe(itemId, user, options = {}) {
  try {
    // Find the item
    const item = await TradeMeItems.findById(itemId);

    if (!item) {
      return {
        success: false,
        error: 'Item not found'
      };
    }

    // Check if item can be relisted
    if (item.status !== 'archived' && item.status !== 'sold') {
      return {
        success: false,
        error: `Cannot relist item with status ${item.status}`
      };
    }

    // Calculate new prices based on relist settings
    const newPrices = item.calculateNextListingPrice();

    // Override with options if provided
    if (options.currentPrice) newPrices.currentPrice = options.currentPrice;
    if (options.buyNowPrice) newPrices.buyNowPrice = options.buyNowPrice;
    if (options.reservePrice) newPrices.reservePrice = options.reservePrice;

    // Update item with new prices
    item.currentPrice = newPrices.currentPrice;
    item.buyNowPrice = newPrices.buyNowPrice;
    item.reservePrice = newPrices.reservePrice;

    // Prepare listing data for TradeMe API
    const listingData = {
      title: item.title,
      description: item.description,
      price: item.currentPrice,
      buyNowPrice: item.buyNowPrice,
      reservePrice: item.reservePrice,
      category: item.category,
      locationId: item.locationId,
      duration: item.duration,
      isNew: item.isNew,
      sku: item.stockCode,
      shippingOptions: item.shippingOptions,
      paymentOptions: item.paymentOptions,
      // Add other fields as needed
    };

    // Call TradeMe service to create the listing
    const result = await tradeMeService.createListing(listingData, user);

    if (!result.success) {
      return result;
    }

    // Update the item with the new listing information
    const listingHistoryEntry = {
      trademeListingId: result.listingId,
      startDate: new Date(),
      endDate: new Date(result.endDate),
      startPrice: item.currentPrice,
      buyNowPrice: item.buyNowPrice,
      reservePrice: item.reservePrice,
      status: 'active',
      createdBy: user._id
    };

    // Add to listing history
    item.listingHistory.push(listingHistoryEntry);

    // Update item status and current listing ID
    item.status = 'active';
    item.currentListingId = result.listingId;

    // Save the item
    await item.save();

    // Create audit entry
    await createAuditEntry({
      itemId: item._id,
      trademeListingId: result.listingId,
      action: 'relist',
      details: `Item relisted on TradeMe with ID ${result.listingId}`,
      previousValues: {
        status: 'archived',
        currentPrice: item.currentPrice,
        buyNowPrice: item.buyNowPrice,
        reservePrice: item.reservePrice
      },
      newValues: {
        trademeListingId: result.listingId,
        status: 'active',
        endDate: result.endDate,
        currentPrice: newPrices.currentPrice,
        buyNowPrice: newPrices.buyNowPrice,
        reservePrice: newPrices.reservePrice
      },
      performedBy: user._id
    });

    return {
      success: true,
      item,
      listingId: result.listingId,
      message: 'Item successfully relisted on TradeMe'
    };
  } catch (error) {
    console.error('Error relisting item on TradeMe:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Archive an item
 * @param {string} itemId - ID of the item to archive
 * @param {Object} user - User performing the archiving
 * @returns {Promise<Object>} Result of the archive operation
 */
async function archiveItem(itemId, user) {
  try {
    // Find the item
    const item = await TradeMeItems.findById(itemId);

    if (!item) {
      return {
        success: false,
        error: 'Item not found'
      };
    }

    // Check if item can be archived
    if (item.status !== 'sold') {
      return {
        success: false,
        error: `Cannot archive item with status ${item.status}`
      };
    }

    // Update item status
    const previousStatus = item.status;
    item.status = 'archived';

    // Save the item
    await item.save();

    // Create audit entry
    await createAuditEntry({
      itemId: item._id,
      trademeListingId: item.currentListingId,
      action: 'archive',
      details: 'Item archived',
      previousValues: { status: previousStatus },
      newValues: { status: 'archived' },
      performedBy: user._id
    });

    return {
      success: true,
      item,
      message: 'Item successfully archived'
    };
  } catch (error) {
    console.error('Error archiving TradeMe item:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Create an audit entry for a TradeMe item
 * @param {Object} auditData - Audit data
 * @returns {Promise<Object>} Created audit entry
 */
async function createAuditEntry(auditData) {
  try {
    const audit = new TradeMeItemAudit(auditData);
    return await audit.save();
  } catch (error) {
    console.error('Error creating TradeMe item audit entry:', error);
    throw error;
  }
}

/**
 * Get audit history for a TradeMe item
 * @param {string} itemId - ID of the item
 * @param {Object} options - Query options
 * @returns {Promise<Object>} Result with audit entries
 */
async function getItemAuditHistory(itemId, options = {}) {
  try {
    const {
      page = 1,
      limit = 20,
      action
    } = options;

    // Build query
    const query = { itemId };

    // Filter by action if provided
    if (action) {
      query.action = action;
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Create the query
    const auditQuery = TradeMeItemAudit.find(query)
      .sort({ timestamp: -1 })
      .skip(skip)
      .limit(limit)
      .lean(); // Use lean to get plain JavaScript objects

    // Execute query with population
    const auditEntries = await auditQuery
      .populate({
        path: 'performedBy',
        select: 'username fullName',
        // Only populate if performedBy is a valid ObjectId (not a string)
        match: { _id: { $type: 'objectId' } }
      });

    // Post-process audit entries to handle string values
    auditEntries.forEach(entry => {
      if (entry.performedBy === 'Imported') {
        entry.performedBy = { username: 'Imported', fullName: 'Imported' };
      }
    });

    // Get total count for pagination
    // Create a modified query that handles 'Imported' string values
    const countQuery = { ...query };

    // Handle fields that might contain 'Imported' string
    if (countQuery.performedBy === 'Imported') {
      countQuery.performedBy = { $in: ['Imported'] };
    }

    const total = await TradeMeItemAudit.countDocuments(countQuery);

    return {
      success: true,
      auditEntries,
      pagination: {
        total,
        page: parseInt(page),
        pages: Math.ceil(total / limit),
        limit: parseInt(limit)
      }
    };
  } catch (error) {
    console.error('Error getting TradeMe item audit history:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Add a note to a TradeMe item
 * @param {string} itemId - ID of the item
 * @param {string} noteText - Text of the note
 * @param {Object} user - User adding the note
 * @returns {Promise<Object>} Result of the operation
 */
async function addNote(itemId, noteText, user) {
  try {
    // Find the item
    const item = await TradeMeItems.findById(itemId);

    if (!item) {
      return {
        success: false,
        error: 'Item not found'
      };
    }

    // Add the note
    item.notes.push({
      text: noteText,
      createdBy: user._id
    });

    // Save the item
    await item.save();

    // Create audit entry
    await createAuditEntry({
      itemId: item._id,
      trademeListingId: item.currentListingId,
      action: 'comment_added',
      details: 'Note added to item',
      newValues: { note: noteText },
      performedBy: user._id
    });

    return {
      success: true,
      item
    };
  } catch (error) {
    console.error('Error adding note to TradeMe item:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Get notes for a TradeMe item
 * @param {string} itemId - ID of the item
 * @returns {Promise<Object>} Result with notes
 */
async function getNotes(itemId) {
  try {
    // Create the query
    const itemQuery = TradeMeItems.findById(itemId)
      .lean(); // Use lean to get plain JavaScript objects

    // Execute the query with population
    const item = await itemQuery
      .populate({
        path: 'notes.createdBy',
        select: 'username fullName',
        // Only populate if createdBy is a valid ObjectId (not a string)
        match: { _id: { $type: 'objectId' } }
      });

    if (!item) {
      return {
        success: false,
        error: 'Item not found'
      };
    }

    // Post-process notes to handle string values
    if (item.notes && item.notes.length > 0) {
      item.notes.forEach(note => {
        if (note.createdBy === 'Imported') {
          note.createdBy = { username: 'Imported', fullName: 'Imported' };
        }
      });
    }

    return {
      success: true,
      notes: item.notes
    };
  } catch (error) {
    console.error('Error getting notes for TradeMe item:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Delete a note from a TradeMe item
 * @param {string} itemId - ID of the item
 * @param {string} noteId - ID of the note to delete
 * @param {Object} user - User deleting the note
 * @returns {Promise<Object>} Result of the operation
 */
async function deleteNote(itemId, noteId, user) {
  try {
    // Find the item
    const item = await TradeMeItems.findById(itemId);

    if (!item) {
      return {
        success: false,
        error: 'Item not found'
      };
    }

    // Find the note
    const noteIndex = item.notes.findIndex(note => note._id.toString() === noteId);

    if (noteIndex === -1) {
      return {
        success: false,
        error: 'Note not found'
      };
    }

    // Check if user is authorized to delete the note
    // Only the creator of the note or an admin/manager can delete it
    const note = item.notes[noteIndex];
    if (note.createdBy.toString() !== user._id.toString() &&
      user.role !== 'admin' && user.role !== 'manager') {
      return {
        success: false,
        error: 'You are not authorized to delete this note'
      };
    }

    // Remove the note
    item.notes.splice(noteIndex, 1);

    // Save the item
    await item.save();

    // Create audit entry
    await createAuditEntry({
      itemId: item._id,
      trademeListingId: item.currentListingId,
      action: 'comment_removed',
      details: 'Note removed from item',
      previousValues: { note: note.text },
      performedBy: user._id
    });

    return {
      success: true,
      item
    };
  } catch (error) {
    console.error('Error deleting note from TradeMe item:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

module.exports = {
  createItem,
  updateItem,
  getItems,
  getItemById,
  getItemByListingId,
  listItemOnTradeMe,
  relistItemOnTradeMe,
  archiveItem,
  createAuditEntry,
  getItemAuditHistory,
  addNote,
  getNotes,
  deleteNote
};