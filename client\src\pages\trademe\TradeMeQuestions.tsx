import { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { getItems, answerQuestion, TradeMeItem } from '@/api/tradeMeItems';
import { getTemplates } from '@/api/tradeMeTemplates';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/useToast';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue, SelectGroup,SelectLabel } from "@/components/ui/select";
import { MessageSquare, Search, Send, Clock, ExternalLink, ChevronRight, HelpCircle, User, ShoppingBag, Loader2, RefreshCw } from 'lucide-react';
// import { InfiniteScroll } from '@/components/InfiniteScroll';

export function TradeMeQuestions() {
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  // No longer need syncing state as it's handled by background service
  // Define a type for questions from TradeMeItem
  type Question = {
    _id: string;
    trademeQuestionId: string;
    question: string;
    answer?: string;
    askerName: string;
    askDate: string;
    answerDate?: string;
    isAnswered: boolean;
    answeredBy?: {
      _id: string;
      username: string;
      fullName?: string;
    };
    originalListingId: string;
  };

  // State for questions
  const [questions, setQuestions] = useState<Question[]>([]);
  const [templates, setTemplates] = useState<any[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredQuestions, setFilteredQuestions] = useState<Question[]>([]);
  // const [currentPage, setCurrentPage] = useState(1);
  // const [hasMore, setHasMore] = useState(true);
  const [expandedQuestions, setExpandedQuestions] = useState<Record<string, boolean>>({});
  const [selectedTemplates, setSelectedTemplates] = useState<Record<string, string>>({});
  const [answerTexts, setAnswerTexts] = useState<Record<string, string>>({});
  const [submitting, setSubmitting] = useState<Record<string, boolean>>({});

  // Fetch data on component mount
  useEffect(() => {
    // Fetch questions from the database (no sync needed as it's handled by background service)
    fetchQuestions();
    fetchTemplates();
  }, []);

  // Filter questions based on search term
  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredQuestions(questions);
    } else {
      const lowercaseSearch = searchTerm.toLowerCase();
      setFilteredQuestions(
        questions.filter(
          (q) =>
            q.question?.toLowerCase().includes(lowercaseSearch) ||
            q.askerName?.toLowerCase().includes(lowercaseSearch)
        )
      );
    }
  }, [searchTerm, questions]);

  const fetchQuestions = async () => {
    try {
      setLoading(true);

      console.log('Fetching questions from database');
      // Get items with unanswered questions
      const response = await getItems({ filter: 'unanswered-questions' });

      if (response.success) {
        // Extract all unanswered questions from the items
        const allQuestions: Question[] = [];

        response.items.forEach((item: TradeMeItem) => {
          if (item.questions && item.questions.length > 0) {
            const unansweredQuestions = item.questions.filter(q => !q.isAnswered);
            if (unansweredQuestions.length > 0) {
              allQuestions.push(...unansweredQuestions);
            }
          }
        });

        console.log('Questions fetched successfully:', allQuestions.length);
        setQuestions(allQuestions);
      } else {
        console.error('Failed to load questions:', response);
        toast({
          title: 'Error',
          description: response.error || 'Failed to load questions',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Failed to fetch questions:', error);
      toast({
        title: 'Error',
        description: (error as Error).message || 'Failed to load questions',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Refresh questions from the database
  const refreshQuestions = () => {
    fetchQuestions();
  };

  const fetchTemplates = async () => {
    try {
      const response = await getTemplates('question');
      if (response.success) {
        setTemplates(response.templates || []);
      }
    } catch (error) {
      console.error('Failed to fetch templates:', error);
      toast({
        title: 'Error',
        description: (error as Error).message || 'Failed to load templates',
        variant: 'destructive',
      });
    }
  };

  // const loadMoreQuestions = () => {
  //   if (!loading && hasMore) {
  //     fetchQuestions(currentPage + 1);
  //   }
  // };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-NZ', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleTemplateChange = (questionId: string, templateId: string) => {
    setSelectedTemplates({
      ...selectedTemplates,
      [questionId]: templateId
    });

    if (templateId === "custom") {
      // Don't change the answer for custom selection
      return;
    }

    if (templateId) {
      const template = templates.find(t => t._id === templateId);
      if (template) {
        setAnswerTexts({
          ...answerTexts,
          [questionId]: template.content
        });
      }
    }
  };

  const handleAnswerChange = (questionId: string, text: string) => {
    setAnswerTexts({
      ...answerTexts,
      [questionId]: text
    });

    // Set template selection to "custom" if user modifies the answer
    setSelectedTemplates(prev => ({ ...prev, [questionId]: "custom" }));
  };

  const handleSubmitAnswer = async (questionId: string) => {
    const answer = answerTexts[questionId];

    if (!answer || !answer.trim()) {
      toast({
        title: 'Error',
        description: 'Please enter an answer',
        variant: 'destructive',
      });
      return;
    }

    try {
      setSubmitting({ ...submitting, [questionId]: true });

      // Find the question to get the item ID
      const question = questions.find(q => q._id === questionId);
      if (!question) {
        throw new Error('Question not found');
      }

      // Get the item ID from the question
      const itemId = question.originalListingId;

      // Answer the question using the new API
      const response = await answerQuestion(itemId, questionId, answer);

      if (response.success) {
        toast({
          title: 'Success',
          description: 'Answer submitted successfully',
        });

        console.log('Answer submitted successfully:', response);

        // Refresh the questions to get the updated data
        fetchQuestions();

        // Close the expanded question
        setExpandedQuestions({
          ...expandedQuestions,
          [questionId]: false
        });

        // Clear answer text
        const newAnswerTexts = { ...answerTexts };
        delete newAnswerTexts[questionId];
        setAnswerTexts(newAnswerTexts);

        // Clear selected template
        const newSelectedTemplates = { ...selectedTemplates };
        delete newSelectedTemplates[questionId];
        setSelectedTemplates(newSelectedTemplates);
      } else {
        toast({
          title: 'Error',
          description: response.error || 'Failed to submit answer',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error(`Error submitting answer for question ${questionId}:`, error);
      toast({
        title: 'Error',
        description: (error as Error).message || 'Failed to submit answer',
        variant: 'destructive',
      });
    } finally {
      setSubmitting({ ...submitting, [questionId]: false });
    }
  };

  const toggleQuestion = (questionId: string) => {
    setExpandedQuestions({
      ...expandedQuestions,
      [questionId]: !expandedQuestions[questionId]
    });
  };

  // Group templates by category
  const groupedTemplates = templates.reduce((acc, template: any) => {
    const category = template.category || 'Uncategorized';
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(template);
    return acc;
  }, {} as Record<string, any[]>);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold flex items-center">
          <HelpCircle className="mr-2 h-6 w-6" />
          Unanswered Questions
        </h1>

        <div className="flex gap-4 items-center">
          <div className="relative w-64">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              className="pl-8"
              placeholder="Search questions..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          {/* Removed duplicate button */}
          <Button
            variant="default"
            onClick={refreshQuestions}
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
        </div>
      </div>

      {loading && questions.length === 0 ? (
        <div className="flex justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto"></div>
            <p className="mt-4 text-lg">Loading questions...</p>
          </div>
        </div>
      ) : filteredQuestions.length === 0 ? (
        <Card>
          <CardContent className="py-12">
            <div className="text-center">
              <MessageSquare className="h-12 w-12 mx-auto text-muted-foreground" />
              <h3 className="mt-4 text-lg font-medium">No questions found</h3>
              <p className="text-muted-foreground mt-2">
                {searchTerm ? 'Try a different search term' : 'All questions have been answered!'}
              </p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {filteredQuestions.map((question) => (
            <Card key={question._id} className="overflow-hidden">
              <div
                className={`p-4 ${expandedQuestions[question._id] ? 'bg-muted/50' : ''}`}
                onClick={() => toggleQuestion(question._id)}
              >
                <div className="flex justify-between cursor-pointer">
                  <div className="flex-1">
                    <div className="flex items-center">
                      <div className="mr-2">
                        <Badge variant="outline" className="bg-amber-500/10 text-amber-500 hover:bg-amber-500/20 hover:text-amber-600">
                          Question
                        </Badge>
                      </div>
                      <h3 className="font-medium line-clamp-1">{question.listingTitle}</h3>
                    </div>

                    <div className="text-sm text-muted-foreground mt-1 line-clamp-1">
                      {question.question}
                    </div>
                  </div>

                  <div className="flex items-center gap-2 shrink-0 ml-2">
                    <div className="text-sm text-muted-foreground flex items-center">
                      <Clock className="h-3.5 w-3.5 mr-1" />
                      {formatDate(question.askDate)}
                    </div>
                    <ChevronRight className={`h-5 w-5 transition-transform ${expandedQuestions[question._id] ? 'rotate-90' : ''}`} />
                  </div>
                </div>
              </div>

              {expandedQuestions[question._id] && (
                <div className="border-t">
                  <CardContent className="p-4 space-y-4">
                    <div className="flex gap-4">
                      {question.listingImage ? (
                        <Link
                          to={`/trademe/listing/${question.listingId}`}
                          className="w-24 h-24 shrink-0 bg-muted rounded overflow-hidden"
                        >
                          <img
                            src={question.listingImage}
                            alt={question.listingTitle}
                            className="w-full h-full object-cover"
                          />
                        </Link>
                      ) : (
                        <div className="w-24 h-24 shrink-0 bg-muted rounded flex items-center justify-center">
                          <ShoppingBag className="h-8 w-8 text-muted-foreground" />
                        </div>
                      )}

                      <div className="flex-1">
                        <Link
                          to={`/trademe/listing/${question.listingId}`}
                          className="font-medium hover:underline"
                        >
                          {question.listingTitle}
                        </Link>

                        <div className="mt-1 flex flex-wrap gap-x-3 gap-y-1 text-sm">
                          <span className="text-muted-foreground flex items-center">
                            <User className="h-3 w-3 mr-1" />
                            <span className="font-medium text-foreground">{question.askerName}</span>
                          </span>

                          <span className="text-muted-foreground">
                            Asked: <span className="font-medium text-foreground">{formatDate(question.askDate)}</span>
                          </span>
                        </div>

                        <div className="mt-3 p-3 bg-muted rounded-md">
                          <p className="font-medium">Question:</p>
                          <p className="mt-1">{question.question}</p>
                        </div>
                      </div>
                    </div>

                    <Separator />

                    <div>
                      <div className="flex justify-between items-center mb-2">
                        <p className="font-medium">Your Answer:</p>
                        <div className="flex items-center">
                          <Select
                            value={selectedTemplates[question._id] || ''}
                            onValueChange={(value) => handleTemplateChange(question._id, value)}
                          >
                            <SelectTrigger className="w-[250px]">
                              <SelectValue placeholder="Use template" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="custom">Custom Answer</SelectItem>
                              {Object.entries(groupedTemplates).map(([category, items]) => (
                                <SelectGroup key={category}>
                                  <SelectLabel>{category}</SelectLabel>
                                  {(items as any[]).map((template: any) => (
                                    <SelectItem key={template._id} value={template._id}>
                                      {template.name || template.title}
                                    </SelectItem>
                                  ))}
                                </SelectGroup>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <Textarea
                        placeholder="Type your answer here..."
                        value={answerTexts[question._id] || ''}
                        onChange={(e) => handleAnswerChange(question._id, e.target.value)}
                        className="min-h-[120px]"
                      />

                      <div className="flex justify-between mt-3">
                        <Button
                          variant="outline"
                          size="sm"
                          asChild
                        >
                          <Link to={`/trademe/listing/${question.listingId}`}>
                            <ExternalLink className="mr-2 h-4 w-4" />
                            View Listing
                          </Link>
                        </Button>

                        <Button
                          onClick={() => handleSubmitAnswer(question._id)}
                          disabled={submitting[question._id] || !answerTexts[question._id]?.trim()}
                        >
                          {submitting[question._id] ? (
                            <div className="flex items-center">
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                              Submitting...
                            </div>
                          ) : (
                            <div className="flex items-center">
                              <Send className="mr-2 h-4 w-4" />
                              Submit Answer
                            </div>
                          )}
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </div>
              )}
            </Card>
          ))}
        </div>
      )}

      {loading && questions.length > 0 && (
        <div className="flex justify-center py-4">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
        </div>
      )}
    </div>
  );
}