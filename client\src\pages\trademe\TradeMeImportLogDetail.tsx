import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, AlertCircle, ArrowLeft, AlertTriangle, Info } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { getImportLog } from '@/api/tradeMeImport';
import { formatDate } from '@/lib/utils';

const TradeMeImportLogDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { user } = useAuth();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [importLog, setImportLog] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  // Check if user has permission to access this page
  const hasPermission = user?.role === 'admin' || user?.role === 'manager';

  useEffect(() => {
    const fetchImportLog = async () => {
      if (!id) return;

      try {
        setIsLoading(true);
        const result = await getImportLog(id);

        if (result.success) {
          setImportLog(result.log);
        } else {
          setError(result.error || 'Failed to load import log');
        }
      } catch (error: any) {
        console.error('Error loading import log:', error);
        setError(error.message || 'Failed to load import log');
      } finally {
        setIsLoading(false);
      }
    };

    fetchImportLog();
  }, [id]);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline">Pending</Badge>;
      case 'in_progress':
        return <Badge variant="secondary">In Progress</Badge>;
      case 'completed':
        return <Badge variant="success">Completed</Badge>;
      case 'failed':
        return <Badge variant="destructive">Failed</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getLogIcon = (level: string) => {
    switch (level) {
      case 'info':
        return <Info className="h-4 w-4 text-blue-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-amber-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Info className="h-4 w-4 text-blue-500" />;
    }
  };

  if (!hasPermission) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Import Log Details</CardTitle>
          <CardDescription>View details of a TradeMe import operation</CardDescription>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Access Denied</AlertTitle>
            <AlertDescription>
              You do not have permission to access this page.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Import Log Details</CardTitle>
          <CardDescription>View details of a TradeMe import operation</CardDescription>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm" onClick={() => navigate('/trademe/import')}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Import
          </Button>
          <Button variant="default" size="sm" onClick={() => navigate('/trademe/import/items')}>
            Manage Imported Items
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        ) : error ? (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        ) : importLog ? (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-muted-foreground">Status</h3>
                <div>{getStatusBadge(importLog.status)}</div>
              </div>
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-muted-foreground">Environment</h3>
                <div>
                  <Badge variant={importLog.environment === 'production' ? 'default' : 'outline'}>
                    {importLog.environment === 'production' ? 'Production' : 'Sandbox'}
                  </Badge>
                </div>
              </div>
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-muted-foreground">Started By</h3>
                <div>{importLog.initiatedBy?.fullName || importLog.initiatedBy?.username || 'Unknown'}</div>
              </div>
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-muted-foreground">Start Time</h3>
                <div>{formatDate(importLog.startTime)}</div>
              </div>
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-muted-foreground">End Time</h3>
                <div>{importLog.endTime ? formatDate(importLog.endTime) : 'Not completed'}</div>
              </div>
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-muted-foreground">Duration</h3>
                <div>{importLog.duration ? `${(importLog.duration / 1000).toFixed(2)} seconds` : 'N/A'}</div>
              </div>
            </div>

            <div className="space-y-2">
              <h3 className="text-sm font-medium">Statistics</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-card border rounded-md p-3">
                  <div className="text-xs text-muted-foreground">Items Processed</div>
                  <div className="text-2xl font-bold">{importLog.itemsProcessed || 0}</div>
                </div>
                <div className="bg-card border rounded-md p-3">
                  <div className="text-xs text-muted-foreground">New Items</div>
                  <div className="text-2xl font-bold">{importLog.newItems || 0}</div>
                </div>
                <div className="bg-card border rounded-md p-3">
                  <div className="text-xs text-muted-foreground">Duplicate Items</div>
                  <div className="text-2xl font-bold">{importLog.duplicateItems || 0}</div>
                </div>
                <div className="bg-card border rounded-md p-3">
                  <div className="text-xs text-muted-foreground">Failed Items</div>
                  <div className="text-2xl font-bold">{importLog.failedItems || 0}</div>
                </div>
              </div>
            </div>

            {importLog.error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>{importLog.error}</AlertDescription>
              </Alert>
            )}

            <div className="space-y-2">
              <h3 className="text-sm font-medium">Log Entries</h3>
              <div className="border rounded-md overflow-hidden">
                <div className="max-h-96 overflow-y-auto">
                  <table className="w-full">
                    <thead className="bg-muted">
                      <tr>
                        <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground">Time</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground">Level</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground">Message</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y">
                      {importLog.logs && importLog.logs.length > 0 ? (
                        importLog.logs.map((log: any, index: number) => (
                          <tr key={index} className="hover:bg-muted/50">
                            <td className="px-4 py-2 text-xs">
                              {formatDate(log.timestamp)}
                            </td>
                            <td className="px-4 py-2">
                              <div className="flex items-center">
                                {getLogIcon(log.level)}
                                <span className="ml-1 text-xs capitalize">{log.level}</span>
                              </div>
                            </td>
                            <td className="px-4 py-2 text-xs">{log.message}</td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td colSpan={3} className="px-4 py-2 text-center text-sm text-muted-foreground">
                            No log entries found
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Not Found</AlertTitle>
            <AlertDescription>Import log not found</AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
};

export default TradeMeImportLogDetail;
