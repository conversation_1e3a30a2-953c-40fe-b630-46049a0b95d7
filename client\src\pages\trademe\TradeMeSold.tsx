import { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { getItems, updateSaleStatus, TradeMeItem as TradeItem, SaleStatus } from '@/api/tradeMeItems';
import { useToast } from '@/hooks/useToast';
import { TradeMeListingsLayout } from '@/components/trademe/TradeMeListingsLayout';
import { TradeMeListingCard } from '@/components/trademe/TradeMeListingCard';

export function TradeMeSold() {
  const [searchParams, setSearchParams] = useSearchParams();
  const { toast } = useToast();

  // State for listings and loading
  const [listings, setListings] = useState<TradeItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [syncing, setSyncing] = useState(false);
  const [totalItems, setTotalItems] = useState(0);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  // State for filters and search
  const [searchTerm, setSearchTerm] = useState('');
  const [filter, setFilter] = useState(searchParams.get('filter') || 'all');
  const [sortBy, setSortBy] = useState(searchParams.get('sort') || 'default');
  const [timeFilter, setTimeFilter] = useState<number | null>(null);
  const [statusFilter, setStatusFilter] = useState<SaleStatus | null>(null);
  const [updatingStatus, setUpdatingStatus] = useState(false);

  // Only fetch on initial load
  useEffect(() => {
    fetchListings(1, true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Fetch listings from the API
  const fetchListings = async (pageToFetch = page, reset = false) => {
    try {
      // Set loading state only if we're resetting the list
      if (reset) {
        setLoading(true);
      }

      // Build query parameters
      const queryParams: any = {
        status: 'sold',
        page: pageToFetch,
        limit: 20,
        sync: false,
        filter: filter !== 'all' ? filter : undefined,
        sort: sortBy !== 'default' ? sortBy : undefined,
        search: searchTerm
      };

      // Add time filter if set
      if (timeFilter) {
        queryParams.days = timeFilter;
      }

      // Add status filter if set
      if (statusFilter) {
        queryParams.soldStatus = statusFilter;
      }

      console.log('Fetching sold listings with params:', queryParams);

      const data = await getItems(queryParams);

      console.log('Received data from API:', data);

      if (reset) {
        setListings(data.items || []);
      } else {
        setListings(prev => [...prev, ...(data.items || [])]);
      }

      setTotalItems(data.pagination?.total || 0);
      setHasMore(data.pagination?.page < data.pagination?.pages);
      setPage(pageToFetch);
    } catch (error: any) {
      console.error('Failed to fetch listings:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to fetch listings',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Load more listings when scrolling
  const loadMore = async () => {
    if (hasMore) {
      await fetchListings(page + 1);
    }
  };

  // Handle search input
  const handleSearch = (term: string) => {
    // Skip if the search term is the same
    if (term === searchTerm) return;

    console.log('Search term changed to:', term);
    setSearchTerm(term);
    setPage(1);
    fetchListings(1, true);
  };

  // Handle filter change
  const handleFilter = (value: string) => {
    console.log('Filter changed to:', value);
    setFilter(value);
    setSearchParams(prev => {
      prev.set('filter', value);
      return prev;
    });
    // Trigger a new fetch when filter changes
    setPage(1);
    fetchListings(1, true);
  };

  // Handle sort change
  const handleSort = (value: string) => {
    console.log('Sort changed to:', value);
    setSortBy(value);
    setSearchParams(prev => {
      prev.set('sort', value);
      return prev;
    });
    // Trigger a new fetch when sort changes
    setPage(1);
    fetchListings(1, true);
  };

  // Handle time filter change
  const handleTimeFilter = (days: number) => {
    console.log('Time filter changed to:', days);
    setTimeFilter(days === 0 ? null : days);
    // Trigger a new fetch when time filter changes
    setPage(1);
    fetchListings(1, true);
  };

  // Handle status filter change
  const handleStatusFilter = (status: SaleStatus | null) => {
    console.log('Status filter changed to:', status);
    setStatusFilter(status);
    // Trigger a new fetch when status filter changes
    setPage(1);
    fetchListings(1, true);
  };

  // Sync with TradeMe
  const handleSync = async () => {
    try {
      setSyncing(true);
      toast({
        title: 'Syncing',
        description: 'Syncing with TradeMe...',
      });

      const data = await getItems({
        status: 'sold',
        page: 1,
        limit: 20,
        sync: true
      });

      console.log('Received sync data from API:', data);

      setListings(data.items || []);
      setTotalItems(data.pagination?.total || 0);
      setPage(1);
      setHasMore(data.pagination?.page < data.pagination?.pages);

      toast({
        title: 'Sync Complete',
        description: `Successfully synced ${data.pagination?.total || 0} sold listings from TradeMe.`,
      });
    } catch (error: any) {
      console.error('Failed to sync with TradeMe:', error);
      toast({
        title: 'Sync Failed',
        description: error.message || 'Failed to sync with TradeMe',
        variant: 'destructive',
      });
    } finally {
      setSyncing(false);
    }
  };

  // Handle sale status update
  const handleSaleStatusUpdate = async (listingId: string, newStatus: SaleStatus) => {
    try {
      setUpdatingStatus(true);
      const result = await updateSaleStatus(listingId, newStatus);

      if (result.success) {
        toast({
          title: 'Status updated',
          description: `Sale status changed successfully`,
        });

        setListings(prevListings =>
          prevListings.map(item =>
            item._id === listingId
              ? {
                  ...item,
                  soldStatus: newStatus,
                  soldStatusDate: result.soldStatusDate,
                  soldStatusUpdatedBy: result.soldStatusUpdatedBy,
                  // Keep legacy fields updated for backward compatibility
                  saleStatus: newStatus,
                  saleStatusDate: result.soldStatusDate,
                  statusUpdatedBy: result.soldStatusUpdatedBy
                }
              : item
          )
        );
      }
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update status',
        variant: 'destructive',
      });
    } finally {
      setUpdatingStatus(false);
    }
  };

  return (
    <TradeMeListingsLayout
      title="Sold Items"
      type="sold"
      listings={listings}
      totalItems={totalItems}
      loading={loading}
      syncing={syncing}
      onSync={handleSync}
      onSearch={handleSearch}
      onFilter={handleFilter}
      onSort={handleSort}
      onTimeFilter={handleTimeFilter}
      onStatusFilter={handleStatusFilter}
    >
      <div className="grid gap-6">
        {(!listings || listings.length === 0) && !loading ? (
          <div className="text-center py-12">
            <h3 className="mt-4 text-lg font-medium">No sold items found</h3>
            <p className="text-muted-foreground mt-2">
              You haven't sold any items on TradeMe yet.
            </p>
          </div>
        ) : (
          listings.map((listing) => (
            <TradeMeListingCard
              key={listing._id}
              listing={listing}
              type="sold"
              onStatusChange={handleSaleStatusUpdate}
            />
          ))
        )}
      </div>
    </TradeMeListingsLayout>
  );
}
