import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { getCust<PERSON>, Customer, deleteCustomer } from "@/api/customers";
import { deleteContact } from "@/api/contacts";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Mail, Phone, MapPin, ArrowLeft, Pencil, Trash2, Calendar, FileText } from "lucide-react";
import { useToast } from "@/hooks/useToast";

export function CustomerDetails() {
  const { id } = useParams<{ id: string }>();
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const navigate = useNavigate();
  const { toast } = useToast();

  useEffect(() => {
    if (!id) return;

    const fetchCustomer = async () => {
      try {
        setLoading(true);
        const data = await getCustomer(id);
        setCustomer(data.customer);
        setError(null);
      } catch (err) {
        console.error('Failed to fetch customer:', err);
        setError(err.message || "Failed to fetch customer details");
        setCustomer(null);
      } finally {
        setLoading(false);
      }
    };

    fetchCustomer();
  }, [id, toast]);

  const handleBack = () => {
    navigate("/address-book");
  };

  const handleEdit = () => {
    navigate(`/address-book/edit/${customer?._id}`);
  };

  const handleDeleteClick = () => {
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!customer) return;

    setIsDeleting(true);
    try {
      await deleteContact(customer._id);
      toast({
        title: "Contact deleted",
        description: `${customer.name} has been successfully deleted.`,
      });
      navigate("/address-book");
    } catch (err) {
      toast({
        variant: "destructive",
        title: "Error",
        description: err.message || "Failed to delete customer",
      });
      setIsDeleting(false);
      setIsDeleteDialogOpen(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Loading customer details...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center">
          <Button variant="ghost" onClick={handleBack} className="mr-4">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Address Book
          </Button>
        </div>

        <Card className="border-red-200">
          <CardHeader className="bg-red-50 dark:bg-red-900/20">
            <CardTitle className="text-red-700 dark:text-red-400">Error</CardTitle>
            <CardDescription>
              We encountered a problem while trying to fetch the customer details.
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-6">
            <p>{error}</p>
          </CardContent>
          <CardFooter>
            <Button variant="outline" onClick={handleBack}>
              Return to Address Book
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  if (!customer) {
    return (
      <div className="space-y-6">
        <div className="flex items-center">
          <Button variant="ghost" onClick={handleBack} className="mr-4">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Address Book
          </Button>
          <h1 className="text-3xl font-bold">Customer Not Found</h1>
        </div>
        <Card className="border-amber-200">
          <CardHeader className="bg-amber-50 dark:bg-amber-900/20">
            <CardTitle className="text-amber-700 dark:text-amber-400">Customer Not Found</CardTitle>
            <CardDescription>
              The customer you're looking for does not exist or has been deleted.
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-6">
            <p>Please check the customer ID and try again, or return to the address book.</p>
          </CardContent>
          <CardFooter>
            <Button variant="outline" onClick={handleBack}>
              Return to Address Book
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button variant="ghost" onClick={handleBack} className="mr-4">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Address Book
        </Button>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleEdit}
            className="flex items-center gap-1"
          >
            <Pencil className="h-4 w-4" /> Edit
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleDeleteClick}
            className="flex items-center gap-1 text-red-500 hover:text-red-700 hover:bg-red-100"
          >
            <Trash2 className="h-4 w-4" /> Delete
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Customer Information</CardTitle>
          <CardDescription>
            Customer information and contact details
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-medium text-sm text-muted-foreground mb-2">Contact Information</h3>
                {customer.email && (
                  <div className="flex items-center mb-2">
                    <Mail className="h-4 w-4 mr-2 text-muted-foreground" />
                    <a href={`mailto:${customer.email}`} className="text-blue-500 hover:underline">
                      {customer.email}
                    </a>
                  </div>
                )}
                {customer.phone && (
                  <div className="flex items-center mb-2">
                    <Phone className="h-4 w-4 mr-2 text-muted-foreground" />
                    <a href={`tel:${customer.phone}`} className="text-blue-500 hover:underline">
                      {customer.phone}
                    </a>
                  </div>
                )}
                {(!customer.email && !customer.phone) && (
                  <p className="text-muted-foreground">No contact information provided</p>
                )}
              </div>

              <div>
                <h3 className="font-medium text-sm text-muted-foreground mb-2">Address</h3>
                {customer.address ? (
                  <div className="flex items-center">
                    <MapPin className="h-4 w-4 mr-2 text-muted-foreground" />
                    <span>{customer.address}</span>
                  </div>
                ) : (
                  <p className="text-muted-foreground">No address provided</p>
                )}
              </div>
            </div>

            <Separator />
            
            <div>
              <h3 className="font-medium text-sm text-muted-foreground mb-2">Contact Type</h3>
              <div className="flex items-center">
                <span className="capitalize">{customer.type || 'customer'}</span>
              </div>
            </div>

            <Separator />

            <div>
              <h3 className="font-medium text-sm text-muted-foreground mb-2">Additional Information</h3>
              <div className="flex items-center mb-2">
                <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                <span>Added on {new Date(customer.dateAdded).toLocaleDateString()} at {new Date(customer.dateAdded).toLocaleTimeString()}</span>
              </div>
            </div>

            <Separator />

            <div>
              <h3 className="font-medium text-sm text-muted-foreground mb-2">Notes</h3>
              <div className="flex items-start">
                <FileText className="h-4 w-4 mr-2 mt-1 text-muted-foreground" />
                <div>
                  {customer.notes ? (
                    <p className="whitespace-pre-line">{customer.notes}</p>
                  ) : (
                    <p className="text-muted-foreground">No notes available</p>
                  )}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={handleBack}>
            Back
          </Button>
          <Button onClick={handleEdit}>
            Edit Customer
          </Button>
        </CardFooter>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Customer</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete {customer.name}? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              disabled={isDeleting}
              className="bg-red-500 hover:bg-red-600"
            >
              {isDeleting ? 'Deleting...' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}