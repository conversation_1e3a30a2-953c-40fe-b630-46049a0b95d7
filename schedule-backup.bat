@echo off
echo Setting up scheduled MongoDB backup task
echo =====================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo This script requires administrator privileges.
    echo Please run as administrator.
    pause
    exit /b 1
)

REM Get the current directory
set SCRIPT_DIR=%~dp0
set BACKUP_SCRIPT=%SCRIPT_DIR%backup-db.bat

echo Creating scheduled task to run daily at 2:00 AM...
schtasks /create /tn "GH0ST MongoDB Backup" /tr "%BACKUP_SCRIPT%" /sc daily /st 02:00 /ru SYSTEM /f

if %ERRORLEVEL% EQU 0 (
    echo Scheduled task created successfully!
) else (
    echo Failed to create scheduled task. Error code: %ERRORLEVEL%
)

echo.
echo You can view and modify this task in Task Scheduler.
echo.
pause
