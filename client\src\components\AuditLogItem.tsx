import { format } from 'date-fns';
import { <PERSON>, <PERSON><PERSON>ontent, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AuditLog } from '@/api/audit';

interface AuditLogItemProps {
  log: AuditLog;
}

export function AuditLogItem({ log }: AuditLogItemProps) {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return format(date, 'PPp'); // e.g. "Mar 15, 2023, 2:30 PM"
  };

  // Format field names for display
  const formatFieldName = (name: string) => {
    // Convert camelCase to Title Case with spaces
    return name
      .replace(/([A-Z])/g, ' $1') // Add space before capital letters
      .replace(/^./, (str) => str.toUpperCase()); // Capitalize the first letter
  };

  // Format value for display
  const formatValue = (value: any) => {
    if (value === null || value === undefined) return 'None';
    if (typeof value === 'boolean') return value ? 'Yes' : 'No';
    if (typeof value === 'number') {
      // Check if the number is a price
      const fieldsThatArePrices = ['lastRRP', 'currentPrice'];
      const isPriceField = Object.keys(log.changes).some(field =>
        fieldsThatArePrices.includes(field) && value === log.newValues[field]
      );
      return isPriceField ? `$${value.toFixed(2)}` : value.toString();
    }
    return String(value);
  };

  // Get color for the action type
  const getActionColor = (action: string) => {
    switch (action) {
      case 'create': return 'bg-green-500 hover:bg-green-600';
      case 'update': return 'bg-blue-500 hover:bg-blue-600';
      case 'delete': return 'bg-red-500 hover:bg-red-600';
      default: return 'bg-gray-500 hover:bg-gray-600';
    }
  };

  return (
    <Card className="mb-3">
      <CardContent className="pt-4">
        <div className="flex items-center mb-2">
          <Badge className={getActionColor(log.action)}>
            {log.action.toUpperCase()}
          </Badge>
          <span className="ml-2 text-sm text-muted-foreground">
            by {log.userId.username || log.userId.email}
          </span>
        </div>
        {log.action === 'update' && (
          <div className="grid gap-2">
            {Object.keys(log.changes).map(field => (
              <div key={field} className="grid grid-cols-3 gap-2">
                <div className="font-medium">{formatFieldName(field)}</div>
                <div className="text-muted-foreground">
                  {formatValue(log.previousValues[field])}
                </div>
                <div className="font-medium">
                  {formatValue(log.newValues[field])}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
      <CardFooter className="border-t px-4 py-2 text-sm text-muted-foreground">
        {formatDate(log.timestamp)}
      </CardFooter>
    </Card>
  );
}