import api from './api';

export interface AuditLog {
  _id: string;
  itemId: string;
  userId: {
    _id: string;
    email: string;
    username?: string;
    name?: string;
  };
  action: 'create' | 'update' | 'delete';
  timestamp: string;
  changes: Record<string, boolean>;
  previousValues: Record<string, any>;
  newValues: Record<string, any>;
}

// Description: Get audit logs for an item
// Endpoint: GET /api/audit/item/:itemId
// Request: {}
// Response: { success: boolean, data: AuditLog[] }
export const getItemAuditLogs = async (itemId: string) => {
  try {
    const response = await api.get(`/audit/item/${itemId}`);
    return response.data;
  } catch (error: any) {
    console.error(error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};