import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import { useToast } from '@/hooks/useToast';
import { getAuditStats, exportAuditData, getAudits, deleteAudit as deleteAuditApi } from '@/api/buyPawnAudits';
import { AuditNavigation } from './AuditNavigation';
import { AuditStatsCards } from './AuditStatsCards';
import { AuditFlaggingActions } from './AuditFlaggingActions';
import { Loader2, Plus, Download, Search, FileText, Edit, Trash2 } from 'lucide-react';
import { format } from 'date-fns';

/**
 * Dashboard component for Buy/Pawn Audits
 * Shows statistics, charts, and quick access to audit functions
 */
export function AuditDashboard() {
  const [stats, setStats] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [audits, setAudits] = useState<any[]>([]);
  const [isLoadingAudits, setIsLoadingAudits] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const navigate = useNavigate();
  const { toast } = useToast();

  // Fetch audit statistics
  useEffect(() => {
    const fetchStats = async () => {
      try {
        setIsLoading(true);
        const result = await getAuditStats();

        if (result.success) {
          setStats(result.data);
        } else {
          toast({
            title: 'Error',
            description: result.error || 'Failed to load audit statistics.',
            variant: 'destructive',
          });
        }
      } catch (error: any) {
        toast({
          title: 'Error',
          description: error.message || 'An unexpected error occurred.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchStats();
  }, [toast]);

  // Fetch audits for the list
  useEffect(() => {
    const fetchAudits = async () => {
      try {
        console.log('Fetching audits with filters:', { filterType, filterStatus, currentPage, searchTerm });
        setIsLoadingAudits(true);
        const result = await getAudits({
          type: filterType !== 'all' ? filterType as any : undefined,
          status: filterStatus !== 'all' ? filterStatus as any : undefined,
          page: currentPage,
          limit: 10,
          search: searchTerm,
        });

        console.log('Audit fetch result:', result);

        if (result.success) {
          setAudits(result.data.audits || []);
          setTotalPages(result.data.pagination?.pages || 1);
        } else {
          toast({
            title: 'Error',
            description: result.error || 'Failed to load audits.',
            variant: 'destructive',
          });
        }
      } catch (error: any) {
        console.error('Error fetching audits:', error);
        setAudits([]);
        toast({
          title: 'Error',
          description: error.message || 'An unexpected error occurred.',
          variant: 'destructive',
        });
      } finally {
        setIsLoadingAudits(false);
      }
    };

    fetchAudits();
  }, [currentPage, filterType, filterStatus, searchTerm, toast]);

  // Handle export to PDF
  const handleExportPDF = async () => {
    try {
      toast({
        title: 'Export Started',
        description: 'Generating PDF report...',
      });

      // Call the API to generate and download the PDF
      const result = await exportAuditData('pdf', {
        timeRange: '30days',
        includeCharts: true,
        includeDetails: true
      });

      if (result.success) {
        toast({
          title: 'Export Complete',
          description: 'PDF report has been downloaded.',
        });
      } else {
        toast({
          title: 'Export Failed',
          description: result.error || 'Failed to generate PDF report.',
          variant: 'destructive',
        });
      }
    } catch (error: any) {
      toast({
        title: 'Export Failed',
        description: error.message || 'An unexpected error occurred.',
        variant: 'destructive',
      });
    }
  };

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
  };

  // Format date
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'dd/MM/yyyy');
  };

  // Get audit type display
  const getAuditTypeDisplay = (type: string) => {
    switch (type) {
      case 'buy':
        return 'Buy Deal';
      case 'pawn':
        return 'Pawn Loan';
      case 'price':
        return 'Price';
      default:
        return type;
    }
  };

  // Get compliance badge
  const getComplianceBadge = (compliance: string) => {
    switch (compliance) {
      case 'compliant':
        return <Badge className="bg-green-500">Compliant</Badge>;
      case 'minor_non_compliant':
        return <Badge className="bg-yellow-500">Minor Non-Compliant</Badge>;
      case 'major_non_compliant':
        return <Badge className="bg-red-500">Major Non-Compliant</Badge>;
      default:
        return <Badge variant="outline">{compliance}</Badge>;
    }
  };

  // View audit details
  const viewAuditDetails = (id: string) => {
    navigate(`/buys/audits/${id}`);
  };

  // Edit audit
  const editAudit = (id: string) => {
    navigate(`/buys/audits/${id}/edit`);
  };

  // Delete audit
  const deleteAudit = async (id: string) => {
    if (confirm('Are you sure you want to delete this audit? This action cannot be undone.')) {
      try {
        const result = await deleteAuditApi(id);

        if (result.success) {
          toast({
            title: 'Audit Deleted',
            description: 'The audit has been deleted successfully.',
          });

          // Refresh the list
          setAudits((prevAudits) => (prevAudits || []).filter(audit => audit._id !== id));
        } else {
          toast({
            title: 'Error',
            description: result.error || 'Failed to delete audit.',
            variant: 'destructive',
          });
        }
      } catch (error: any) {
        toast({
          title: 'Error',
          description: error.message || 'Failed to delete audit.',
          variant: 'destructive',
        });
      }
    }
  };

  // Refresh audits list
  const refreshAudits = async () => {
    try {
      setIsLoadingAudits(true);
      const result = await getAudits({
        type: filterType !== 'all' ? filterType as any : undefined,
        status: filterStatus !== 'all' ? filterStatus as any : undefined,
        page: currentPage,
        limit: 10,
        search: searchTerm,
      });

      if (result.success) {
        setAudits(result.data.audits || []);
        setTotalPages(result.data.pagination?.pages || 1);
      }
    } catch (error: any) {
      console.error('Error refreshing audits:', error);
    } finally {
      setIsLoadingAudits(false);
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col gap-4 mb-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold">Audit Dashboard</h1>
            <p className="text-muted-foreground">Monitor audit performance and compliance</p>
          </div>
          <div className="flex gap-2">
            <Button onClick={() => navigate('/buys/audits/new')}>
              <Plus className="mr-2 h-4 w-4" />
              New Audit
            </Button>
            <Button variant="outline" onClick={handleExportPDF}>
              <Download className="mr-2 h-4 w-4" />
              Export Report
            </Button>
          </div>
        </div>

        <AuditNavigation />
      </div>

      {stats && stats.totalAudits > 0 ? (
        <div className="space-y-6">
          <AuditStatsCards stats={stats} />

          {/* Audit List Section */}
          <Card>
            <CardHeader>
              <CardTitle>All Audits</CardTitle>
              <CardDescription>
                Complete list of all buy/pawn audit transactions
              </CardDescription>
            </CardHeader>
            <CardContent>
              {/* Filters and Search */}
              <div className="flex flex-col sm:flex-row gap-4 mb-6">
                <form onSubmit={handleSearch} className="flex gap-2 flex-1">
                  <div className="relative flex-1">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search by transaction ID, employee, or description..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-8"
                    />
                  </div>
                  <Button type="submit" variant="outline">
                    Search
                  </Button>
                </form>

                <div className="flex gap-2">
                  <Select value={filterType} onValueChange={setFilterType}>
                    <SelectTrigger className="w-[140px]">
                      <SelectValue placeholder="Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="buy">Buy Deal</SelectItem>
                      <SelectItem value="pawn">Pawn Loan</SelectItem>
                      <SelectItem value="price">Price</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={filterStatus} onValueChange={setFilterStatus}>
                    <SelectTrigger className="w-[140px]">
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="completed">Completed</SelectItem>
                      <SelectItem value="flagged">Flagged</SelectItem>
                      <SelectItem value="resolved">Resolved</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Audit Table */}
              {isLoadingAudits && (!audits || audits.length === 0) ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin" />
                  <span className="ml-2">Loading audits...</span>
                </div>
              ) : (
                <>
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Transaction ID</TableHead>
                          <TableHead>Type</TableHead>
                          <TableHead>Employee</TableHead>
                          <TableHead>Amount</TableHead>
                          <TableHead>Compliance</TableHead>
                          <TableHead>Date</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Follow-up</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {!audits || audits.length === 0 ? (
                          <TableRow>
                            <TableCell colSpan={9} className="text-center py-4">
                              No audits found
                            </TableCell>
                          </TableRow>
                        ) : (
                          audits.map((audit) => (
                            <TableRow key={audit._id}>
                              <TableCell className="font-medium">
                                {audit.transactionId}
                              </TableCell>
                              <TableCell>
                                {getAuditTypeDisplay(audit.auditType)}
                              </TableCell>
                              <TableCell>{audit.employeeName}</TableCell>
                              <TableCell>${audit.amount?.toFixed(2)}</TableCell>
                              <TableCell>
                                {getComplianceBadge(audit.complianceLevel)}
                              </TableCell>
                              <TableCell>
                                {formatDate(audit.auditDate)}
                              </TableCell>
                              <TableCell>
                                <Badge variant={audit.status === 'completed' ? 'default' : 'secondary'}>
                                  {audit.status}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                <AuditFlaggingActions
                                  audit={audit}
                                  onUpdate={refreshAudits}
                                  size="sm"
                                  variant="ghost"
                                />
                              </TableCell>
                              <TableCell className="text-right">
                                <div className="flex justify-end gap-2">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => viewAuditDetails(audit._id)}
                                  >
                                    <FileText className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => editAudit(audit._id)}
                                  >
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => deleteAudit(audit._id)}
                                    className="text-destructive hover:text-destructive"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          ))
                        )}
                      </TableBody>
                    </Table>
                  </div>

                  {/* Pagination */}
                  {totalPages > 1 && (
                    <div className="flex justify-center mt-4">
                      <Pagination>
                        <PaginationContent>
                          <PaginationItem>
                            <PaginationPrevious
                              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                              className={currentPage === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                            />
                          </PaginationItem>

                          {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                            <PaginationItem key={page}>
                              <PaginationLink
                                onClick={() => setCurrentPage(page)}
                                isActive={currentPage === page}
                                className="cursor-pointer"
                              >
                                {page}
                              </PaginationLink>
                            </PaginationItem>
                          ))}

                          <PaginationItem>
                            <PaginationNext
                              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                              className={currentPage === totalPages ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                            />
                          </PaginationItem>
                        </PaginationContent>
                      </Pagination>
                    </div>
                  )}
                </>
              )}
            </CardContent>
          </Card>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Show empty stats cards */}
          <AuditStatsCards stats={null} />

          {/* Show audit list even when no stats */}
          <Card>
            <CardHeader>
              <CardTitle>All Audits</CardTitle>
              <CardDescription>
                Complete list of all buy/pawn audit transactions
              </CardDescription>
            </CardHeader>
            <CardContent>
              {/* Filters and Search */}
              <div className="flex flex-col sm:flex-row gap-4 mb-6">
                <form onSubmit={handleSearch} className="flex gap-2 flex-1">
                  <div className="relative flex-1">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search by transaction ID, employee, or description..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-8"
                    />
                  </div>
                  <Button type="submit" variant="outline">
                    Search
                  </Button>
                </form>

                <div className="flex gap-2">
                  <Select value={filterType} onValueChange={setFilterType}>
                    <SelectTrigger className="w-[140px]">
                      <SelectValue placeholder="Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="buy">Buy Deal</SelectItem>
                      <SelectItem value="pawn">Pawn Loan</SelectItem>
                      <SelectItem value="price">Price</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={filterStatus} onValueChange={setFilterStatus}>
                    <SelectTrigger className="w-[140px]">
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="completed">Completed</SelectItem>
                      <SelectItem value="flagged">Flagged</SelectItem>
                      <SelectItem value="resolved">Resolved</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Audit Table */}
              {isLoadingAudits && (!audits || audits.length === 0) ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin" />
                  <span className="ml-2">Loading audits...</span>
                </div>
              ) : (
                <>
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Transaction ID</TableHead>
                          <TableHead>Type</TableHead>
                          <TableHead>Employee</TableHead>
                          <TableHead>Amount</TableHead>
                          <TableHead>Compliance</TableHead>
                          <TableHead>Date</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {!audits || audits.length === 0 ? (
                          <TableRow>
                            <TableCell colSpan={8} className="text-center py-8">
                              <div className="flex flex-col items-center justify-center">
                                <p className="text-muted-foreground mb-4">
                                  No audits found. Start by creating your first audit.
                                </p>
                                <Button onClick={() => navigate('/buys/audits/new')}>
                                  <Plus className="mr-2 h-4 w-4" />
                                  Create First Audit
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ) : (
                          audits.map((audit) => (
                            <TableRow key={audit._id}>
                              <TableCell className="font-medium">
                                {audit.transactionId}
                              </TableCell>
                              <TableCell>
                                {getAuditTypeDisplay(audit.auditType)}
                              </TableCell>
                              <TableCell>{audit.employeeName}</TableCell>
                              <TableCell>${audit.amount?.toFixed(2)}</TableCell>
                              <TableCell>
                                {getComplianceBadge(audit.complianceLevel)}
                              </TableCell>
                              <TableCell>
                                {formatDate(audit.auditDate)}
                              </TableCell>
                              <TableCell>
                                <Badge variant={audit.status === 'completed' ? 'default' : 'secondary'}>
                                  {audit.status}
                                </Badge>
                              </TableCell>
                              <TableCell className="text-right">
                                <div className="flex justify-end gap-2">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => viewAuditDetails(audit._id)}
                                  >
                                    <FileText className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => editAudit(audit._id)}
                                  >
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => deleteAudit(audit._id)}
                                    className="text-destructive hover:text-destructive"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          ))
                        )}
                      </TableBody>
                    </Table>
                  </div>

                  {/* Pagination */}
                  {totalPages > 1 && (
                    <div className="flex justify-center mt-4">
                      <Pagination>
                        <PaginationContent>
                          <PaginationItem>
                            <PaginationPrevious
                              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                              className={currentPage === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                            />
                          </PaginationItem>

                          {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                            <PaginationItem key={page}>
                              <PaginationLink
                                onClick={() => setCurrentPage(page)}
                                isActive={currentPage === page}
                                className="cursor-pointer"
                              >
                                {page}
                              </PaginationLink>
                            </PaginationItem>
                          ))}

                          <PaginationItem>
                            <PaginationNext
                              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                              className={currentPage === totalPages ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                            />
                          </PaginationItem>
                        </PaginationContent>
                      </Pagination>
                    </div>
                  )}
                </>
              )}
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
