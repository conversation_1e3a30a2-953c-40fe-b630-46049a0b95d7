import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/hooks/useToast';
import { useAuth } from '@/contexts/AuthContext';
import { getAudits, getAuditStats, Audit } from '@/api/buyPawnAudits';
import { AuditNavigation } from '@/components/buys/audits/AuditNavigation';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, Download } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { DatePicker } from '@/components/ui/date-picker';

/**
 * Component for detailed audit reporting and statistics
 */
export function DetailedReporting() {
  const { toast } = useToast();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState<any>({
    totalAudits: 0,
    byType: {
      buy: 0,
      pawn: 0,
      price: 0,
    },
    byCompliance: {
      compliant: 0,
      minorNonCompliant: 0,
      majorNonCompliant: 0,
    },
    flagged: {
      total: 0,
      followedUp: 0,
      pending: 0,
    },
    complianceRate: 0,
  });
  
  const [timeRange, setTimeRange] = useState('all');
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  
  // Load audit statistics
  useEffect(() => {
    const loadStats = async () => {
      setIsLoading(true);
      try {
        const result = await getAuditStats();
        
        if (result.success) {
          setStats(result.data);
        } else {
          toast({
            title: 'Error',
            description: result.error || 'Failed to load audit statistics.',
            variant: 'destructive',
          });
        }
      } catch (error: any) {
        toast({
          title: 'Error',
          description: error.message || 'An unexpected error occurred.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    loadStats();
  }, [toast]);
  
  // Handle time range change
  const handleTimeRangeChange = (value: string) => {
    setTimeRange(value);
    
    // Set date range based on selection
    const now = new Date();
    let start: Date | undefined = undefined;
    let end: Date | undefined = undefined;
    
    switch (value) {
      case 'week':
        start = new Date(now);
        start.setDate(now.getDate() - 7);
        end = now;
        break;
      case 'month':
        start = new Date(now);
        start.setMonth(now.getMonth() - 1);
        end = now;
        break;
      case 'quarter':
        start = new Date(now);
        start.setMonth(now.getMonth() - 3);
        end = now;
        break;
      case 'year':
        start = new Date(now);
        start.setFullYear(now.getFullYear() - 1);
        end = now;
        break;
      case 'custom':
        // Keep current custom dates if set
        break;
      default:
        // 'all' - no date filtering
        break;
    }
    
    setStartDate(start);
    setEndDate(end);
  };
  
  // Handle export to CSV
  const handleExportCSV = async () => {
    try {
      // Get all audits for the selected time range
      const filters: any = {
        limit: 1000, // Get a large number of audits
      };
      
      if (startDate && endDate) {
        filters.startDate = startDate.toISOString();
        filters.endDate = endDate.toISOString();
      }
      
      const result = await getAudits(filters);
      
      if (result.success && result.data.length > 0) {
        // Convert audits to CSV
        const headers = [
          'Transaction ID',
          'Audit Type',
          'Employee',
          'Amount',
          'Compliance',
          'Score',
          'Flagged',
          'Followed Up',
          'Audit Date',
        ];
        
        const rows = result.data.map((audit: any) => [
          audit.transactionId,
          audit.auditType,
          audit.employeeName,
          audit.amount,
          audit.overallCompliance,
          audit.overallScore,
          audit.flaggedForFollowup ? 'Yes' : 'No',
          audit.followedUp ? 'Yes' : 'No',
          new Date(audit.auditDate).toLocaleDateString(),
        ]);
        
        const csvContent = [
          headers.join(','),
          ...rows.map((row: any[]) => row.join(',')),
        ].join('\n');
        
        // Create and download CSV file
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.setAttribute('href', url);
        link.setAttribute('download', `audit-report-${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        toast({
          title: 'Export Successful',
          description: `Exported ${result.data.length} audits to CSV.`,
        });
      } else {
        toast({
          title: 'Export Failed',
          description: 'No audits found for the selected time range.',
          variant: 'destructive',
        });
      }
    } catch (error: any) {
      toast({
        title: 'Export Failed',
        description: error.message || 'An unexpected error occurred.',
        variant: 'destructive',
      });
    }
  };
  
  // Check if user has permission to access this page
  if (user?.role !== 'admin' && user?.role !== 'manager') {
    return (
      <div className="container mx-auto py-6">
        <Card>
          <CardHeader>
            <CardTitle>Access Denied</CardTitle>
            <CardDescription>
              You do not have permission to access the detailed reporting page.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => navigate('/buys/audits')}>Back to Dashboard</Button>
          </CardContent>
        </Card>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col gap-4 mb-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold">Audit Reporting</h1>
            <p className="text-muted-foreground">View detailed audit statistics and reports</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleExportCSV}>
              <Download className="mr-2 h-4 w-4" />
              Export to CSV
            </Button>
          </div>
        </div>
        
        <AuditNavigation />
      </div>
      
      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <Card className="flex-1">
          <CardHeader className="pb-2">
            <CardTitle>Time Range</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col gap-4">
              <Select value={timeRange} onValueChange={handleTimeRangeChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select time range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Time</SelectItem>
                  <SelectItem value="week">Last 7 Days</SelectItem>
                  <SelectItem value="month">Last 30 Days</SelectItem>
                  <SelectItem value="quarter">Last 90 Days</SelectItem>
                  <SelectItem value="year">Last Year</SelectItem>
                  <SelectItem value="custom">Custom Range</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>
        
        <Card className="flex-1">
          <CardHeader className="pb-2">
            <CardTitle>Overview</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">Total Audits</p>
                <p className="text-2xl font-bold">{stats.totalAudits}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">Compliance Rate</p>
                <p className="text-2xl font-bold">{stats.complianceRate.toFixed(1)}%</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">Flagged Issues</p>
                <p className="text-2xl font-bold">{stats.flagged.total}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">Pending Follow-ups</p>
                <p className="text-2xl font-bold">{stats.flagged.pending}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {isLoading ? (
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Coming Soon</CardTitle>
              <CardDescription>Detailed reporting features are under development</CardDescription>
            </CardHeader>
            <CardContent className="h-[300px] flex items-center justify-center">
              <p className="text-muted-foreground text-center">
                Detailed charts and reporting features will be available in a future update.
                <br /><br />
                Check back soon!
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Export Options</CardTitle>
              <CardDescription>Export audit data for further analysis</CardDescription>
            </CardHeader>
            <CardContent className="h-[300px] flex flex-col items-center justify-center gap-4">
              <p className="text-muted-foreground text-center mb-4">
                Export your audit data to CSV for detailed analysis in external tools.
              </p>
              
              <Button onClick={handleExportCSV} className="w-full max-w-xs">
                <Download className="mr-2 h-4 w-4" />
                Export All Audits to CSV
              </Button>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
