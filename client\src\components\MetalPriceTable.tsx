import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { MetalPrice } from '@/api/goldPricing';

interface MetalPriceTableProps {
  prices: MetalPrice[];
}

export function MetalPriceTable({ prices }: MetalPriceTableProps) {
  // Group prices by metal for better organization
  const groupedPrices = prices.reduce((acc, price) => {
    if (!acc[price.metal]) {
      acc[price.metal] = [];
    }
    acc[price.metal].push(price);
    return acc;
  }, {} as Record<string, MetalPrice[]>);

  // Function to extract numeric purity value for sorting
  const getPurityValue = (purity: string): number => {
    // Extract numeric part from strings like "6k", "10k", "999", etc.
    const match = purity.match(/(\d+\.?\d*)/);
    return match ? parseFloat(match[1]) : 0;
  };

  return (
    <div className="space-y-4">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Metal/Purity</TableHead>
            <TableHead className="text-right">Min Pay Price</TableHead>
            <TableHead className="text-right">Max Pay Price</TableHead>
            <TableHead className="text-right">Spot Price</TableHead>
            <TableHead className="text-right">Melt Price</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {Object.entries(groupedPrices).sort((a, b) => a[0].localeCompare(b[0])).map(([metal, metalPrices]) => {
            // Sort by purity (alphabetically by metal, then numerically by purity)
            const sortedPrices = [...metalPrices].sort((a, b) => {
              const purA = getPurityValue(a.purity);
              const purB = getPurityValue(b.purity);
              return purA - purB; // Ascending order (6k, 8k, 10k, etc.)
            });

            return sortedPrices.map((price) => (
              <TableRow key={`${price.metal}-${price.purity}`}>
                <TableCell className="font-medium">
                  {price.metal} - {price.purity}
                </TableCell>
                <TableCell className="text-right">${price.minBuyPrice.toFixed(2)}</TableCell>
                <TableCell className="text-right">${price.maxBuyPrice.toFixed(2)}</TableCell>
                <TableCell className="text-right">${price.spotPrice.toFixed(2)}</TableCell>
                <TableCell className="text-right">${price.meltPrice.toFixed(2)}</TableCell>
              </TableRow>
            ));
          })}
        </TableBody>
      </Table>
    </div>
  );
}