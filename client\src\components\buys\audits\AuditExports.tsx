import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/useToast';
import { AuditNavigation } from './AuditNavigation';
import { Download, FileText, File, Calendar, Loader2 } from 'lucide-react';
import { exportAuditData } from '@/api/buyPawnAudits';

/**
 * Component for exporting audit data in various formats
 */
export function AuditExports() {
  const [exportFormat, setExportFormat] = useState('pdf');
  const [dateRange, setDateRange] = useState('30days');
  const [auditTypes, setAuditTypes] = useState<string[]>(['buy', 'pawn', 'price']);
  const [includeDetails, setIncludeDetails] = useState(true);
  const [includeCharts, setIncludeCharts] = useState(true);
  const [isExporting, setIsExporting] = useState(false);
  const [recentExports, setRecentExports] = useState<any[]>([]);
  const [isLoadingExports, setIsLoadingExports] = useState(false);
  const { toast } = useToast();

  // Fetch recent exports on component mount
  useEffect(() => {
    fetchRecentExports();
  }, []);

  // Fetch recent exports
  const fetchRecentExports = async () => {
    try {
      setIsLoadingExports(true);

      // Call the API to get recent exports
      const response = await fetch('/api/buys/audits/exports/recent');
      const data = await response.json();

      if (data.success) {
        setRecentExports(data.data || []);
      } else {
        console.error('Failed to fetch recent exports:', data.error);
      }
    } catch (error) {
      console.error('Error fetching recent exports:', error);
    } finally {
      setIsLoadingExports(false);
    }
  };

  // Handle download of an existing export
  const handleDownloadExport = async (exportId: string) => {
    try {
      toast({
        title: 'Download Started',
        description: 'Preparing your download...',
      });

      // Call the API to download the export
      const response = await fetch(`/api/buys/audits/exports/${exportId}/download`);

      if (response.ok) {
        // Handle the download (this would typically trigger a file download)
        toast({
          title: 'Download Complete',
          description: 'Your file has been downloaded.',
        });
      } else {
        const errorData = await response.json();
        toast({
          title: 'Download Failed',
          description: errorData.error || 'Failed to download the export.',
          variant: 'destructive',
        });
      }
    } catch (error: any) {
      toast({
        title: 'Download Failed',
        description: error.message || 'An unexpected error occurred.',
        variant: 'destructive',
      });
    }
  };

  // Load more exports
  const loadMoreExports = () => {
    // This would typically navigate to a page with all exports
    // or load more exports in the current view
    toast({
      title: 'View All Exports',
      description: 'This would show all your exports.',
    });
  };

  // Handle export
  const handleExport = async () => {
    try {
      setIsExporting(true);

      toast({
        title: 'Export Started',
        description: `Generating ${exportFormat.toUpperCase()} export...`,
      });

      // Call the API to generate and download the export
      const result = await exportAuditData(exportFormat, {
        timeRange: dateRange,
        auditTypes,
        includeCharts,
        includeDetails
      });

      setIsExporting(false);

      if (result.success) {
        toast({
          title: 'Export Complete',
          description: `${exportFormat.toUpperCase()} export has been downloaded.`,
        });
      } else {
        toast({
          title: 'Export Failed',
          description: result.error || `Failed to generate ${exportFormat.toUpperCase()} export.`,
          variant: 'destructive',
        });
      }
    } catch (error: any) {
      setIsExporting(false);
      toast({
        title: 'Export Failed',
        description: error.message || 'An unexpected error occurred.',
        variant: 'destructive',
      });
    }
  };

  // Toggle audit type selection
  const toggleAuditType = (type: string) => {
    if (auditTypes.includes(type)) {
      setAuditTypes(auditTypes.filter(t => t !== type));
    } else {
      setAuditTypes([...auditTypes, type]);
    }
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col gap-4 mb-6">
        <div>
          <h1 className="text-3xl font-bold">Export Audit Data</h1>
          <p className="text-muted-foreground">Generate reports and export audit data</p>
        </div>

        <AuditNavigation />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Export Options</CardTitle>
              <CardDescription>
                Configure your export settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <Label className="text-base">Export Format</Label>
                  <RadioGroup
                    value={exportFormat}
                    onValueChange={setExportFormat}
                    className="flex flex-col space-y-1 mt-2"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="pdf" id="pdf" />
                      <Label htmlFor="pdf" className="flex items-center">
                        <File className="mr-2 h-4 w-4" />
                        PDF Report
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="excel" id="excel" />
                      <Label htmlFor="excel" className="flex items-center">
                        <FileText className="mr-2 h-4 w-4" />
                        Excel Spreadsheet
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="csv" id="csv" />
                      <Label htmlFor="csv" className="flex items-center">
                        <FileText className="mr-2 h-4 w-4" />
                        CSV File
                      </Label>
                    </div>
                  </RadioGroup>
                </div>

                <div>
                  <Label className="text-base">Date Range</Label>
                  <Select value={dateRange} onValueChange={setDateRange} className="mt-2">
                    <SelectTrigger>
                      <SelectValue placeholder="Select date range" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="7days">Last 7 days</SelectItem>
                      <SelectItem value="30days">Last 30 days</SelectItem>
                      <SelectItem value="90days">Last 90 days</SelectItem>
                      <SelectItem value="year">Last year</SelectItem>
                      <SelectItem value="all">All time</SelectItem>
                      <SelectItem value="custom">Custom range</SelectItem>
                    </SelectContent>
                  </Select>

                  {dateRange === 'custom' && (
                    <div className="grid grid-cols-2 gap-4 mt-4">
                      <div className="space-y-2">
                        <Label htmlFor="start-date">Start Date</Label>
                        <div className="relative">
                          <Calendar className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                          <Input
                            id="start-date"
                            type="date"
                            className="pl-8"
                          />
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="end-date">End Date</Label>
                        <div className="relative">
                          <Calendar className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                          <Input
                            id="end-date"
                            type="date"
                            className="pl-8"
                          />
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                <div>
                  <Label className="text-base">Audit Types</Label>
                  <div className="flex flex-col space-y-2 mt-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="buy"
                        checked={auditTypes.includes('buy')}
                        onCheckedChange={() => toggleAuditType('buy')}
                      />
                      <Label htmlFor="buy">Buy Deal Audits</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="pawn"
                        checked={auditTypes.includes('pawn')}
                        onCheckedChange={() => toggleAuditType('pawn')}
                      />
                      <Label htmlFor="pawn">Pawn Loan Audits</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="price"
                        checked={auditTypes.includes('price')}
                        onCheckedChange={() => toggleAuditType('price')}
                      />
                      <Label htmlFor="price">Price Audits</Label>
                    </div>
                  </div>
                </div>

                {exportFormat === 'pdf' && (
                  <div>
                    <Label className="text-base">Report Options</Label>
                    <div className="flex flex-col space-y-2 mt-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="details"
                          checked={includeDetails}
                          onCheckedChange={(checked) => setIncludeDetails(!!checked)}
                        />
                        <Label htmlFor="details">Include detailed audit information</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="charts"
                          checked={includeCharts}
                          onCheckedChange={(checked) => setIncludeCharts(!!checked)}
                        />
                        <Label htmlFor="charts">Include charts and visualizations</Label>
                      </div>
                    </div>
                  </div>
                )}

                <Button
                  onClick={handleExport}
                  disabled={isExporting || auditTypes.length === 0}
                  className="w-full"
                >
                  {isExporting ? (
                    <>Generating Export...</>
                  ) : (
                    <>
                      <Download className="mr-2 h-4 w-4" />
                      Generate {exportFormat.toUpperCase()} Export
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle>Recent Exports</CardTitle>
              <CardDescription>
                Your recently generated exports
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentExports.length > 0 ? (
                  <>
                    {recentExports.map((exportItem, index) => (
                      <div key={index} className="border rounded-md p-3">
                        <div className="flex items-start justify-between">
                          <div>
                            <p className="font-medium">{exportItem.name}</p>
                            <p className="text-sm text-muted-foreground">
                              {exportItem.format.toUpperCase()} • {new Date(exportItem.date).toLocaleDateString()}
                            </p>
                          </div>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleDownloadExport(exportItem.id)}
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}

                    <div className="text-center">
                      <Button variant="link" size="sm" onClick={loadMoreExports}>
                        View All Exports
                      </Button>
                    </div>
                  </>
                ) : (
                  <div className="text-center py-6">
                    <p className="text-muted-foreground">No recent exports found</p>
                    <p className="text-sm text-muted-foreground mt-1">
                      Generate an export to see it here
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
