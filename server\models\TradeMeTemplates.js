const mongoose = require('mongoose');

/**
 * TradeMeTemplates Schema
 *
 * Unified model for all TradeMe-related templates:
 * - Question templates for answering buyer questions
 * - Footer templates for listing descriptions
 * - Shipping templates for listing shipping options
 * - Withdraw templates for listing withdrawal reasons
 */
const tradeMeTemplateSchema = new mongoose.Schema({
  // Template type
  type: {
    type: String,
    enum: ['question', 'footer', 'shipping', 'withdraw'],
    required: true
  },

  // Template title/name
  title: {
    type: String,
    required: true,
    trim: true
  },

  // Template content
  content: {
    type: String,
    required: true
  },

  // Category (for organizing templates)
  category: {
    type: String,
    default: 'General'
  },

  // For shipping templates
  price: {
    type: Number,
    min: 0,
    default: 0
  },

  // Shipping method (for shipping templates)
  method: {
    type: String,
    trim: true
  },

  // Default template flag
  isDefault: {
    type: Boolean,
    default: false
  },

  // Metadata
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now,
    immutable: true
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  versionKey: false
});

// Add text index for searching
tradeMeTemplateSchema.index({ title: 'text', content: 'text' });

// Add compound index for type and category
tradeMeTemplateSchema.index({ type: 1, category: 1 });

// Pre-save middleware to update timestamps
tradeMeTemplateSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

const TradeMeTemplates = mongoose.model('TradeMeTemplates', tradeMeTemplateSchema);

module.exports = TradeMeTemplates;
