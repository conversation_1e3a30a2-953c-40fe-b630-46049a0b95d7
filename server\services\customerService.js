const Customer = require('../models/Customer');

class CustomerService {
  // Get all customers with pagination
  static async getAll(page = 1, limit = 10, searchTerm = '') {
    try {
      console.log(`Fetching customers - page: ${page}, limit: ${limit}, search: ${searchTerm}`);

      const skip = (page - 1) * limit;

      let query = {};

      // Add search functionality if searchTerm is provided
      if (searchTerm) {
        query = {
          $or: [
            { name: { $regex: searchTerm, $options: 'i' } },
            { email: { $regex: searchTerm, $options: 'i' } },
            { phone: { $regex: searchTerm, $options: 'i' } }
          ]
        };
      }

      // Execute query with pagination
      const customers = await Customer.find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit);

      // Get total count for pagination
      const total = await Customer.countDocuments(query);

      return {
        customers,
        pagination: {
          total,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      console.error(`Error fetching customers: ${error.stack}`);
      throw new Error(`Error fetching customers: ${error.message}`);
    }
  }

  // Get customer by ID
  static async getById(id) {
    try {
      console.log(`Fetching customer with ID: ${id}`);
      const customer = await Customer.findById(id);
      if (!customer) {
        console.log(`Customer with ID ${id} not found`);
        throw new Error('Customer not found');
      }
      return customer;
    } catch (error) {
      console.error(`Error fetching customer: ${error.stack}`);
      throw new Error(`Error fetching customer: ${error.message}`);
    }
  }

  // Create a new customer
  static async create(customerData) {
    try {
      console.log('Creating new customer:', customerData.name);

      // Only check email uniqueness if an email is provided and not empty
      if (customerData.email && customerData.email.trim() !== '') {
        const existingCustomer = await Customer.findOne({ email: customerData.email.toLowerCase() });
        if (existingCustomer) {
          console.log(`Customer with email ${customerData.email} already exists`);
          throw new Error('A customer with this email already exists');
        }
      } else {
        // Set email to undefined when empty to avoid uniqueness issues
        // This avoids both empty strings and null values
        customerData.email = undefined;
      }

      const customer = new Customer(customerData);
      await customer.save();
      console.log(`Customer created successfully with ID: ${customer._id}`);
      return customer;
    } catch (error) {
      console.error(`Error creating customer: ${error.stack}`);
      throw new Error(`Error creating customer: ${error.message}`);
    }
  }

  // Update an existing customer
  static async update(id, customerData) {
    try {
      console.log(`Updating customer with ID: ${id}`);

      // Only check email uniqueness if an email is provided and not empty
      if (customerData.email && customerData.email.trim() !== '') {
        const existingCustomer = await Customer.findOne({
          email: customerData.email.toLowerCase(),
          _id: { $ne: id } // Exclude current customer from check
        });

        if (existingCustomer) {
          console.log(`Another customer with email ${customerData.email} already exists`);
          throw new Error('A customer with this email already exists');
        }
      }

      const customer = await Customer.findByIdAndUpdate(
        id,
        { ...customerData, updatedAt: Date.now() },
        { new: true, runValidators: true }
      );

      if (!customer) {
        console.log(`Customer with ID ${id} not found for update`);
        throw new Error('Customer not found');
      }

      console.log(`Customer with ID ${id} updated successfully`);
      return customer;
    } catch (error) {
      console.error(`Error updating customer: ${error.stack}`);
      throw new Error(`Error updating customer: ${error.message}`);
    }
  }

  // Delete a customer
  static async delete(id) {
    try {
      console.log(`Deleting customer with ID: ${id}`);
      const customer = await Customer.findByIdAndDelete(id);
      if (!customer) {
        console.log(`Customer with ID ${id} not found for deletion`);
        throw new Error('Customer not found');
      }
      console.log(`Customer with ID ${id} deleted successfully`);
      return customer;
    } catch (error) {
      console.error(`Error deleting customer: ${error.stack}`);
      throw new Error(`Error deleting customer: ${error.message}`);
    }
  }
}

module.exports = CustomerService;