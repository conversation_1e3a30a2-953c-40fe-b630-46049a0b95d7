/* Stars/particles background effect */
.stars-container {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.stars-container::before,
.stars-container::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(1.5px 1.5px at 20px 30px, rgba(255, 255, 255, 0.7), rgba(0, 0, 0, 0)),
    radial-gradient(1.5px 1.5px at 40px 70px, rgba(255, 255, 255, 0.6), rgba(0, 0, 0, 0)),
    radial-gradient(1.5px 1.5px at 50px 160px, rgba(255, 255, 255, 0.7), rgba(0, 0, 0, 0)),
    radial-gradient(1.5px 1.5px at 90px 40px, rgba(255, 255, 255, 0.6), rgba(0, 0, 0, 0)),
    radial-gradient(1.5px 1.5px at 130px 80px, rgba(255, 255, 255, 0.7), rgba(0, 0, 0, 0)),
    radial-gradient(1.5px 1.5px at 160px 120px, rgba(255, 255, 255, 0.6), rgba(0, 0, 0, 0)),
    radial-gradient(1.5px 1.5px at 180px 90px, rgba(255, 255, 255, 0.7), rgba(0, 0, 0, 0)),
    radial-gradient(1.5px 1.5px at 210px 110px, rgba(255, 255, 255, 0.6), rgba(0, 0, 0, 0)),
    radial-gradient(1.5px 1.5px at 240px 50px, rgba(255, 255, 255, 0.7), rgba(0, 0, 0, 0)),
    radial-gradient(1.5px 1.5px at 270px 140px, rgba(255, 255, 255, 0.6), rgba(0, 0, 0, 0)),
    radial-gradient(1.5px 1.5px at 300px 70px, rgba(255, 255, 255, 0.7), rgba(0, 0, 0, 0)),
    radial-gradient(1.5px 1.5px at 330px 130px, rgba(255, 255, 255, 0.6), rgba(0, 0, 0, 0));
  background-repeat: repeat;
  background-size: 200px 200px;
  opacity: 0.5;
  will-change: transform;
  pointer-events: none;
  animation: twinkle 8s ease-in-out infinite alternate;
}

.stars-container::before {
  transform: translateZ(0);
  animation: twinkle 15s ease-in-out infinite alternate-reverse;
}

.stars-container::after {
  background-size: 300px 300px;
  background-position: 50px 50px;
  opacity: 0.3;
  transform: translateZ(0);
  background-image:
    radial-gradient(2px 2px at 100px 100px, rgba(255, 255, 255, 0.5), rgba(0, 0, 0, 0)),
    radial-gradient(2px 2px at 200px 200px, rgba(255, 255, 255, 0.6), rgba(0, 0, 0, 0)),
    radial-gradient(2px 2px at 300px 300px, rgba(255, 255, 255, 0.5), rgba(0, 0, 0, 0));
  animation: twinkle 12s ease-in-out infinite alternate;
}

@keyframes twinkle {
  0% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 0.7;
  }
}



/* Glowing effect for the login button */
.glow-effect {
  position: relative;
  overflow: hidden;
}

.glow-effect::after {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
  opacity: 0;
  transition: opacity 0.5s ease;
}

.glow-effect:hover::after {
  opacity: 1;
}

/* Floating animation for the card */
.floating-card {
  animation: floating 6s ease-in-out infinite;
  box-shadow: 0 10px 30px -10px rgba(0, 0, 0, 0.5);
}

@keyframes floating {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Theme switcher ghost button */
.theme-switcher {
  position: relative;
}

.theme-switcher::after {
  content: "Click to change theme";
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  margin-top: 5px;
}

.theme-switcher:hover::after {
  opacity: 1;
}
