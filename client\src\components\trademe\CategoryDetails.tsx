import React from 'react';
import { TradeMeCategory, CategoryAttribute } from '@/api/tradeMeCategories';
import { 
  Alert,
  AlertTitle,
  AlertDescription,
} from '@/components/ui/alert';
import { 
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AlertTriangle, Info, DollarSign } from 'lucide-react';

interface CategoryDetailsProps {
  category: TradeMeCategory;
}

const CategoryDetails: React.FC<CategoryDetailsProps> = ({ category }) => {
  // Check if we have detailed information
  const hasDetailedInfo = category.fees || category.attributes?.length > 0;
  
  // Format currency
  const formatCurrency = (value: number | undefined) => {
    if (value === undefined) return 'N/A';
    return `$${value.toFixed(2)}`;
  };

  // Format percentage
  const formatPercentage = (value: number | undefined) => {
    if (value === undefined) return 'N/A';
    return `${value.toFixed(2)}%`;
  };

  // Get success fee information
  const getSuccessFeeInfo = () => {
    if (!category.fees) return null;
    
    const { minimumSuccessFee, maximumSuccessFee, successFeeTiers } = category.fees;
    
    if (!successFeeTiers || successFeeTiers.length === 0) {
      return (
        <div className="text-sm">
          <p>No success fee information available</p>
        </div>
      );
    }
    
    return (
      <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span>Minimum Fee:</span>
          <span className="font-medium">{formatCurrency(minimumSuccessFee)}</span>
        </div>
        <div className="flex justify-between text-sm">
          <span>Maximum Fee:</span>
          <span className="font-medium">{formatCurrency(maximumSuccessFee)}</span>
        </div>
        <div className="mt-2">
          <p className="text-xs text-muted-foreground mb-1">Fee Tiers:</p>
          <div className="space-y-1">
            {successFeeTiers.map((tier, index) => (
              <div key={index} className="text-xs flex justify-between">
                <span>
                  {index === 0 
                    ? `Up to ${formatCurrency(tier.minimumTierPrice)}` 
                    : `${formatCurrency(successFeeTiers[index-1].minimumTierPrice)} - ${formatCurrency(tier.minimumTierPrice)}`}
                </span>
                <span className="font-medium">
                  {tier.fixedFee > 0 
                    ? `${formatCurrency(tier.fixedFee)} + ${formatPercentage(tier.percentageFee)}` 
                    : formatPercentage(tier.percentageFee)}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  // Get required attributes
  const getRequiredAttributes = () => {
    if (!category.attributes || category.attributes.length === 0) {
      return null;
    }
    
    const requiredAttributes = category.attributes.filter(attr => attr.isRequiredForSell);
    
    if (requiredAttributes.length === 0) {
      return null;
    }
    
    return (
      <div className="space-y-2">
        <p className="text-sm font-medium">Required Attributes:</p>
        <div className="space-y-1">
          {requiredAttributes.map((attr, index) => (
            <div key={index} className="text-sm">
              <span className="font-medium">{attr.displayName}</span>
              {attr.options && attr.options.length > 0 && (
                <div className="mt-1 flex flex-wrap gap-1">
                  {attr.options.map((option, optIndex) => (
                    <Badge key={optIndex} variant="outline" className="text-xs">
                      {option.display}
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-4">
      {/* Legal Notice */}
      {category.hasLegalNotice && category.legalNotice && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Legal Notice</AlertTitle>
          <AlertDescription>
            {category.legalNotice}
          </AlertDescription>
        </Alert>
      )}
      
      {/* Category Restrictions */}
      {category.isRestricted && (
        <Alert>
          <Info className="h-4 w-4" />
          <AlertTitle>Restricted Category</AlertTitle>
          <AlertDescription>
            This category has special restrictions. Please review TradeMe's terms and conditions before listing.
          </AlertDescription>
        </Alert>
      )}
      
      {/* Category Details */}
      {hasDetailedInfo && (
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base flex items-center">
              <DollarSign className="h-4 w-4 mr-1" />
              Category Details
            </CardTitle>
            <CardDescription>
              Important information about this category
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Success Fee Information */}
            {category.fees && (
              <div>
                <h4 className="text-sm font-medium mb-2">Success Fee</h4>
                {getSuccessFeeInfo()}
              </div>
            )}
            
            {/* Required Attributes */}
            {getRequiredAttributes()}
            
            {/* Other Important Details */}
            <div className="grid grid-cols-2 gap-2 text-xs">
              {category.freePhotoCount !== undefined && (
                <div>
                  <span className="text-muted-foreground">Free Photos:</span>{' '}
                  <span className="font-medium">{category.freePhotoCount}</span>
                </div>
              )}
              {category.maximumPhotoCount !== undefined && (
                <div>
                  <span className="text-muted-foreground">Max Photos:</span>{' '}
                  <span className="font-medium">{category.maximumPhotoCount}</span>
                </div>
              )}
              {category.isFreeToRelist !== undefined && (
                <div>
                  <span className="text-muted-foreground">Free Relisting:</span>{' '}
                  <span className="font-medium">{category.isFreeToRelist ? 'Yes' : 'No'}</span>
                </div>
              )}
              {category.canRelist !== undefined && (
                <div>
                  <span className="text-muted-foreground">Can Relist:</span>{' '}
                  <span className="font-medium">{category.canRelist ? 'Yes' : 'No'}</span>
                </div>
              )}
              {category.defaultDuration !== undefined && (
                <div>
                  <span className="text-muted-foreground">Default Duration:</span>{' '}
                  <span className="font-medium">{category.defaultDuration} days</span>
                </div>
              )}
              {category.canListAuctions !== undefined && (
                <div>
                  <span className="text-muted-foreground">Auctions:</span>{' '}
                  <span className="font-medium">{category.canListAuctions ? 'Allowed' : 'Not Allowed'}</span>
                </div>
              )}
              {category.canListClassifieds !== undefined && (
                <div>
                  <span className="text-muted-foreground">Classifieds:</span>{' '}
                  <span className="font-medium">{category.canListClassifieds ? 'Allowed' : 'Not Allowed'}</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default CategoryDetails;
