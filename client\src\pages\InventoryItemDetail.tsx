import { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  ArrowLeft,
  Loader2,
  Edit,
  Save,
  X,
  Search,
  Trash2
} from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { useToast } from '@/hooks/useToast';
import { useAuth } from '@/contexts/AuthContext';
import { getInventoryItem, updateInventoryItem, deleteInventoryItem } from '@/api/inventory';
import { getItemNotes, addItemNote, deleteItemNote, ItemNote as ItemNoteType } from '@/api/notes';
import { getItemAuditLogs, AuditLog } from '@/api/audit';
import { ItemNote } from '@/components/ItemNote';
import { AuditLogItem } from '@/components/AuditLogItem';

// Define the form schema
const inventoryItemSchema = z.object({
  name: z.string().min(1, "Name is required"),
  category: z.string().min(1, "Category is required"),
  brand: z.string().optional(),
  model: z.string().optional(),
  modelNumber: z.string().optional(),
  releaseYear: z.coerce.number().nullable(),
  lastRRP: z.coerce.number().min(0, "Last RRP must be at least 0"),
  currentPrice: z.coerce.number().min(0, "Current price must be at least 0"),
});

type InventoryItemFormValues = z.infer<typeof inventoryItemSchema>;

export default function InventoryItemDetail() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user } = useAuth();

  const [isLoading, setIsLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Notes state
  const [notes, setNotes] = useState<ItemNoteType[]>([]);
  const [noteContent, setNoteContent] = useState('');
  const [isAddingNote, setIsAddingNote] = useState(false);
  const [isLoadingNotes, setIsLoadingNotes] = useState(false);

  // Audit logs state
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
  const [isLoadingAuditLogs, setIsLoadingAuditLogs] = useState(false);

  // Form definition
  const form = useForm<InventoryItemFormValues>({
    resolver: zodResolver(inventoryItemSchema),
    defaultValues: {
      name: '',
      category: '',
      brand: '',
      model: '',
      modelNumber: '',
      releaseYear: null,
      lastRRP: 0,
      currentPrice: 0,
    },
  });

  // Fetch item details
  useEffect(() => {
    async function fetchItemDetails() {
      try {
        setIsLoading(true);

        if (!id) return;

        // Fetch inventory item
        const itemResponse = await getInventoryItem(id);
        if (itemResponse.success && itemResponse.data) {
          // Set form values
          const item = itemResponse.data;
          form.reset({
            name: item.name || '',
            category: item.category || '',
            brand: item.brand || '',
            model: item.model || '',
            modelNumber: item.modelNumber || '',
            releaseYear: item.releaseYear || null,
            lastRRP: item.lastRRP || 0,
            currentPrice: item.currentPrice || 0,
          });
        }
      } catch (error: any) {
        console.error("Error fetching item details:", error);
        toast({
          variant: "destructive",
          title: "Error",
          description: error.message || "Failed to fetch item details"
        });
      } finally {
        setIsLoading(false);
      }
    }

    fetchItemDetails();
  }, [id, form, toast]);

  // Fetch notes and audit logs on component mount
  useEffect(() => {
    if (!id) return;

    fetchNotes();
    fetchAuditLogs();
  }, [id]);

  // Fetch notes
  const fetchNotes = async () => {
    if (!id) return;

    try {
      setIsLoadingNotes(true);
      const response = await getItemNotes(id);
      if (response.success) {
        setNotes(response.data);
      }
    } catch (error: any) {
      console.error("Error fetching notes:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to fetch notes"
      });
    } finally {
      setIsLoadingNotes(false);
    }
  };

  // Fetch audit logs
  const fetchAuditLogs = async () => {
    if (!id) return;

    try {
      setIsLoadingAuditLogs(true);
      const response = await getItemAuditLogs(id);
      if (response.success) {
        setAuditLogs(response.data);
      }
    } catch (error: any) {
      console.error("Error fetching audit logs:", error);
      // Don't show error toast for permission issues (403)
      if (!error.message.includes("permission") && !error.message.includes("403")) {
        toast({
          variant: "destructive",
          title: "Error",
          description: error.message || "Failed to fetch audit logs"
        });
      }
      // Set empty audit logs when permission is denied
      setAuditLogs([]);
    } finally {
      setIsLoadingAuditLogs(false);
    }
  };

  // Handle form submission
  const onSubmit = async (data: InventoryItemFormValues) => {
    if (!id) return;

    try {
      setIsSubmitting(true);
      const formData = {...data};

      const response = await updateInventoryItem(id, formData);

      if (response.success) {
        toast({
          title: "Success",
          description: "Item updated successfully"
        });
        setIsEditing(false);

        // Refresh audit logs since we've made changes
        fetchAuditLogs();
      }
    } catch (error: any) {
      console.error("Error updating item:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to update item"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Add a note
  const handleAddNote = async () => {
    if (!id || !noteContent.trim()) return;

    try {
      setIsAddingNote(true);
      const response = await addItemNote(id, noteContent);

      if (response.success) {
        toast({
          title: "Success",
          description: "Note added successfully"
        });
        setNoteContent('');
        fetchNotes();
      }
    } catch (error: any) {
      console.error("Error adding note:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to add note"
      });
    } finally {
      setIsAddingNote(false);
    }
  };

  // Delete a note
  const handleDeleteNote = async (noteId: string) => {
    try {
      const response = await deleteItemNote(noteId);

      if (response.success) {
        toast({
          title: "Success",
          description: "Note deleted successfully"
        });
        // Update the notes list
        setNotes(prevNotes => prevNotes.filter(note => note._id !== noteId));
      }
    } catch (error: any) {
      console.error("Error deleting note:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to delete note"
      });
      throw error; // Re-throw for the component to handle
    }
  };

  // Delete an item
  const handleDeleteItem = async () => {
    if (!id) return;

    try {
      setIsDeleting(true);
      const response = await deleteInventoryItem(id);

      if (response.success) {
        toast({
          title: "Success",
          description: "Item deleted successfully"
        });
        navigate('/inventory');
      }
    } catch (error: any) {
      console.error("Error deleting item:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to delete item"
      });
    } finally {
      setIsDeleting(false);
      setDeleteDialogOpen(false);
    }
  };

  // Check if user can edit
  const canEdit = user?.role === 'admin' || user?.role === 'manager';

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="container py-6">
      <div className="flex items-center mb-6">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => navigate('/inventory')}
          className="mr-2"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Pricing Database
        </Button>

      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        {/* Item Information Card - Left side */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Item Information</CardTitle>
              <CardDescription>All details about this item</CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              {/* Search buttons */}
              <div className="flex items-center space-x-1 mr-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const searchQuery = encodeURIComponent(form.getValues('model') || form.getValues('name'));
                    window.open(`https://www.google.com/search?q=${searchQuery}`, '_blank');
                  }}
                  title="Search on Google"
                  className="px-2"
                >
                  <img
                    src="/img/icons/google-logo.ico"
                    alt="Google"
                    className="h-4 w-4"
                  />
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const searchQuery = encodeURIComponent(form.getValues('model') || form.getValues('name'));
                    window.open(`https://www.bidbud.co.nz/search?search_string=${searchQuery}&condition=Used`, '_blank');
                  }}
                  title="Search on TradeMe/BidBud"
                  className="px-2"
                >
                  <img
                    src="/img/icons/trademe-icon.png"
                    alt="TradeMe"
                    className="h-4 w-4"
                  />
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const searchQuery = encodeURIComponent(form.getValues('model') || form.getValues('name'));
                    window.open(`https://pricespy.co.nz/search?search=${searchQuery}`, '_blank');
                  }}
                  title="Search on PriceSpy"
                  className="px-2"
                >
                  <img
                    src="/img/icons/pricespy-logo.ico"
                    alt="PriceSpy"
                    className="h-4 w-4"
                  />
                </Button>
              </div>

              {canEdit && !isEditing && (
                <Button
                  onClick={() => setIsEditing(true)}
                  className="ml-2"
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Item
                </Button>
              )}
              {canEdit && !isEditing && (
                <Button
                  variant="destructive"
                  onClick={() => setDeleteDialogOpen(true)}
                  className="ml-2"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Item
                </Button>
              )}
              {isEditing && (
                <div className="flex">
                  <Button
                    variant="ghost"
                    onClick={() => setIsEditing(false)}
                    className="ml-2"
                  >
                    <X className="h-4 w-4 mr-2" />
                    Cancel
                  </Button>
                  <Button
                    onClick={form.handleSubmit(onSubmit)}
                    disabled={isSubmitting}
                    className="ml-2"
                  >
                    {isSubmitting ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Save className="h-4 w-4 mr-2" />
                    )}
                    Save
                  </Button>
                </div>
              )}
            </div>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <div className="grid gap-4">


                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="brand"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Brand</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            disabled={!isEditing}
                            placeholder="Brand"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="category"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Category</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            disabled={!isEditing}
                            placeholder="Category"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="model"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Model Name</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            disabled={!isEditing}
                            placeholder="Model name"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="modelNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Model Number</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            disabled={!isEditing}
                            placeholder="Model number"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                  <FormField
                    control={form.control}
                    name="releaseYear"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Release Year</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="YYYY"
                            disabled={!isEditing}
                            value={field.value !== null ? field.value : ''}
                            onChange={(e) => {
                              const value = e.target.value !== '' ? Number(e.target.value) : null;
                              field.onChange(value);
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="lastRRP"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Last RRP ($)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="0.00"
                            disabled={!isEditing}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="currentPrice"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Sell Price ($)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="0.00"
                            disabled={!isEditing}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </Form>
          </CardContent>
        </Card>

        {/* Item History Card - Right side - Shown for all users */}
        <Card>
          <CardHeader>
            <CardTitle>Item History</CardTitle>
            <CardDescription>Audit trail of changes to this item</CardDescription>
          </CardHeader>
          <CardContent className="max-h-[400px] overflow-y-auto">
            {isLoadingAuditLogs ? (
              <div className="flex justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
            ) : auditLogs.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No history available for this item.
              </div>
            ) : (
              <div className="space-y-3">
                {auditLogs.map((log) => (
                  <AuditLogItem key={log._id} log={log} />
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Notes Section - Full width below */}
      <Card>
        <CardHeader>
          <CardTitle>Item Notes</CardTitle>
          <CardDescription>Notes and comments about this item</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="mb-4">
            <Textarea
              placeholder="Add a note about this item..."
              value={noteContent}
              onChange={(e) => setNoteContent(e.target.value)}
              className="resize-none mb-2"
              rows={3}
            />
            <Button
              onClick={handleAddNote}
              disabled={isAddingNote || !noteContent.trim()}
              className="w-full"
            >
              {isAddingNote ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : null}
              Add Note
            </Button>
          </div>

          <Separator className="my-4" />

          {isLoadingNotes ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : notes.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No notes yet. Add the first note above.
            </div>
          ) : (
            <div className="space-y-3">
              {notes.map((note) => (
                <ItemNote
                  key={note._id}
                  note={note}
                  onDelete={handleDeleteNote}
                />
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the inventory
              item and all its associated data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault();
                handleDeleteItem();
              }}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Trash2 className="h-4 w-4 mr-2" />
              )}
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}