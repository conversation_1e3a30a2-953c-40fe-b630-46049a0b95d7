# Authentication Security Test Plan

## Test Cases for ReAuth Modal Security Fixes

### 1. ReAuth Modal Non-Dismissible Test
**Objective**: Verify that the ReAuth modal cannot be dismissed by users

**Steps**:
1. Log into the application
2. Wait for idle timeout (or trigger manually via dev tools)
3. Verify ReAuth modal appears
4. Try to dismiss modal by:
   - Clicking outside the modal
   - Pressing Escape key
   - Clicking any close buttons (should not exist)
5. Verify modal remains open and blocks interaction

**Expected Result**: Modal cannot be dismissed and blocks all app interaction

### 2. Session Expiration Overlay Test
**Objective**: Verify that when session expires, users cannot interact with the app

**Steps**:
1. Log into the application
2. Trigger session expiration
3. Verify overlay appears blocking interaction
4. Try to click on app elements behind the modal
5. Verify ReAuth modal is shown

**Expected Result**: App is completely blocked until re-authentication

### 3. API Authentication Failure Test
**Objective**: Verify that API authentication failures trigger ReAuth modal

**Steps**:
1. Log into the application
2. Manually expire tokens in localStorage or server
3. Make an API call (navigate to a page that loads data)
4. Verify ReAuth modal appears when API returns 401

**Expected Result**: ReAuth modal appears on API authentication failure

### 4. Idle Timer Activity Tracking Test
**Objective**: Verify that user activity properly resets the idle timer

**Steps**:
1. Log into the application
2. Monitor idle timer (via dev tools)
3. Perform various activities:
   - Mouse movements
   - Keyboard input
   - API calls
   - Page navigation
4. Verify timer resets with each activity

**Expected Result**: Timer resets on all user activities

### 5. Successful Re-authentication Test
**Objective**: Verify that successful re-authentication restores normal app function

**Steps**:
1. Trigger ReAuth modal (idle timeout or API failure)
2. Enter correct password
3. Submit re-authentication
4. Verify modal closes
5. Verify overlay disappears
6. Verify app functionality is restored
7. Verify idle timer is reset

**Expected Result**: App returns to normal operation after successful re-auth

### 6. Failed Re-authentication Test
**Objective**: Verify that failed re-authentication shows appropriate error

**Steps**:
1. Trigger ReAuth modal
2. Enter incorrect password
3. Submit re-authentication
4. Verify error message appears
5. Verify modal remains open
6. Verify app remains blocked

**Expected Result**: Error shown, modal stays open, app remains blocked

### 7. Logout from ReAuth Modal Test
**Objective**: Verify that logout button works correctly

**Steps**:
1. Trigger ReAuth modal
2. Click "Logout" button
3. Verify user is redirected to login page
4. Verify all session data is cleared

**Expected Result**: User is logged out and redirected to login

## Security Verification Checklist

- [ ] ReAuth modal cannot be dismissed with Escape key
- [ ] ReAuth modal cannot be dismissed by clicking outside
- [ ] Session expiration blocks all app interaction
- [ ] API authentication failures trigger ReAuth modal
- [ ] Idle timer tracks comprehensive user activity
- [ ] API calls reset idle timer
- [ ] Successful re-auth restores app functionality
- [ ] Failed re-auth shows error and keeps modal open
- [ ] Logout from ReAuth modal works correctly
- [ ] Session state is properly managed throughout
- [ ] No way to bypass authentication requirements
- [ ] User data is hidden when session expired
- [ ] Authentication state reflects session expiration

## Manual Testing Commands

```javascript
// Test idle timer manually in browser console
window.authContext = document.querySelector('[data-testid="auth-provider"]');

// Trigger idle timeout manually
if (window.authContext && window.authContext.idleTimer) {
  window.authContext.idleTimer.onTimeout();
}

// Check session state
console.log('Auth state:', {
  isAuthenticated: window.authContext?.isAuthenticated,
  sessionExpired: window.authContext?.sessionExpired,
  showReAuthModal: window.authContext?.showReAuthModal
});

// Clear tokens to test API failure
localStorage.removeItem('accessToken');
localStorage.removeItem('refreshToken');
```
