const express = require('express');
const router = express.Router();
const { requireUser } = require('./middleware/auth');
const tradeMeItemsSyncService = require('../services/tradeMeItemsSyncService');
const TrademeImportLog = require('../models/TrademeImportLog');
const TradeMeItems = require('../models/TradeMeItems');
const TradeMeItemAudit = require('../models/TradeMeItemAudit');
const Location = require('../models/Location');

/**
 * @route POST /api/trademe/import/listings
 * @description Import TradeMe listings
 * @access Private (Admin, Manager)
 */
router.post('/listings', requireUser, async (req, res) => {
  try {
    // Check if user has permission (admin or manager)
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to import TradeMe listings',
      });
    }

    // Get parameters from request body or use defaults
    const { maxPages = 5, type = 'active' } = req.body;

    // Start the import
    const result = await tradeMeItemsSyncService.importUserListings({
      maxPagesPerEndpoint: parseInt(maxPages),
      user: req.user,
      type: type // Pass the type parameter to determine which listings to import
    });

    return res.status(200).json(result);
  } catch (error) {
    console.error('Error importing TradeMe listings:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/trademe/import/logs
 * @description Get import logs
 * @access Private (Admin, Manager)
 */
router.get('/logs', requireUser, async (req, res) => {
  try {
    // Check if user has permission (admin or manager)
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to view import logs',
      });
    }

    // Get parameters from query or use defaults
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Get total count
    const total = await TrademeImportLog.countDocuments();

    // Get logs with pagination
    const logs = await TrademeImportLog.find()
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate('initiatedBy', 'username fullName')
      .lean();

    return res.status(200).json({
      success: true,
      logs,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error getting import logs:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/trademe/import/logs/:id
 * @description Get import log by ID
 * @access Private (Admin, Manager)
 */
router.get('/logs/:id', requireUser, async (req, res) => {
  try {
    // Allow any authenticated user to access import logs
    // This is needed for the import wizard to work

    // Get log by ID
    const log = await TrademeImportLog.findById(req.params.id)
      .populate('initiatedBy', 'username fullName')
      .lean();

    if (!log) {
      return res.status(404).json({
        success: false,
        error: 'Import log not found'
      });
    }

    return res.status(200).json({
      success: true,
      log
    });
  } catch (error) {
    console.error('Error getting import log:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/trademe/import/logs/:id/items
 * @description Get items from an import log
 * @access Private
 */
router.get('/logs/:id/items', requireUser, async (req, res) => {
  try {
    // Allow any authenticated user to access import log items
    // This is needed for the import wizard to work

    const { page = 1, limit = 50 } = req.query;
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);

    // Find the import log
    const importLog = await TrademeImportLog.findById(req.params.id);
    if (!importLog) {
      return res.status(404).json({
        success: false,
        error: 'Import log not found'
      });
    }

    // Calculate pagination
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = pageNum * limitNum;

    // Get items for the current page
    const items = importLog.fetchedItems.slice(startIndex, endIndex);

    // Get the total count
    const total = importLog.fetchedItems.length;

    // Add processing status to each item
    const itemsWithStatus = items.map((item, index) => {
      const actualIndex = startIndex + index;
      const isSaved = importLog.savedItemIds.some(id => id.toString() === actualIndex.toString());
      const isSkipped = importLog.skippedItemIndices.includes(actualIndex);

      let status = 'pending';
      if (isSaved) status = 'saved';
      if (isSkipped) status = 'skipped';

      return {
        ...item,
        index: actualIndex,
        status
      };
    });

    return res.json({
      success: true,
      items: itemsWithStatus,
      pagination: {
        total,
        page: pageNum,
        pages: Math.ceil(total / limitNum),
        limit: limitNum
      }
    });
  } catch (error) {
    console.error('Error getting import log items:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/trademe/import/items
 * @description Get imported items
 * @access Private
 */
router.get('/items', requireUser, async (req, res) => {
  try {
    // Get parameters from query or use defaults
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Build filter query
    const filter = { importedAt: { $exists: true } };

    // Add additional filters if provided
    if (req.query.stockCode) {
      filter.stockCode = { $regex: req.query.stockCode, $options: 'i' };
    }

    if (req.query.title) {
      filter.title = { $regex: req.query.title, $options: 'i' };
    }

    if (req.query.location) {
      filter.location = { $regex: req.query.location, $options: 'i' };
    }

    if (req.query.status && req.query.status !== 'all') {
      filter.status = req.query.status;
    }

    // Get total count
    // Create a modified query that handles 'Imported' string values
    const countFilter = { ...filter };

    // Handle fields that might contain 'Imported' string
    const userReferenceFields = ['createdBy', 'buyer', 'soldStatusUpdatedBy', 'importedBy', 'removedBy'];

    userReferenceFields.forEach(field => {
      if (countFilter[field]) {
        // If the field is 'Imported', use $in to match either the string or an ObjectId
        if (countFilter[field] === 'Imported') {
          countFilter[field] = { $in: ['Imported'] };
        }
      }
    });

    const total = await TradeMeItems.countDocuments(countFilter);

    // Get items with pagination
    // Create a modified query that handles 'Imported' string values
    const findFilter = { ...filter };

    // Handle fields that might contain 'Imported' string
    userReferenceFields.forEach(field => {
      if (findFilter[field]) {
        // If the field is 'Imported', use $in to match either the string or an ObjectId
        if (findFilter[field] === 'Imported') {
          findFilter[field] = { $in: ['Imported'] };
        }
      }
    });

    const items = await TradeMeItems.find(findFilter)
      .sort({ importedAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate({
        path: 'importedBy',
        select: 'username fullName',
        // Only populate if importedBy is a valid ObjectId (not a string)
        match: { _id: { $type: 'objectId' } }
      })
      .populate('locationId', 'name')
      .lean();

    // Post-process items to handle string values
    items.forEach(item => {
      if (item.importedBy === 'Imported') {
        item.importedBy = { username: 'Imported', fullName: 'Imported' };
      }
    });

    // Get all locations for dropdown
    const locations = await Location.find().select('_id name').lean();

    return res.status(200).json({
      success: true,
      items,
      locations,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error getting imported items:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route POST /api/trademe/import/save-item
 * @description Save a single item from the import log to the database
 * @access Private
 */
router.post('/save-item', requireUser, async (req, res) => {
  try {
    const { logId, itemIndex, itemData } = req.body;

    if (!logId || itemIndex === undefined || !itemData) {
      return res.status(400).json({
        success: false,
        error: 'Missing required parameters: logId, itemIndex, and itemData are required'
      });
    }

    // Find the import log
    const importLog = await TrademeImportLog.findById(logId);
    if (!importLog) {
      return res.status(404).json({
        success: false,
        error: 'Import log not found'
      });
    }

    // Check if the item index is valid
    if (itemIndex < 0 || itemIndex >= importLog.fetchedItems.length) {
      return res.status(400).json({
        success: false,
        error: 'Invalid item index'
      });
    }

    // Check if the item has already been saved or skipped
    if (importLog.savedItemIds.some(id => id.toString() === itemIndex.toString()) ||
        importLog.skippedItemIndices.includes(itemIndex)) {
      return res.status(400).json({
        success: false,
        error: 'This item has already been processed'
      });
    }

    // Get the original item data from the import log
    const originalItemData = importLog.fetchedItems[itemIndex];

    // Merge the original data with the updated data from the client
    const mergedItemData = {
      ...originalItemData,
      ...itemData
    };

    // Create a new TradeMeItems document
    const newItem = new TradeMeItems(mergedItemData);

    // Save the item to the database
    await newItem.save();

    // Create an audit entry
    const auditEntry = new TradeMeItemAudit({
      itemId: newItem._id,
      trademeListingId: newItem.currentListingId,
      action: 'import',
      details: {
        listingId: newItem.currentListingId,
        title: newItem.title || 'Unknown'
      },
      performedBy: req.user._id
    });

    await auditEntry.save();

    // Add the item ID to the savedItemIds array in the import log
    importLog.savedItemIds.push(newItem._id);
    await importLog.save();

    return res.status(200).json({
      success: true,
      item: newItem
    });
  } catch (error) {
    console.error('Error saving imported item:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route POST /api/trademe/import/skip-item
 * @description Mark an item in the import log as skipped
 * @access Private
 */
router.post('/skip-item', requireUser, async (req, res) => {
  try {
    const { logId, itemIndex } = req.body;

    if (!logId || itemIndex === undefined) {
      return res.status(400).json({
        success: false,
        error: 'Missing required parameters: logId and itemIndex are required'
      });
    }

    // Find the import log
    const importLog = await TrademeImportLog.findById(logId);
    if (!importLog) {
      return res.status(404).json({
        success: false,
        error: 'Import log not found'
      });
    }

    // Check if the item index is valid
    if (itemIndex < 0 || itemIndex >= importLog.fetchedItems.length) {
      return res.status(400).json({
        success: false,
        error: 'Invalid item index'
      });
    }

    // Check if the item has already been saved or skipped
    if (importLog.savedItemIds.some(id => id.toString() === itemIndex.toString()) ||
        importLog.skippedItemIndices.includes(itemIndex)) {
      return res.status(400).json({
        success: false,
        error: 'This item has already been processed'
      });
    }

    // Add the item index to the skippedItemIndices array
    importLog.skippedItemIndices.push(itemIndex);
    await importLog.save();

    return res.status(200).json({
      success: true
    });
  } catch (error) {
    console.error('Error skipping imported item:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/trademe/import/items-needing-update
 * @description Get imported items that need updating with filtering and pagination
 * @access Private
 */
router.get('/items-needing-update', requireUser, async (req, res) => {
  try {
    const { page = 1, limit = 20 } = req.query;

    // Build query
    const query = {
      importedAt: { $exists: true },
      $or: [
        { stockCode: { $exists: false } },
        { stockCode: '' },
        { stockCode: null }
      ]
    };

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Execute query with pagination
    // Create a modified query that handles 'Imported' string values
    const findQuery = { ...query };

    // Handle fields that might contain 'Imported' string
    // Use a different variable name to avoid conflict
    const userRefFields = ['createdBy', 'buyer', 'soldStatusUpdatedBy', 'importedBy', 'removedBy'];

    userRefFields.forEach(field => {
      if (findQuery[field]) {
        // If the field is 'Imported', use $in to match either the string or an ObjectId
        if (findQuery[field] === 'Imported') {
          findQuery[field] = { $in: ['Imported'] };
        }
      }
    });

    const items = await TradeMeItems.find(findQuery)
      .sort({ importedAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .populate({
        path: 'createdBy',
        select: 'username fullName',
        // Only populate if createdBy is a valid ObjectId (not a string)
        match: { _id: { $type: 'objectId' } }
      })
      .populate('locationId', 'name')
      .lean();

    // Post-process items to handle string values
    items.forEach(item => {
      if (item.createdBy === 'Imported') {
        item.createdBy = { username: 'Imported', fullName: 'Imported' };
      }
    });

    // Get total count for pagination
    // Create a modified query that handles 'Imported' string values
    const countQuery = { ...query };

    // Handle fields that might contain 'Imported' string
    // Use the same variable from above
    userRefFields.forEach(field => {
      if (countQuery[field]) {
        // If the field is 'Imported', use $in to match either the string or an ObjectId
        if (countQuery[field] === 'Imported') {
          countQuery[field] = { $in: ['Imported'] };
        }
      }
    });

    const total = await TradeMeItems.countDocuments(countQuery);

    return res.json({
      success: true,
      items,
      pagination: {
        total,
        page: parseInt(page),
        pages: Math.ceil(total / parseInt(limit)),
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Error getting imported items needing update:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route PUT /api/trademe/import/items/:id
 * @description Update imported item
 * @access Private
 */
router.put('/items/:id', requireUser, async (req, res) => {
  try {
    // Find the item
    const item = await TradeMeItems.findById(req.params.id);

    if (!item) {
      return res.status(404).json({
        success: false,
        error: 'Item not found'
      });
    }

    // Check if the item was imported
    if (!item.importedAt) {
      return res.status(400).json({
        success: false,
        error: 'This item was not imported and cannot be updated through this endpoint'
      });
    }

    // Update allowed fields
    const allowedFields = [
      'stockCode',
      'cost',
      'location',
      'locationId',
      'buyer',
      'isFaulty',
      'faultCategories',
      'faultDescription',
      'relistSettings',
      'notes'
    ];

    // Update only allowed fields
    allowedFields.forEach(field => {
      if (req.body[field] !== undefined) {
        item[field] = req.body[field];
      }
    });

    // Save the updated item
    await item.save();

    return res.status(200).json({
      success: true,
      item
    });
  } catch (error) {
    console.error('Error updating imported item:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/trademe/import/check-stockcode
 * @description Check if a stock code is already in use by another item
 * @access Private
 */
router.get('/check-stockcode', requireUser, async (req, res) => {
  try {
    const { stockCode, currentItemId } = req.query;

    if (!stockCode) {
      return res.status(400).json({
        success: false,
        error: 'Stock code is required'
      });
    }

    // Build query to check for duplicate stock code
    const query = { stockCode };

    // Exclude current item from check if provided
    if (currentItemId) {
      query._id = { $ne: currentItemId };
    }

    // Check if any other item has this stock code
    const existingItem = await TradeMeItems.findOne(query);

    return res.json({
      success: true,
      isDuplicate: !!existingItem,
      item: existingItem ? {
        _id: existingItem._id,
        title: existingItem.title,
        stockCode: existingItem.stockCode
      } : null
    });
  } catch (error) {
    console.error('Error checking duplicate stock code:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route PUT /api/trademe/import/items/batch
 * @description Batch update imported items
 * @access Private
 */
router.put('/items/batch', requireUser, async (req, res) => {
  try {
    const { ids, data } = req.body;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'No item IDs provided'
      });
    }

    if (!data || Object.keys(data).length === 0) {
      return res.status(400).json({
        success: false,
        error: 'No update data provided'
      });
    }

    // Allowed fields for batch update
    const allowedFields = [
      'stockCode',
      'cost',
      'location',
      'locationId',
      'isFaulty',
      'faultCategories',
      'faultDescription',
      'relistSettings'
    ];

    // Filter out disallowed fields
    const updateData = {};
    allowedFields.forEach(field => {
      if (data[field] !== undefined) {
        updateData[field] = data[field];
      }
    });

    // Update the items
    const result = await TradeMeItems.updateMany(
      { _id: { $in: ids }, importedAt: { $exists: true } },
      { $set: updateData }
    );

    return res.status(200).json({
      success: true,
      message: `Updated ${result.modifiedCount} items`,
      modifiedCount: result.modifiedCount
    });
  } catch (error) {
    console.error('Error batch updating imported items:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = router;
