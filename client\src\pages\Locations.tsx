import { useEffect, useState } from 'react';
import { getLocations, Location, seedLocations, deleteLocation, toggleLocationStatus } from '@/api/locations';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { MapPin, Phone, Clock, Trash2, Power, PowerOff } from 'lucide-react';
import { LocationForm } from '@/components/LocationForm';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/useToast';
import { useAuth } from '@/contexts/AuthContext';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

export function Locations() {
  const [locations, setLocations] = useState<Location[]>([]);
  const [loading, setLoading] = useState(true);
  const [open, setOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [locationToDelete, setLocationToDelete] = useState<Location | null>(null);
  const { toast } = useToast();
  const { user } = useAuth();
  const canManageLocations = user?.role === 'admin' || user?.role === 'manager';
  const isAdmin = user?.role === 'admin';

  const fetchLocations = async () => {
    setLoading(true);
    try {
      const data = await getLocations();
      setLocations(data.locations);
    } catch (error) {
      console.error('Error fetching locations:', error);
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSeedLocations = async () => {
    try {
      const result = await seedLocations();
      toast({
        title: 'Success',
        description: result.message,
      });
      fetchLocations();
    } catch (error) {
      console.error('Error seeding locations:', error);
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    }
  };

  const handleDeleteClick = (location: Location) => {
    setLocationToDelete(location);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!locationToDelete) return;

    try {
      await deleteLocation(locationToDelete._id);
      toast({
        title: 'Success',
        description: 'Location deleted successfully',
      });
      fetchLocations();
    } catch (error) {
      console.error('Error deleting location:', error);
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    } finally {
      setDeleteDialogOpen(false);
      setLocationToDelete(null);
    }
  };

  const handleToggleStatus = async (location: Location) => {
    try {
      const newStatus = !location.isActive;
      await toggleLocationStatus(location._id, newStatus);
      toast({
        title: 'Success',
        description: `Location ${newStatus ? 'enabled' : 'disabled'} successfully`,
      });
      fetchLocations();
    } catch (error) {
      console.error('Error toggling location status:', error);
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    }
  };

  useEffect(() => {
    fetchLocations();
  }, []);

  if (loading) {
    return <div className="flex justify-center items-center h-64">Loading locations...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Store Locations</h1>
        {canManageLocations && (
          <div className="flex gap-2">
            <Dialog open={open} onOpenChange={setOpen}>
              <DialogTrigger asChild>
                <Button>Add Location</Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[425px]">
                <LocationForm onSuccess={() => {
                  setOpen(false);
                  fetchLocations();
                }} />
              </DialogContent>
            </Dialog>

            {locations.length === 0 && (
              <Button variant="outline" onClick={handleSeedLocations}>
                Seed Locations
              </Button>
            )}
          </div>
        )}
      </div>

      {locations.length === 0 ? (
        <div className="text-center p-8 border rounded-lg">
          <p className="text-xl mb-4">No locations found</p>
          {canManageLocations && (
            <p className="text-muted-foreground">
              Add your first location.
            </p>
          )}
        </div>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {locations.map((location) => (
            <Card key={location._id} className={`hover:shadow-lg transition-shadow ${!location.isActive ? 'opacity-70' : ''}`}>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>{location.name}</span>
                  <Badge variant={location.isActive ? 'default' : 'destructive'}>
                    {location.isActive ? 'Active' : 'Inactive'}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-2 text-muted-foreground">
                  <MapPin className="h-4 w-4" />
                  <span>{location.address || 'No address provided'}</span>
                </div>
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Phone className="h-4 w-4" />
                  <span>{location.phone || 'No phone provided'}</span>
                </div>
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Clock className="h-4 w-4" />
                  <span>Created: {new Date(location.createdAt).toLocaleDateString()}</span>
                </div>
              </CardContent>
              {isAdmin && (
                <CardFooter className="pt-2 pb-4 flex justify-between">
                  <Button
                    variant="outline"
                    size="sm"
                    className={`${location.isActive ? 'text-amber-500' : 'text-green-500'}`}
                    onClick={() => handleToggleStatus(location)}
                  >
                    {location.isActive ? (
                      <>
                        <PowerOff className="h-4 w-4 mr-2" />
                        Disable
                      </>
                    ) : (
                      <>
                        <Power className="h-4 w-4 mr-2" />
                        Enable
                      </>
                    )}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-destructive hover:bg-destructive hover:text-destructive-foreground"
                    onClick={() => handleDeleteClick(location)}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </Button>
                </CardFooter>
              )}
            </Card>
          ))}
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the location "{locationToDelete?.name}".
              This action cannot be undone.
              <p className="mt-2 font-medium text-amber-500">
                Consider disabling the location instead if you might need it again in the future.
              </p>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteConfirm} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}