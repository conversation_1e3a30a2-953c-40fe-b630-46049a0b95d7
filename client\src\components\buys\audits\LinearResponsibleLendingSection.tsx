import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Shield, Users, AlertTriangle, FileCheck, CheckCircle, XCircle, AlertCircle } from 'lucide-react';

interface LinearResponsibleLendingSectionProps {
  control: any;
  disabled?: boolean;
}

/**
 * Linear responsible lending section component for pawn audits
 * Shows all responsible lending checks in a single flow
 */
export function LinearResponsibleLendingSection({ control, disabled = false }: LinearResponsibleLendingSectionProps) {
  // Common fail reasons for POL suitability
  const polFailReasons = [
    { id: 'insufficient_pol', label: 'Insufficient proof of ownership' },
    { id: 'invalid_pol', label: 'Invalid proof of ownership' },
    { id: 'suspicious_pol', label: 'Suspicious proof of ownership' },
    { id: 'no_pol_check', label: 'No POL check conducted' },
  ];

  // Essential item categories
  const essentialCategories = [
    { id: 'household_appliances', label: 'Household Appliances' },
    { id: 'tools_work', label: 'Tools for Work' },
    { id: 'transportation', label: 'Transportation' },
    { id: 'communication', label: 'Communication Devices' },
    { id: 'medical_equipment', label: 'Medical Equipment' },
    { id: 'other', label: 'Other Essential Items' },
  ];

  // Function to render status badge
  const renderStatusBadge = (status: string) => {
    if (status === 'pass') {
      return (
        <Badge className="bg-green-500 hover:bg-green-600 flex items-center gap-1">
          <CheckCircle className="h-3 w-3" />
          Pass
        </Badge>
      );
    } else if (status === 'fail') {
      return (
        <Badge className="bg-red-500 hover:bg-red-600 flex items-center gap-1">
          <XCircle className="h-3 w-3" />
          Fail
        </Badge>
      );
    } else if (status === 'not_applicable') {
      return (
        <Badge variant="secondary" className="flex items-center gap-1">
          N/A
        </Badge>
      );
    } else {
      return (
        <Badge variant="outline" className="flex items-center gap-1">
          <AlertCircle className="h-3 w-3" />
          Not Assessed
        </Badge>
      );
    }
  };

  // Function to render compliance badge
  const renderComplianceBadge = (compliance: string) => {
    if (compliance === 'compliant') {
      return (
        <Badge className="bg-green-500 hover:bg-green-600 flex items-center gap-1">
          <CheckCircle className="h-3 w-3" />
          Compliant
        </Badge>
      );
    } else if (compliance === 'non_compliant') {
      return (
        <Badge className="bg-red-500 hover:bg-red-600 flex items-center gap-1">
          <XCircle className="h-3 w-3" />
          Non-Compliant
        </Badge>
      );
    } else if (compliance === 'not_applicable') {
      return (
        <Badge variant="secondary" className="flex items-center gap-1">
          N/A
        </Badge>
      );
    } else {
      return (
        <Badge variant="outline" className="flex items-center gap-1">
          <AlertCircle className="h-3 w-3" />
          Not Assessed
        </Badge>
      );
    }
  };

  return (
    <div className="space-y-6">
      {/* POL Suitability Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <FileCheck className="h-5 w-5 text-primary" />
              <CardTitle>Proof of Ownership & Legitimacy (POL)</CardTitle>
            </div>
            <FormField
              control={control}
              name="polSuitability.status"
              render={({ field }) => renderStatusBadge(field.value)}
            />
          </div>
          <CardDescription>
            Assess the adequacy and validity of proof of ownership documentation
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <FormField
            control={control}
            name="polSuitability.polText"
            render={({ field }) => (
              <FormItem>
                <FormLabel>POL Documentation Details</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Describe the proof of ownership documentation provided"
                    className="min-h-[80px]"
                    {...field}
                    disabled={disabled}
                  />
                </FormControl>
                <FormDescription>
                  Detail what documentation was provided as proof of ownership
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="polSuitability.status"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>POL Assessment</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    value={field.value}
                    className="flex flex-row space-x-6"
                    disabled={disabled}
                  >
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="pass" id="polPass" />
                      </FormControl>
                      <FormLabel className="font-normal cursor-pointer" htmlFor="polPass">
                        Pass
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="fail" id="polFail" />
                      </FormControl>
                      <FormLabel className="font-normal cursor-pointer" htmlFor="polFail">
                        Fail
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="not_assessed" id="polNotAssessed" />
                      </FormControl>
                      <FormLabel className="font-normal cursor-pointer" htmlFor="polNotAssessed">
                        Not Assessed
                      </FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Show fail reasons if status is "fail" */}
          <FormField
            control={control}
            name="polSuitability.status"
            render={({ field }) => (
              field.value === 'fail' && (
                <FormField
                  control={control}
                  name="polSuitability.failReasons"
                  render={({ field: failReasonsField }) => (
                    <FormItem>
                      <FormLabel>Fail Reasons</FormLabel>
                      <FormDescription>Select all that apply</FormDescription>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 border rounded-md p-3">
                        {polFailReasons.map((reason) => (
                          <FormItem
                            key={reason.id}
                            className="flex flex-row items-start space-x-3 space-y-0 p-2 hover:bg-accent rounded-md"
                          >
                            <FormControl>
                              <Checkbox
                                checked={failReasonsField.value?.includes(reason.id)}
                                onCheckedChange={(checked) => {
                                  const currentValue = failReasonsField.value || [];
                                  if (checked) {
                                    failReasonsField.onChange([...currentValue, reason.id]);
                                  } else {
                                    failReasonsField.onChange(
                                      currentValue.filter((value: string) => value !== reason.id)
                                    );
                                  }
                                }}
                                disabled={disabled}
                              />
                            </FormControl>
                            <FormLabel className="font-normal cursor-pointer">
                              {reason.label}
                            </FormLabel>
                          </FormItem>
                        ))}
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )
            )}
          />

          <FormField
            control={control}
            name="polSuitability.notes"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Notes</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Enter any additional notes about POL assessment"
                    className="min-h-[80px]"
                    {...field}
                    disabled={disabled}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </CardContent>
      </Card>

      {/* Customer Understanding Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Users className="h-5 w-5 text-primary" />
              <CardTitle>Customer Understanding</CardTitle>
            </div>
            <FormField
              control={control}
              name="customerUnderstanding.status"
              render={({ field }) => renderStatusBadge(field.value)}
            />
          </div>
          <CardDescription>
            Assess whether the customer understood the loan terms and conditions
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <FormField
            control={control}
            name="customerUnderstanding.status"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>Assessment</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    value={field.value}
                    className="flex flex-row space-x-6"
                    disabled={disabled}
                  >
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="pass" id="understandingPass" />
                      </FormControl>
                      <FormLabel className="font-normal cursor-pointer" htmlFor="understandingPass">
                        Pass
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="fail" id="understandingFail" />
                      </FormControl>
                      <FormLabel className="font-normal cursor-pointer" htmlFor="understandingFail">
                        Fail
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="not_assessed" id="understandingNotAssessed" />
                      </FormControl>
                      <FormLabel className="font-normal cursor-pointer" htmlFor="understandingNotAssessed">
                        Not Assessed
                      </FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="customerUnderstanding.notes"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Notes</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Enter notes about customer understanding assessment"
                    className="min-h-[80px]"
                    {...field}
                    disabled={disabled}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </CardContent>
      </Card>

      {/* Vulnerable Customer Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Shield className="h-5 w-5 text-primary" />
              <CardTitle>Vulnerable Customer Assessment</CardTitle>
            </div>
            <FormField
              control={control}
              name="vulnerableCustomer.status"
              render={({ field }) => renderStatusBadge(field.value)}
            />
          </div>
          <CardDescription>
            Assess whether the customer showed signs of vulnerability and if appropriate protections were applied
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <FormField
            control={control}
            name="vulnerableCustomer.status"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>Assessment</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    value={field.value}
                    className="flex flex-row space-x-6"
                    disabled={disabled}
                  >
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="pass" id="vulnerablePass" />
                      </FormControl>
                      <FormLabel className="font-normal cursor-pointer" htmlFor="vulnerablePass">
                        Pass
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="fail" id="vulnerableFail" />
                      </FormControl>
                      <FormLabel className="font-normal cursor-pointer" htmlFor="vulnerableFail">
                        Fail
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="not_assessed" id="vulnerableNotAssessed" />
                      </FormControl>
                      <FormLabel className="font-normal cursor-pointer" htmlFor="vulnerableNotAssessed">
                        Not Assessed
                      </FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="vulnerableCustomer.notes"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Notes</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Enter notes about vulnerable customer assessment"
                    className="min-h-[80px]"
                    {...field}
                    disabled={disabled}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </CardContent>
      </Card>

      {/* Essential Item Check Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-primary" />
              <CardTitle>Essential Item Check</CardTitle>
            </div>
            <FormField
              control={control}
              name="essentialItemCheck.compliance"
              render={({ field }) => renderComplianceBadge(field.value)}
            />
          </div>
          <CardDescription>
            Assess whether the item being pawned is essential for the customer's daily life or livelihood
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <FormField
            control={control}
            name="essentialItemCheck.hasEssentialItems"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                    disabled={disabled}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>
                    Customer has essential items being pawned
                  </FormLabel>
                  <FormDescription>
                    Check this if the customer is pawning items that could be considered essential
                  </FormDescription>
                </div>
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="essentialItemCheck.hasEssentialItems"
            render={({ field }) => (
              field.value && (
                <div className="space-y-4 border rounded-md p-4 bg-yellow-50 dark:bg-yellow-950/20">
                  <FormField
                    control={control}
                    name="essentialItemCheck.category"
                    render={({ field: categoryField }) => (
                      <FormItem>
                        <FormLabel>Essential Item Category</FormLabel>
                        <FormControl>
                          <RadioGroup
                            onValueChange={categoryField.onChange}
                            value={categoryField.value}
                            className="grid grid-cols-1 md:grid-cols-2 gap-2"
                            disabled={disabled}
                          >
                            {essentialCategories.map((category) => (
                              <FormItem key={category.id} className="flex items-center space-x-2 space-y-0">
                                <FormControl>
                                  <RadioGroupItem value={category.id} id={`category-${category.id}`} />
                                </FormControl>
                                <FormLabel className="font-normal cursor-pointer" htmlFor={`category-${category.id}`}>
                                  {category.label}
                                </FormLabel>
                              </FormItem>
                            ))}
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={control}
                    name="essentialItemCheck.isEssential"
                    render={({ field: essentialField }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={essentialField.value}
                            onCheckedChange={essentialField.onChange}
                            disabled={disabled}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>
                            Item is essential for customer's daily life or livelihood
                          </FormLabel>
                          <FormDescription>
                            Check this if the item is truly essential and cannot be easily replaced
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={control}
                    name="essentialItemCheck.reasonNotEssential"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Reason Item is Not Essential (if applicable)</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Explain why the item is not considered essential"
                            className="min-h-[80px]"
                            {...field}
                            disabled={disabled}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={control}
                    name="essentialItemCheck.compliance"
                    render={({ field: complianceField }) => (
                      <FormItem className="space-y-3">
                        <FormLabel>Compliance Assessment</FormLabel>
                        <FormControl>
                          <RadioGroup
                            onValueChange={complianceField.onChange}
                            value={complianceField.value}
                            className="flex flex-row space-x-6"
                            disabled={disabled}
                          >
                            <FormItem className="flex items-center space-x-2 space-y-0">
                              <FormControl>
                                <RadioGroupItem value="compliant" id="essentialCompliant" />
                              </FormControl>
                              <FormLabel className="font-normal cursor-pointer" htmlFor="essentialCompliant">
                                Compliant
                              </FormLabel>
                            </FormItem>
                            <FormItem className="flex items-center space-x-2 space-y-0">
                              <FormControl>
                                <RadioGroupItem value="non_compliant" id="essentialNonCompliant" />
                              </FormControl>
                              <FormLabel className="font-normal cursor-pointer" htmlFor="essentialNonCompliant">
                                Non-Compliant
                              </FormLabel>
                            </FormItem>
                            <FormItem className="flex items-center space-x-2 space-y-0">
                              <FormControl>
                                <RadioGroupItem value="not_applicable" id="essentialNotApplicable" />
                              </FormControl>
                              <FormLabel className="font-normal cursor-pointer" htmlFor="essentialNotApplicable">
                                Not Applicable
                              </FormLabel>
                            </FormItem>
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              )
            )}
          />

          <FormField
            control={control}
            name="essentialItemCheck.notes"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Notes</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Enter notes about essential item assessment"
                    className="min-h-[80px]"
                    {...field}
                    disabled={disabled}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </CardContent>
      </Card>
    </div>
  );
}
