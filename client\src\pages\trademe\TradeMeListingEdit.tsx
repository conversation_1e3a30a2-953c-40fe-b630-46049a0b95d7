import { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate, Link } from 'react-router-dom';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/hooks/useToast';
import {
  ArrowLeft,
  Save,
  Trash2,
  Upload,
  Plus,
  DollarSign,
  Calendar,
  Search,
  RefreshCw,
  AlertTriangle,
  X,
} from 'lucide-react';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { format } from 'date-fns';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import { getItemById as getListing, createItem as createListing, updateItem as updateListing, addNote } from '@/api/tradeMeItems';
import { getTemplates } from '@/api/tradeMeTemplates';
import { getAllUsers, User as UserType } from '@/api/user';
import {
  getCategories,
  searchCategories,
  getCategorySuggestions,
  getCategoryById,
  TradeMeCategory,
  TradeMeCategorySuggestion,
  TradeMeAutoSuggestionCategory
} from '@/api/tradeMeCategories';
import CategoryDetails from '@/components/trademe/CategoryDetails';

// Form schema for validation
const listingFormSchema = z.object({
  title: z.string()
    .min(5, { message: 'Title must be at least 5 characters' })
    .max(80, { message: 'Title must be no more than 80 characters' }),
  subtitle: z.string().max(80, { message: 'Subtitle must be no more than 80 characters' }).optional(),
  description: z.string()
    .min(10, { message: 'Description must be at least 10 characters' }),
  footerTemplate: z.string().optional(),
  footerText: z.string().optional(),
  category: z.string().min(1, { message: 'Category is required' }),
  categoryPath: z.string().optional(),
  tradeMeCategoryId: z.string().optional(),
  price: z.coerce.number().min(1, { message: 'Price must be at least $1' }),
  reservePrice: z.coerce.number().min(0).optional(),
  customReserve: z.boolean().default(false),
  buyNowPrice: z.coerce.number().min(0).optional(),
  allowOffers: z.boolean().default(true),
  authenticatedBidders: z.boolean().default(true),
  location: z.string().min(1, { message: 'Location name is required' }),
  locationId: z.string().min(1, { message: 'Location is required' }),
  isNew: z.boolean().default(false),
  duration: z.coerce.number().min(2).max(10).default(7),
  customEndDate: z.boolean().default(false),
  endDate: z.date().optional(),
  pickup: z.coerce.number().min(0).max(2).default(0), // 0 = can pickup, 1 = must pickup, 2 = no pickups
  freeShipping: z.boolean().default(false),
  shippingOptions: z.array(z.object({
    name: z.string(),
    cost: z.coerce.number(),
    details: z.string().optional()
  })).optional(),
  paymentMethods: z.object({
    ping: z.boolean().default(false),
    afterpay: z.boolean().default(false),
    cash: z.boolean().default(true),
    bankDeposit: z.boolean().default(true),
    other: z.boolean().default(false),
    otherDetails: z.string().optional()
  }),
  sendPaymentInstructions: z.boolean().default(true),
  auctionUpgrade: z.enum(['basic', 'gallery', 'feature', 'featureCombo', 'superFeature']).default('basic'),
  stockCode: z.string().optional(), // Changed from sku to stockCode to match model
  itemCost: z.coerce.number().min(0).optional(),
  autoRelist: z.boolean().default(false),
  relistPriceDecrease: z.coerce.number().min(0).max(100).default(5),
  relistBuyNowDecrease: z.coerce.number().min(0).max(100).default(5),
  expiredPawnLoan: z.boolean().default(false),
  buyer: z.string().optional(), // Added buyer field
});

export type ListingFormValues = z.infer<typeof listingFormSchema>;

// Template types
const TEMPLATE_TYPES = {
  FOOTER: 'footer',
  SHIPPING: 'shipping',
  QUESTION: 'question'
};

export function TradeMeListingEdit() {
  const { id } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [images, setImages] = useState<string[]>([]);
  const [isEditing, setIsEditing] = useState(!!id);
  const [categorySearch, setCategorySearch] = useState('');
  const [categories, setCategories] = useState<TradeMeCategory[]>([]);
  const [filteredCategories, setFilteredCategories] = useState<TradeMeCategory[]>([]);
  const [categoryMenuOpen, setCategoryMenuOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<TradeMeCategory | null>(null);
  const [characterCount, setCharacterCount] = useState(0);
  const [loadingCategories, setLoadingCategories] = useState(false);
  const [loadingUsers, setLoadingUsers] = useState(false);
  const [users, setUsers] = useState<UserType[]>([]);

  // Category suggestion states
  const [categorySuggestions, setCategorySuggestions] = useState<TradeMeCategorySuggestion[]>([]);
  const [loadingSuggestions, setLoadingSuggestions] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [categoryHistory, setCategoryHistory] = useState<string[]>([]);

  // Template states
  const [footerTemplates, setFooterTemplates] = useState<any[]>([]);
  const [shippingTemplates, setShippingTemplates] = useState<any[]>([]);

  // Notes state
  const [notes, setNotes] = useState<any[]>([]);
  const [newNote, setNewNote] = useState('');

  const [shippingOptions, setShippingOptions] = useState([
    { id: 1, name: 'Standard Shipping', cost: 5.00, details: 'North Island' },
    { id: 2, name: 'Rural Delivery', cost: 8.00, details: 'Rural areas' }
  ]);
  const [successFee, setSuccessFee] = useState(0);
  const [upgradesFee, setUpgradesFee] = useState(0);
  const [totalFees, setTotalFees] = useState(0);

  // Initialize the form with react-hook-form
  const form = useForm<ListingFormValues>({
    resolver: zodResolver(listingFormSchema),
    defaultValues: {
      title: '',
      subtitle: '',
      description: '',
      footerTemplate: '',
      footerText: '',
      category: '',
      categoryPath: '',
      tradeMeCategoryId: '',
      price: 0,
      reservePrice: 0,
      customReserve: false,
      buyNowPrice: 0,
      allowOffers: true,
      authenticatedBidders: true,
      location: '',
      locationId: '',
      isNew: false,
      duration: 7,
      customEndDate: false,
      endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
      pickup: 0,
      freeShipping: false,
      shippingOptions: [],
      paymentMethods: {
        ping: false,
        afterpay: false,
        cash: true,
        bankDeposit: true,
        other: false,
        otherDetails: ''
      },
      sendPaymentInstructions: true,
      auctionUpgrade: 'basic',
      stockCode: '', // Changed from sku to stockCode
      itemCost: 0,
      autoRelist: false,
      relistPriceDecrease: 5,
      relistBuyNowDecrease: 5,
      expiredPawnLoan: false,
      buyer: 'none' // Added buyer field
    },
  });

  // Watch form values for calculations
  const watchPrice = form.watch('price');
  const watchDescription = form.watch('description');
  const watchFooterText = form.watch('footerText');
  const watchAuctionUpgrade = form.watch('auctionUpgrade');
  const watchCustomReserve = form.watch('customReserve');
  const watchReservePrice = form.watch('reservePrice');
  const watchCustomEndDate = form.watch('customEndDate');

  // Calculate fees based on price and upgrades
  useEffect(() => {
    // Calculate success fee (example: 7.9% of price)
    const calculatedSuccessFee = watchPrice * 0.079;
    setSuccessFee(calculatedSuccessFee);

    // Calculate upgrades fee
    let upgradeFee = 0;
    switch (watchAuctionUpgrade) {
      case 'gallery':
        upgradeFee = 0.55; // 55c
        break;
      case 'feature':
        upgradeFee = 3.45; // $3.45
        break;
      case 'featureCombo':
        upgradeFee = 3.95; // $3.95
        break;
      case 'superFeature':
        upgradeFee = 9.95; // $9.95
        break;
      default:
        upgradeFee = 0; // Basic
    }

    // Add custom reserve fee if applicable
    if (watchCustomReserve) {
      upgradeFee += 0.25; // 25c for custom reserve
    }

    // Add custom end date fee if applicable
    if (watchCustomEndDate) {
      upgradeFee += 0.25; // 25c for custom end date
    }

    setUpgradesFee(upgradeFee);

    // Calculate total fees
    setTotalFees(calculatedSuccessFee + upgradeFee);
  }, [watchPrice, watchAuctionUpgrade, watchCustomReserve, watchCustomEndDate]);

  // Calculate character count for description + footer
  useEffect(() => {
    const descLength = (watchDescription || '').length;
    const footerLength = (watchFooterText || '').length;
    setCharacterCount(descLength + footerLength);
  }, [watchDescription, watchFooterText]);

  // Fetch templates, categories, and users
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch footer templates
        const footerResponse = await getTemplates(TEMPLATE_TYPES.FOOTER);
        if (footerResponse.success) {
          setFooterTemplates(footerResponse.templates || []);
        }

        // Fetch shipping templates
        const shippingResponse = await getTemplates(TEMPLATE_TYPES.SHIPPING);
        if (shippingResponse.success) {
          setShippingTemplates(shippingResponse.templates || []);
        }

        // Fetch root categories
        await fetchRootCategories();

        // Fetch users
        await fetchUsers();
      } catch (error) {
        console.error('Error fetching initial data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load initial data',
          variant: 'destructive',
        });
      }
    };

    fetchData();
  }, [toast]);

  // Fetch root categories
  const fetchRootCategories = async () => {
    try {
      setLoadingCategories(true);
      const response = await getCategories(null);
      if (response.success) {
        setCategories(response.categories);
        setFilteredCategories(response.categories);
      } else {
        toast({
          title: 'Error',
          description: 'Failed to load categories',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
      toast({
        title: 'Error',
        description: 'Failed to load categories',
        variant: 'destructive',
      });
    } finally {
      setLoadingCategories(false);
    }
  };

  // Fetch subcategories
  const fetchSubcategories = async (parentId: string) => {
    try {
      setLoadingCategories(true);
      const response = await getCategories(parentId);
      if (response.success) {
        setFilteredCategories(response.categories);
      } else {
        toast({
          title: 'Error',
          description: 'Failed to load subcategories',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error fetching subcategories:', error);
      toast({
        title: 'Error',
        description: 'Failed to load subcategories',
        variant: 'destructive',
      });
    } finally {
      setLoadingCategories(false);
    }
  };

  // Fetch users
  const fetchUsers = async () => {
    try {
      setLoadingUsers(true);
      const response = await getAllUsers();
      // Sort users alphabetically by fullName
      const sortedUsers = [...response].sort((a, b) =>
        a.fullName.localeCompare(b.fullName)
      );
      setUsers(sortedUsers);
    } catch (error) {
      console.error('Error fetching users:', error);
      toast({
        title: 'Error',
        description: 'Failed to load users',
        variant: 'destructive',
      });
    } finally {
      setLoadingUsers(false);
    }
  };

  // Filter categories based on search with debouncing
  useEffect(() => {
    // Create a debounced search function
    const debouncedSearch = setTimeout(() => {
      const performCategorySearch = async () => {
        if (categorySearch && categorySearch.length >= 2) {
          try {
            setLoadingCategories(true);
            const response = await searchCategories(categorySearch);
            if (response.success) {
              setFilteredCategories(response.categories);
            }
          } catch (error) {
            console.error('Error searching categories:', error);
          } finally {
            setLoadingCategories(false);
          }
        } else if (!categorySearch) {
          // If search is cleared, show root categories
          setFilteredCategories(categories);
        }
      };

      performCategorySearch();
    }, 500); // 500ms debounce delay

    // Cleanup function to clear the timeout if the component unmounts or categorySearch changes
    return () => clearTimeout(debouncedSearch);
  }, [categorySearch, categories]);



  // Set initial reserve price to match start price
  useEffect(() => {
    // Only auto-update reserve if it's not a custom reserve value
    if (!watchCustomReserve) {
      form.setValue('reservePrice', watchPrice);
    }
  }, [watchPrice, watchCustomReserve, form]);

  // If editing, fetch the listing details
  useEffect(() => {
    if (id) {
      setIsEditing(true);
      async function fetchListing() {
        try {
          console.log(`Fetching TradeMe listing details for ID: ${id}`);
          setLoading(true);
          const response = await getListing(id);
          if (response.success) {
            console.log('Successfully loaded listing details:', response.listing.title);
            const listing = response.listing;
            form.reset({
              title: listing.title,
              subtitle: listing.subtitle || '',
              description: listing.description,
              footerTemplate: listing.footerTemplate || '',
              footerText: listing.footerText || '',
              category: listing.category || '',
              categoryPath: listing.categoryPath || '',
              tradeMeCategoryId: listing.tradeMeCategoryId || '',
              price: listing.price,
              reservePrice: listing.reservePrice || listing.price,
              customReserve: listing.customReserve || false,
              buyNowPrice: listing.buyNowPrice || 0,
              allowOffers: listing.allowOffers !== false,
              authenticatedBidders: listing.authenticatedBidders !== false,
              location: listing.location || '',
              locationId: listing.locationId || '',
              isNew: listing.isNew || false,
              duration: listing.duration || 7,
              customEndDate: listing.customEndDate || false,
              endDate: listing.endDate ? new Date(listing.endDate) : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
              pickup: listing.pickup !== undefined ? listing.pickup : 0,
              freeShipping: listing.freeShipping || false,
              shippingOptions: listing.shippingOptions || [],
              paymentMethods: listing.paymentMethods || {
                ping: false,
                afterpay: false,
                cash: true,
                bankDeposit: true,
                other: false,
                otherDetails: ''
              },
              sendPaymentInstructions: listing.sendPaymentInstructions !== false,
              auctionUpgrade: listing.auctionUpgrade || 'basic',
              stockCode: listing.stockCode || '', // Changed from sku to stockCode
              itemCost: listing.itemCost || 0,
              autoRelist: listing.autoRelist || false,
              relistPriceDecrease: listing.relistPriceDecrease || 5,
              relistBuyNowDecrease: listing.relistBuyNowDecrease || 5,
              expiredPawnLoan: listing.expiredPawnLoan || false,
              buyer: listing.buyer || 'none' // Added buyer field
            });
            setImages(listing.images || []);

            // If we have a tradeMeCategoryId, try to find the category
            if (listing.tradeMeCategoryId) {
              try {
                const categoryResponse = await getCategoryById(listing.tradeMeCategoryId);
                if (categoryResponse.success) {
                  setSelectedCategory(categoryResponse.category);
                }
              } catch (error) {
                console.error('Error fetching category by ID:', error);
              }
            }

            // Set shipping options if available
            if (listing.shippingOptions && listing.shippingOptions.length > 0) {
              setShippingOptions(listing.shippingOptions.map((opt, idx) => ({
                id: idx + 1,
                name: opt.name,
                cost: opt.cost,
                details: opt.details || ''
              })));
            }

            // Set notes if available
            if (listing.notes && listing.notes.length > 0) {
              setNotes(listing.notes);
            }
          } else {
            console.error('Failed to load listing details:', response.error);
            toast({
              title: 'Error',
              description: 'Failed to load listing details',
              variant: 'destructive',
            });
            navigate('/trademe/selling');
          }
        } catch (error) {
          console.error('Error fetching listing:', error);
          toast({
            title: 'Error',
            description: error.message,
            variant: 'destructive',
          });
          navigate('/trademe/selling');
        } finally {
          setLoading(false);
        }
      }
      fetchListing();
    }
  }, [id, form, toast, navigate]);

  // Get category suggestions based on search box value
  const getSuggestions = async () => {
    try {
      const searchTerm = categorySearch;

      if (!searchTerm || searchTerm.length < 2) {
        toast({
          title: 'Error',
          description: 'Please enter a search term with at least 2 characters',
          variant: 'destructive',
        });
        return;
      }

      setLoadingSuggestions(true);
      setShowSuggestions(true);

      const response = await getCategorySuggestions(searchTerm);

      if (response.success && response.suggestions) {
        console.log('Category suggestions:', response.suggestions.categorySuggestions);

        // Only use direct category suggestions
        setCategorySuggestions(response.suggestions.categorySuggestions);

        // If no category suggestions found, check auto suggestions for categories
        if (response.suggestions.categorySuggestions.length === 0 &&
            response.suggestions.autoSuggestions &&
            response.suggestions.autoSuggestions.length > 0) {

          // Extract categories from auto suggestions and convert them to the same format
          const extractedCategories: TradeMeCategorySuggestion[] = [];

          response.suggestions.autoSuggestions.forEach(suggestion => {
            suggestion.categories.forEach(cat => {
              // Extract category name and path from description
              let name = cat.name;
              let path = cat.path;

              extractedCategories.push({
                id: cat.id,
                name: name,
                path: path
              });
            });
          });

          // Set the extracted categories
          if (extractedCategories.length > 0) {
            setCategorySuggestions(extractedCategories);
          }
        }

        // Hide suggestions if none found
        if (response.suggestions.categorySuggestions.length === 0 &&
            (!response.suggestions.autoSuggestions ||
             response.suggestions.autoSuggestions.length === 0)) {
          setTimeout(() => {
            setShowSuggestions(false);
          }, 2000); // Hide after 2 seconds
        }
      } else {
        toast({
          title: 'Error',
          description: response.error || 'Failed to get category suggestions',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error getting category suggestions:', error);
      toast({
        title: 'Error',
        description: 'Failed to get category suggestions',
        variant: 'destructive',
      });
    } finally {
      setLoadingSuggestions(false);
    }
  };

  // Handle category selection
  const handleCategorySelect = (category: TradeMeCategory | TradeMeCategorySuggestion | TradeMeAutoSuggestionCategory) => {
    console.log(`Selected category:`, category);

    // Create a simplified category object that matches the TradeMeCategory interface
    let simplifiedCategory: TradeMeCategory;

    // If it's a regular category from the browse menu
    if ('categoryId' in category) {
      // It's a regular category, use it directly
      simplifiedCategory = category as TradeMeCategory;
    }
    // If it's a suggestion from direct category suggestions or auto suggestions
    else {
      // Create a simplified category from the suggestion data
      const id = category.id;
      const name = category.name;
      const path = category.path;

      simplifiedCategory = {
        _id: id,
        categoryId: id,
        name: name,
        path: path,
        parentId: null,
        isLeaf: true,
        hasLegalNotice: false,
        count: 0,
        subcategories: []
      };
    }

    // Set the selected category and form values
    setSelectedCategory(simplifiedCategory);
    form.setValue('category', simplifiedCategory.name);
    form.setValue('categoryPath', simplifiedCategory.path);
    form.setValue('tradeMeCategoryId', simplifiedCategory.categoryId);

    // Close menus and clear search
    setCategoryMenuOpen(false);
    setCategorySearch('');
    setShowSuggestions(false);
  };

  // Handle category navigation
  const handleCategoryNavigation = (category: TradeMeCategory) => {
    if (category.isLeaf) {
      // If it's a leaf category, select it
      handleCategorySelect(category);
    } else {
      // Otherwise, fetch its subcategories
      // Save current parent ID to history for back navigation
      if (category.parentId) {
        setCategoryHistory(prev => [...prev, category.parentId]);
      }
      fetchSubcategories(category.categoryId);
    }
  };

  // Navigate back in category hierarchy
  const handleCategoryBack = () => {
    if (categoryHistory.length > 0) {
      // Get the last parent ID from history
      const lastParentId = categoryHistory[categoryHistory.length - 1];

      // Remove it from history
      setCategoryHistory(prev => prev.slice(0, -1));

      // Fetch categories for this parent
      fetchSubcategories(lastParentId);
    } else {
      // If no history, go back to root categories
      fetchRootCategories();
    }
  };

  // Clear selected category
  const handleClearCategory = () => {
    setSelectedCategory(null);
    form.setValue('category', '');
    form.setValue('categoryPath', '');
    form.setValue('tradeMeCategoryId', '');
  };

  // Handle footer template selection
  const handleFooterTemplateChange = (templateId: string) => {
    console.log(`Selected footer template ID: ${templateId}`);
    const template = footerTemplates.find(t => t._id.toString() === templateId);
    if (template) {
      form.setValue('footerTemplate', templateId);
      form.setValue('footerText', template.content);
    }
  };

  // Handle image upload via file input
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    console.log(`Uploading ${files.length} images`);

    // Store the actual File objects for later upload
    const newImages = Array.from(files);

    // Create preview URLs for display
    const previewUrls = newImages.map(file => URL.createObjectURL(file));

    // Store both the File objects and preview URLs
    setImages([...images, ...newImages]);
  };

  // Handle image upload via drag and drop
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      console.log(`Dropped ${e.dataTransfer.files.length} images`);

      // Store the actual File objects for later upload
      const newImages = Array.from(e.dataTransfer.files);

      // Store the File objects
      setImages([...images, ...newImages]);
    }
  };

  // Handle image removal
  const handleRemoveImage = (index: number) => {
    console.log(`Removing image at index ${index}`);
    setImages(images.filter((_, i) => i !== index));
  };

  // Add a new shipping option
  const addShippingOption = () => {
    const newId = shippingOptions.length > 0
      ? Math.max(...shippingOptions.map(o => o.id)) + 1
      : 1;

    console.log(`Adding new shipping option with ID ${newId}`);
    setShippingOptions([
      ...shippingOptions,
      { id: newId, name: 'New Option', cost: 0, details: '' }
    ]);
  };

  // Update a shipping option
  const updateShippingOption = (id, field, value) => {
    console.log(`Updating shipping option ${id}, field ${field} to ${value}`);
    setShippingOptions(shippingOptions.map(opt =>
      opt.id === id ? { ...opt, [field]: value } : opt
    ));
  };

  // Remove a shipping option
  const removeShippingOption = (id) => {
    console.log(`Removing shipping option with ID ${id}`);
    setShippingOptions(shippingOptions.filter(opt => opt.id !== id));
  };

  // Handle duration change
  const handleDurationChange = (value: string) => {
    const days = parseInt(value);
    console.log(`Changed duration to ${days} days`);
    form.setValue('duration', days);

    // Update end date if not using custom end date
    if (!form.getValues('customEndDate')) {
      const newEndDate = new Date();
      newEndDate.setDate(newEndDate.getDate() + days);
      form.setValue('endDate', newEndDate);
    }
  };

  // Handle custom end date toggle
  const handleCustomEndDateToggle = (checked: boolean) => {
    console.log(`Custom end date toggled: ${checked}`);
    form.setValue('customEndDate', checked);

    if (!checked) {
      // Reset to standard duration-based end date
      const days = form.getValues('duration');
      const newEndDate = new Date();
      newEndDate.setDate(newEndDate.getDate() + days);
      form.setValue('endDate', newEndDate);
    }
  };

  // Reset the form
  // Handle adding a note
  const handleAddNote = async () => {
    if (!newNote.trim()) {
      toast({
        title: 'Error',
        description: 'Please enter a note',
        variant: 'destructive',
      });
      return;
    }

    if (id) {
      try {
        const response = await addNote(id, newNote);
        if (response.success) {
          toast({
            title: 'Success',
            description: 'Note added successfully',
          });

          // Add the new note to the local state
          const newNoteObj = {
            _id: response.noteId || Date.now().toString(),
            content: newNote,
            createdAt: new Date().toISOString(),
            createdBy: {
              _id: 'current-user', // This would be the actual user ID in a real app
              username: 'You'
            }
          };

          setNotes([...notes, newNoteObj]);
          setNewNote('');
        } else {
          toast({
            title: 'Error',
            description: response.error || 'Failed to add note',
            variant: 'destructive',
          });
        }
      } catch (error) {
        console.error('Error adding note:', error);
        toast({
          title: 'Error',
          description: 'Failed to add note',
          variant: 'destructive',
        });
      }
    } else {
      // For new listings, just add to local state
      const newNoteObj = {
        _id: Date.now().toString(),
        content: newNote,
        createdAt: new Date().toISOString(),
        createdBy: {
          _id: 'current-user',
          username: 'You'
        }
      };

      setNotes([...notes, newNoteObj]);
      setNewNote('');

      toast({
        title: 'Note Added',
        description: 'Note will be saved when you create the listing',
      });
    }
  };

  const resetForm = () => {
    console.log('Resetting form to defaults');
    form.reset({
      title: '',
      subtitle: '',
      description: '',
      footerTemplate: '',
      footerText: '',
      category: '',
      categoryPath: '',
      tradeMeCategoryId: '',
      price: 0,
      reservePrice: 0,
      customReserve: false,
      buyNowPrice: 0,
      allowOffers: true,
      authenticatedBidders: true,
      location: '',
      locationId: '',
      isNew: false,
      duration: 7,
      customEndDate: false,
      endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
      pickup: 0,
      freeShipping: false,
      shippingOptions: [],
      paymentMethods: {
        ping: false,
        afterpay: false,
        cash: true,
        bankDeposit: true,
        other: false,
        otherDetails: ''
      },
      sendPaymentInstructions: true,
      auctionUpgrade: 'basic',
      stockCode: '',
      itemCost: 0,
      autoRelist: false,
      relistPriceDecrease: 5,
      relistBuyNowDecrease: 5,
      expiredPawnLoan: false,
      buyer: 'none'
    });
    setImages([]);
    setSelectedCategory(null);
    setShippingOptions([
      { id: 1, name: 'Standard Shipping', cost: 5.00, details: 'North Island' },
      { id: 2, name: 'Rural Delivery', cost: 8.00, details: 'Rural areas' }
    ]);
    setNotes([]);
    setNewNote('');
  };

  // Save as draft (without listing)
  const saveAsDraft = async () => {
    try {
      console.log('Saving listing as draft');
      setLoading(true);

      // Prepare listing data
      const formValues = form.getValues();

      // Rename stockCode to match the model
      const { stockCode, ...otherValues } = formValues;

      // Create FormData for file uploads
      const formData = new FormData();

      // Add images to FormData if they are File objects
      const imageFiles: File[] = [];
      const existingImageUrls: string[] = [];

      // Separate File objects from URLs
      images.forEach(image => {
        if (image instanceof File) {
          imageFiles.push(image);
        } else {
          existingImageUrls.push(image);
        }
      });

      // Add image files to FormData
      imageFiles.forEach(file => {
        formData.append('images', file);
      });

      // Prepare the listing data
      const listingData = {
        ...otherValues,
        stockCode, // Use stockCode instead of sku
        images: existingImageUrls, // Only include existing image URLs
        shippingOptions: formValues.freeShipping ? [] : shippingOptions,
        notes: notes.length > 0 ? notes : undefined,
        isDraft: true
      };

      // Add listing data as JSON string
      formData.append('itemData', JSON.stringify(listingData));

      let response;
      if (isEditing && id) {
        // Update existing draft
        console.log(`Updating existing draft with ID ${id}`);
        response = await updateListing(id, formData);
        if (response.success) {
          console.log('Draft updated successfully');
          toast({
            title: 'Success',
            description: 'Draft saved successfully',
          });
        }
      } else {
        // Create new draft
        console.log('Creating new draft');
        response = await createListing(formData);
        if (response.success) {
          console.log('Draft created successfully');
          toast({
            title: 'Success',
            description: 'Draft saved successfully',
          });
        }
      }

      // Navigate back to listings
      navigate('/trademe/selling');
    } catch (error) {
      console.error('Error saving draft:', error);
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle form submission
  const onSubmit = async (values: ListingFormValues) => {
    try {
      console.log('Submitting listing form', values);
      setLoading(true);

      // Rename stockCode to match the model
      const { stockCode, ...otherValues } = values;

      // Create FormData for file uploads
      const formData = new FormData();

      // Add images to FormData if they are File objects
      const imageFiles: File[] = [];
      const existingImageUrls: string[] = [];

      // Separate File objects from URLs
      images.forEach(image => {
        if (image instanceof File) {
          imageFiles.push(image);
        } else {
          existingImageUrls.push(image);
        }
      });

      // Add image files to FormData
      imageFiles.forEach(file => {
        formData.append('images', file);
      });

      // Prepare shipping options
      const finalShippingOptions = values.freeShipping ? [] : shippingOptions;

      // Prepare the listing data
      const listingData = {
        ...otherValues,
        stockCode, // Use stockCode instead of sku
        images: existingImageUrls, // Only include existing image URLs
        shippingOptions: finalShippingOptions,
        notes: notes.length > 0 ? notes : undefined,
        isDraft: false
      };

      // Add listing data as JSON string
      formData.append('itemData', JSON.stringify(listingData));

      let response;
      if (isEditing && id) {
        // Update existing listing
        console.log(`Updating existing listing with ID ${id}`);
        response = await updateListing(id, formData);
        if (response.success) {
          console.log('Listing updated successfully');
          toast({
            title: 'Success',
            description: 'Listing updated successfully',
          });
        }
      } else {
        // Create new listing
        console.log('Creating new listing');
        response = await createListing(formData);
        if (response.success) {
          console.log('Listing created successfully');
          toast({
            title: 'Success',
            description: 'Listing created successfully',
          });
        }
      }

      // Navigate back to listings
      navigate('/trademe/selling');
    } catch (error) {
      console.error('Error submitting listing:', error);
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6 pb-20">
      <div className="flex items-center justify-between">
        <Button variant="outline" asChild>
          <Link to="/trademe/selling">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to listings
          </Link>
        </Button>
        <h1 className="text-2xl font-semibold">
          {isEditing ? 'Edit TradeMe Listing' : 'Create New TradeMe Listing'}
        </h1>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Left column - Main details */}
            <div className="md:col-span-2 space-y-6">
              {/* Category selection */}
              <Card>
                <CardHeader>
                  <CardTitle>Category</CardTitle>
                  <CardDescription>Select the most specific category for your item to improve visibility.</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="category"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Category*</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <div className="flex space-x-2">
                              <div className="relative flex-1">
                                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                                <Input
                                  type="text"
                                  placeholder="Search for a category"
                                  className="pl-8"
                                  value={categorySearch}
                                  onChange={(e) => {
                                    setCategorySearch(e.target.value);
                                    // Hide suggestions when search box is cleared
                                    if (!e.target.value) {
                                      setShowSuggestions(false);
                                    }
                                  }}
                                  onKeyDown={(e) => {
                                    if (e.key === 'Enter') {
                                      e.preventDefault();
                                      getSuggestions();
                                    }
                                  }}
                                  onFocus={() => {
                                    // Show suggestions again if there's text and suggestions available
                                    if (categorySearch && categorySuggestions.length > 0) {
                                      setShowSuggestions(true);
                                    }
                                  }}
                                />

                                {/* Search button inside the input */}
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="icon"
                                  className="absolute right-1 top-1 h-7 w-7"
                                  onClick={getSuggestions}
                                  disabled={loadingSuggestions || !categorySearch}
                                >
                                  {loadingSuggestions ? (
                                    <RefreshCw className="h-4 w-4 animate-spin" />
                                  ) : (
                                    <Search className="h-4 w-4" />
                                  )}
                                </Button>

                                {/* Category Suggestions Dropdown */}
                                {showSuggestions && (
                                  <div className="absolute z-10 w-full mt-1 bg-popover border rounded-md shadow-md max-h-80 overflow-y-auto">
                                    {loadingSuggestions ? (
                                      <div className="p-4 text-center">
                                        <RefreshCw className="h-5 w-5 animate-spin mx-auto mb-2" />
                                        <p className="text-sm text-muted-foreground">Loading suggestions...</p>
                                      </div>
                                    ) : (
                                      <>
                                        {categorySuggestions.length > 0 ? (
                                          <div className="py-1">
                                            {categorySuggestions.map((suggestion, index) => (
                                              <div
                                                key={`cat-${suggestion.id}-${index}`}
                                                className="px-3 py-2 hover:bg-accent hover:text-accent-foreground cursor-pointer transition-colors"
                                                onClick={() => handleCategorySelect(suggestion)}
                                              >
                                                <div className="font-medium text-sm">{suggestion.name}</div>
                                                <div className="text-xs text-muted-foreground truncate">{suggestion.path}</div>
                                              </div>
                                            ))}
                                          </div>
                                        ) : (
                                          <div className="p-3 text-muted-foreground text-sm text-center">
                                            No category suggestions found
                                          </div>
                                        )}
                                      </>
                                    )}
                                  </div>
                                )}
                              </div>

                              <Popover open={categoryMenuOpen} onOpenChange={setCategoryMenuOpen}>
                                <PopoverTrigger asChild>
                                  <Button variant="outline">
                                    Browse Categories
                                  </Button>
                                </PopoverTrigger>
                                <PopoverContent className="w-80 max-h-96 overflow-y-auto p-0">
                                  <div className="p-4 flex justify-between items-center">
                                    <h3 className="font-medium">Select a Category</h3>
                                    {categoryHistory.length > 0 && (
                                      <Button
                                        type="button"
                                        variant="ghost"
                                        size="sm"
                                        onClick={handleCategoryBack}
                                        className="h-8"
                                      >
                                        <ArrowLeft className="h-4 w-4 mr-1" />
                                        Back
                                      </Button>
                                    )}
                                  </div>
                                  <Separator />
                                  <div className="p-2">
                                    {loadingCategories ? (
                                      <div className="p-4 text-center">
                                        <RefreshCw className="h-5 w-5 animate-spin mx-auto mb-2" />
                                        <p className="text-sm text-muted-foreground">Loading categories...</p>
                                      </div>
                                    ) : filteredCategories.length > 0 ? (
                                      filteredCategories.map(category => (
                                        <div
                                          key={category.categoryId}
                                          className="p-2 hover:bg-accent rounded-md cursor-pointer flex justify-between items-center"
                                          onClick={() => handleCategoryNavigation(category)}
                                        >
                                          <span>{category.name}</span>
                                          {!category.isLeaf && (
                                            <ArrowLeft className="h-4 w-4 transform rotate-180" />
                                          )}
                                        </div>
                                      ))
                                    ) : (
                                      <div className="p-2 text-muted-foreground">No categories found</div>
                                    )}
                                  </div>
                                </PopoverContent>
                              </Popover>
                            </div>

                            {/* Selected Category Display */}
                            {selectedCategory && (
                              <div className="mt-2 space-y-2">
                                <div className="flex items-center">
                                  <Badge variant="secondary" className="text-xs flex-1">
                                    {selectedCategory.path}
                                  </Badge>
                                  <Button
                                    type="button"
                                    variant="ghost"
                                    size="sm"
                                    className="ml-2 h-6 w-6 p-0"
                                    onClick={handleClearCategory}
                                  >
                                    <X className="h-4 w-4" />
                                  </Button>
                                </div>

                                {/* Display category details */}
                                <CategoryDetails category={selectedCategory} />
                              </div>
                            )}

                            {/* Category Suggestions Dropdown */}
                            {showSuggestions && (
                              <div className="absolute z-10 w-full mt-1 bg-popover border rounded-md shadow-md max-h-80 overflow-y-auto">
                                {loadingSuggestions ? (
                                  <div className="p-4 text-center">
                                    <RefreshCw className="h-5 w-5 animate-spin mx-auto mb-2" />
                                    <p className="text-sm text-muted-foreground">Loading suggestions...</p>
                                  </div>
                                ) : (
                                  <>
                                    {categorySuggestions.length > 0 ? (
                                      <div className="py-1">
                                        {categorySuggestions.map((suggestion, index) => (
                                          <div
                                            key={`cat-${suggestion.id}-${index}`}
                                            className="px-3 py-2 hover:bg-accent hover:text-accent-foreground cursor-pointer transition-colors"
                                            onClick={() => handleCategorySelect(suggestion)}
                                          >
                                            <div className="font-medium text-sm">{suggestion.name}</div>
                                            <div className="text-xs text-muted-foreground truncate">{suggestion.path}</div>
                                          </div>
                                        ))}
                                      </div>
                                    ) : (
                                      <div className="p-3 text-muted-foreground text-sm text-center">
                                        No category suggestions found
                                      </div>
                                    )}
                                  </>
                                )}
                              </div>
                            )}
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

          {/* Image Upload */}
          <Card>
            <CardHeader>
              <CardTitle>Images</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div
                  className="border-2 border-dashed border-gray-300 dark:border-gray-700 rounded-md p-4 text-center hover:border-primary cursor-pointer transition-colors"
                  onDragOver={handleDragOver}
                  onDrop={handleDrop}
                  onClick={() => document.getElementById('image-upload')?.click()}
                >
                  <input
                    id="image-upload"
                    type="file"
                    accept="image/*"
                    multiple
                    className="hidden"
                    onChange={handleImageUpload}
                  />
                  <Upload className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                  <p className="text-sm text-muted-foreground mb-1">Drag and drop images here</p>
                  <p className="text-xs text-muted-foreground">or click to browse</p>
                </div>

                {images.length > 0 && (
                  <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                    {images.map((image, index) => (
                      <div key={index} className="relative group">
                        <img
                          src={image instanceof File ? URL.createObjectURL(image) : image}
                          alt={`Listing image ${index + 1}`}
                          className="w-full h-24 object-cover rounded-md"
                        />
                        <button
                          type="button"
                          onClick={() => handleRemoveImage(index)}
                          className="absolute top-1 right-1 bg-black bg-opacity-50 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <X className="h-4 w-4" />
                        </button>
                        {image instanceof File && (
                          <Badge variant="secondary" className="absolute bottom-1 left-1 text-xs">
                            New
                          </Badge>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Basic Details */}
          <Card>
            <CardHeader>
              <CardTitle>Listing Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Title */}
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Title*</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter listing title" {...field} />
                    </FormControl>
                    <FormDescription>
                      Maximum 80 characters. Be descriptive and include keywords.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Subtitle */}
              <FormField
                control={form.control}
                name="subtitle"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Subtitle</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter optional subtitle" {...field} />
                    </FormControl>
                    <FormDescription>
                      Maximum 80 characters. Add additional details to attract buyers.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Description */}
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description*</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter detailed description"
                        className="min-h-32"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Describe the item in detail, including condition, features, and any defects.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Footer Template */}
              <div className="space-y-4">
                <FormLabel>Footer Template</FormLabel>
                <Select
                  value={form.getValues("footerTemplate")}
                  onValueChange={handleFooterTemplateChange}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a footer template" />
                  </SelectTrigger>
                  <SelectContent>
                    {footerTemplates.length === 0 ? (
                      <SelectItem value="none" disabled>No templates available</SelectItem>
                    ) : (
                      footerTemplates.map(template => (
                        <SelectItem key={template._id} value={template._id.toString()}>
                          {template.title}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
                <FormField
                  control={form.control}
                  name="footerText"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Footer Text</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter footer text"
                          className="min-h-20"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        This text will be appended to the end of your description.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="text-sm">
                  <span className={characterCount > 4000 ? "text-red-500" : "text-muted-foreground"}>
                    Character count: {characterCount}/4000
                    {characterCount > 4000 && " (exceeds maximum)"}
                  </span>
                </div>
              </div>

              {/* Is Brand New */}
              <FormField
                control={form.control}
                name="isNew"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Brand New Item</FormLabel>
                      <FormDescription>
                        Brand new, unused item with original manuals and packaging
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Pricing */}
          <Card>
            <CardHeader>
              <CardTitle>Pricing</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Start Price */}
              <FormField
                control={form.control}
                name="price"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Start Price*</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <DollarSign className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                        <Input
                          type="number"
                          placeholder="0.00"
                          className="pl-8"
                          {...field}
                          onChange={(e) => {
                            field.onChange(e);
                            if (!watchCustomReserve) {
                              form.setValue('reservePrice', parseFloat(e.target.value) || 0);
                            }
                          }}
                        />
                      </div>
                    </FormControl>
                    <FormDescription>
                      The starting bid price for your auction.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Reserve Price */}
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="customReserve"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Specify a reserve price (25c)</FormLabel>
                        <FormDescription>
                          Set a minimum price you're willing to accept.
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />

                {watchCustomReserve && (
                  <FormField
                    control={form.control}
                    name="reservePrice"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Reserve Price</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <DollarSign className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                            <Input type="number" placeholder="0.00" className="pl-8" {...field} />
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </div>

              {/* Buy Now Price */}
              <FormField
                control={form.control}
                name="buyNowPrice"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Buy Now Price</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <DollarSign className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                        <Input type="number" placeholder="0.00" className="pl-8" {...field} />
                      </div>
                    </FormControl>
                    <FormDescription>
                      Optional. Set a price at which buyers can purchase immediately.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Allow Offers */}
              <FormField
                control={form.control}
                name="allowOffers"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Allow buyers to make offers</FormLabel>
                      <FormDescription>
                        Buyers can make offers which you can accept or decline.
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              {/* Authenticated Bidders */}
              <FormField
                control={form.control}
                name="authenticatedBidders"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Allow bids from authenticated members only</FormLabel>
                      <FormDescription>
                        Only members with verified accounts can bid on your listing.
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Duration */}
          <Card>
            <CardHeader>
              <CardTitle>Listing Duration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <FormLabel>Duration</FormLabel>
                <RadioGroup
                  defaultValue={form.getValues("duration").toString()}
                  onValueChange={handleDurationChange}
                  className="grid grid-cols-1 sm:grid-cols-3 gap-4"
                >
                  {[2, 3, 4, 5, 6, 7, 8, 9, 10].map(days => (
                    <div key={days} className="flex items-center space-x-2">
                      <RadioGroupItem value={days.toString()} id={`duration-${days}`} />
                      <label htmlFor={`duration-${days}`}>{days} days</label>
                    </div>
                  ))}
                </RadioGroup>

                <FormField
                  control={form.control}
                  name="customEndDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-3 space-y-0 mt-4">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={(checked) => handleCustomEndDateToggle(!!checked)}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Use custom end date and time (25c)</FormLabel>
                      </div>
                    </FormItem>
                  )}
                />

                {watchCustomEndDate && (
                  <FormField
                    control={form.control}
                    name="endDate"
                    render={({ field }) => (
                      <FormItem className="flex flex-col mt-4">
                        <FormLabel>Select end date and time</FormLabel>
                        <div className="space-y-2">
                          <Popover>
                            <PopoverTrigger asChild>
                              <Button
                                variant="outline"
                                className="w-full justify-start text-left"
                              >
                                <Calendar className="mr-2 h-4 w-4" />
                                {field.value ? (
                                  format(field.value, "PPP")
                                ) : (
                                  <span>Pick a date</span>
                                )}
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0">
                              <CalendarComponent
                                mode="single"
                                selected={field.value}
                                onSelect={field.onChange}
                                initialFocus
                                disabled={(date) => {
                                  // Disable dates more than 10 days in the future
                                  const maxDate = new Date();
                                  maxDate.setDate(maxDate.getDate() + 10);
                                  return date < new Date() || date > maxDate;
                                }}
                              />
                            </PopoverContent>
                          </Popover>
                          <div className="grid grid-cols-2 gap-4">
                            <Select
                              value={format(field.value, "HH")}
                              onValueChange={(value) => {
                                const newDate = new Date(field.value);
                                newDate.setHours(parseInt(value));
                                field.onChange(newDate);
                              }}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Hour" />
                              </SelectTrigger>
                              <SelectContent>
                                {Array.from({ length: 24 }, (_, i) => (
                                  <SelectItem key={i} value={i.toString().padStart(2, "0")}>
                                    {i.toString().padStart(2, "0")}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <Select
                              value={format(field.value, "mm")}
                              onValueChange={(value) => {
                                const newDate = new Date(field.value);
                                newDate.setMinutes(parseInt(value));
                                field.onChange(newDate);
                              }}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Minute" />
                              </SelectTrigger>
                              <SelectContent>
                                {Array.from({ length: 12 }, (_, i) => i * 5).map((minute) => (
                                  <SelectItem key={minute} value={minute.toString().padStart(2, "0")}>
                                    {minute.toString().padStart(2, "0")}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                        <FormDescription>
                          Maximum 10 days from now.
                        </FormDescription>
                      </FormItem>
                    )}
                  />
                )}
              </div>
            </CardContent>
          </Card>

          {/* Shipping */}
          <Card>
            <CardHeader>
              <CardTitle>Pickup & Shipping</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Pickup Options */}
              <FormField
                control={form.control}
                name="pickup"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Pickup Options</FormLabel>
                    <Select
                      value={field.value.toString()}
                      onValueChange={(value) => field.onChange(parseInt(value))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select pickup option" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="0">Buyer can pickup</SelectItem>
                        <SelectItem value="1">Buyer must pickup</SelectItem>
                        <SelectItem value="2">No pickups</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />

              {/* Shipping Options */}
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="freeShipping"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Free Shipping within New Zealand</FormLabel>
                      </div>
                    </FormItem>
                  )}
                />

                {!form.getValues('freeShipping') && (
                  <div className="space-y-4">
                    {/* Shipping Templates */}
                    <div className="space-y-2">
                      <FormLabel>Shipping Templates</FormLabel>
                      <Select
                        onValueChange={(templateId) => {
                          const template = shippingTemplates.find(t => t._id.toString() === templateId);
                          if (template && template.content) {
                            try {
                              // Parse the template content as JSON
                              const shippingData = JSON.parse(template.content);
                              if (Array.isArray(shippingData)) {
                                // Map the shipping options with new IDs
                                const newOptions = shippingData.map((opt, idx) => ({
                                  id: idx + 1,
                                  name: opt.name,
                                  cost: opt.cost,
                                  details: opt.details || ''
                                }));
                                setShippingOptions(newOptions);
                                toast({
                                  title: 'Success',
                                  description: 'Shipping template applied',
                                });
                              }
                            } catch (error) {
                              console.error('Error parsing shipping template:', error);
                              toast({
                                title: 'Error',
                                description: 'Invalid shipping template format',
                                variant: 'destructive',
                              });
                            }
                          }
                        }}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select a shipping template" />
                        </SelectTrigger>
                        <SelectContent>
                          {shippingTemplates.length === 0 ? (
                            <SelectItem value="none" disabled>No templates available</SelectItem>
                          ) : (
                            shippingTemplates.map(template => (
                              <SelectItem key={template._id} value={template._id.toString()}>
                                {template.title}
                              </SelectItem>
                            ))
                          )}
                        </SelectContent>
                      </Select>
                    </div>

                    <FormLabel>Shipping Options</FormLabel>
                    {shippingOptions.map((option) => (
                      <div key={option.id} className="grid grid-cols-3 gap-4 items-center pb-4 border-b">
                        <Input
                          placeholder="Option Name"
                          value={option.name}
                          onChange={(e) => updateShippingOption(option.id, 'name', e.target.value)}
                        />
                        <div className="relative">
                          <DollarSign className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                          <Input
                            type="number"
                            placeholder="Cost"
                            className="pl-8"
                            value={option.cost}
                            onChange={(e) => updateShippingOption(option.id, 'cost', parseFloat(e.target.value) || 0)}
                          />
                        </div>
                        <div className="flex items-center space-x-2">
                          <Input
                            placeholder="Details"
                            value={option.details || ''}
                            onChange={(e) => updateShippingOption(option.id, 'details', e.target.value)}
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            onClick={() => removeShippingOption(option.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                    <Button
                      type="button"
                      variant="outline"
                      onClick={addShippingOption}
                      className="mt-2"
                    >
                      <Plus className="mr-2 h-4 w-4" />
                      Add Shipping Option
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Payment Methods */}
          <Card>
            <CardHeader>
              <CardTitle>Payment Methods</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <FormLabel>Accepted Payment Methods</FormLabel>
                <FormField
                  control={form.control}
                  name="paymentMethods.ping"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Ping (1.89% transaction fee applies)</FormLabel>
                      </div>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="paymentMethods.afterpay"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Afterpay (4.95% transaction fee applies)</FormLabel>
                      </div>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="paymentMethods.cash"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Cash</FormLabel>
                      </div>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="paymentMethods.bankDeposit"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>NZ Bank Deposit</FormLabel>
                      </div>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="paymentMethods.other"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Other</FormLabel>
                      </div>
                    </FormItem>
                  )}
                />
                {form.getValues('paymentMethods.other') && (
                  <FormField
                    control={form.control}
                    name="paymentMethods.otherDetails"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Other Payment Details</FormLabel>
                        <FormControl>
                          <Input placeholder="Specify other payment methods" {...field} />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                )}

                <FormField
                  control={form.control}
                  name="sendPaymentInstructions"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-3 space-y-0 mt-6">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Send payment instructions automatically</FormLabel>
                        <FormDescription>
                          Payment instructions will be sent to the buyer after purchase.
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Auction Upgrades */}
          <Card>
            <CardHeader>
              <CardTitle>Auction Upgrades</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <FormField
                control={form.control}
                name="auctionUpgrade"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Listing Upgrade</FormLabel>
                    <FormControl>
                      <RadioGroup
                        value={field.value}
                        onValueChange={field.onChange}
                        className="space-y-4"
                      >
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="basic" />
                          </FormControl>
                          <FormLabel className="font-normal">
                            Basic (Free)
                          </FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="gallery" />
                          </FormControl>
                          <FormLabel className="font-normal">
                            Gallery (55c)
                          </FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="feature" />
                          </FormControl>
                          <FormLabel className="font-normal">
                            Feature ($3.45)
                          </FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="featureCombo" />
                          </FormControl>
                          <FormLabel className="font-normal">
                            Feature Combo ($3.95)
                          </FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="superFeature" />
                          </FormControl>
                          <FormLabel className="font-normal">
                            Super Feature ($9.95)
                          </FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Ghost Specific Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Ghost Specific Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Stock Code (SKU) */}
              <FormField
                control={form.control}
                name="stockCode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Stock Code (SKU)</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter Stock Code" {...field} />
                    </FormControl>
                    <FormDescription>
                      Internal reference number for this item. Images will be saved to a folder with this name.
                    </FormDescription>
                  </FormItem>
                )}
              />

              {/* Buyer */}
              <FormField
                control={form.control}
                name="buyer"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Buyer</FormLabel>
                    <Select
                      value={field.value}
                      onValueChange={field.onChange}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select a buyer" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">None</SelectItem>
                        <SelectItem value="Imported">Imported</SelectItem>
                        {loadingUsers ? (
                          <div className="p-2 text-center">
                            <RefreshCw className="h-4 w-4 animate-spin mx-auto mb-1" />
                            <span className="text-sm">Loading users...</span>
                          </div>
                        ) : (
                          users.map(user => (
                            <SelectItem key={user._id} value={user._id}>
                              {user.fullName}
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Employee who purchased the item from customer (not the TradeMe auction winner).
                    </FormDescription>
                  </FormItem>
                )}
              />

              {/* Item Cost */}
              <FormField
                control={form.control}
                name="itemCost"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Item Cost</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <DollarSign className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                        <Input type="number" placeholder="0.00" className="pl-8" {...field} />
                      </div>
                    </FormControl>
                    <FormDescription>
                      Cost Price for this item (not shown on Trademe, used for profit calculations).
                    </FormDescription>
                  </FormItem>
                )}
              />

              {/* Auto Relist */}
              <FormField
                control={form.control}
                name="autoRelist"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Automatic Relist</FormLabel>
                      <FormDescription>
                        Automatically relist this item if it doesn't sell.
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />
              {form.getValues('autoRelist') === true && (
                <div className="space-y-4 mt-4 ml-7">
                  <FormField
                    control={form.control}
                    name="relistPriceDecrease"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Decrease Start Price By</FormLabel>
                        <div className="flex items-center">
                          <FormControl>
                            <Input
                              type="number"
                              min="0"
                              max="100"
                              className="w-20"
                              {...field}
                            />
                          </FormControl>
                          <span className="ml-2">%</span>
                        </div>
                        <FormDescription>
                          Automatically reduce the starting price when relisting.
                        </FormDescription>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="relistBuyNowDecrease"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Decrease Buy Now Price By</FormLabel>
                        <div className="flex items-center">
                          <FormControl>
                            <Input
                              type="number"
                              min="0"
                              max="100"
                              className="w-20"
                              {...field}
                            />
                          </FormControl>
                          <span className="ml-2">%</span>
                        </div>
                        <FormDescription>
                          Automatically reduce the Buy Now price when relisting.
                        </FormDescription>
                      </FormItem>
                    )}
                  />
                </div>
              )}

              {/* Expired Pawn Loan */}
              <FormField
                control={form.control}
                name="expiredPawnLoan"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Expired Pawn Loan Listing</FormLabel>
                      <FormDescription>
                        Mark this item as coming from an expired pawn loan.
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

{/* Notes Section */}
<Card>
  <CardHeader>
    <CardTitle>Notes</CardTitle>
    <CardDescription>Add internal notes about this listing (not visible to buyers)</CardDescription>
  </CardHeader>
  <CardContent>
    <div className="space-y-4">
      {/* Display existing notes */}
      {notes.length > 0 ? (
        <div className="space-y-3 max-h-60 overflow-y-auto p-2 border rounded-md">
          {notes.map((note) => (
            <div key={note._id} className="p-3 bg-secondary rounded-md">
              <div className="text-sm">{note.content}</div>
              <div className="text-xs text-muted-foreground mt-1 flex justify-between">
                <span>By: {note.createdBy?.username || 'Unknown'}</span>
                <span>{new Date(note.createdAt).toLocaleString()}</span>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-4 text-muted-foreground">
          No notes yet
        </div>
      )}

      {/* Add new note */}
      <div className="flex flex-col space-y-2">
        <Textarea
          placeholder="Add a note about this listing..."
          value={newNote}
          onChange={(e) => setNewNote(e.target.value)}
          className="min-h-20"
        />
        <Button
          type="button"
          onClick={handleAddNote}
          className="self-end"
          variant="outline"
          size="sm"
        >
          Add Note
        </Button>
      </div>
    </div>
  </CardContent>
</Card>
</div>

{/* Right column - Summary and actions */}
<div className="space-y-6">

<Card className="sticky top-28">
<CardHeader>
<CardTitle>Listing Summary</CardTitle>
</CardHeader>
<CardContent className="space-y-6">
<div className="space-y-2">
<div className="flex justify-between text-sm">
<span>Start Price:</span>
<span>${watchPrice.toFixed(2)}</span>
</div>
{watchCustomReserve && (
<div className="flex justify-between text-sm">
  <span>Reserve Price:</span>
  <span>${watchReservePrice?.toFixed(2) || '0.00'}</span>
</div>
)}
{(form.getValues('buyNowPrice') || 0) > 0 && (
<div className="flex justify-between text-sm">
  <span>Buy Now Price:</span>
  <span>${(form.getValues('buyNowPrice') || 0).toFixed(2)}</span>
</div>
)}
<Separator />
<div className="flex justify-between text-sm">
<span>Success Fee (7.9%):</span>
<span>${successFee.toFixed(2)}</span>
</div>
<div className="flex justify-between text-sm">
<span>Upgrades Fee:</span>
<span>${upgradesFee.toFixed(2)}</span>
</div>
<Separator />
<div className="flex justify-between font-bold">
<span>Total Fees:</span>
<span>${totalFees.toFixed(2)}</span>
</div>
{(form.getValues('itemCost') || 0) > 0 && (
<>
  <Separator />
  <div className="flex justify-between text-sm">
    <span>Item Cost:</span>
    <span>${(form.getValues('itemCost') || 0).toFixed(2)}</span>
  </div>
  <div className="flex justify-between text-sm">
    <span>Potential Profit:</span>
    <span>${(watchPrice - (form.getValues('itemCost') || 0) - totalFees).toFixed(2)}</span>
  </div>
</>
)}
</div>

{characterCount > 4000 && (
<div className="bg-red-100 dark:bg-red-900/20 p-4 rounded-md flex items-start space-x-2">
<AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400 flex-shrink-0 mt-0.5" />
<div className="text-sm text-red-600 dark:text-red-400">
  <p className="font-medium">Description too long</p>
  <p>Combined description and footer text exceeds the 4000 character limit.</p>
</div>
</div>
)}

<div className="space-y-3 pt-4">
<Button
type="button"
className="w-full"
onClick={saveAsDraft}
disabled={loading}
>
<Save className="mr-2 h-4 w-4" />
Save as Draft
</Button>
<Button
type="submit"
className="w-full"
disabled={loading || characterCount > 4000}
>
{isEditing ? 'Update Listing' : 'List Item'}
</Button>
<Button
type="button"
variant="outline"
className="w-full"
onClick={resetForm}
disabled={loading}
>
Reset Form
</Button>
</div>
</CardContent>
</Card>

</div>
</div>
</form>
</Form>
</div>
);

}