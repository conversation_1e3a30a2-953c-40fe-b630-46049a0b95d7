import { toast } from '@/hooks/useToast';

// Define interfaces for HID functionality
export interface HIDDeviceInfo {
  device: HIDDevice;
  connected: boolean;
  productName: string;
  vendorId: number;
  productId: number;
  collections: HIDCollectionInfo[];
  reports: {
    input: Map<number, DataView>;
    feature: Map<number, DataView>;
  };
}

export interface HIDCollectionInfo {
  usage: number;
  usagePage: number;
  children: HIDCollectionInfo[];
  inputReports: HIDReportInfo[];
  outputReports: HIDReportInfo[];
  featureReports: HIDReportInfo[];
}

export interface HIDReportInfo {
  reportId: number;
  items: HIDReportItemInfo[];
}

export interface HIDReportItemInfo {
  usagePage: number;
  usage: number;
  reportSize: number;
  reportCount: number;
}

// Controller-specific constants
export const KNOWN_CONTROLLERS = [
  {
    name: 'DualSense (PS5)',
    vendorId: 0x054c, // Sony
    productId: 0x0ce6, // DualSense
    usagePage: 0x01,   // Generic Desktop Controls
    usage: 0x05        // Game Pad
  },
  {
    name: 'DualSense Edge (PS5)',
    vendorId: 0x054c, // Sony
    productId: 0x0df2, // DualSense Edge
    usagePage: 0x01,   // Generic Desktop Controls
    usage: 0x05        // Game Pad
  },
  {
    name: 'DualShock 4 (PS4)',
    vendorId: 0x054c, // Sony
    productId: 0x09cc, // DualShock 4
    usagePage: 0x01,   // Generic Desktop Controls
    usage: 0x05        // Game Pad
  },
  {
    name: 'Xbox Controller',
    vendorId: 0x045e, // Microsoft
    productId: 0x02fd, // Xbox Controller
    usagePage: 0x01,   // Generic Desktop Controls
    usage: 0x05        // Game Pad
  },
  {
    name: 'Nintendo Switch Pro Controller',
    vendorId: 0x057e, // Nintendo
    productId: 0x2009, // Switch Pro Controller
    usagePage: 0x01,   // Generic Desktop Controls
    usage: 0x05        // Game Pad
  },
  {
    name: 'Nintendo Switch Joy-Con (L)',
    vendorId: 0x057e, // Nintendo
    productId: 0x2006, // Joy-Con Left
    usagePage: 0x01,   // Generic Desktop Controls
    usage: 0x05        // Game Pad
  },
  {
    name: 'Nintendo Switch Joy-Con (R)',
    vendorId: 0x057e, // Nintendo
    productId: 0x2007, // Joy-Con Right
    usagePage: 0x01,   // Generic Desktop Controls
    usage: 0x05        // Game Pad
  }
];

// Class to handle HID device connections and interactions
export class HIDManager {
  private devices: Map<string, HIDDeviceInfo> = new Map();
  private onDeviceConnectedCallbacks: ((device: HIDDeviceInfo) => void)[] = [];
  private onDeviceDisconnectedCallbacks: ((device: HIDDeviceInfo) => void)[] = [];
  private onInputReportCallbacks: ((device: HIDDeviceInfo, reportId: number, data: DataView) => void)[] = [];

  constructor() {
    this.setupEventListeners();
  }

  private setupEventListeners() {
    if ('hid' in navigator) {
      navigator.hid.addEventListener('connect', this.handleDeviceConnect.bind(this));
      navigator.hid.addEventListener('disconnect', this.handleDeviceDisconnect.bind(this));
    }
  }

  private async handleDeviceConnect(event: HIDConnectionEvent) {
    const { device } = event;

    // Create device info object
    const deviceInfo: HIDDeviceInfo = {
      device,
      connected: true,
      productName: device.productName,
      vendorId: device.vendorId,
      productId: device.productId,
      collections: device.collections,
      reports: {
        input: new Map(),
        feature: new Map()
      }
    };

    // Add device to map
    const deviceKey = `${device.vendorId}-${device.productId}`;
    this.devices.set(deviceKey, deviceInfo);

    // Setup input report listener
    device.addEventListener('inputreport', (event: HIDInputReportEvent) => {
      const { reportId, data } = event;
      deviceInfo.reports.input.set(reportId, data);
      this.onInputReportCallbacks.forEach(callback => callback(deviceInfo, reportId, data));
    });

    // Notify listeners
    this.onDeviceConnectedCallbacks.forEach(callback => callback(deviceInfo));

    toast({
      title: "HID Device Connected",
      description: `${device.productName} connected`,
    });
  }

  private handleDeviceDisconnect(event: HIDConnectionEvent) {
    const { device } = event;
    const deviceKey = `${device.vendorId}-${device.productId}`;

    const deviceInfo = this.devices.get(deviceKey);
    if (deviceInfo) {
      deviceInfo.connected = false;
      this.onDeviceDisconnectedCallbacks.forEach(callback => callback(deviceInfo));

      toast({
        title: "HID Device Disconnected",
        description: `${device.productName} disconnected`,
        variant: "destructive",
      });
    }
  }

  public async requestDevice() {
    if (!('hid' in navigator)) {
      toast({
        title: "WebHID Not Supported",
        description: "Your browser doesn't support WebHID API. Try using Chrome or Edge.",
        variant: "destructive",
      });
      return null;
    }

    try {
      // Create filters for known controllers
      const filters = KNOWN_CONTROLLERS.map(controller => ({
        vendorId: controller.vendorId,
        productId: controller.productId,
        usagePage: controller.usagePage,
        usage: controller.usage
      }));

      // Add a generic gamepad filter
      filters.push({
        usagePage: 0x01,  // Generic Desktop Controls
        usage: 0x05       // Game Pad
      });

      const devices = await navigator.hid.requestDevice({ filters });

      if (devices.length === 0) {
        toast({
          title: "No Device Selected",
          description: "No controller was selected.",
        });
        return null;
      }

      // Open the device
      const device = devices[0];
      if (!device.opened) {
        await device.open();
      }

      // Create device info
      const deviceInfo: HIDDeviceInfo = {
        device,
        connected: true,
        productName: device.productName,
        vendorId: device.vendorId,
        productId: device.productId,
        collections: device.collections,
        reports: {
          input: new Map(),
          feature: new Map()
        }
      };

      // Setup input report listener
      device.addEventListener('inputreport', (event: HIDInputReportEvent) => {
        const { reportId, data } = event;
        deviceInfo.reports.input.set(reportId, data);
        this.onInputReportCallbacks.forEach(callback => callback(deviceInfo, reportId, data));
      });

      // Add to devices map
      const deviceKey = `${device.vendorId}-${device.productId}`;
      this.devices.set(deviceKey, deviceInfo);

      // Notify listeners
      this.onDeviceConnectedCallbacks.forEach(callback => callback(deviceInfo));

      toast({
        title: "HID Device Connected",
        description: `${device.productName} connected and opened`,
      });

      return deviceInfo;
    } catch (error) {
      console.error('Error requesting HID device:', error);
      toast({
        title: "Error",
        description: `Failed to connect to HID device: ${error.message}`,
        variant: "destructive",
      });
      return null;
    }
  }

  public async getDevices() {
    if (!('hid' in navigator)) {
      return [];
    }

    try {
      const devices = await navigator.hid.getDevices();

      // Create device info objects for each device
      for (const device of devices) {
        const deviceKey = `${device.vendorId}-${device.productId}`;

        // Skip if we already have this device
        if (this.devices.has(deviceKey)) {
          continue;
        }

        // Create device info
        const deviceInfo: HIDDeviceInfo = {
          device,
          connected: true,
          productName: device.productName,
          vendorId: device.vendorId,
          productId: device.productId,
          collections: device.collections,
          reports: {
            input: new Map(),
            feature: new Map()
          }
        };

        // Setup input report listener
        device.addEventListener('inputreport', (event: HIDInputReportEvent) => {
          const { reportId, data } = event;
          deviceInfo.reports.input.set(reportId, data);
          this.onInputReportCallbacks.forEach(callback => callback(deviceInfo, reportId, data));
        });

        // Add to devices map
        this.devices.set(deviceKey, deviceInfo);

        // Open the device if it's not already open
        if (!device.opened) {
          try {
            await device.open();
          } catch (error) {
            console.error(`Error opening device ${device.productName}:`, error);
          }
        }
      }

      return Array.from(this.devices.values());
    } catch (error) {
      console.error('Error getting HID devices:', error);
      return [];
    }
  }

  public async sendReport(device: HIDDeviceInfo, reportId: number, data: Uint8Array) {
    if (!device.connected || !device.device.opened) {
      try {
        await device.device.open();
      } catch (error) {
        console.error('Error opening device:', error);
        toast({
          title: "Error",
          description: `Failed to open device: ${error.message}`,
          variant: "destructive",
        });
        return false;
      }
    }

    try {
      // Make sure the first byte of the data is the report ID
      if (data[0] !== reportId) {
        console.warn(`Report ID mismatch: data[0]=${data[0]}, reportId=${reportId}. Setting data[0] to reportId.`);
        data[0] = reportId;
      }

      // Log the report being sent
      console.log(`Sending report ID ${reportId} to device ${device.productName}:`,
                  Array.from(data.slice(0, Math.min(20, data.length))).map(b => b.toString(16).padStart(2, '0')).join(' ') +
                  (data.length > 20 ? '...' : ''));

      await device.device.sendReport(reportId, data);
      return true;
    } catch (error) {
      console.error('Error sending report:', error);
      toast({
        title: "Error",
        description: `Failed to send report: ${error.message}`,
        variant: "destructive",
      });
      return false;
    }
  }

  public async sendFeatureReport(device: HIDDeviceInfo, reportId: number, data: Uint8Array) {
    if (!device.connected || !device.device.opened) {
      try {
        await device.device.open();
      } catch (error) {
        console.error('Error opening device:', error);
        toast({
          title: "Error",
          description: `Failed to open device: ${error.message}`,
          variant: "destructive",
        });
        return false;
      }
    }

    try {
      await device.device.sendFeatureReport(reportId, data);
      return true;
    } catch (error) {
      console.error('Error sending feature report:', error);
      toast({
        title: "Error",
        description: `Failed to send feature report: ${error.message}`,
        variant: "destructive",
      });
      return false;
    }
  }

  public async receiveFeatureReport(device: HIDDeviceInfo, reportId: number) {
    if (!device.connected || !device.device.opened) {
      try {
        await device.device.open();
      } catch (error) {
        console.error('Error opening device:', error);
        toast({
          title: "Error",
          description: `Failed to open device: ${error.message}`,
          variant: "destructive",
        });
        return null;
      }
    }

    try {
      const report = await device.device.receiveFeatureReport(reportId);
      device.reports.feature.set(reportId, report);
      return report;
    } catch (error) {
      console.error('Error receiving feature report:', error);
      toast({
        title: "Error",
        description: `Failed to receive feature report: ${error.message}`,
        variant: "destructive",
      });
      return null;
    }
  }

  public onDeviceConnected(callback: (device: HIDDeviceInfo) => void) {
    this.onDeviceConnectedCallbacks.push(callback);
  }

  public onDeviceDisconnected(callback: (device: HIDDeviceInfo) => void) {
    this.onDeviceDisconnectedCallbacks.push(callback);
  }

  public onInputReport(callback: (device: HIDDeviceInfo, reportId: number, data: DataView) => void) {
    this.onInputReportCallbacks.push(callback);
  }

  public isWebHIDSupported(): boolean {
    return 'hid' in navigator;
  }

  // Helper function to get a known controller name from vendor and product IDs
  public getControllerName(vendorId: number, productId: number): string {
    const controller = KNOWN_CONTROLLERS.find(
      c => c.vendorId === vendorId && c.productId === productId
    );
    return controller ? controller.name : 'Unknown Controller';
  }
}

// Create a singleton instance
export const hidManager = new HIDManager();

// Helper functions for specific controllers

// DualSense (PS5) controller helpers
export const DualSense = {
  // Report IDs
  REPORT_STATE: 0x01,
  REPORT_OUTPUT: 0x02,

  // Output report flags
  FLAGS: {
    COMPATIBLE_VIBRATION: 0x01,
    HAPTICS_SELECT: 0x02,
    LED_CONTROL: 0x04,
    LED_BRIGHTNESS: 0x08,
    LED_PLAYER: 0x10,
    PLAYER_LEDS: 0x20,
    RESET_LIGHTS: 0x40,
    RESET_LEDS: 0x80
  },

  // Trigger effect modes
  TRIGGER_EFFECT_MODE: {
    OFF: 0x00,
    FEEDBACK: 0x01,
    WEAPON: 0x02,
    VIBRATION: 0x05,
    MULTIPLE_POSITION_FEEDBACK: 0x20,
    SLOPE_FEEDBACK: 0x21,
    MULTIPLE_POSITION_VIBRATION: 0x22,
    CALIBRATION: 0xFC
  },

  // Vibration
  async setVibration(device: HIDDeviceInfo, leftIntensity: number, rightIntensity: number) {
    try {
      // Clamp values between 0 and 255
      leftIntensity = Math.max(0, Math.min(255, leftIntensity));
      rightIntensity = Math.max(0, Math.min(255, rightIntensity));

      // Create output report
      const data = new Uint8Array(48).fill(0);

      // Set report ID
      data[0] = DualSense.REPORT_OUTPUT;

      // Set flags for vibration
      data[1] = DualSense.FLAGS.HAPTICS_SELECT;

      // Set vibration data
      data[3] = rightIntensity; // Right motor
      data[4] = leftIntensity;  // Left motor

      console.log("Sending vibration report:", Array.from(data).map(b => b.toString(16).padStart(2, '0')).join(' '));

      return await hidManager.sendReport(device, DualSense.REPORT_OUTPUT, data);
    } catch (error) {
      console.error("Error setting DualSense vibration:", error);
      return false;
    }
  },

  // LED control
  async setLightbarColor(device: HIDDeviceInfo, r: number, g: number, b: number) {
    try {
      // Clamp RGB values between 0 and 255
      r = Math.max(0, Math.min(255, r));
      g = Math.max(0, Math.min(255, g));
      b = Math.max(0, Math.min(255, b));

      // Create output report
      const data = new Uint8Array(48).fill(0);

      // Set report ID
      data[0] = DualSense.REPORT_OUTPUT;

      // Set flags for LED control
      data[1] = DualSense.FLAGS.LED_CONTROL;

      // Set lightbar color (bytes 45, 46, 47)
      data[45] = r;
      data[46] = g;
      data[47] = b;

      console.log("Sending LED report:", Array.from(data).map(b => b.toString(16).padStart(2, '0')).join(' '));

      return await hidManager.sendReport(device, DualSense.REPORT_OUTPUT, data);
    } catch (error) {
      console.error("Error setting DualSense LED color:", error);
      return false;
    }
  },

  // Adaptive triggers
  async setAdaptiveTrigger(device: HIDDeviceInfo, trigger: 'left' | 'right', mode: number, start: number, force: number) {
    try {
      // Create output report
      const data = new Uint8Array(48).fill(0);

      // Set report ID
      data[0] = DualSense.REPORT_OUTPUT;

      // Set flags for trigger effect
      data[1] = 0; // No haptics or LED control

      // Trigger effect parameters
      if (trigger === 'left') {
        data[11] = mode;     // Effect mode
        data[12] = start;    // Start position
        data[13] = force;    // Force
      } else {
        data[20] = mode;     // Effect mode
        data[21] = start;    // Start position
        data[22] = force;    // Force
      }

      console.log("Sending trigger report:", Array.from(data).map(b => b.toString(16).padStart(2, '0')).join(' '));

      return await hidManager.sendReport(device, DualSense.REPORT_OUTPUT, data);
    } catch (error) {
      console.error(`Error setting DualSense ${trigger} trigger:`, error);
      return false;
    }
  },

  // Combined function to set multiple features at once
  async setControllerState(device: HIDDeviceInfo, options: {
    leftVibration?: number,
    rightVibration?: number,
    lightR?: number,
    lightG?: number,
    lightB?: number,
    leftTriggerMode?: number,
    leftTriggerStart?: number,
    leftTriggerForce?: number,
    rightTriggerMode?: number,
    rightTriggerStart?: number,
    rightTriggerForce?: number
  }) {
    try {
      // Create output report
      const data = new Uint8Array(48).fill(0);

      // Set report ID
      data[0] = DualSense.REPORT_OUTPUT;

      // Initialize flags
      let flags = 0;

      // Set vibration if provided
      if (options.leftVibration !== undefined || options.rightVibration !== undefined) {
        flags |= DualSense.FLAGS.HAPTICS_SELECT;
        data[3] = options.rightVibration !== undefined ? Math.max(0, Math.min(255, options.rightVibration)) : 0;
        data[4] = options.leftVibration !== undefined ? Math.max(0, Math.min(255, options.leftVibration)) : 0;
      }

      // Set LED color if provided
      if (options.lightR !== undefined || options.lightG !== undefined || options.lightB !== undefined) {
        flags |= DualSense.FLAGS.LED_CONTROL;
        data[45] = options.lightR !== undefined ? Math.max(0, Math.min(255, options.lightR)) : 0;
        data[46] = options.lightG !== undefined ? Math.max(0, Math.min(255, options.lightG)) : 0;
        data[47] = options.lightB !== undefined ? Math.max(0, Math.min(255, options.lightB)) : 0;
      }

      // Set left trigger if provided
      if (options.leftTriggerMode !== undefined) {
        data[11] = options.leftTriggerMode;
        data[12] = options.leftTriggerStart !== undefined ? options.leftTriggerStart : 0;
        data[13] = options.leftTriggerForce !== undefined ? options.leftTriggerForce : 0;
      }

      // Set right trigger if provided
      if (options.rightTriggerMode !== undefined) {
        data[20] = options.rightTriggerMode;
        data[21] = options.rightTriggerStart !== undefined ? options.rightTriggerStart : 0;
        data[22] = options.rightTriggerForce !== undefined ? options.rightTriggerForce : 0;
      }

      // Set flags
      data[1] = flags;

      console.log("Sending combined state report:", Array.from(data).map(b => b.toString(16).padStart(2, '0')).join(' '));

      return await hidManager.sendReport(device, DualSense.REPORT_OUTPUT, data);
    } catch (error) {
      console.error("Error setting DualSense controller state:", error);
      return false;
    }
  },

  // Parse input report
  parseInputReport(data: DataView): any {
    // This is a simplified parser - a real implementation would be more comprehensive
    if (data.byteLength < 10) return null;

    return {
      buttons: {
        square: (data.getUint8(8) & 0x10) !== 0,
        cross: (data.getUint8(8) & 0x20) !== 0,
        circle: (data.getUint8(8) & 0x40) !== 0,
        triangle: (data.getUint8(8) & 0x80) !== 0,
        l1: (data.getUint8(8) & 0x01) !== 0,
        r1: (data.getUint8(8) & 0x02) !== 0,
        l2: (data.getUint8(8) & 0x04) !== 0,
        r2: (data.getUint8(8) & 0x08) !== 0,
        create: (data.getUint8(9) & 0x10) !== 0,
        options: (data.getUint8(9) & 0x20) !== 0,
        l3: (data.getUint8(9) & 0x40) !== 0,
        r3: (data.getUint8(9) & 0x80) !== 0,
        ps: (data.getUint8(10) & 0x01) !== 0,
        touchpad: (data.getUint8(10) & 0x02) !== 0
      },
      axes: {
        leftX: data.getUint8(1) - 128,
        leftY: data.getUint8(2) - 128,
        rightX: data.getUint8(3) - 128,
        rightY: data.getUint8(4) - 128
      },
      triggers: {
        l2: data.getUint8(5),
        r2: data.getUint8(6)
      }
    };
  }
};

// Xbox controller helpers
export const Xbox = {
  // Vibration
  async setVibration(device: HIDDeviceInfo, leftIntensity: number, rightIntensity: number) {
    // Clamp values between 0 and 255
    leftIntensity = Math.max(0, Math.min(255, leftIntensity));
    rightIntensity = Math.max(0, Math.min(255, rightIntensity));

    // Create output report - this is a simplified example
    const data = new Uint8Array(8);
    data[0] = 0x03; // Report ID for Xbox controllers
    data[1] = 0x00; // Command
    data[2] = 0x0F; // Mode
    data[3] = leftIntensity;  // Left motor
    data[4] = rightIntensity; // Right motor

    return hidManager.sendReport(device, 0x03, data);
  }
};

// Nintendo Switch Pro controller helpers
export const SwitchPro = {
  // Vibration
  async setVibration(device: HIDDeviceInfo, leftIntensity: number, rightIntensity: number) {
    // Clamp values between 0 and 255
    leftIntensity = Math.max(0, Math.min(255, leftIntensity));
    rightIntensity = Math.max(0, Math.min(255, rightIntensity));

    // Enable vibration first
    const enableData = new Uint8Array([0x01, 0x00, 0x01, 0x40, 0x40, 0x00, 0x01, 0x40, 0x40]);
    await hidManager.sendReport(device, 0x01, enableData);

    // Create rumble data
    const data = new Uint8Array(10);
    data[0] = 0x10; // Report ID
    data[1] = 0x80; // Timer

    // Set rumble data for left and right
    const rumbleData = calculateSwitchRumbleData(leftIntensity, rightIntensity);
    data.set(rumbleData, 2);

    return hidManager.sendReport(device, 0x10, data);
  }
};

// Helper function to calculate Switch rumble data
function calculateSwitchRumbleData(leftIntensity: number, rightIntensity: number): Uint8Array {
  // This is a simplified implementation
  // Real implementation would use frequency and amplitude calculations
  const data = new Uint8Array(8);

  // Convert intensity (0-255) to amplitude (0-1)
  const leftAmp = leftIntensity / 255;
  const rightAmp = rightIntensity / 255;

  // Set frequency to a mid-range value
  const freq = 0x40;

  // Encode values
  data[0] = freq; // Left low frequency
  data[1] = Math.floor(leftAmp * 255); // Left low amplitude
  data[2] = freq; // Left high frequency
  data[3] = Math.floor(leftAmp * 255); // Left high amplitude

  data[4] = freq; // Right low frequency
  data[5] = Math.floor(rightAmp * 255); // Right low amplitude
  data[6] = freq; // Right high frequency
  data[7] = Math.floor(rightAmp * 255); // Right high amplitude

  return data;
}
