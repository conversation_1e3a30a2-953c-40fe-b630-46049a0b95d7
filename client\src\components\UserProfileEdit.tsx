import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { updateCurrentUser, checkUsernameAvailability } from "@/api/user";
import { useToast } from "@/hooks/useToast";
import { getLocations } from "@/api/locations";
import { UserPasswordChange } from "@/components/UserPasswordChange";
import { Separator } from "@/components/ui/separator";

const formSchema = z.object({
  role: z.string().optional(),
  location: z.string().nullable().optional(),
  username: z.string().min(3, "Username must be at least 3 characters").optional().nullable(),
});

export function UserProfileEdit() {
  const { user, refreshUserData } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [locations, setLocations] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isFormReady, setIsFormReady] = useState(false);
  const [isCheckingUsername, setIsCheckingUsername] = useState(false);
  const [isUsernameAvailable, setIsUsernameAvailable] = useState(true);

  // Initialize form with empty values first
  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      role: "",
      location: "",
      username: "",
    },
  });

  // First, fetch locations
  useEffect(() => {
    const fetchLocations = async () => {
      try {
        const response = await getLocations();
        setLocations(response.locations || []);
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to load locations: " + error.message,
          variant: "destructive",
        });
      }
    };

    fetchLocations();
  }, [toast]);

  // Then, when user data and locations are available, reset the form
  useEffect(() => {
    if (user) {
      console.log("Setting form values with user data:", {
        role: user.role || "",
        location: user.location?._id || "none",
        username: user.username || "",
      });

      // Reset form with user data
      form.reset({
        role: user.role || "",
        location: user.location?._id || "none",
        username: user.username || "",
      });

      setIsFormReady(true);
    }
  }, [user, form]);

  // Check username availability when it changes
  const checkUsername = async (username) => {
    if (!username || (user && username === user.username)) {
      setIsUsernameAvailable(true);
      return true;
    }

    setIsCheckingUsername(true);
    try {
      const available = await checkUsernameAvailability(username, user?._id);
      setIsUsernameAvailable(available);
      return available;
    } catch (error) {
      console.error("Error checking username:", error);
      return false;
    } finally {
      setIsCheckingUsername(false);
    }
  };

  // Log form values for debugging
  useEffect(() => {
    const subscription = form.watch((value) => {
      console.log("Form values changed:", value);

      // Check username availability when it changes
      if (value.username !== user?.username) {
        const timeoutId = setTimeout(() => {
          checkUsername(value.username);
        }, 500);

        return () => clearTimeout(timeoutId);
      }
    });
    return () => subscription.unsubscribe();
  }, [form, user]);

  const onSubmit = async (values) => {
    console.log("Form submitted with values:", values);

    // Verify username availability before submitting
    if (values.username && values.username !== user.username) {
      const available = await checkUsername(values.username);
      if (!available) {
        toast({
          title: "Error",
          description: "Username is already taken",
          variant: "destructive",
        });
        return;
      }
    }

    setIsLoading(true);
    try {
      // Convert "none" location to null
      const submitData = {
        ...values,
        location: values.location === "none" ? null : values.location,
        username: values.username || null,
      };

      console.log("Submitting data to API:", submitData);
      await updateCurrentUser(submitData);
      console.log("API call successful");

      await refreshUserData();

      toast({
        title: "Success",
        description: "Profile updated successfully",
      });

      navigate("/profile");
    } catch (error) {
      console.error("Error updating profile:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to update profile",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!isFormReady) {
    return <div>Loading user information...</div>;
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Edit Profile</CardTitle>
          <CardDescription>Update your account information</CardDescription>
        </CardHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <CardContent className="space-y-4">
              <div>
                <h3 className="text-sm font-medium mb-2">Email</h3>
                <p className="text-gray-500">{user?.email}</p>
              </div>

              <FormField
                control={form.control}
                name="username"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Username
                      {isCheckingUsername && <span className="ml-2 text-xs text-muted-foreground">Checking...</span>}
                      {field.value && !isCheckingUsername && isUsernameAvailable &&
                        <span className="ml-2 text-xs text-green-500">Available</span>
                      }
                      {field.value && !isCheckingUsername && !isUsernameAvailable &&
                        <span className="ml-2 text-xs text-red-500">Already taken</span>
                      }
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="Enter a username"
                      />
                    </FormControl>
                    <FormDescription>
                      A unique username allows you to log in with either username or email
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="role"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Role</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      value={field.value || ""}
                      disabled={user?.role !== 'admin'}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select role" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="admin">Admin</SelectItem>
                        <SelectItem value="manager">Manager</SelectItem>
                        <SelectItem value="employee">Employee</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      {user?.role === 'admin'
                        ? "Your role determines what actions you can perform in the system"
                        : "Only admin users can change roles"}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Location</FormLabel>
                    <Select
                      onValueChange={(value) => field.onChange(value === "none" ? null : value)}
                      value={field.value || "none"}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select location" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="none">No Location</SelectItem>
                        {locations.map((location) => (
                          <SelectItem key={location._id} value={location._id}>
                            {location.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      The location you are assigned to
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" onClick={() => navigate("/profile")} type="button">
                Cancel
              </Button>
              <Button 
                type="submit" 
                disabled={isLoading || (form.getValues().username && !isUsernameAvailable)}
              >
                {isLoading ? "Saving..." : "Save Changes"}
              </Button>
            </CardFooter>
          </form>
        </Form>
      </Card>

      <Separator />

      <UserPasswordChange />
    </div>
  );
}