import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/useToast';
import { getAuditStats } from '@/api/buyPawnAudits';
import { AuditNavigation } from './AuditNavigation';
import { AuditComplianceChart } from './AuditComplianceChart';
import { AuditTypeDistribution } from './AuditTypeDistribution';
import { StaffComplianceTable } from './StaffComplianceTable';
import { Loader2, Download, FileText } from 'lucide-react';

/**
 * Component for detailed audit reporting and analytics
 */
export function AuditReporting() {
  const [stats, setStats] = useState<any>(null);
  const [staffData, setStaffData] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isExporting, setIsExporting] = useState(false);
  const [timeRange, setTimeRange] = useState('30days');
  const { toast } = useToast();

  // Fetch audit statistics
  useEffect(() => {
    const fetchStats = async () => {
      try {
        setIsLoading(true);

        // Get audit stats with the selected time range
        const result = await getAuditStats({ timeRange });

        if (result.success) {
          setStats(result.data);

          // Set staff data from the stats
          if (result.data.staffCompliance) {
            setStaffData(result.data.staffCompliance);
          }
        } else {
          toast({
            title: 'Error',
            description: result.error || 'Failed to load audit statistics.',
            variant: 'destructive',
          });
        }
      } catch (error: any) {
        toast({
          title: 'Error',
          description: error.message || 'An unexpected error occurred.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchStats();
  }, [timeRange, toast]);

  // Handle export to PDF
  const handleExportPDF = async () => {
    try {
      setIsExporting(true);
      toast({
        title: 'Export Started',
        description: 'Generating PDF report...',
      });

      // Call the API to generate and download the PDF
      const response = await fetch('/api/buys/audits/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          format: 'pdf',
          type: 'audit_reporting',
          timeRange,
          includeCharts: true,
          includeDetails: true,
        }),
      });

      setIsExporting(false);

      if (response.ok) {
        toast({
          title: 'Export Complete',
          description: 'PDF report has been downloaded.',
        });
      } else {
        const errorData = await response.json();
        toast({
          title: 'Export Failed',
          description: errorData.error || 'Failed to generate PDF report.',
          variant: 'destructive',
        });
      }
    } catch (error: any) {
      setIsExporting(false);
      toast({
        title: 'Export Failed',
        description: error.message || 'An unexpected error occurred.',
        variant: 'destructive',
      });
    }
  };

  // Handle export to CSV
  const handleExportCSV = async () => {
    try {
      setIsExporting(true);
      toast({
        title: 'Export Started',
        description: 'Generating CSV export...',
      });

      // Call the API to generate and download the CSV
      const response = await fetch('/api/buys/audits/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          format: 'csv',
          type: 'audit_reporting',
          timeRange,
          includeDetails: true,
        }),
      });

      setIsExporting(false);

      if (response.ok) {
        toast({
          title: 'Export Complete',
          description: 'CSV data has been downloaded.',
        });
      } else {
        const errorData = await response.json();
        toast({
          title: 'Export Failed',
          description: errorData.error || 'Failed to generate CSV export.',
          variant: 'destructive',
        });
      }
    } catch (error: any) {
      setIsExporting(false);
      toast({
        title: 'Export Failed',
        description: error.message || 'An unexpected error occurred.',
        variant: 'destructive',
      });
    }
  };

  if (isLoading && !stats) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col gap-4 mb-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold">Audit Reporting</h1>
            <p className="text-muted-foreground">Detailed analytics and reporting for audits</p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={handleExportCSV}
              disabled={isExporting}
            >
              {isExporting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Exporting...
                </>
              ) : (
                <>
                  <FileText className="mr-2 h-4 w-4" />
                  Export CSV
                </>
              )}
            </Button>
            <Button
              onClick={handleExportPDF}
              disabled={isExporting}
            >
              {isExporting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Exporting...
                </>
              ) : (
                <>
                  <Download className="mr-2 h-4 w-4" />
                  Export PDF
                </>
              )}
            </Button>
          </div>
        </div>

        <AuditNavigation />
      </div>

      <div className="flex justify-end mb-4">
        <Select value={timeRange} onValueChange={setTimeRange}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select time range" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7days">Last 7 days</SelectItem>
            <SelectItem value="30days">Last 30 days</SelectItem>
            <SelectItem value="90days">Last 90 days</SelectItem>
            <SelectItem value="year">Last year</SelectItem>
            <SelectItem value="all">All time</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {stats && (
        <div className="space-y-6">
          <Tabs defaultValue="trends">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="trends">Compliance Trends</TabsTrigger>
              <TabsTrigger value="distribution">Audit Distribution</TabsTrigger>
              <TabsTrigger value="staff">Staff Performance</TabsTrigger>
            </TabsList>

            <TabsContent value="trends" className="space-y-6 mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Compliance Trends Over Time</CardTitle>
                  <CardDescription>
                    Tracking compliance rates across all audit types
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[400px]">
                    <AuditComplianceChart data={stats.complianceTrends} />
                  </div>
                </CardContent>
              </Card>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Compliance by Month</CardTitle>
                    <CardDescription>
                      Monthly compliance rates
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[300px]">
                      {/* Additional chart would go here */}
                      <div className="flex items-center justify-center h-full">
                        <p className="text-muted-foreground">Monthly compliance chart</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Compliance by Week</CardTitle>
                    <CardDescription>
                      Weekly compliance rates
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[300px]">
                      {/* Additional chart would go here */}
                      <div className="flex items-center justify-center h-full">
                        <p className="text-muted-foreground">Weekly compliance chart</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="distribution" className="space-y-6 mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Audit Type Distribution</CardTitle>
                  <CardDescription>
                    Breakdown of audits by type and compliance status
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[400px]">
                    <AuditTypeDistribution data={stats.auditTypeDistribution} />
                  </div>
                </CardContent>
              </Card>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Common Non-Compliance Issues</CardTitle>
                    <CardDescription>
                      Most frequent reasons for non-compliance
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[300px]">
                      {/* Additional chart would go here */}
                      <div className="flex items-center justify-center h-full">
                        <p className="text-muted-foreground">Non-compliance issues chart</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Follow-up Status</CardTitle>
                    <CardDescription>
                      Status of follow-up actions for flagged audits
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[300px]">
                      {/* Additional chart would go here */}
                      <div className="flex items-center justify-center h-full">
                        <p className="text-muted-foreground">Follow-up status chart</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="staff" className="space-y-6 mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Staff Compliance Performance</CardTitle>
                  <CardDescription>
                    Detailed performance metrics by staff member
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <StaffComplianceTable data={staffData} />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Staff Compliance Over Time</CardTitle>
                  <CardDescription>
                    Tracking individual staff member compliance trends
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[400px]">
                    {/* Staff compliance trend chart would go here */}
                    <div className="flex items-center justify-center h-full">
                      <p className="text-muted-foreground">Staff compliance trend chart</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      )}
    </div>
  );
}
