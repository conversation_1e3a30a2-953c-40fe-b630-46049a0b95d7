import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
    conditions: ['module', 'browser', 'development'],
  },
  server: {
    host: true, // Listen on all network interfaces
    port: 5173,
    allowedHosts: ['rotorua.ghst.nz'], // <-- Add this line
    proxy: {
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true,
        secure: false,
        xfwd: true, // Forward the original client IP
        // Don't rewrite the path since the server expects /api prefix
        // rewrite: (path) => path.replace(/^\/api/, '')
      },
      '/uploads': {
        target: 'http://localhost:3000',
        changeOrigin: true,
        secure: false,
      },
    },
  },
})
