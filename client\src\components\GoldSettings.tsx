import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { getMetalPriceSettings, updateMetalPriceSettings, MetalPriceSettings, getApiUsage, ApiUsage } from '@/api/goldPricing';
import { useToast } from '@/hooks/useToast';
import { CheckIcon, Loader, RefreshCw } from 'lucide-react';
import { Progress } from '@/components/ui/progress';

interface GoldSettingsProps {
  onSettingsUpdated?: () => void;
}

export function GoldSettings({ onSettingsUpdated }: GoldSettingsProps) {
  const [settings, setSettings] = useState<MetalPriceSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [apiUsage, setApiUsage] = useState<ApiUsage | null>(null);
  const [loadingApiUsage, setLoadingApiUsage] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    fetchSettings();
    fetchApiUsage();
  }, []);

  const fetchSettings = async () => {
    setLoading(true);
    try {
      const data = await getMetalPriceSettings();
      setSettings(data);
    } catch (error) {
      toast({
        title: "Error fetching settings",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchApiUsage = async () => {
    setLoadingApiUsage(true);
    try {
      const data = await getApiUsage();
      setApiUsage(data);
    } catch (error) {
      console.error('Error fetching API usage:', error);
      // Don't show a toast for API usage errors to avoid overwhelming the user
    } finally {
      setLoadingApiUsage(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!settings) return;

    setSaving(true);
    setSuccessMessage(null);

    try {
      await updateMetalPriceSettings(settings);
      setSuccessMessage("Settings updated successfully");
      toast({
        title: "Success",
        description: "Metal price settings have been updated",
        variant: "default"
      });
      // Clear success message after 3 seconds
      setTimeout(() => setSuccessMessage(null), 3000);

      // Refresh API usage after updating settings (especially if API key changed)
      fetchApiUsage();

      // Call the callback if provided
      if (onSettingsUpdated) {
        onSettingsUpdated();
      }
    } catch (error) {
      toast({
        title: "Error updating settings",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setSaving(false);
    }
  };

  const handleChange = (field: keyof MetalPriceSettings, value: string) => {
    if (!settings) return;

    const numValue = parseFloat(value);
    if (isNaN(numValue) && value !== '') return;

    setSettings({
      ...settings,
      [field]: value === '' ? '' : numValue
    });
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-6">
          <div className="flex items-center space-x-2">
            <Loader className="h-5 w-5 animate-spin" />
            <span>Loading settings...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!settings) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            No settings available. Please try again later.
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Gold Price Settings</CardTitle>
        <CardDescription>
          Configure pricing percentages and API settings
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <h3 className="text-sm font-medium">General Pricing</h3>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div className="space-y-2">
                <label className="text-sm">Min Buy Percentage (%)</label>
                <div className="flex">
                  <Input
                    type="number"
                    value={settings.minBuyPercentage.toString()}
                    onChange={(e) => handleChange('minBuyPercentage', e.target.value)}
                    min="0"
                    max="100"
                    step="0.1"
                  />
                  <span className="ml-2 flex items-center">%</span>
                </div>
                <p className="text-xs text-muted-foreground">
                  Percentage of spot price for minimum buying price
                </p>
              </div>

              <div className="space-y-2">
                <label className="text-sm">Max Buy Percentage (%)</label>
                <div className="flex">
                  <Input
                    type="number"
                    value={settings.maxBuyPercentage.toString()}
                    onChange={(e) => handleChange('maxBuyPercentage', e.target.value)}
                    min="0"
                    max="100"
                    step="0.1"
                  />
                  <span className="ml-2 flex items-center">%</span>
                </div>
                <p className="text-xs text-muted-foreground">
                  Percentage of spot price for maximum buying price
                </p>
              </div>

              <div className="space-y-2">
                <label className="text-sm">Melt Percentage (%)</label>
                <div className="flex">
                  <Input
                    type="number"
                    value={settings.meltPercentage.toString()}
                    onChange={(e) => handleChange('meltPercentage', e.target.value)}
                    min="0"
                    step="0.1"
                  />
                  <span className="ml-2 flex items-center">%</span>
                </div>
                <p className="text-xs text-muted-foreground">
                  Percentage added to spot price for melt value
                </p>
              </div>

              <div className="space-y-2">
                <label className="text-sm">Update Frequency (minutes)</label>
                <Input
                  type="number"
                  value={settings.updateFrequencyMinutes.toString()}
                  onChange={(e) => handleChange('updateFrequencyMinutes', e.target.value)}
                  min="5"
                  step="1"
                />
                <p className="text-xs text-muted-foreground">
                  How often the spot prices are updated from the API
                </p>
              </div>
            </div>
          </div>

          <div className="space-y-2 pt-4 border-t">
            <h3 className="text-sm font-medium">MHJ Calculator Settings</h3>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div className="space-y-2">
                <label className="text-sm">MHJ Min Percentage (%)</label>
                <div className="flex">
                  <Input
                    type="number"
                    value={settings.mhjMinPercentage.toString()}
                    onChange={(e) => handleChange('mhjMinPercentage', e.target.value)}
                    min="0"
                    max="100"
                    step="0.1"
                  />
                  <span className="ml-2 flex items-center">%</span>
                </div>
                <p className="text-xs text-muted-foreground">
                  Minimum percentage of MHJ retail price for buying
                </p>
              </div>

              <div className="space-y-2">
                <label className="text-sm">MHJ Max Percentage (%)</label>
                <div className="flex">
                  <Input
                    type="number"
                    value={settings.mhjMaxPercentage.toString()}
                    onChange={(e) => handleChange('mhjMaxPercentage', e.target.value)}
                    min="0"
                    max="100"
                    step="0.1"
                  />
                  <span className="ml-2 flex items-center">%</span>
                </div>
                <p className="text-xs text-muted-foreground">
                  Maximum percentage of MHJ retail price for buying
                </p>
              </div>
            </div>
          </div>

          <div className="space-y-4 pt-4 border-t">
            <h3 className="text-sm font-medium">API Settings</h3>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <label className="text-sm">Metal Price API Key</label>
                {!settings.apiKey && (
                  <span className="text-xs text-red-500 font-medium">Required</span>
                )}
              </div>
              <Input
                type="password"
                value={settings.apiKey || ''}
                onChange={(e) => handleChange('apiKey', e.target.value)}
                placeholder="Enter your Metal Price API key"
                className={!settings.apiKey ? "border-red-500 focus-visible:ring-red-500" : ""}
              />
              <p className="text-xs text-muted-foreground">
                API key for metalpriceapi.com - keep this secure
              </p>
              {!settings.apiKey && (
                <div className="mt-2 p-2 bg-red-500/10 border border-red-500/30 rounded-md">
                  <p className="text-xs text-red-500">
                    An API key is required for metal price functionality.
                    Please enter your API key from metalpriceapi.com to enable price updates.
                  </p>
                </div>
              )}
            </div>

            {/* API Usage Section */}
            <div className="mt-4 p-4 bg-muted/30 rounded-lg">
              <div className="flex justify-between items-center mb-2">
                <h4 className="text-sm font-medium">API Usage</h4>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={fetchApiUsage}
                  disabled={loadingApiUsage || !settings.apiKey}
                  className="h-8 px-2"
                >
                  {loadingApiUsage ? (
                    <Loader className="h-4 w-4 animate-spin" />
                  ) : (
                    <RefreshCw className="h-4 w-4" />
                  )}
                  <span className="ml-1">Refresh</span>
                </Button>
              </div>

              {!settings.apiKey ? (
                <div className="py-2 text-sm text-muted-foreground">
                  API usage information will be available after setting an API key
                </div>
              ) : apiUsage ? (
                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span>Plan: <span className="font-medium">{apiUsage.plan}</span></span>
                    <span>{apiUsage.used} / {apiUsage.total} requests</span>
                  </div>

                  <Progress
                    value={(apiUsage.used / apiUsage.total) * 100}
                    className="h-2"
                  />

                  <div className="text-xs text-muted-foreground flex justify-between">
                    <span>{apiUsage.remaining} requests remaining</span>
                    <span>{Math.round((apiUsage.remaining / apiUsage.total) * 100)}% available</span>
                  </div>
                </div>
              ) : loadingApiUsage ? (
                <div className="py-2 text-sm text-muted-foreground">Loading API usage data...</div>
              ) : (
                <div className="py-2 text-sm text-muted-foreground">API usage data not available</div>
              )}
            </div>
          </div>

          <div className="pt-4 flex items-center justify-between">
            <div>
              {successMessage && (
                <div className="flex items-center text-green-600">
                  <CheckIcon className="h-4 w-4 mr-1" />
                  <span className="text-sm">{successMessage}</span>
                </div>
              )}
            </div>
            <Button type="submit" disabled={saving}>
              {saving ? (
                <>
                  <Loader className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                "Save Settings"
              )}
            </Button>
          </div>

          <div className="text-xs text-muted-foreground pt-4">
            <p>Last updated: {new Date(settings.lastUpdated).toLocaleString()}</p>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}