const express = require('express');
const router = express.Router();
const noteService = require('../services/noteService');
const { requireUser } = require('./middleware/auth');

// Get notes for an item
router.get('/item/:itemId', requireUser, async (req, res) => {
  try {

    const itemId = req.params.itemId;
    const result = await noteService.getNotesByItem(itemId);

    if (result.success) {
      console.log(`Successfully retrieved ${result.notes.length} notes for item ${itemId}`);
      return res.status(200).json({
        success: true,
        data: result.notes,
      });
    } else {
      console.error(`Failed to retrieve notes: ${result.error}`);
      return res.status(400).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error('Error in get notes route:', error);
    return res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

// Add a note to an item
router.post('/item/:itemId', requireUser, async (req, res) => {
  try {
    console.log(`POST /api/notes/item/${req.params.itemId} request received from user: ${req.user._id}`);

    const { content } = req.body;
    if (!content || content.trim() === '') {
      return res.status(400).json({
        success: false,
        error: 'Note content is required',
      });
    }

    const itemId = req.params.itemId;
    const userId = req.user._id;

    const result = await noteService.addNote(itemId, userId, content);

    if (result.success) {
      console.log(`Successfully added note to item ${itemId}`);
      return res.status(201).json({
        success: true,
        data: result.note,
      });
    } else {
      console.error(`Failed to add note: ${result.error}`);
      return res.status(400).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error('Error in add note route:', error);
    return res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

// Delete a note
router.delete('/:noteId', requireUser, async (req, res) => {
  try {
    console.log(`DELETE /api/notes/${req.params.noteId} request received from user: ${req.user._id}`);

    const noteId = req.params.noteId;
    const userId = req.user._id;
    const userRole = req.user.role;

    const result = await noteService.deleteNote(noteId, userId, userRole);

    if (result.success) {
      console.log(`Successfully deleted note ${noteId}`);
      return res.status(200).json({
        success: true,
        message: result.message,
      });
    } else {
      console.error(`Failed to delete note: ${result.error}`);

      // Determine appropriate status code
      const statusCode = result.error === 'Note not found' ? 404 : 403;

      return res.status(statusCode).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error('Error in delete note route:', error);
    return res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

module.exports = router;