import api from './api';

export interface AttributeOption {
  value: string;
  display: string;
}

export interface AttributeUnit {
  display: string;
  multiplier: number;
}

export interface AttributeRange {
  lower: string;
  upper: string;
}

export interface CategoryAttribute {
  name: string;
  displayName: string;
  type: number; // 0: None, 1: <PERSON><PERSON><PERSON>, 2: Integer, 3: Decimal, 4: String, 5: DateTime
  range?: AttributeRange;
  maxStringLength?: number;
  options?: AttributeOption[];
  units?: AttributeUnit[];
  isRequiredForSell: boolean;
  groupName?: string;
}

export interface FeeTier {
  minimumTierPrice: number;
  fixedFee: number;
  percentageFee: number;
}

export interface CategoryFees {
  bold?: number;
  bundle?: number;
  endDate?: number;
  feature?: number;
  gallery?: number;
  galleryPlus?: number;
  highlight?: number;
  homepage?: number;
  listing?: number;
  multiPhoto?: number;
  reserve?: number;
  subtitle?: number;
  tenDays?: number;
  withdrawal?: number;
  superFeature?: number;
  superFeatureBundle?: number;
  highVolume?: number;
  listingFeeTiers?: FeeTier[];
  minimumSuccessFee?: number;
  maximumSuccessFee?: number;
  successFeeTiers?: FeeTier[];
  branding?: number;
  secondCategory?: number;
}

export interface TradeMeCategory {
  _id: string;
  categoryId: string;
  name: string;
  path: string;
  parentId: string | null;
  isLeaf: boolean;
  hasLegalNotice: boolean;
  legalNotice?: string;
  count: number;
  // Additional fields
  isRestricted?: boolean;
  isWine?: boolean;
  canListAuctions?: boolean;
  canListClassifieds?: boolean;
  canRelist?: boolean;
  authenticatedBidsOnly?: boolean;
  defaultDuration?: number;
  allowedDurations?: number[];
  fees?: CategoryFees;
  freePhotoCount?: number;
  maximumPhotoCount?: number;
  isFreeToRelist?: boolean;
  attributes?: CategoryAttribute[];
  hasAutomaticallyCreatedTitle?: boolean;
  canUseTradeMeShipping?: boolean;
  maximumTitleLength?: number;
  areaOfBusiness?: number;
  extensionPeriod?: number;
  defaultRelistDuration?: number;
  canHaveSecondCategory?: boolean;
  canBeSecondCategory?: boolean;
  isFirearms?: boolean;
  subcategories?: TradeMeCategory[];
}

export interface TradeMeCategorySuggestion {
  id: string;
  name: string;
  path: string;
  ancestors?: string[];
}

export interface TradeMeAutoSuggestionCategory {
  id: string;
  name: string;
  description: string;
  path: string;
}

export interface TradeMeAutoSuggestion {
  searchTerm: string;
  categories: TradeMeAutoSuggestionCategory[];
}

export interface TradeMeSuggestions {
  categorySuggestions: TradeMeCategorySuggestion[];
  autoSuggestions: TradeMeAutoSuggestion[];
}

/**
 * Get TradeMe categories
 * @param parentId - Parent category ID (null for root categories)
 * @returns Promise with categories
 */
export const getCategories = async (parentId: string | null = null): Promise<{
  success: boolean;
  categories: TradeMeCategory[];
  error?: string;
}> => {
  try {
    const response = await api.get('/trademe/categories', {
      params: { parentId }
    });
    return response.data;
  } catch (error: any) {
    console.error('Error fetching TradeMe categories:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

/**
 * Get a TradeMe category by ID
 * @param categoryId - Category ID
 * @returns Promise with category
 */
export const getCategoryById = async (categoryId: string): Promise<{
  success: boolean;
  category: TradeMeCategory;
  error?: string;
}> => {
  try {
    const response = await api.get(`/trademe/categories/${categoryId}`);
    return response.data;
  } catch (error: any) {
    console.error('Error fetching TradeMe category:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

/**
 * Search TradeMe categories
 * @param query - Search query
 * @returns Promise with matching categories
 */
export const searchCategories = async (query: string): Promise<{
  success: boolean;
  categories: TradeMeCategory[];
  error?: string;
}> => {
  try {
    if (!query || query.length < 2) {
      return {
        success: false,
        categories: [],
        error: 'Search query must be at least 2 characters'
      };
    }

    const response = await api.get('/trademe/categories/search', {
      params: { query }
    });
    return response.data;
  } catch (error: any) {
    console.error('Error searching TradeMe categories:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

/**
 * Get category suggestions from TradeMe API based on search term
 * @param searchString - Search term to get suggestions for
 * @returns Promise with category suggestions
 */
export const getCategorySuggestions = async (searchString: string): Promise<{
  success: boolean;
  suggestions?: TradeMeSuggestions;
  error?: string;
}> => {
  try {
    if (!searchString || searchString.length < 2) {
      return {
        success: false,
        error: 'Search string must be at least 2 characters'
      };
    }

    const response = await api.get('/trademe/categories/suggestions', {
      params: { searchString }
    });
    return response.data;
  } catch (error: any) {
    console.error('Error getting TradeMe category suggestions:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};
