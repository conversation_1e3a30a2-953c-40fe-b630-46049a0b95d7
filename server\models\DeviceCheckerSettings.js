const mongoose = require('mongoose');

const deviceCheckerSettingsSchema = new mongoose.Schema({
  url: {
    type: String,
    default: ''
  },
  apiKey: {
    type: String,
    default: ''
  },
  enabledServices: [{
    serviceId: String,
    name: String,
    cost: Number,
    description: String,
    enabled: {
      type: Boolean,
      default: true
    }
  }],
  accountBalance: {
    type: Number,
    default: 0
  },
  lastUpdated: {
    type: Date,
    default: Date.now
  },
  createdAt: {
    type: Date,
    default: Date.now,
    immutable: true
  }
}, {
  versionKey: false
});

// There should only be one settings document
deviceCheckerSettingsSchema.statics.getSettings = async function() {
  let settings = await this.findOne();
  if (!settings) {
    settings = await this.create({});
  }
  return settings;
};

const DeviceCheckerSettings = mongoose.model('DeviceCheckerSettings', deviceCheckerSettingsSchema, 'devicecheckersettings');

module.exports = DeviceCheckerSettings;
