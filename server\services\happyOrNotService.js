const axios = require('axios');
const HappyOrNotFeedback = require('../models/HappyOrNotFeedback');
const HappyOrNotSettings = require('../models/HappyOrNotSettings');

class HappyOrNotService {
  /**
   * Get the API key from settings
   * @returns {Promise<string>} The API key
   */
  async getApiKey() {
    const settings = await HappyOrNotSettings.getSettings();
    return settings.apiKey;
  }

  /**
   * Get the settings
   * @returns {Promise<Object>} The settings object
   */
  async getSettings() {
    try {
      const settings = await HappyOrNotSettings.getSettings();

      // Create a copy of the settings to avoid modifying the original
      const settingsCopy = settings.toObject();

      // Mask the API key for security
      if (settingsCopy.apiKey) {
        settingsCopy.apiKey = settings.maskApiKey();
      }

      return {
        success: true,
        settings: settingsCopy
      };
    } catch (error) {
      console.error('Error getting Happy or Not settings:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get just the goal settings (for all users)
   * @returns {Promise<Object>} The goal settings
   */
  async getGoalSettings() {
    try {
      const settings = await HappyOrNotSettings.getSettings();

      // Only return the goal-related settings
      const goalSettings = {
        weeklyFeedbackGoal: settings.weeklyFeedbackGoal,
        happinessScoreGoal: settings.happinessScoreGoal,
        lastSynced: settings.lastSynced,
        lastUpdated: settings.lastUpdated
      };

      return {
        success: true,
        goals: goalSettings
      };
    } catch (error) {
      console.error('Error getting Happy or Not goal settings:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Update the settings
   * @param {Object} newSettings - The new settings
   * @param {Object} user - The user making the update
   * @returns {Promise<Object>} The updated settings
   */
  async updateSettings(newSettings, user) {
    try {
      const settings = await HappyOrNotSettings.getSettings();

      // Update fields if provided
      if (newSettings.experiencePointName !== undefined) {
        settings.experiencePointName = newSettings.experiencePointName;
      }

      // Only update API key if it's provided and not masked
      if (newSettings.apiKey && !newSettings.apiKey.includes('X')) {
        settings.apiKey = newSettings.apiKey;
      }

      // Update goal fields if provided
      if (newSettings.weeklyFeedbackGoal !== undefined) {
        settings.weeklyFeedbackGoal = newSettings.weeklyFeedbackGoal;
      }

      if (newSettings.happinessScoreGoal !== undefined) {
        settings.happinessScoreGoal = newSettings.happinessScoreGoal;
      }

      // Update metadata
      settings.lastUpdated = new Date();
      settings.updatedBy = user._id;

      await settings.save();

      // Create a copy of the settings to avoid modifying the original
      const settingsCopy = settings.toObject();

      // Mask the API key for security
      if (settingsCopy.apiKey) {
        settingsCopy.apiKey = settings.maskApiKey();
      }

      return {
        success: true,
        settings: settingsCopy
      };
    } catch (error) {
      console.error('Error updating Happy or Not settings:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  /**
   * Get feedback data with filtering and aggregation
   * @param {Object} options - Query options
   * @param {string} options.startDate - Start date (YYYY-MM-DD)
   * @param {string} options.endDate - End date (YYYY-MM-DD)
   * @param {string} options.experiencePointId - Experience point ID
   * @param {string} options.groupBy - Group by field (day, week, month)
   * @param {number} options.buttonIndex - Filter by button index (0-3 or 0-10)
   * @param {boolean} options.hasText - Filter for feedback with text comments
   * @param {boolean} options.hasFollowUp - Filter for feedback with follow-up responses
   * @returns {Promise<Object>} The feedback data
   */
  async getFeedbackData(options = {}) {
    try {
      // Default to last 30 days if no dates provided
      const endDate = options.endDate ? new Date(options.endDate) : new Date();
      // Make sure we include the full day by setting time to end of day
      endDate.setHours(23, 59, 59, 999); // End of day
      console.log(`Using end date: ${endDate.toISOString()}`); // Debug log

      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      thirtyDaysAgo.setHours(0, 0, 0, 0); // Start of day

      const startDate = options.startDate ? new Date(options.startDate) : thirtyDaysAgo;
      // Make sure we start at the beginning of the day
      startDate.setHours(0, 0, 0, 0); // Start of day
      console.log(`Using start date: ${startDate.toISOString()}`); // Debug log

      // Build query
      const query = {
        localTime: {
          $gte: startDate,
          $lte: endDate
        }
      };

      // Add experience point ID if provided
      if (options.experiencePointId) {
        query.experiencePointId = options.experiencePointId;
      }

      // Add button index filter if provided
      if (options.buttonIndex !== undefined && options.buttonIndex !== null) {
        query.buttonIndex = parseInt(options.buttonIndex);
      }

      // Add text comment filter if provided
      if (options.hasText === 'true') {
        query.text = { $exists: true, $ne: null, $ne: '' };
      }

      // Add follow-up filter if provided
      if (options.hasFollowUp === 'true') {
        query.followupOptionText = { $exists: true, $ne: null, $ne: '' };
      }

      // Get total count
      const totalCount = await HappyOrNotFeedback.countDocuments(query);

      // Get button counts
      const buttonCounts = await HappyOrNotFeedback.aggregate([
        { $match: query },
        { $group: { _id: '$buttonIndex', count: { $sum: 1 } } },
        { $sort: { _id: 1 } }
      ]);

      // Calculate happiness score (weighted average)
      let happinessScore = 0;
      let totalWeightedCount = 0;

      buttonCounts.forEach(button => {
        // For 4-button scale (0-3)
        if (button._id <= 3) {
          // Convert 0-3 scale to 0-100 percentage
          // 0 = 0%, 1 = 33.33%, 2 = 66.67%, 3 = 100%
          const normalizedScore = (button._id / 3) * 100;
          happinessScore += normalizedScore * button.count;
          totalWeightedCount += button.count;
        } else {
          // For NPS scale (0-10)
          // Convert 0-10 scale to 0-100 percentage
          const normalizedScore = (button._id / 10) * 100;
          happinessScore += normalizedScore * button.count;
          totalWeightedCount += button.count;
        }
      });

      if (totalWeightedCount > 0) {
        // Round to nearest integer for cleaner display
        happinessScore = Math.round(happinessScore / totalWeightedCount);
      } else {
        happinessScore = 0;
      }

      // Get time series data
      let timeSeriesData = [];

      if (options.groupBy) {
        const groupByField = options.groupBy;
        let groupId;

        if (groupByField === 'day') {
          groupId = {
            year: { $year: '$localTime' },
            month: { $month: '$localTime' },
            day: { $dayOfMonth: '$localTime' }
          };
        } else if (groupByField === 'week') {
          groupId = {
            year: { $year: '$localTime' },
            week: { $week: '$localTime' }
          };
        } else if (groupByField === 'month') {
          groupId = {
            year: { $year: '$localTime' },
            month: { $month: '$localTime' }
          };
        }

        const timeSeriesAggregation = await HappyOrNotFeedback.aggregate([
          { $match: query },
          {
            $group: {
              _id: groupId,
              date: { $first: '$localTime' },
              total: { $sum: 1 },
              button0: {
                $sum: {
                  $cond: [{ $eq: ['$buttonIndex', 0] }, 1, 0]
                }
              },
              button1: {
                $sum: {
                  $cond: [{ $eq: ['$buttonIndex', 1] }, 1, 0]
                }
              },
              button2: {
                $sum: {
                  $cond: [{ $eq: ['$buttonIndex', 2] }, 1, 0]
                }
              },
              button3: {
                $sum: {
                  $cond: [{ $eq: ['$buttonIndex', 3] }, 1, 0]
                }
              }
            }
          },
          { $sort: { date: 1 } }
        ]);

        timeSeriesData = timeSeriesAggregation.map(item => {
          let date;

          if (groupByField === 'day') {
            date = new Date(item.date).toISOString().split('T')[0];
          } else if (groupByField === 'week') {
            const year = item._id.year;
            const week = item._id.week;
            date = `${year}-W${week.toString().padStart(2, '0')}`;
          } else if (groupByField === 'month') {
            const year = item._id.year;
            const month = item._id.month.toString().padStart(2, '0');
            date = `${year}-${month}`;
          }

          return {
            date,
            total: item.total,
            0: item.button0,
            1: item.button1,
            2: item.button2,
            3: item.button3
          };
        });
      }

      // Get comparison data
      const previousPeriodStartDate = new Date(startDate);
      previousPeriodStartDate.setDate(previousPeriodStartDate.getDate() - (endDate.getTime() - startDate.getTime()) / (24 * 60 * 60 * 1000));

      const previousPeriodQuery = {
        ...query,
        localTime: {
          $gte: previousPeriodStartDate,
          $lt: startDate
        }
      };

      const previousPeriodCount = await HappyOrNotFeedback.countDocuments(previousPeriodQuery);

      const previousPeriodButtonCounts = await HappyOrNotFeedback.aggregate([
        { $match: previousPeriodQuery },
        { $group: { _id: '$buttonIndex', count: { $sum: 1 } } },
        { $sort: { _id: 1 } }
      ]);

      let previousPeriodHappinessScore = 0;
      let previousPeriodTotalWeightedCount = 0;

      previousPeriodButtonCounts.forEach(button => {
        // For 4-button scale (0-3)
        if (button._id <= 3) {
          // Convert 0-3 scale to 0-100 percentage
          const normalizedScore = (button._id / 3) * 100;
          previousPeriodHappinessScore += normalizedScore * button.count;
          previousPeriodTotalWeightedCount += button.count;
        } else {
          // For NPS scale (0-10)
          // Convert 0-10 scale to 0-100 percentage
          const normalizedScore = (button._id / 10) * 100;
          previousPeriodHappinessScore += normalizedScore * button.count;
          previousPeriodTotalWeightedCount += button.count;
        }
      });

      if (previousPeriodTotalWeightedCount > 0) {
        // Round to nearest integer for cleaner display
        previousPeriodHappinessScore = Math.round(previousPeriodHappinessScore / previousPeriodTotalWeightedCount);
      } else {
        previousPeriodHappinessScore = 0;
      }

      // Calculate change
      const change = previousPeriodCount > 0 ? ((totalCount - previousPeriodCount) / previousPeriodCount * 100).toFixed(2) : 0;
      const scoreChange = previousPeriodHappinessScore > 0 ? ((happinessScore - previousPeriodHappinessScore) / previousPeriodHappinessScore * 100).toFixed(2) : 0;

      // Get last year comparison
      const lastYearStartDate = new Date(startDate);
      lastYearStartDate.setFullYear(lastYearStartDate.getFullYear() - 1);

      const lastYearEndDate = new Date(endDate);
      lastYearEndDate.setFullYear(lastYearEndDate.getFullYear() - 1);

      const lastYearQuery = {
        ...query,
        localTime: {
          $gte: lastYearStartDate,
          $lte: lastYearEndDate
        }
      };

      const lastYearCount = await HappyOrNotFeedback.countDocuments(lastYearQuery);

      const lastYearButtonCounts = await HappyOrNotFeedback.aggregate([
        { $match: lastYearQuery },
        { $group: { _id: '$buttonIndex', count: { $sum: 1 } } },
        { $sort: { _id: 1 } }
      ]);

      let lastYearHappinessScore = 0;
      let lastYearTotalWeightedCount = 0;

      lastYearButtonCounts.forEach(button => {
        // For 4-button scale (0-3)
        if (button._id <= 3) {
          // Convert 0-3 scale to 0-100 percentage
          const normalizedScore = (button._id / 3) * 100;
          lastYearHappinessScore += normalizedScore * button.count;
          lastYearTotalWeightedCount += button.count;
        } else {
          // For NPS scale (0-10)
          // Convert 0-10 scale to 0-100 percentage
          const normalizedScore = (button._id / 10) * 100;
          lastYearHappinessScore += normalizedScore * button.count;
          lastYearTotalWeightedCount += button.count;
        }
      });

      if (lastYearTotalWeightedCount > 0) {
        // Round to nearest integer for cleaner display
        lastYearHappinessScore = Math.round(lastYearHappinessScore / lastYearTotalWeightedCount);
      } else {
        lastYearHappinessScore = 0;
      }

      // Calculate last year change
      const lastYearChange = lastYearCount > 0 ? ((totalCount - lastYearCount) / lastYearCount * 100).toFixed(2) : 0;
      const lastYearScoreChange = lastYearHappinessScore > 0 ? ((happinessScore - lastYearHappinessScore) / lastYearHappinessScore * 100).toFixed(2) : 0;

      // Get recent feedbacks with text or follow-up (limited to 10 for dashboard)
      const recentFeedbacks = await HappyOrNotFeedback.find({
        ...query,
        $or: [
          { text: { $exists: true, $ne: null, $ne: '' } },
          { followupOptionText: { $exists: true, $ne: null, $ne: '' } }
        ]
      })
      .sort({ localTime: -1 })
      .limit(10);

      return {
        success: true,
        data: {
          totalCount,
          buttonCounts,
          happinessScore,
          timeSeriesData,
          comparison: {
            previousPeriod: {
              totalCount: previousPeriodCount,
              buttonCounts: previousPeriodButtonCounts,
              happinessScore: previousPeriodHappinessScore,
              change,
              scoreChange
            },
            lastYear: {
              totalCount: lastYearCount,
              buttonCounts: lastYearButtonCounts,
              happinessScore: lastYearHappinessScore,
              change: lastYearChange,
              scoreChange: lastYearScoreChange
            }
          },
          recentFeedbacks
        }
      };
    } catch (error) {
      console.error('Error getting feedback data:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

/**
 * Sync feedback data from the Happy or Not API
 * @param {Object} options - Optional parameters
 * @param {string} options.startDate - Optional start date (YYYY-MM-DD)
 * @param {string} options.endDate - Optional end date (YYYY-MM-DD)
 * @param {string} options.experiencePointId - Optional experience point ID
 * @returns {Promise<Object>} The sync result
 */
async syncFeedback(options = {}) {
  try {
    // Get the API key
    const apiKey = await this.getApiKey();
    if (!apiKey) {
      console.error('No API key found in settings');
      return {
        success: false,
        error: 'No API key found in settings'
      };
    }

    console.log('Using API key:', apiKey.substring(0, 3) + '...' + apiKey.substring(apiKey.length - 3));

    const settings = await HappyOrNotSettings.getSettings();

    // Initialize combinedStats with default values
    let combinedStats = {
      totalSynced: 0,
      totalSaved: 0,
      totalUpdated: 0,
      button: {
        synced: 0,
        saved: 0
      },
      text: {
        synced: 0,
        saved: 0,
        updated: 0
      },
      followUp: {
        synced: 0,
        saved: 0,
        updated: 0
      }
    };

    // Test API connection before proceeding
    try {
      const testResponse = await axios.get('https://api.happy-or-not.com/v2/results/button-feedbacks.json', {
        headers: {
          'X-HON-API-Token': apiKey
        },
        params: {
          period: 'today',
          limit: 1
        }
      });

      console.log('API connection test successful. Status:', testResponse.status);
      if (testResponse.data && testResponse.data.length === 0) {
        console.log('API connection test returned 0 items for today. This may be normal if there is no data for today.');
      }
    } catch (error) {
      console.error('API connection test failed:', error.message);
      if (error.response) {
        console.error('API response status:', error.response.status);
        console.error('API response data:', error.response.data);
      }
      return {
        success: false,
        error: `API connection test failed: ${error.message}`
      };
    }

    // Get the last synced date if no start date provided
    let startDate;
    if (options.startDate) {
      startDate = new Date(options.startDate);
    } else {
      const lastSynced = settings.lastSynced || new Date(0);
      startDate = new Date(lastSynced);
      startDate.setDate(startDate.getDate() - 1); // Overlap by 1 day to ensure no data is missed
    }

    // Set end date to current time if not provided
    let endDate;
    if (options.endDate) {
      endDate = new Date(options.endDate);
      // Set to end of day
      endDate.setHours(23, 59, 59, 999);
    } else {
      // Use current date
      endDate = new Date();
    }

    // Format dates for API
    // The API expects dates in YYYY-MM-DD format
    let startDateStr = startDate.toISOString().split('T')[0];
    const endDateStr = endDate.toISOString().split('T')[0];

    console.log(`Syncing data from ${startDateStr} to ${endDateStr}`);

    // Check if the date range is within the API's 1-year limit
    const oneYearInMs = 366 * 24 * 60 * 60 * 1000; // 366 days to account for leap years
    if (endDate.getTime() - startDate.getTime() > oneYearInMs) {
      console.warn('Date range exceeds 1 year, which is the API limit. Adjusting start date.');
      startDate = new Date(endDate);
      startDate.setFullYear(startDate.getFullYear() - 1);
      startDate.setDate(startDate.getDate() + 1); // Add 1 day to stay within the 366-day limit
    }

    // Sync strategy:
    // 1. Use period parameter for recent data (today, yesterday)
    // 2. Use date range for historical data

    // First, sync today's data using period parameter
    console.log(`Step 1: Syncing today's data using 'period=today'`);
    const todaySyncResult = await this.syncHistoricalDataWithPeriod(
      'today',
      apiKey,
      settings
    );

    // Then sync yesterday's data using period parameter
    console.log(`Step 2: Syncing yesterday's data using 'period=yesterday'`);
    const yesterdaySyncResult = await this.syncHistoricalDataWithPeriod(
      'yesterday',
      apiKey,
      settings
    );

    // Finally, sync historical data using date range
    // Adjust the end date to exclude today and yesterday (which we already synced)
    const twoDaysAgo = new Date();
    twoDaysAgo.setDate(twoDaysAgo.getDate() - 2);
    twoDaysAgo.setHours(23, 59, 59, 999);

    const historicalEndDateStr = twoDaysAgo.toISOString().split('T')[0];

    // Ensure start date is before or equal to end date
    const startDateForHistorical = new Date(startDateStr);
    const endDateForHistorical = new Date(historicalEndDateStr);

    // If start date is after end date, adjust it to be 30 days before end date
    if (startDateForHistorical > endDateForHistorical) {
      console.log(`Adjusting start date (${startDateStr}) to be before end date (${historicalEndDateStr})`);
      startDateForHistorical.setDate(endDateForHistorical.getDate() - 30);
      startDateStr = startDateForHistorical.toISOString().split('T')[0];
      console.log(`New start date: ${startDateStr}`);
    }

    // Only sync historical data if there's a valid date range (at least one day)
    if (new Date(startDateStr) <= new Date(historicalEndDateStr)) {
      console.log(`Step 3: Syncing historical data from ${startDateStr} to ${historicalEndDateStr}`);
      const syncResult = await this.syncHistoricalDataWithDateRange(
        startDateStr,
        historicalEndDateStr,
        apiKey,
        settings
      );

      // Combine the results from all three syncs
      combinedStats = {
        totalSynced: syncResult.stats.totalSynced + todaySyncResult.stats.totalSynced + yesterdaySyncResult.stats.totalSynced,
        totalSaved: syncResult.stats.totalSaved + todaySyncResult.stats.totalSaved + yesterdaySyncResult.stats.totalSaved,
        totalUpdated: syncResult.stats.totalUpdated + todaySyncResult.stats.totalUpdated + yesterdaySyncResult.stats.totalUpdated,
        button: {
          synced: syncResult.stats.button.synced + todaySyncResult.stats.button.synced + yesterdaySyncResult.stats.button.synced,
          saved: syncResult.stats.button.saved + todaySyncResult.stats.button.saved + yesterdaySyncResult.stats.button.saved
        },
        text: {
          synced: syncResult.stats.text.synced + todaySyncResult.stats.text.synced + yesterdaySyncResult.stats.text.synced,
          saved: syncResult.stats.text.saved + todaySyncResult.stats.text.saved + yesterdaySyncResult.stats.text.saved,
          updated: syncResult.stats.text.updated + todaySyncResult.stats.text.updated + yesterdaySyncResult.stats.text.updated
        },
        followUp: {
          synced: syncResult.stats.followUp.synced + todaySyncResult.stats.followUp.synced + yesterdaySyncResult.stats.followUp.synced,
          saved: syncResult.stats.followUp.saved + todaySyncResult.stats.followUp.saved + yesterdaySyncResult.stats.followUp.saved,
          updated: syncResult.stats.followUp.updated + todaySyncResult.stats.followUp.updated + yesterdaySyncResult.stats.followUp.updated
        }
      };
    } else {
      console.log(`Step 3: Skipping historical data sync because start date (${startDateStr}) is after end date (${historicalEndDateStr}) and could not be adjusted`);

      // Just combine today and yesterday results
      combinedStats = {
        totalSynced: todaySyncResult.stats.totalSynced + yesterdaySyncResult.stats.totalSynced,
        totalSaved: todaySyncResult.stats.totalSaved + yesterdaySyncResult.stats.totalSaved,
        totalUpdated: todaySyncResult.stats.totalUpdated + yesterdaySyncResult.stats.totalUpdated,
        button: {
          synced: todaySyncResult.stats.button.synced + yesterdaySyncResult.stats.button.synced,
          saved: todaySyncResult.stats.button.saved + yesterdaySyncResult.stats.button.saved
        },
        text: {
          synced: todaySyncResult.stats.text.synced + yesterdaySyncResult.stats.text.synced,
          saved: todaySyncResult.stats.text.saved + yesterdaySyncResult.stats.text.saved,
          updated: todaySyncResult.stats.text.updated + yesterdaySyncResult.stats.text.updated
        },
        followUp: {
          synced: todaySyncResult.stats.followUp.synced + yesterdaySyncResult.stats.followUp.synced,
          saved: todaySyncResult.stats.followUp.saved + yesterdaySyncResult.stats.followUp.saved,
          updated: todaySyncResult.stats.followUp.updated + yesterdaySyncResult.stats.followUp.updated
        }
      };
    }

    // Update last synced timestamp
    settings.lastSynced = new Date();
    await settings.save();

    return {
      success: true,
      message: 'Full sync completed successfully',
      results: combinedStats
    };
  } catch (error) {
    console.error('Error running full sync:', error);
    return {
      success: false,
      error: error.message
    };
  }
}
/**
 * Helper method to sync data using the period parameter
 * @param {string} period - Period parameter (today, yesterday, this-week, etc.)
 * @param {string} apiKey - The API key
 * @param {Object} settings - The settings object
 * @returns {Promise<Object>} The sync result
 */
async syncHistoricalDataWithPeriod(period, apiKey, settings) {
  try {
    // Track overall stats
    let totalSynced = 0;
    let totalSaved = 0;
    let totalUpdated = 0;

    console.log(`Syncing data for period: ${period}`);

    // Get all existing feedback IDs to avoid duplicate queries
    console.log('Fetching existing feedback IDs from database...');
    const existingFeedbacks = await HappyOrNotFeedback.find({}, 'feedbackId buttonIndex text followupOptionId').lean();
    console.log(`For period sync, checking against all ${existingFeedbacks.length} existing records`);

    // Create lookup maps for faster checking
    const existingFeedbackMap = new Map();
    const existingTextFeedbackMap = new Map();
    const existingFollowUpFeedbackMap = new Map();

    existingFeedbacks.forEach(feedback => {
      existingFeedbackMap.set(feedback.feedbackId, true);

      // Track which feedbacks already have text
      if (feedback.text) {
        existingTextFeedbackMap.set(feedback.feedbackId, true);
      }

      // Track which feedbacks already have follow-up data
      if (feedback.followupOptionId) {
        existingFollowUpFeedbackMap.set(feedback.feedbackId, true);
      }
    });

    console.log(`Found ${existingFeedbackMap.size} existing feedbacks in database`);

    // Sync button feedbacks with pagination
    let buttonPage = 1;
    let buttonHasMore = true;
    let buttonTotalSynced = 0;
    let buttonTotalSaved = 0;
    let buttonNewFeedbacks = []; // Array to collect new feedbacks for bulk insert
    let seenFeedbackIds = new Set(); // Track feedback IDs we've already seen in this sync

    console.log('Syncing button feedbacks...');

    // Set a reasonable page limit based on expected data volume
    const maxButtonPages = 50; // Increased from 20 to ensure we get all data

    while (buttonHasMore && buttonPage <= maxButtonPages) {
      // Build query parameters
      const params = {
        period,
        limit: 1000, // API maximum limit
        order: 'desc', // Use desc to get newest first
        page: buttonPage,
        experiencePointId: '96955' // Hard-coded for testing
      };

      try {
        // Make API request
        console.log(`Making API request for button feedbacks page ${buttonPage} with params:`, params);
        const response = await axios.get('https://api.happy-or-not.com/v2/results/button-feedbacks.json', {
          headers: {
            'X-HON-API-Token': apiKey
          },
          params
        });

        // Process feedbacks
        const feedbacks = response.data;
        console.log(`Processing button feedbacks page ${buttonPage}, received ${feedbacks.length} items`);

        // Track if this page contains any new feedback IDs
        let newFeedbacksInPage = 0;
        let duplicateFeedbacksInPage = 0;

        // Log the date range of the first and last items for debugging
        if (feedbacks.length > 0) {
          const firstItem = feedbacks[0];
          const lastItem = feedbacks[feedbacks.length - 1];
          const firstItemDate = new Date(firstItem.localTime);
          const lastItemDate = new Date(lastItem.localTime);

          console.log(`  Date range of page ${buttonPage}: ${firstItemDate.toISOString()} to ${lastItemDate.toISOString()}`);
        }

        // Collect new feedbacks for bulk insert
        for (const feedback of feedbacks) {
          // Check if we've already seen this feedback ID in this sync operation
          if (seenFeedbackIds.has(feedback.feedbackId)) {
            duplicateFeedbacksInPage++;
            continue; // Skip this feedback as we've already processed it
          }

          // Add to seen set
          seenFeedbackIds.add(feedback.feedbackId);
          newFeedbacksInPage++;

          // Check if feedback already exists in database
          if (!existingFeedbackMap.has(feedback.feedbackId)) {
            buttonNewFeedbacks.push({
              feedbackId: feedback.feedbackId,
              buttonIndex: feedback.buttonIndex,
              localTime: new Date(feedback.localTime),
              experiencePointId: feedback.experiencePointId,
              experiencePointName: settings.experiencePointName || 'Unknown',
              surveyId: feedback.surveyId,
              smileyId: feedback.smileyId,
              smileyType: feedback.smileyType,
              misuse: feedback.misuse || false
            });
          }
        }

        // Log statistics
        console.log(`  Page ${buttonPage}: ${newFeedbacksInPage} new items, ${duplicateFeedbacksInPage} duplicates`);

        // Only count new items toward our sync total
        buttonTotalSynced += newFeedbacksInPage;

        // Check if we need to continue pagination - only if we got a full page
        buttonHasMore = feedbacks.length === 1000;

        buttonPage++;
      } catch (error) {
        console.error(`Error syncing button feedbacks page ${buttonPage}:`, error.message);
        if (error.response) {
          console.error('API response status:', error.response.status);
          console.error('API response data:', error.response.data);

          if (error.response.data?.validationErrors) {
            console.error('Validation errors:', error.response.data.validationErrors);
          }
        }
        buttonHasMore = false; // Stop pagination on error
      }
    }

    // Insert any remaining button feedbacks
    if (buttonNewFeedbacks.length > 0) {
      console.log(`  Inserting batch of ${buttonNewFeedbacks.length} button feedbacks`);
      try {
        // Use ordered: false to continue processing even if some documents fail
        const result = await HappyOrNotFeedback.insertMany(buttonNewFeedbacks, {
          ordered: false,
          rawResult: true
        });

        // Count how many were actually inserted
        const insertedCount = result.insertedCount || 0;
        buttonTotalSaved += insertedCount;

        if (insertedCount < buttonNewFeedbacks.length) {
          console.log(`  Note: Only ${insertedCount} of ${buttonNewFeedbacks.length} button feedbacks were inserted (${buttonNewFeedbacks.length - insertedCount} duplicates or errors)`);
        }
      } catch (error) {
        // Handle bulk write errors more gracefully
        if (error.code === 11000 || (error.name === 'MongoBulkWriteError' && error.code === 11000)) {
          // This is a duplicate key error
          const insertedCount = error.result?.insertedCount || 0;
          buttonTotalSaved += insertedCount;
          console.log(`  Warning: Encountered duplicate keys during button feedback insertion. ${insertedCount} of ${buttonNewFeedbacks.length} were inserted.`);
        } else {
          // Log other errors but continue processing
          console.error(`  Error inserting button feedbacks:`, error.message);
        }
      }
    }

    console.log(`Button feedbacks sync complete. Synced ${buttonTotalSynced}, saved ${buttonTotalSaved} new items.`);

    // Sync text feedbacks with pagination
    let textPage = 1;
    let textHasMore = true;
    let textTotalSynced = 0;
    let textTotalSaved = 0;
    let textTotalUpdated = 0;
    let textNewFeedbacks = []; // Array to collect new feedbacks for bulk insert
    let textUpdates = []; // Array to collect updates for existing feedbacks
    let seenTextFeedbackIds = new Set(); // Track feedback IDs we've already seen in this sync

    console.log('Syncing text feedbacks...');

    // Set a reasonable page limit based on expected data volume
    const maxTextPages = 20;

    while (textHasMore && textPage <= maxTextPages) {
      // Build query parameters
      const params = {
        period,
        limit: 1000, // API maximum limit
        order: 'desc', // Use desc to get newest first
        page: textPage
      };

      try {
        // Make API request
        console.log(`Making API request for text feedbacks page ${textPage} with params:`, params);
        const response = await axios.get('https://api.happy-or-not.com/v2/results/text-feedbacks.json', {
          headers: {
            'X-HON-API-Token': apiKey
          },
          params
        });

        // Process feedbacks
        const feedbacks = response.data;
        console.log(`Processing text feedbacks page ${textPage}, received ${feedbacks.length} items`);

        // Track if this page contains any new feedback IDs
        let newFeedbacksInPage = 0;
        let duplicateFeedbacksInPage = 0;
        let updatesInPage = 0;

        // Log the date range of the first and last items for debugging
        if (feedbacks.length > 0) {
          const firstItem = feedbacks[0];
          const lastItem = feedbacks[feedbacks.length - 1];
          const firstItemDate = new Date(firstItem.localTime);
          const lastItemDate = new Date(lastItem.localTime);

          console.log(`  Date range of page ${textPage}: ${firstItemDate.toISOString()} to ${lastItemDate.toISOString()}`);
        }

        // Process feedbacks
        for (const feedback of feedbacks) {
          // Check if we've already seen this feedback ID in this sync operation
          if (seenTextFeedbackIds.has(feedback.feedbackId)) {
            duplicateFeedbacksInPage++;
            continue; // Skip this feedback as we've already processed it
          }

          // Add to seen set
          seenTextFeedbackIds.add(feedback.feedbackId);

          // Check if feedback already exists
          if (!existingFeedbackMap.has(feedback.feedbackId)) {
            // New feedback
            newFeedbacksInPage++;
            textNewFeedbacks.push({
              feedbackId: feedback.feedbackId,
              buttonIndex: feedback.buttonIndex,
              localTime: new Date(feedback.localTime),
              experiencePointId: feedback.experiencePointId,
              experiencePointName: settings.experiencePointName || 'Unknown',
              surveyId: feedback.surveyId,
              smileyId: feedback.smileyId,
              smileyType: feedback.smileyType,
              text: feedback.text,
              textInEnglish: feedback.textInEnglish,
              spam: feedback.spam || false,
              misuse: feedback.misuse || false
            });
          } else if (!existingTextFeedbackMap.has(feedback.feedbackId)) {
            // Existing feedback without text - queue for update
            updatesInPage++;
            textUpdates.push({
              feedbackId: feedback.feedbackId,
              text: feedback.text,
              textInEnglish: feedback.textInEnglish,
              spam: feedback.spam || false
            });
          }
        }

        // Log statistics
        console.log(`  Page ${textPage}: ${newFeedbacksInPage} new items, ${updatesInPage} updates, ${duplicateFeedbacksInPage} duplicates`);

        // Only count new items toward our sync total
        textTotalSynced += newFeedbacksInPage + updatesInPage;

        // Check if we need to continue pagination - only if we got a full page
        textHasMore = feedbacks.length === 1000;

        textPage++;
      } catch (error) {
        console.error(`Error syncing text feedbacks page ${textPage}:`, error.message);
        if (error.response) {
          console.error('API response status:', error.response.status);
          console.error('API response data:', error.response.data);

          if (error.response.data?.validationErrors) {
            console.error('Validation errors:', error.response.data.validationErrors);
          }
        }
        textHasMore = false; // Stop pagination on error
      }
    }

    // Insert any new text feedbacks
    if (textNewFeedbacks.length > 0) {
      console.log(`  Inserting batch of ${textNewFeedbacks.length} text feedbacks`);
      try {
        // Use ordered: false to continue processing even if some documents fail
        const result = await HappyOrNotFeedback.insertMany(textNewFeedbacks, {
          ordered: false,
          rawResult: true
        });

        // Count how many were actually inserted
        const insertedCount = result.insertedCount || 0;
        textTotalSaved += insertedCount;

        if (insertedCount < textNewFeedbacks.length) {
          console.log(`  Note: Only ${insertedCount} of ${textNewFeedbacks.length} text feedbacks were inserted (${textNewFeedbacks.length - insertedCount} duplicates or errors)`);
        }
      } catch (error) {
        // Handle bulk write errors more gracefully
        if (error.code === 11000 || (error.name === 'MongoBulkWriteError' && error.code === 11000)) {
          // This is a duplicate key error
          const insertedCount = error.result?.insertedCount || 0;
          textTotalSaved += insertedCount;
          console.log(`  Warning: Encountered duplicate keys during text feedback insertion. ${insertedCount} of ${textNewFeedbacks.length} were inserted.`);

          // Even with errors, update our maps with the successfully inserted feedbacks
          textNewFeedbacks.forEach(feedback => {
            existingFeedbackMap.set(feedback.feedbackId, true);
            existingTextFeedbackMap.set(feedback.feedbackId, true);
            seenTextFeedbackIds.add(feedback.feedbackId);
          });
        } else {
          // Log other errors but continue processing
          console.error(`  Error inserting text feedbacks:`, error.message);
        }
      }
    }

    // Process text updates in batches
    if (textUpdates.length > 0) {
      console.log(`  Processing ${textUpdates.length} text updates`);

      // Process updates in batches of 100
      const batchSize = 100;
      for (let i = 0; i < textUpdates.length; i += batchSize) {
        const batch = textUpdates.slice(i, i + batchSize);

        // Use bulkWrite for efficient updates
        const bulkOps = batch.map(update => ({
          updateOne: {
            filter: { feedbackId: update.feedbackId },
            update: {
              $set: {
                text: update.text,
                textInEnglish: update.textInEnglish,
                spam: update.spam
              }
            }
          }
        }));

        try {
          await HappyOrNotFeedback.bulkWrite(bulkOps);
          textTotalUpdated += batch.length;
        } catch (error) {
          console.error(`  Error updating text feedbacks:`, error.message);
        }
      }
    }

    console.log(`Text feedbacks sync complete. Synced ${textTotalSynced}, saved ${textTotalSaved} new and updated ${textTotalUpdated} existing items.`);

    // Sync follow-up feedbacks with pagination
    let followUpPage = 1;
    let followUpHasMore = true;
    let followUpTotalSynced = 0;
    let followUpTotalSaved = 0;
    let followUpTotalUpdated = 0;
    let followUpNewFeedbacks = []; // Array to collect new feedbacks for bulk insert
    let followUpUpdates = []; // Array to collect updates for existing feedbacks
    let seenFollowUpFeedbackIds = new Set(); // Track feedback IDs we've already seen in this sync

    console.log('Syncing follow-up feedbacks...');

    // Set a reasonable page limit based on expected data volume
    const maxFollowUpPages = 20;

    while (followUpHasMore && followUpPage <= maxFollowUpPages) {
      // Build query parameters
      const params = {
        period,
        limit: 1000, // API maximum limit
        order: 'desc', // Use desc to get newest first
        page: followUpPage
      };

      try {
        // Make API request
        console.log(`Making API request for follow-up feedbacks page ${followUpPage} with params:`, params);
        const response = await axios.get('https://api.happy-or-not.com/v2/results/follow-up-feedbacks.json', {
          headers: {
            'X-HON-API-Token': apiKey
          },
          params
        });

        // Process feedbacks
        const feedbacks = response.data;
        console.log(`Processing follow-up feedbacks page ${followUpPage}, received ${feedbacks.length} items`);

        // Track if this page contains any new feedback IDs
        let newFeedbacksInPage = 0;
        let duplicateFeedbacksInPage = 0;
        let updatesInPage = 0;

        // Log the date range of the first and last items for debugging
        if (feedbacks.length > 0) {
          const firstItem = feedbacks[0];
          const lastItem = feedbacks[feedbacks.length - 1];
          const firstItemDate = new Date(firstItem.localTime);
          const lastItemDate = new Date(lastItem.localTime);

          console.log(`  Date range of page ${followUpPage}: ${firstItemDate.toISOString()} to ${lastItemDate.toISOString()}`);
        }

        // Process feedbacks
        for (const feedback of feedbacks) {
          // Check if we've already seen this feedback ID in this sync operation
          if (seenFollowUpFeedbackIds.has(feedback.feedbackId)) {
            duplicateFeedbacksInPage++;
            continue; // Skip this feedback as we've already processed it
          }

          // Add to seen set
          seenFollowUpFeedbackIds.add(feedback.feedbackId);

          // Check if feedback already exists
          if (!existingFeedbackMap.has(feedback.feedbackId)) {
            // New feedback
            newFeedbacksInPage++;
            followUpNewFeedbacks.push({
              feedbackId: feedback.feedbackId,
              buttonIndex: feedback.buttonIndex,
              localTime: new Date(feedback.localTime),
              experiencePointId: feedback.experiencePointId,
              experiencePointName: settings.experiencePointName || 'Unknown',
              surveyId: feedback.surveyId,
              smileyId: feedback.smileyId,
              smileyType: feedback.smileyType,
              followupQuestionId: feedback.followupQuestionId,
              followupOptionId: feedback.followupOptionId,
              followupOptionText: feedback.followupOptionTextInDefaultLocale || feedback.followupOptionText
            });
          } else if (!existingFollowUpFeedbackMap.has(feedback.feedbackId)) {
            // Existing feedback without follow-up - queue for update
            updatesInPage++;
            followUpUpdates.push({
              feedbackId: feedback.feedbackId,
              followupQuestionId: feedback.followupQuestionId,
              followupOptionId: feedback.followupOptionId,
              followupOptionText: feedback.followupOptionTextInDefaultLocale || feedback.followupOptionText
            });
          }
        }

        // Log statistics
        console.log(`  Page ${followUpPage}: ${newFeedbacksInPage} new items, ${updatesInPage} updates, ${duplicateFeedbacksInPage} duplicates`);

        // Only count new items toward our sync total
        followUpTotalSynced += newFeedbacksInPage + updatesInPage;

        // Check if we need to continue pagination - only if we got a full page
        followUpHasMore = feedbacks.length === 1000;

        followUpPage++;
      } catch (error) {
        console.error(`Error syncing follow-up feedbacks page ${followUpPage}:`, error.message);
        if (error.response) {
          console.error('API response status:', error.response.status);
          console.error('API response data:', error.response.data);

          if (error.response.data?.validationErrors) {
            console.error('Validation errors:', error.response.data.validationErrors);
          }
        }
        followUpHasMore = false; // Stop pagination on error
      }
    }

    // Insert any new follow-up feedbacks
    if (followUpNewFeedbacks.length > 0) {
      console.log(`  Inserting batch of ${followUpNewFeedbacks.length} follow-up feedbacks`);
      try {
        // Use ordered: false to continue processing even if some documents fail
        const result = await HappyOrNotFeedback.insertMany(followUpNewFeedbacks, {
          ordered: false,
          rawResult: true
        });

        // Count how many were actually inserted
        const insertedCount = result.insertedCount || 0;
        followUpTotalSaved += insertedCount;

        if (insertedCount < followUpNewFeedbacks.length) {
          console.log(`  Note: Only ${insertedCount} of ${followUpNewFeedbacks.length} follow-up feedbacks were inserted (${followUpNewFeedbacks.length - insertedCount} duplicates or errors)`);
        }
      } catch (error) {
        // Handle bulk write errors more gracefully
        if (error.code === 11000 || (error.name === 'MongoBulkWriteError' && error.code === 11000)) {
          // This is a duplicate key error
          const insertedCount = error.result?.insertedCount || 0;
          followUpTotalSaved += insertedCount;
          console.log(`  Warning: Encountered duplicate keys during follow-up feedback insertion. ${insertedCount} of ${followUpNewFeedbacks.length} were inserted.`);

          // Even with errors, update our maps with the successfully inserted feedbacks
          followUpNewFeedbacks.forEach(feedback => {
            existingFeedbackMap.set(feedback.feedbackId, true);
            existingFollowUpFeedbackMap.set(feedback.feedbackId, true);
            seenFollowUpFeedbackIds.add(feedback.feedbackId);
          });
        } else {
          // Log other errors but continue processing
          console.error(`  Error inserting follow-up feedbacks:`, error.message);
        }
      }
    }

    // Process follow-up updates in batches
    if (followUpUpdates.length > 0) {
      console.log(`  Processing ${followUpUpdates.length} follow-up updates`);

      // Process updates in batches of 100
      const batchSize = 100;
      for (let i = 0; i < followUpUpdates.length; i += batchSize) {
        const batch = followUpUpdates.slice(i, i + batchSize);

        // Use bulkWrite for efficient updates
        const bulkOps = batch.map(update => ({
          updateOne: {
            filter: { feedbackId: update.feedbackId },
            update: {
              $set: {
                followupQuestionId: update.followupQuestionId,
                followupOptionId: update.followupOptionId,
                followupOptionText: update.followupOptionText || update.followupOptionTextInDefaultLocale
              }
            }
          }
        }));

        try {
          await HappyOrNotFeedback.bulkWrite(bulkOps);
          followUpTotalUpdated += batch.length;
        } catch (error) {
          console.error(`  Error updating follow-up feedbacks:`, error.message);
        }
      }
    }

    console.log(`Follow-up feedbacks sync complete. Synced ${followUpTotalSynced}, saved ${followUpTotalSaved} new and updated ${followUpTotalUpdated} existing items.`);

    // Calculate totals
    totalSynced = buttonTotalSynced + textTotalSynced + followUpTotalSynced;
    totalSaved = buttonTotalSaved + textTotalSaved + followUpTotalSaved;
    totalUpdated = textTotalUpdated + followUpTotalUpdated;

    // Log unique items processed for debugging
    const uniqueItemsProcessed = {
      button: seenFeedbackIds.size,
      text: seenTextFeedbackIds.size,
      followUp: seenFollowUpFeedbackIds.size,
      total: seenFeedbackIds.size + seenTextFeedbackIds.size + seenFollowUpFeedbackIds.size
    };

    console.log('Sync completed with duplicate detection:');
    console.log(`  Button feedbacks: ${uniqueItemsProcessed.button} unique items processed`);
    console.log(`  Text feedbacks: ${uniqueItemsProcessed.text} unique items processed`);
    console.log(`  Follow-up feedbacks: ${uniqueItemsProcessed.followUp} unique items processed`);
    console.log(`  Total unique items: ${uniqueItemsProcessed.total}`);

    return {
      success: true,
      message: `Data sync for period ${period} completed successfully`,
      stats: {
        totalSynced,
        totalSaved,
        totalUpdated,
        uniqueItemsProcessed,
        button: {
          synced: buttonTotalSynced,
          saved: buttonTotalSaved
        },
        text: {
          synced: textTotalSynced,
          saved: textTotalSaved,
          updated: textTotalUpdated
        },
        followUp: {
          synced: followUpTotalSynced,
          saved: followUpTotalSaved,
          updated: followUpTotalUpdated
        }
      }
    };
  } catch (error) {
    console.error(`Error in syncHistoricalDataWithPeriod (${period}):`, error);
    return {
      success: false,
      error: error.message,
      stats: {
        totalSynced: 0,
        totalSaved: 0,
        totalUpdated: 0,
        uniqueItemsProcessed: {
          button: 0,
          text: 0,
          followUp: 0,
          total: 0
        },
        button: {
          synced: 0,
          saved: 0
        },
        text: {
          synced: 0,
          saved: 0,
          updated: 0
        },
        followUp: {
          synced: 0,
          saved: 0,
          updated: 0
        }
      }
    };
  }
}

  /**
   * Helper method to sync historical data with a specific date range
   * @param {string} startDate - Start date in YYYY-MM-DD format
   * @param {string} endDate - End date in YYYY-MM-DD format
   * @param {string} apiKey - The API key
   * @param {Object} settings - The settings object
   * @param {boolean} isTodaySync - Whether this is a sync specifically for today's data
   * @returns {Promise<Object>} The sync result
   */
  async syncHistoricalDataWithDateRange(startDate, endDate, apiKey, settings, isTodaySync = false) {
    try {
      // Track overall stats
      let totalSynced = 0;
      let totalSaved = 0;
      let totalUpdated = 0;

      // Parse dates for validation
      const startDateObj = new Date(startDate);

      // For the end date, make sure we include the full day
      const endDateObj = new Date(endDate);
      endDateObj.setHours(23, 59, 59, 999); // Set to end of day to include all data

      if (isTodaySync) {
        console.log(`Syncing TODAY'S data specifically from ${startDate} to ${endDate} (forced refresh)`);
      } else {
        console.log(`Syncing data from ${startDate} to ${endDate} (including full day)`);
      }

      // Validate date range
      if (isNaN(startDateObj.getTime()) || isNaN(endDateObj.getTime())) {
        throw new Error('Invalid date format. Use YYYY-MM-DD format.');
      }

      // If start date is after end date, adjust it to be 30 days before end date
      if (startDateObj > endDateObj) {
        console.log(`Adjusting start date (${startDate}) to be before end date (${endDate})`);
        startDateObj.setDate(endDateObj.getDate() - 30);
        startDate = startDateObj.toISOString().split('T')[0];
        console.log(`New start date: ${startDate}`);
      }

      // Get all existing feedback IDs for this date range to avoid duplicate queries
      console.log('Fetching existing feedback IDs from database...');

      // For today's sync, we want to be more aggressive about checking for new data
      // so we'll use a different query that ensures we don't miss any updates
      let existingFeedbacks;
      if (isTodaySync) {
        // For today's sync, we'll get all feedback IDs from the database
        // This ensures we don't miss any updates to existing records
        existingFeedbacks = await HappyOrNotFeedback.find({
          // No date filter for today's sync - we want to check against all existing records
          // to ensure we don't miss any updates
        }, 'feedbackId buttonIndex text followupOptionId').lean();
        console.log(`For today's sync, checking against all ${existingFeedbacks.length} existing records`);
      } else {
        // For regular sync, just get the feedback IDs for this date range
        existingFeedbacks = await HappyOrNotFeedback.find({
          localTime: {
            $gte: startDateObj,
            $lte: endDateObj
          }
        }, 'feedbackId buttonIndex text followupOptionId').lean();
      }

      // Create lookup maps for faster checking
      const existingFeedbackMap = new Map();
      const existingTextFeedbackMap = new Map();
      const existingFollowUpFeedbackMap = new Map();

      existingFeedbacks.forEach(feedback => {
        existingFeedbackMap.set(feedback.feedbackId, true);

        // Track which feedbacks already have text
        if (feedback.text) {
          existingTextFeedbackMap.set(feedback.feedbackId, true);
        }

        // Track which feedbacks already have follow-up data
        if (feedback.followupOptionId) {
          existingFollowUpFeedbackMap.set(feedback.feedbackId, true);
        }
      });

      console.log(`Found ${existingFeedbackMap.size} existing feedbacks in database for this date range`);

      // Sync button feedbacks with pagination
      let buttonPage = 1;
      let buttonHasMore = true;
      let buttonTotalSynced = 0;
      let buttonTotalSaved = 0;
      let buttonNewFeedbacks = []; // Array to collect new feedbacks for bulk insert
      let lastFeedbackDate = null;
      let seenFeedbackIds = new Set(); // Track feedback IDs we've already seen in this sync
      let duplicatePageCount = 0; // Track consecutive duplicate pages

      console.log('Syncing button feedbacks...');

      // Set a reasonable page limit based on expected data volume
      const maxButtonPages = 20; // Reduced from 100 to avoid excessive API calls
      const maxConsecutiveDuplicates = 2; // Stop after this many consecutive duplicate pages

      while (buttonHasMore && buttonPage <= maxButtonPages && duplicatePageCount < maxConsecutiveDuplicates) {
        // Build query parameters
        const params = {
          startDate,
          endDate,
          limit: 1000, // API maximum limit
          order: isTodaySync ? 'desc' : 'asc', // Use desc for today's sync to get newest first
          page: buttonPage,
          experiencePointId: '96955' // Hard-coded for testing
        };

        try {
          // Make API request
          const response = await axios.get('https://api.happy-or-not.com/v2/results/button-feedbacks.json', {
            headers: {
              'X-HON-API-Token': apiKey
            },
            params
          });

          // Process feedbacks
          const feedbacks = response.data;
          console.log(`Processing button feedbacks page ${buttonPage}, received ${feedbacks.length} items`);

          // Track if this page contains any new feedback IDs
          let newFeedbacksInPage = 0;
          let duplicateFeedbacksInPage = 0;

          // Log the date range of the first and last items for debugging
          if (feedbacks.length > 0) {
            const firstItem = feedbacks[0];
            const lastItem = feedbacks[feedbacks.length - 1];
            const firstItemDate = new Date(firstItem.localTime);
            const lastItemDate = new Date(lastItem.localTime);

            console.log(`  Date range of page ${buttonPage}: ${firstItemDate.toISOString()} to ${lastItemDate.toISOString()}`);

            // Update last feedback date for tracking
            lastFeedbackDate = lastItemDate;

            // Check if we're getting data outside our requested range
            if (firstItemDate < startDateObj || lastItemDate > endDateObj) {
              console.warn(`  Warning: Received data outside requested date range on page ${buttonPage}`);
            }

            // Check if we're getting duplicate data across pages
            if (buttonPage > 1 && lastFeedbackDate && firstItemDate <= lastFeedbackDate) {
              console.warn(`  Warning: Possible duplicate data detected on page ${buttonPage}`);
            }
          }

          // Collect new feedbacks for bulk insert
          for (const feedback of feedbacks) {
            // Only process if within our date range
            const feedbackDate = new Date(feedback.localTime);
            if (feedbackDate >= startDateObj && feedbackDate <= endDateObj) {
              // Check if we've already seen this feedback ID in this sync operation
              if (seenFeedbackIds.has(feedback.feedbackId)) {
                duplicateFeedbacksInPage++;
                continue; // Skip this feedback as we've already processed it
              }

              // Add to seen set
              seenFeedbackIds.add(feedback.feedbackId);
              newFeedbacksInPage++;

              // Check if feedback already exists in database
              if (!existingFeedbackMap.has(feedback.feedbackId)) {
                buttonNewFeedbacks.push({
                  feedbackId: feedback.feedbackId,
                  buttonIndex: feedback.buttonIndex,
                  localTime: new Date(feedback.localTime),
                  experiencePointId: feedback.experiencePointId,
                  experiencePointName: settings.experiencePointName || 'Unknown',
                  surveyId: feedback.surveyId,
                  smileyId: feedback.smileyId,
                  smileyType: feedback.smileyType,
                  misuse: feedback.misuse || false
                });
              }
            }
          }

          // Log duplicate statistics
          console.log(`  Page ${buttonPage}: ${newFeedbacksInPage} new items, ${duplicateFeedbacksInPage} duplicates`);

          // If most items on this page were duplicates, increment our duplicate counter
          if (feedbacks.length > 0 && newFeedbacksInPage === 0) {
            duplicatePageCount++;
            console.warn(`  Warning: Page ${buttonPage} contained only duplicate data (${duplicatePageCount}/${maxConsecutiveDuplicates})`);
          } else {
            duplicatePageCount = 0; // Reset counter if we found new items
          }

          // Only count new items toward our sync total
          buttonTotalSynced += newFeedbacksInPage;

          // Check if we need to continue pagination - only if we got a full page AND found new items
          buttonHasMore = feedbacks.length === 1000 && newFeedbacksInPage > 0;

          // If we have a lot of new feedbacks, insert them in batches to avoid memory issues
          if (buttonNewFeedbacks.length >= 5000) {
            if (buttonNewFeedbacks.length > 0) {
              console.log(`  Inserting batch of ${buttonNewFeedbacks.length} button feedbacks`);
              try {
                // Use ordered: false to continue processing even if some documents fail
                const result = await HappyOrNotFeedback.insertMany(buttonNewFeedbacks, {
                  ordered: false,
                  // Add a catch for duplicate key errors
                  rawResult: true
                });

                // Count how many were actually inserted
                const insertedCount = result.insertedCount || 0;
                buttonTotalSaved += insertedCount;

                if (insertedCount < buttonNewFeedbacks.length) {
                  console.log(`  Note: Only ${insertedCount} of ${buttonNewFeedbacks.length} button feedbacks were inserted (${buttonNewFeedbacks.length - insertedCount} duplicates or errors)`);
                }
              } catch (error) {
                // Handle bulk write errors more gracefully
                if (error.code === 11000 || (error.name === 'MongoBulkWriteError' && error.code === 11000)) {
                  // This is a duplicate key error
                  const insertedCount = error.result?.insertedCount || 0;
                  buttonTotalSaved += insertedCount;
                  console.log(`  Warning: Encountered duplicate keys during batch button feedback insertion. ${insertedCount} of ${buttonNewFeedbacks.length} were inserted.`);
                } else {
                  // Re-throw other errors
                  throw error;
                }
              }

              // Update our maps with the new feedbacks to avoid duplicates in future batches
              buttonNewFeedbacks.forEach(feedback => {
                existingFeedbackMap.set(feedback.feedbackId, true);
                // Also add to the seen set to avoid duplicate processing
                seenFeedbackIds.add(feedback.feedbackId);
              });

              // Clear the array for the next batch
              buttonNewFeedbacks = [];
            }
          }

          buttonPage++;
        } catch (error) {
          console.error(`Error syncing button feedbacks page ${buttonPage}:`, error.message);
          if (error.response?.data?.validationErrors) {
            console.error('Validation errors:', error.response.data.validationErrors);
          }
          buttonHasMore = false; // Stop pagination on error
        }

        // Add a small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      // Insert any remaining button feedbacks
      if (buttonNewFeedbacks.length > 0) {
        console.log(`  Inserting final batch of ${buttonNewFeedbacks.length} button feedbacks`);
        try {
          // Use ordered: false to continue processing even if some documents fail
          const result = await HappyOrNotFeedback.insertMany(buttonNewFeedbacks, {
            ordered: false,
            // Add a catch for duplicate key errors
            rawResult: true
          });

          // Count how many were actually inserted
          const insertedCount = result.insertedCount || 0;
          buttonTotalSaved += insertedCount;

          if (insertedCount < buttonNewFeedbacks.length) {
            console.log(`  Note: Only ${insertedCount} of ${buttonNewFeedbacks.length} button feedbacks were inserted (${buttonNewFeedbacks.length - insertedCount} duplicates or errors)`);
          }
        } catch (error) {
          // Handle bulk write errors more gracefully
          if (error.code === 11000 || (error.name === 'MongoBulkWriteError' && error.code === 11000)) {
            // This is a duplicate key error
            const insertedCount = error.result?.insertedCount || 0;
            buttonTotalSaved += insertedCount;
            console.log(`  Warning: Encountered duplicate keys during button feedback insertion. ${insertedCount} of ${buttonNewFeedbacks.length} were inserted.`);

            // Log the duplicate keys for debugging
            if (error.writeErrors && error.writeErrors.length > 0) {
              console.log(`  Duplicate keys detected: ${error.writeErrors.length} errors`);
            }

            // Even with errors, update our maps with the successfully inserted feedbacks
            buttonNewFeedbacks.forEach(feedback => {
              existingFeedbackMap.set(feedback.feedbackId, true);
              seenFeedbackIds.add(feedback.feedbackId);
            });
          } else {
            // Re-throw other errors
            throw error;
          }
        }
      }

      console.log(`Button feedbacks sync complete. Synced ${buttonTotalSynced}, saved ${buttonTotalSaved} new items.`);

      // If we hit the page limit, log a warning
      if (buttonPage > maxButtonPages) {
        console.warn(`Warning: Reached maximum page limit (${maxButtonPages}) for button feedbacks. Some data may not have been synced.`);
      }

      // Sync text feedbacks with pagination
      let textPage = 1;
      let textHasMore = true;
      let textTotalSynced = 0;
      let textTotalSaved = 0;
      let textTotalUpdated = 0;
      let textNewFeedbacks = []; // Array to collect new feedbacks for bulk insert
      let textUpdates = []; // Array to collect updates for existing feedbacks
      lastFeedbackDate = null;
      let seenTextFeedbackIds = new Set(); // Track feedback IDs we've already seen in this sync
      let duplicateTextPageCount = 0; // Track consecutive duplicate pages

      console.log('Syncing text feedbacks...');

      // Set a reasonable page limit based on expected data volume
      const maxTextPages = 20; // Reduced from 50 to avoid excessive API calls
      const maxConsecutiveTextDuplicates = 2; // Stop after this many consecutive duplicate pages

      while (textHasMore && textPage <= maxTextPages && duplicateTextPageCount < maxConsecutiveTextDuplicates) {
        // Build query parameters
        const params = {
          startDate,
          endDate,
          limit: 1000, // API maximum limit
          order: isTodaySync ? 'desc' : 'asc', // Use desc for today's sync to get newest first
          page: textPage
        };

        try {
          // Make API request
          const response = await axios.get('https://api.happy-or-not.com/v2/results/text-feedbacks.json', {
            headers: {
              'X-HON-API-Token': apiKey
            },
            params
          });

          // Process feedbacks
          const feedbacks = response.data;
          console.log(`Processing text feedbacks page ${textPage}, received ${feedbacks.length} items`);

          // Track if this page contains any new feedback IDs
          let newFeedbacksInPage = 0;
          let duplicateFeedbacksInPage = 0;
          let updatesInPage = 0;

          // Log the date range of the first and last items for debugging
          if (feedbacks.length > 0) {
            const firstItem = feedbacks[0];
            const lastItem = feedbacks[feedbacks.length - 1];
            const firstItemDate = new Date(firstItem.localTime);
            const lastItemDate = new Date(lastItem.localTime);

            console.log(`  Date range of page ${textPage}: ${firstItemDate.toISOString()} to ${lastItemDate.toISOString()}`);

            // Update last feedback date for tracking
            lastFeedbackDate = lastItemDate;

            // Check if we're getting duplicate data across pages
            if (textPage > 1 && lastFeedbackDate && firstItemDate <= lastFeedbackDate) {
              console.warn(`  Warning: Possible duplicate data detected on page ${textPage}`);
            }
          }

          // Process feedbacks
          for (const feedback of feedbacks) {
            // Only process if within our date range
            const feedbackDate = new Date(feedback.localTime);
            if (feedbackDate >= startDateObj && feedbackDate <= endDateObj) {
              // Check if we've already seen this feedback ID in this sync operation
              if (seenTextFeedbackIds.has(feedback.feedbackId)) {
                duplicateFeedbacksInPage++;
                continue; // Skip this feedback as we've already processed it
              }

              // Add to seen set
              seenTextFeedbackIds.add(feedback.feedbackId);

              // Check if feedback already exists
              if (!existingFeedbackMap.has(feedback.feedbackId)) {
                // New feedback
                newFeedbacksInPage++;
                textNewFeedbacks.push({
                  feedbackId: feedback.feedbackId,
                  buttonIndex: feedback.buttonIndex,
                  localTime: new Date(feedback.localTime),
                  experiencePointId: feedback.experiencePointId,
                  experiencePointName: settings.experiencePointName || 'Unknown',
                  surveyId: feedback.surveyId,
                  smileyId: feedback.smileyId,
                  smileyType: feedback.smileyType,
                  text: feedback.text,
                  textInEnglish: feedback.textInEnglish,
                  spam: feedback.spam || false,
                  misuse: feedback.misuse || false
                });
              } else if (!existingTextFeedbackMap.has(feedback.feedbackId)) {
                // Existing feedback without text - queue for update
                updatesInPage++;
                textUpdates.push({
                  feedbackId: feedback.feedbackId,
                  text: feedback.text,
                  textInEnglish: feedback.textInEnglish,
                  spam: feedback.spam || false
                });
              }
            }
          }

          // Log duplicate statistics
          console.log(`  Page ${textPage}: ${newFeedbacksInPage} new items, ${updatesInPage} updates, ${duplicateFeedbacksInPage} duplicates`);

          // If most items on this page were duplicates, increment our duplicate counter
          if (feedbacks.length > 0 && newFeedbacksInPage === 0 && updatesInPage === 0) {
            duplicateTextPageCount++;
            console.warn(`  Warning: Page ${textPage} contained only duplicate data (${duplicateTextPageCount}/${maxConsecutiveTextDuplicates})`);
          } else {
            duplicateTextPageCount = 0; // Reset counter if we found new items
          }

          // Only count new items toward our sync total
          textTotalSynced += newFeedbacksInPage + updatesInPage;

          // Check if we need to continue pagination - only if we got a full page AND found new items or updates
          textHasMore = feedbacks.length === 1000 && (newFeedbacksInPage > 0 || updatesInPage > 0);

          // If we have a lot of new feedbacks, insert them in batches
          if (textNewFeedbacks.length >= 1000) {
            if (textNewFeedbacks.length > 0) {
              console.log(`  Inserting batch of ${textNewFeedbacks.length} text feedbacks`);
              try {
                // Use ordered: false to continue processing even if some documents fail
                const result = await HappyOrNotFeedback.insertMany(textNewFeedbacks, {
                  ordered: false,
                  // Add a catch for duplicate key errors
                  rawResult: true
                });

                // Count how many were actually inserted
                const insertedCount = result.insertedCount || 0;
                textTotalSaved += insertedCount;

                if (insertedCount < textNewFeedbacks.length) {
                  console.log(`  Note: Only ${insertedCount} of ${textNewFeedbacks.length} text feedbacks were inserted (${textNewFeedbacks.length - insertedCount} duplicates or errors)`);
                }
              } catch (error) {
                // Handle bulk write errors more gracefully
                if (error.code === 11000 || (error.name === 'MongoBulkWriteError' && error.code === 11000)) {
                  // This is a duplicate key error
                  const insertedCount = error.result?.insertedCount || 0;
                  textTotalSaved += insertedCount;
                  console.log(`  Warning: Encountered duplicate keys during batch text feedback insertion. ${insertedCount} of ${textNewFeedbacks.length} were inserted.`);
                } else {
                  // Re-throw other errors
                  throw error;
                }
              }

              // Update our maps
              textNewFeedbacks.forEach(feedback => {
                existingFeedbackMap.set(feedback.feedbackId, true);
                existingTextFeedbackMap.set(feedback.feedbackId, true);
                // Also add to the seen set to avoid duplicate processing
                seenTextFeedbackIds.add(feedback.feedbackId);
              });

              // Clear the array for the next batch
              textNewFeedbacks = [];
            }
          }

          textPage++;
        } catch (error) {
          console.error(`Error syncing text feedbacks page ${textPage}:`, error.message);
          if (error.response?.data?.validationErrors) {
            console.error('Validation errors:', error.response.data.validationErrors);
          }
          textHasMore = false; // Stop pagination on error
        }

        // Add a small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      // Insert any remaining text feedbacks
      if (textNewFeedbacks.length > 0) {
        console.log(`  Inserting final batch of ${textNewFeedbacks.length} text feedbacks`);
        try {
          // Use ordered: false to continue processing even if some documents fail
          const result = await HappyOrNotFeedback.insertMany(textNewFeedbacks, {
            ordered: false,
            // Add a catch for duplicate key errors
            rawResult: true
          });

          // Count how many were actually inserted
          const insertedCount = result.insertedCount || 0;
          textTotalSaved += insertedCount;

          if (insertedCount < textNewFeedbacks.length) {
            console.log(`  Note: Only ${insertedCount} of ${textNewFeedbacks.length} text feedbacks were inserted (${textNewFeedbacks.length - insertedCount} duplicates or errors)`);
          }
        } catch (error) {
          // Handle bulk write errors more gracefully
          if (error.code === 11000 || (error.name === 'MongoBulkWriteError' && error.code === 11000)) {
            // This is a duplicate key error
            const insertedCount = error.result?.insertedCount || 0;
            textTotalSaved += insertedCount;
            console.log(`  Warning: Encountered duplicate keys during text feedback insertion. ${insertedCount} of ${textNewFeedbacks.length} were inserted.`);

            // Log the duplicate keys for debugging
            if (error.writeErrors && error.writeErrors.length > 0) {
              console.log(`  Duplicate keys detected: ${error.writeErrors.length} errors`);
            }

            // Even with errors, update our maps with the successfully inserted feedbacks
            textNewFeedbacks.forEach(feedback => {
              existingFeedbackMap.set(feedback.feedbackId, true);
              existingTextFeedbackMap.set(feedback.feedbackId, true);
              seenTextFeedbackIds.add(feedback.feedbackId);
            });
          } else {
            // Re-throw other errors
            throw error;
          }
        }
      }

      // Process text updates in batches
      if (textUpdates.length > 0) {
        console.log(`  Processing ${textUpdates.length} text updates`);

        // Process updates in batches of 100
        const batchSize = 100;
        for (let i = 0; i < textUpdates.length; i += batchSize) {
          const batch = textUpdates.slice(i, i + batchSize);

          // Use bulkWrite for efficient updates
          const bulkOps = batch.map(update => ({
            updateOne: {
              filter: { feedbackId: update.feedbackId },
              update: {
                $set: {
                  text: update.text,
                  textInEnglish: update.textInEnglish,
                  spam: update.spam
                }
              }
            }
          }));

          await HappyOrNotFeedback.bulkWrite(bulkOps);
          textTotalUpdated += batch.length;
        }
      }

      console.log(`Text feedbacks sync complete. Synced ${textTotalSynced}, saved ${textTotalSaved} new and updated ${textTotalUpdated} existing items.`);

      // If we hit the page limit, log a warning
      if (textPage > maxTextPages) {
        console.warn(`Warning: Reached maximum page limit (${maxTextPages}) for text feedbacks. Some data may not have been synced.`);
      }

      // Sync follow-up feedbacks with pagination
      let followUpPage = 1;
      let followUpHasMore = true;
      let followUpTotalSynced = 0;
      let followUpTotalSaved = 0;
      let followUpTotalUpdated = 0;
      let followUpNewFeedbacks = []; // Array to collect new feedbacks for bulk insert
      let followUpUpdates = []; // Array to collect updates for existing feedbacks
      lastFeedbackDate = null;
      let seenFollowUpFeedbackIds = new Set(); // Track feedback IDs we've already seen in this sync
      let duplicateFollowUpPageCount = 0; // Track consecutive duplicate pages

      console.log('Syncing follow-up feedbacks...');

      // Set a reasonable page limit based on expected data volume
      const maxFollowUpPages = 20; // Reduced from 50 to avoid excessive API calls
      const maxConsecutiveFollowUpDuplicates = 2; // Stop after this many consecutive duplicate pages

      while (followUpHasMore && followUpPage <= maxFollowUpPages && duplicateFollowUpPageCount < maxConsecutiveFollowUpDuplicates) {
        // Build query parameters
        const params = {
          startDate,
          endDate,
          limit: 1000, // API maximum limit
          order: isTodaySync ? 'desc' : 'asc', // Use desc for today's sync to get newest first
          page: followUpPage
        };

        try {
          // Make API request
          const response = await axios.get('https://api.happy-or-not.com/v2/results/follow-up-feedbacks.json', {
            headers: {
              'X-HON-API-Token': apiKey
            },
            params
          });

          // Process feedbacks
          const feedbacks = response.data;
          console.log(`Processing follow-up feedbacks page ${followUpPage}, received ${feedbacks.length} items`);

          // Track if this page contains any new feedback IDs
          let newFeedbacksInPage = 0;
          let duplicateFeedbacksInPage = 0;
          let updatesInPage = 0;

          // Log the date range of the first and last items for debugging
          if (feedbacks.length > 0) {
            const firstItem = feedbacks[0];
            const lastItem = feedbacks[feedbacks.length - 1];
            const firstItemDate = new Date(firstItem.localTime);
            const lastItemDate = new Date(lastItem.localTime);

            console.log(`  Date range of page ${followUpPage}: ${firstItemDate.toISOString()} to ${lastItemDate.toISOString()}`);

            // Update last feedback date for tracking
            lastFeedbackDate = lastItemDate;

            // Check if we're getting duplicate data across pages
            if (followUpPage > 1 && lastFeedbackDate && firstItemDate <= lastFeedbackDate) {
              console.warn(`  Warning: Possible duplicate data detected on page ${followUpPage}`);
            }
          }

          // Process feedbacks
          for (const feedback of feedbacks) {
            // Only process if within our date range
            const feedbackDate = new Date(feedback.localTime);
            if (feedbackDate >= startDateObj && feedbackDate <= endDateObj) {
              // Check if we've already seen this feedback ID in this sync operation
              if (seenFollowUpFeedbackIds.has(feedback.feedbackId)) {
                duplicateFeedbacksInPage++;
                continue; // Skip this feedback as we've already processed it
              }

              // Add to seen set
              seenFollowUpFeedbackIds.add(feedback.feedbackId);

              // Check if feedback already exists
              if (!existingFeedbackMap.has(feedback.feedbackId)) {
                // New feedback
                newFeedbacksInPage++;
                followUpNewFeedbacks.push({
                  feedbackId: feedback.feedbackId,
                  buttonIndex: feedback.buttonIndex,
                  localTime: new Date(feedback.localTime),
                  experiencePointId: feedback.experiencePointId,
                  experiencePointName: settings.experiencePointName || 'Unknown',
                  surveyId: feedback.surveyId,
                  smileyId: feedback.smileyId,
                  smileyType: feedback.smileyType,
                  followupQuestionId: feedback.followupQuestionId,
                  followupOptionId: feedback.followupOptionId,
                  followupOptionText: feedback.followupOptionTextInDefaultLocale || feedback.followupOptionText,
                  misuse: feedback.misuse || false
                });
              } else if (!existingFollowUpFeedbackMap.has(feedback.feedbackId)) {
                // Existing feedback without follow-up data - queue for update
                updatesInPage++;
                followUpUpdates.push({
                  feedbackId: feedback.feedbackId,
                  followupQuestionId: feedback.followupQuestionId,
                  followupOptionId: feedback.followupOptionId,
                  followupOptionText: feedback.followupOptionTextInDefaultLocale || feedback.followupOptionText
                });
              }
            }
          }

          // Log duplicate statistics
          console.log(`  Page ${followUpPage}: ${newFeedbacksInPage} new items, ${updatesInPage} updates, ${duplicateFeedbacksInPage} duplicates`);

          // If most items on this page were duplicates, increment our duplicate counter
          if (feedbacks.length > 0 && newFeedbacksInPage === 0 && updatesInPage === 0) {
            duplicateFollowUpPageCount++;
            console.warn(`  Warning: Page ${followUpPage} contained only duplicate data (${duplicateFollowUpPageCount}/${maxConsecutiveFollowUpDuplicates})`);
          } else {
            duplicateFollowUpPageCount = 0; // Reset counter if we found new items
          }

          // Only count new items toward our sync total
          followUpTotalSynced += newFeedbacksInPage + updatesInPage;

          // Check if we need to continue pagination - only if we got a full page AND found new items or updates
          followUpHasMore = feedbacks.length === 1000 && (newFeedbacksInPage > 0 || updatesInPage > 0);

          // If we have a lot of new feedbacks, insert them in batches
          if (followUpNewFeedbacks.length >= 1000) {
            if (followUpNewFeedbacks.length > 0) {
              console.log(`  Inserting batch of ${followUpNewFeedbacks.length} follow-up feedbacks`);
              try {
                // Use ordered: false to continue processing even if some documents fail
                const result = await HappyOrNotFeedback.insertMany(followUpNewFeedbacks, {
                  ordered: false,
                  // Add a catch for duplicate key errors
                  rawResult: true
                });

                // Count how many were actually inserted
                const insertedCount = result.insertedCount || 0;
                followUpTotalSaved += insertedCount;

                if (insertedCount < followUpNewFeedbacks.length) {
                  console.log(`  Note: Only ${insertedCount} of ${followUpNewFeedbacks.length} follow-up feedbacks were inserted (${followUpNewFeedbacks.length - insertedCount} duplicates or errors)`);
                }
              } catch (error) {
                // Handle bulk write errors more gracefully
                if (error.code === 11000 || (error.name === 'MongoBulkWriteError' && error.code === 11000)) {
                  // This is a duplicate key error
                  const insertedCount = error.result?.insertedCount || 0;
                  followUpTotalSaved += insertedCount;
                  console.log(`  Warning: Encountered duplicate keys during batch follow-up feedback insertion. ${insertedCount} of ${followUpNewFeedbacks.length} were inserted.`);
                } else {
                  // Re-throw other errors
                  throw error;
                }
              }

              // Update our maps
              followUpNewFeedbacks.forEach(feedback => {
                existingFeedbackMap.set(feedback.feedbackId, true);
                existingFollowUpFeedbackMap.set(feedback.feedbackId, true);
                // Also add to the seen set to avoid duplicate processing
                seenFollowUpFeedbackIds.add(feedback.feedbackId);
              });

              // Clear the array for the next batch
              followUpNewFeedbacks = [];
            }
          }

          followUpPage++;
        } catch (error) {
          console.error(`Error syncing follow-up feedbacks page ${followUpPage}:`, error.message);
          if (error.response?.data?.validationErrors) {
            console.error('Validation errors:', error.response.data.validationErrors);
          }
          followUpHasMore = false; // Stop pagination on error
        }

        // Add a small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      // Insert any remaining follow-up feedbacks
      if (followUpNewFeedbacks.length > 0) {
        console.log(`  Inserting final batch of ${followUpNewFeedbacks.length} follow-up feedbacks`);
        try {
          // Use ordered: false to continue processing even if some documents fail
          const result = await HappyOrNotFeedback.insertMany(followUpNewFeedbacks, {
            ordered: false,
            // Add a catch for duplicate key errors
            rawResult: true
          });

          // Count how many were actually inserted
          const insertedCount = result.insertedCount || 0;
          followUpTotalSaved += insertedCount;

          if (insertedCount < followUpNewFeedbacks.length) {
            console.log(`  Note: Only ${insertedCount} of ${followUpNewFeedbacks.length} follow-up feedbacks were inserted (${followUpNewFeedbacks.length - insertedCount} duplicates or errors)`);
          }
        } catch (error) {
          // Handle bulk write errors more gracefully
          if (error.code === 11000 || (error.name === 'MongoBulkWriteError' && error.code === 11000)) {
            // This is a duplicate key error
            const insertedCount = error.result?.insertedCount || 0;
            followUpTotalSaved += insertedCount;
            console.log(`  Warning: Encountered duplicate keys during follow-up feedback insertion. ${insertedCount} of ${followUpNewFeedbacks.length} were inserted.`);

            // Log the duplicate keys for debugging
            if (error.writeErrors && error.writeErrors.length > 0) {
              console.log(`  Duplicate keys detected: ${error.writeErrors.length} errors`);
            }

            // Even with errors, update our maps with the successfully inserted feedbacks
            followUpNewFeedbacks.forEach(feedback => {
              existingFeedbackMap.set(feedback.feedbackId, true);
              existingFollowUpFeedbackMap.set(feedback.feedbackId, true);
              seenFollowUpFeedbackIds.add(feedback.feedbackId);
            });
          } else {
            // Re-throw other errors
            throw error;
          }
        }
      }

      // Process follow-up updates in batches
      if (followUpUpdates.length > 0) {
        console.log(`  Processing ${followUpUpdates.length} follow-up updates`);

        // Process updates in batches of 100
        const batchSize = 100;
        for (let i = 0; i < followUpUpdates.length; i += batchSize) {
          const batch = followUpUpdates.slice(i, i + batchSize);

          // Use bulkWrite for efficient updates
          const bulkOps = batch.map(update => ({
            updateOne: {
              filter: { feedbackId: update.feedbackId },
              update: {
                $set: {
                  followupQuestionId: update.followupQuestionId,
                  followupOptionId: update.followupOptionId,
                  followupOptionText: update.followupOptionText
                }
              }
            }
          }));

          await HappyOrNotFeedback.bulkWrite(bulkOps);
          followUpTotalUpdated += batch.length;
        }
      }

      console.log(`Follow-up feedbacks sync complete. Synced ${followUpTotalSynced}, saved ${followUpTotalSaved} new and updated ${followUpTotalUpdated} existing items.`);

      // If we hit the page limit, log a warning
      if (followUpPage > maxFollowUpPages) {
        console.warn(`Warning: Reached maximum page limit (${maxFollowUpPages}) for follow-up feedbacks. Some data may not have been synced.`);
      }

      // Calculate totals
      totalSynced = buttonTotalSynced + textTotalSynced + followUpTotalSynced;
      totalSaved = buttonTotalSaved + textTotalSaved + followUpTotalSaved;
      totalUpdated = textTotalUpdated + followUpTotalUpdated;

      // Log information about duplicate detection
      console.log(`Sync completed with duplicate detection:`);
      console.log(`  Button feedbacks: ${seenFeedbackIds.size} unique items processed`);
      console.log(`  Text feedbacks: ${seenTextFeedbackIds.size} unique items processed`);
      console.log(`  Follow-up feedbacks: ${seenFollowUpFeedbackIds.size} unique items processed`);
      console.log(`  Total unique items: ${seenFeedbackIds.size + seenTextFeedbackIds.size + seenFollowUpFeedbackIds.size}`);

      // Update last synced timestamp
      settings.lastSynced = new Date();
      await settings.save();

      return {
        success: true,
        message: 'Historical data sync completed successfully',
        stats: {
          totalSynced,
          totalSaved,
          totalUpdated,
          uniqueItemsProcessed: {
            button: seenFeedbackIds.size,
            text: seenTextFeedbackIds.size,
            followUp: seenFollowUpFeedbackIds.size,
            total: seenFeedbackIds.size + seenTextFeedbackIds.size + seenFollowUpFeedbackIds.size
          },
          button: {
            synced: buttonTotalSynced,
            saved: buttonTotalSaved
          },
          text: {
            synced: textTotalSynced,
            saved: textTotalSaved,
            updated: textTotalUpdated
          },
          followUp: {
            synced: followUpTotalSynced,
            saved: followUpTotalSaved,
            updated: followUpTotalUpdated
          }
        }
      };
    } catch (error) {
      console.error('Error in syncHistoricalDataWithDateRange:', error);

      // Extract validation errors if available
      let errorMessage = error.message;
      if (error.response?.data?.validationErrors) {
        const validationErrors = error.response.data.validationErrors;
        errorMessage = 'Validation errors: ' + validationErrors.map(err =>
          `${err.field || ''} ${err.message || ''}`
        ).join(', ');
        console.error('API validation errors:', validationErrors);
      }

      return {
        success: false,
        error: errorMessage
      };
    }
  }
  /**
   * Sync all historical data from the Happy or Not API
   * This method will fetch all available data from the beginning of time
   * @returns {Promise<Object>} The sync result
   */
  async syncAllHistoricalData() {
    try {
      console.log('Starting historical data sync...');

      // Get the API key
      const apiKey = await this.getApiKey();
      const settings = await HappyOrNotSettings.getSettings();

      // The API has a limitation of 366 days maximum between start and end dates
      // We need to break the sync into smaller chunks

      // Set a reasonable start date for historical data
      const startYear = 2018; // Start from 2018 to get relevant historical data

      // Determine the end date (today's date including current time)
      const endDate = new Date(); // Current date and time

      console.log(`Syncing data in chunks from ${startYear} to ${endDate.getFullYear()}`);

      // Track overall stats
      let totalStats = {
        totalSynced: 0,
        totalSaved: 0,
        totalUpdated: 0,
        uniqueItemsProcessed: {
          button: 0,
          text: 0,
          followUp: 0,
          total: 0
        },
        button: { synced: 0, saved: 0 },
        text: { synced: 0, saved: 0, updated: 0 },
        followUp: { synced: 0, saved: 0, updated: 0 }
      };

      // Sync data year by year
      for (let year = startYear; year <= endDate.getFullYear(); year++) {
        // Set chunk start date to January 1st of the year
        const chunkStartDate = `${year}-01-01`;

        // Set chunk end date
        let chunkEndDate;
        if (year === endDate.getFullYear()) {
          // If this is the current year, use tomorrow's date to ensure we get all of today's data
          const tomorrowDate = new Date(endDate);
          tomorrowDate.setDate(tomorrowDate.getDate() + 1);
          chunkEndDate = tomorrowDate.toISOString().split('T')[0];
        } else {
          // Otherwise use December 31st of the year
          chunkEndDate = `${year}-12-31`;
        }

        console.log(`Syncing chunk: ${chunkStartDate} to ${chunkEndDate}`);

        // Sync this chunk
        const chunkResult = await this.syncHistoricalDataWithDateRange(
          chunkStartDate,
          chunkEndDate,
          apiKey,
          settings
        );

        if (chunkResult.success) {
          // Add chunk stats to total stats
          totalStats.totalSynced += chunkResult.stats.totalSynced;
          totalStats.totalSaved += chunkResult.stats.totalSaved;
          totalStats.totalUpdated += chunkResult.stats.totalUpdated;

          // Add unique items processed stats if available
          if (chunkResult.stats.uniqueItemsProcessed) {
            totalStats.uniqueItemsProcessed.button += chunkResult.stats.uniqueItemsProcessed.button || 0;
            totalStats.uniqueItemsProcessed.text += chunkResult.stats.uniqueItemsProcessed.text || 0;
            totalStats.uniqueItemsProcessed.followUp += chunkResult.stats.uniqueItemsProcessed.followUp || 0;
            totalStats.uniqueItemsProcessed.total += chunkResult.stats.uniqueItemsProcessed.total || 0;
          }

          totalStats.button.synced += chunkResult.stats.button.synced;
          totalStats.button.saved += chunkResult.stats.button.saved;

          totalStats.text.synced += chunkResult.stats.text.synced;
          totalStats.text.saved += chunkResult.stats.text.saved;
          totalStats.text.updated += chunkResult.stats.text.updated;

          totalStats.followUp.synced += chunkResult.stats.followUp.synced;
          totalStats.followUp.saved += chunkResult.stats.followUp.saved;
          totalStats.followUp.updated += chunkResult.stats.followUp.updated;

          console.log(`Chunk sync completed successfully: ${chunkStartDate} to ${chunkEndDate}`);
        } else {
          console.error(`Error syncing chunk ${chunkStartDate} to ${chunkEndDate}: ${chunkResult.error}`);
        }

        // Add a delay between chunks to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      // Update last synced timestamp
      settings.lastSynced = new Date();
      await settings.save();

      return {
        success: true,
        message: 'Historical data sync completed successfully',
        stats: totalStats
      };
    } catch (error) {
      console.error('Error syncing historical data:', error);

      // Extract validation errors if available
      let errorMessage = error.message;
      if (error.response?.data?.validationErrors) {
        const validationErrors = error.response.data.validationErrors;
        errorMessage = 'Validation errors: ' + validationErrors.map(err =>
          `${err.field || ''} ${err.message || ''}`
        ).join(', ');
        console.error('API validation errors:', validationErrors);
      }

      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * Get paginated feedback with search and filtering
   * @param {Object} options - Query options
   * @param {string} options.startDate - Start date (YYYY-MM-DD)
   * @param {string} options.endDate - End date (YYYY-MM-DD)
   * @param {string} options.experiencePointId - Experience point ID
   * @param {number} options.buttonIndex - Filter by button index (0-3 or 0-10)
   * @param {boolean} options.hasText - Filter for feedback with text comments
   * @param {boolean} options.hasFollowUp - Filter for feedback with follow-up responses
   * @param {string} options.searchText - Search text in comments
   * @param {number} options.page - Page number (1-based)
   * @param {number} options.limit - Items per page
   * @param {string} options.sortField - Field to sort by
   * @param {string} options.sortOrder - Sort order (asc or desc)
   * @returns {Promise<Object>} The paginated feedback data
   */
  async getPaginatedFeedback(options = {}) {
    try {
      // Default to last 30 days if no dates provided
      const endDate = options.endDate ? new Date(options.endDate) : new Date();
      endDate.setHours(23, 59, 59, 999); // End of day

      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      thirtyDaysAgo.setHours(0, 0, 0, 0); // Start of day

      const startDate = options.startDate ? new Date(options.startDate) : thirtyDaysAgo;

      // Build query
      const query = {
        localTime: {
          $gte: startDate,
          $lte: endDate
        }
      };

      // Add experience point ID if provided
      if (options.experiencePointId) {
        query.experiencePointId = options.experiencePointId;
      }

      // Add button index filter if provided
      if (options.buttonIndex !== undefined && options.buttonIndex !== null) {
        query.buttonIndex = parseInt(options.buttonIndex);
      }

      // Add text comment filter if provided
      if (options.hasText === 'true') {
        query.text = { $exists: true, $ne: null, $ne: '' };
      }

      // Add follow-up filter if provided
      if (options.hasFollowUp === 'true') {
        query.followupOptionText = { $exists: true, $ne: null, $ne: '' };
      }

      // Add search text filter if provided
      if (options.searchText) {
        const searchRegex = new RegExp(options.searchText, 'i');
        query.$or = [
          { text: searchRegex },
          { textInEnglish: searchRegex },
          { followupOptionText: searchRegex }
        ];
      }

      // Set up pagination
      const page = parseInt(options.page) || 1;
      const limit = parseInt(options.limit) || 20;
      const skip = (page - 1) * limit;

      // Set up sorting
      const sortField = options.sortField || 'localTime';
      const sortOrder = options.sortOrder === 'asc' ? 1 : -1;
      const sort = { [sortField]: sortOrder };

      // Get total count for pagination
      const totalCount = await HappyOrNotFeedback.countDocuments(query);

      // Get paginated feedback
      const feedbacks = await HappyOrNotFeedback.find(query)
        .sort(sort)
        .skip(skip)
        .limit(limit);

      return {
        success: true,
        data: {
          feedbacks,
          pagination: {
            total: totalCount,
            page,
            limit,
            pages: Math.ceil(totalCount / limit)
          }
        }
      };
    } catch (error) {
      console.error('Error getting paginated feedback:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get follow-up response data aggregated by response type
   * @param {Object} options - Query options
   * @param {string} options.startDate - Start date (YYYY-MM-DD)
   * @param {string} options.endDate - End date (YYYY-MM-DD)
   * @param {string} options.experiencePointId - Experience point ID
   * @param {string} options.feedbackType - Type of feedback (positive, negative, all)
   * @returns {Promise<Object>} The aggregated follow-up response data
   */
  async getFollowUpResponseData(options = {}) {
    try {
      // Default to last 30 days if no dates provided
      const endDate = options.endDate ? new Date(options.endDate) : new Date();
      // Make sure we include the full day by setting time to end of day
      endDate.setHours(23, 59, 59, 999);

      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      thirtyDaysAgo.setHours(0, 0, 0, 0); // Start of day

      const startDate = options.startDate ? new Date(options.startDate) : thirtyDaysAgo;
      // Make sure we start at the beginning of the day
      startDate.setHours(0, 0, 0, 0);

      // Build base query
      const query = {
        localTime: {
          $gte: startDate,
          $lte: endDate
        },
        followupOptionText: { $exists: true, $ne: null, $ne: '' }
      };

      // Add experience point ID if provided
      if (options.experiencePointId) {
        query.experiencePointId = options.experiencePointId;
      }

      // Add feedback type filter if provided
      if (options.feedbackType === 'positive') {
        // Consider buttons 2 and 3 as positive (Happy and Very Happy)
        query.buttonIndex = { $in: [2, 3] };
      } else if (options.feedbackType === 'negative') {
        // Consider buttons 0 and 1 as negative (Very Unhappy and Unhappy)
        query.buttonIndex = { $in: [0, 1] };
      }

      // Aggregate follow-up responses
      const followUpResponses = await HappyOrNotFeedback.aggregate([
        { $match: query },
        {
          $group: {
            _id: '$followupOptionText',
            count: { $sum: 1 }
          }
        },
        { $sort: { count: -1 } }
      ]);

      // Calculate total responses for percentage calculation
      const totalResponses = followUpResponses.reduce((sum, item) => sum + item.count, 0);

      // Format the response data with percentages
      const formattedResponses = followUpResponses.map(item => ({
        option: item._id,
        count: item.count,
        percentage: totalResponses > 0 ? (item.count / totalResponses * 100).toFixed(1) : 0
      }));

      return {
        success: true,
        data: {
          responses: formattedResponses,
          totalResponses,
          feedbackType: options.feedbackType || 'all',
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString()
        }
      };
    } catch (error) {
      console.error('Error getting follow-up response data:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get available years with feedback data
   * @returns {Promise<Object>} The available years
   */
  async getAvailableYears() {
    try {
      // Aggregate to get distinct years from the localTime field
      const years = await HappyOrNotFeedback.aggregate([
        {
          $group: {
            _id: { $year: '$localTime' }
          }
        },
        {
          $sort: { _id: 1 }
        }
      ]);

      // Extract years from the aggregation result
      const availableYears = years.map(year => year._id);

      return {
        success: true,
        years: availableYears
      };
    } catch (error) {
      console.error('Error getting available years:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

module.exports = new HappyOrNotService();





