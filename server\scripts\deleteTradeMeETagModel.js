/**
 * <PERSON><PERSON><PERSON> to delete the TradeMeETag model file
 * Run this after migrating all ETags to the TradeMeCategory collection
 */

const fs = require('fs');
const path = require('path');

const filePath = path.join(__dirname, '..', 'models', 'TradeMeETag.js');

try {
  // Check if the file exists
  if (fs.existsSync(filePath)) {
    // Delete the file
    fs.unlinkSync(filePath);
    console.log('TradeMeETag.js file deleted successfully');
  } else {
    console.log('TradeMeETag.js file does not exist');
  }
} catch (error) {
  console.error('Error deleting TradeMeETag.js file:', error);
}
