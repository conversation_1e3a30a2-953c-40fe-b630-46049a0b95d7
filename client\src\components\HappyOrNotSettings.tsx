import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Loader2, Save, CheckCircle, Download, AlertCircle } from 'lucide-react';
import { useToast } from '@/hooks/useToast';
import {
  getHappyOrNotSettings,
  updateHappyOrNotSettings,
  syncAllHistoricalHappyOrNotFeedback,
  HappyOrNotSettings as HappyOrNotSettingsType
} from '@/api/happyOrNot';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

export function HappyOrNotSettings() {
  const [settings, setSettings] = useState<HappyOrNotSettingsType | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [syncingHistorical, setSyncingHistorical] = useState(false);

  const { toast } = useToast();

  // Load settings on initial render
  useEffect(() => {
    fetchSettings();
  }, []);

  // Fetch Happy or Not settings
  const fetchSettings = async () => {
    setLoading(true);
    try {
      const response = await getHappyOrNotSettings();

      if (response.success) {
        setSettings(response.data);
      } else {
        toast({
          title: 'Error',
          description: 'Failed to load Happy or Not settings',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error fetching Happy or Not settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to load Happy or Not settings',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Save settings
  const saveSettings = async () => {
    if (!settings) return;

    setSaving(true);
    try {
      const response = await updateHappyOrNotSettings({
        apiKey: settings.apiKey,
        experiencePointName: settings.experiencePointName,
        weeklyFeedbackGoal: settings.weeklyFeedbackGoal,
        happinessScoreGoal: settings.happinessScoreGoal
      });

      if (response.success) {
        setSettings(response.data);
        setShowSuccess(true);
        setTimeout(() => setShowSuccess(false), 3000);

        toast({
          title: 'Success',
          description: 'Happy or Not settings updated successfully',
        });
      } else {
        toast({
          title: 'Error',
          description: 'Failed to update Happy or Not settings',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error updating Happy or Not settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to update Happy or Not settings',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  // Handle input change
  const handleChange = (field: keyof HappyOrNotSettingsType, value: string | number) => {
    if (settings) {
      setSettings({
        ...settings,
        [field]: value
      });
    }
  };

  // Sync all historical data
  const syncAllHistoricalData = async () => {
    setSyncingHistorical(true);
    try {
      const response = await syncAllHistoricalHappyOrNotFeedback();

      if (response.success) {
        toast({
          title: 'Success',
          description: response.message,
        });
      } else {
        toast({
          title: 'Error',
          description: 'Failed to start historical data sync',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error syncing historical data:', error);
      toast({
        title: 'Error',
        description: 'Failed to start historical data sync',
        variant: 'destructive',
      });
    } finally {
      setSyncingHistorical(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Happy or Not Settings</CardTitle>
        <CardDescription>Configure your Happy or Not API integration</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {loading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        ) : (
          <>
            <div className="space-y-4">
              {/* API Key */}
              <div className="space-y-2">
                <Label htmlFor="apiKey">API Key</Label>
                <Input
                  id="apiKey"
                  type="password"
                  value={settings?.apiKey || ''}
                  onChange={(e) => handleChange('apiKey', e.target.value)}
                  placeholder="Enter your Happy or Not API key"
                  className={!settings?.apiKey ? "border-red-500 focus-visible:ring-red-500" : ""}
                />
                <p className="text-xs text-muted-foreground">
                  API key for api.happy-or-not.com - keep this secure
                </p>
                {!settings?.apiKey && (
                  <div className="mt-2 p-2 bg-red-500/10 border border-red-500/30 rounded-md">
                    <p className="text-xs text-red-500">
                      An API key is required for Happy or Not functionality.
                      Please enter your API key from Happy or Not to enable feedback tracking.
                    </p>
                  </div>
                )}
              </div>

              <Separator />

              {/* Experience Point Name */}
              <div className="space-y-2">
                <Label htmlFor="experiencePointName">Kiosk Experience Point Name</Label>
                <Input
                  id="experiencePointName"
                  type="text"
                  value={settings?.experiencePointName || ''}
                  onChange={(e) => handleChange('experiencePointName', e.target.value)}
                  placeholder="Enter your kiosk experience point name"
                  className={!settings?.experiencePointName ? "border-red-500 focus-visible:ring-red-500" : ""}
                />
                <p className="text-xs text-muted-foreground">
                  The name of your kiosk experience point in Happy or Not
                </p>
                {!settings?.experiencePointName && (
                  <div className="mt-2 p-2 bg-red-500/10 border border-red-500/30 rounded-md">
                    <p className="text-xs text-red-500">
                      An experience point name is required to identify your kiosk in the Happy or Not system.
                    </p>
                  </div>
                )}
              </div>

              <Separator />

              {/* Goals Section */}
              <div className="space-y-4">
                <h3 className="text-sm font-medium">Performance Goals</h3>
                <p className="text-xs text-muted-foreground">
                  Set goals for feedback volume and happiness score to track performance
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Weekly Feedback Goal */}
                  <div className="space-y-2">
                    <Label htmlFor="weeklyFeedbackGoal">Weekly Feedback Goal</Label>
                    <Input
                      id="weeklyFeedbackGoal"
                      type="number"
                      min="0"
                      value={settings?.weeklyFeedbackGoal || 100}
                      onChange={(e) => handleChange('weeklyFeedbackGoal', parseInt(e.target.value))}
                      placeholder="100"
                    />
                    <p className="text-xs text-muted-foreground">
                      Target number of feedback responses per week
                    </p>
                  </div>

                  {/* Happiness Score Goal */}
                  <div className="space-y-2">
                    <Label htmlFor="happinessScoreGoal">Happiness Score Goal (%)</Label>
                    <Input
                      id="happinessScoreGoal"
                      type="number"
                      min="0"
                      max="100"
                      value={settings?.happinessScoreGoal || 80}
                      onChange={(e) => handleChange('happinessScoreGoal', parseInt(e.target.value))}
                      placeholder="80"
                    />
                    <p className="text-xs text-muted-foreground">
                      Target happiness score percentage (0-100)
                    </p>
                  </div>
                </div>
              </div>

              {/* Last Synced */}
              {settings?.lastSynced && (
                <div className="pt-2">
                  <p className="text-sm text-muted-foreground">
                    Last synced: {new Date(settings.lastSynced).toLocaleString()}
                  </p>
                </div>
              )}
            </div>

            <div className="flex justify-end">
              <Button
                onClick={saveSettings}
                disabled={saving || !settings?.apiKey || !settings?.experiencePointName}
                className="w-full sm:w-auto"
              >
                {saving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : showSuccess ? (
                  <>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Saved!
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Settings
                  </>
                )}
              </Button>
            </div>
          </>
        )}
      </CardContent>

      {settings?.apiKey && settings?.experiencePointName && (
        <CardFooter className="flex flex-col space-y-4">
          <Separator className="my-2" />

          <div className="w-full">
            <h3 className="text-sm font-medium mb-2">Data Management</h3>
            <p className="text-xs text-muted-foreground mb-4">
              Sync all historical data from Happy or Not API. This will fetch all available data from 2018 to the present.
              Data will be synced year by year to comply with API limitations.
              This operation may take several minutes to complete.
            </p>

            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full"
                  disabled={syncingHistorical}
                >
                  {syncingHistorical ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Syncing Historical Data...
                    </>
                  ) : (
                    <>
                      <Download className="mr-2 h-4 w-4" />
                      Sync All Historical Data
                    </>
                  )}
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Sync All Historical Data</AlertDialogTitle>
                  <AlertDialogDescription>
                    This will fetch all available data from the Happy or Not API from 2018 to the present.
                    The data will be synced year by year to comply with the API's date range limitations.
                    <br /><br />
                    This operation may take several minutes to complete depending on the amount of data.
                    Progress will be logged in the server console.
                    <br /><br />
                    <span className="font-medium text-amber-500 flex items-center">
                      <AlertCircle className="mr-2 h-4 w-4" />
                      This is a long-running operation and cannot be canceled once started.
                    </span>
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction onClick={syncAllHistoricalData}>
                    Start Sync
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>

            {settings?.lastSynced && (
              <p className="text-xs text-muted-foreground mt-2">
                Last synced: {new Date(settings.lastSynced).toLocaleString()}
              </p>
            )}
          </div>
        </CardFooter>
      )}
    </Card>
  );
}
