import api from './api';

export interface DashboardSettings {
  pbiLinks: Array<{
    name: string;
    url: string;
    enabled: boolean;
    screenshot?: string;
  }>;
  showPowerBIImages: boolean;
  screenshotInterval: number;
  screenshotSchedule: {
    enabled: boolean;
    startTime: string;
    endTime: string;
  };
  updatedAt?: string;
}

// Description: Get dashboard settings
// Endpoint: GET /dashboard-settings
// Request: {}
// Response: DashboardSettings
export const getDashboardSettings = async (): Promise<DashboardSettings> => {
  try {
    const response = await api.get('/dashboard-settings');
    return response.data;
  } catch (error: any) {
    console.error('Error fetching dashboard settings:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Description: Update dashboard settings
// Endpoint: PUT /dashboard-settings
// Request: DashboardSettings
// Response: DashboardSettings
export const updateDashboardSettings = async (settings: DashboardSettings): Promise<DashboardSettings> => {
  try {
    const response = await api.put('/dashboard-settings', settings);
    return response.data;
  } catch (error: any) {
    console.error('Error updating dashboard settings:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Description: Force dashboard update (take screenshots)
// Endpoint: POST /dashboard-settings/force-update
// Request: {}
// Response: { success: boolean, message: string, totalLinks?: number, dashboardNames?: string[] }
export const forceDashboardUpdate = async (): Promise<{
  success: boolean;
  message: string;
  totalLinks?: number;
  dashboardNames?: string[];
}> => {
  try {
    const response = await api.post('/dashboard-settings/force-update');
    return response.data;
  } catch (error: any) {
    console.error('Error forcing dashboard update:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Screenshot status interface
export interface ScreenshotStatus {
  inProgress: boolean;
  totalLinks: number;
  processedLinks: number;
  currentLink: string | null;
  currentStep: string | null;
  error: string | null;
  startTime: string | null;
  endTime: string | null;
  logs: Array<{
    time: string;
    message: string;
  }>;
}

// Description: Get the current status of the screenshot process
// Endpoint: GET /dashboard-settings/screenshot-status
// Request: {}
// Response: ScreenshotStatus
export const getScreenshotStatus = async (): Promise<ScreenshotStatus> => {
  try {
    const response = await api.get('/dashboard-settings/screenshot-status');
    return response.data;
  } catch (error: any) {
    console.error('Error getting screenshot status:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};
