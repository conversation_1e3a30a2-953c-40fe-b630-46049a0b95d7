import api from './api';

export interface InventoryItem {
  _id: string;
  name: string;
  category: string;
  brand: string;
  model: string;
  modelNumber: string;
  releaseYear: number | null;
  lastRRP: number;
  currentPrice: number;
  lastUpdated: string;
  location: string;
  status: 'in_stock' | 'listed' | 'sold';
}

export interface CreateInventoryItemData {
  name: string;
  category: string;
  brand?: string;
  model?: string;
  modelNumber?: string;
  releaseYear?: number | null;
  lastRRP?: number;
  currentPrice: number;
  location: string;
  status?: 'in_stock' | 'listed' | 'sold';
}

// Description: Get inventory items
// Endpoint: GET /api/inventory
// Request: {}
// Response: { data: InventoryItem[] }
export const getInventory = async () => {
  try {
    const response = await api.get('/inventory');
    return { items: response.data.data };
  } catch (error) {
    console.error(error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Description: Create a new inventory item
// Endpoint: POST /api/inventory
// Request: CreateInventoryItemData
// Response: { success: boolean, data: InventoryItem }
export const createInventoryItem = async (itemData: CreateInventoryItemData) => {
  try {
    const response = await api.post('/inventory', itemData);
    return response.data;
  } catch (error) {
    console.error(error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Description: Get a single inventory item
// Endpoint: GET /api/inventory/:id
// Request: {}
// Response: { success: boolean, data: InventoryItem }
export const getInventoryItem = async (id: string) => {
  try {
    const response = await api.get(`/inventory/${id}`);
    return response.data;
  } catch (error) {
    console.error(error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Description: Update an inventory item
// Endpoint: PUT /api/inventory/:id
// Request: InventoryItemFormValues
// Response: { success: boolean, data: InventoryItem }
export const updateInventoryItem = async (id: string, itemData: any) => {
  try {
    const response = await api.put(`/inventory/${id}`, itemData);
    return response.data;
  } catch (error) {
    console.error(error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Description: Delete an inventory item
// Endpoint: DELETE /api/inventory/:id
// Request: {}
// Response: { success: boolean, message: string }
export const deleteInventoryItem = async (id: string) => {
  try {
    const response = await api.delete(`/inventory/${id}`);
    return response.data;
  } catch (error) {
    console.error(error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};