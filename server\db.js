/**
 * MongoDB connection utility
 * Provides direct access to MongoDB collections
 */

const mongoose = require('mongoose');

/**
 * Get the MongoDB database instance
 * @returns {Object} MongoDB database instance
 */
const getDb = () => {
  if (!mongoose.connection || !mongoose.connection.db) {
    throw new Error('Database not connected. Call connectDB() first.');
  }
  return mongoose.connection.db;
};

module.exports = {
  getDb
};
