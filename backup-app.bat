@echo off
setlocal enabledelayedexpansion

echo GH0ST Application Backup Tool
echo ===========================
echo.

REM Set variables
set BACKUP_ROOT=X:\Web Design\Ghost\v2_backups
set APP_DIR=%~dp0
set TIMESTAMP=%date:~-4,4%%date:~-7,2%%date:~-10,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set TIMESTAMP=!TIMESTAMP: =0!
set SEVENZIP="C:\Program Files\7-Zip\7z.exe"
set EXCLUSION_FILE="%TEMP%\gh0st_exclusions.txt"

REM Check if 7-Zip exists
if not exist %SEVENZIP% (
    echo Error: 7-Zip not found at %SEVENZIP%
    echo Please install 7-Zip or update the path in the script.
    pause
    exit /b 1
)

REM Create backup directory if it doesn't exist
if not exist "%BACKUP_ROOT%" mkdir "%BACKUP_ROOT%"

echo Creating backup of GH0ST application...
echo Source: %APP_DIR%
echo Destination: %BACKUP_ROOT%\GH0ST_backup_%TIMESTAMP%.7z
echo.
echo This backup excludes:
echo - All node_modules folders
echo - Server uploads folder
echo.

REM Create exclusion list file
echo node_modules > %EXCLUSION_FILE%
echo server\uploads >> %EXCLUSION_FILE%

REM Create 7z archive with exclusions
%SEVENZIP% a -t7z -mx=9 "%BACKUP_ROOT%\GH0ST_backup_%TIMESTAMP%.7z" "%APP_DIR%*" -xr@%EXCLUSION_FILE%

REM Delete the temporary exclusion file
del %EXCLUSION_FILE%

echo.
echo Backup complete!
echo Backup saved to: %BACKUP_ROOT%\GH0ST_backup_%TIMESTAMP%.7z
echo.

REM Ask if user wants to open the backup folder
set /p OPEN_FOLDER=Would you like to open the backup folder? (Y/N):

if /i "%OPEN_FOLDER%"=="Y" (
    start explorer "%BACKUP_ROOT%"
)

echo Thanks! Future you may thank you!
pause