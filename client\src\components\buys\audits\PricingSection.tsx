import { useState, useEffect } from 'react';
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';

interface PricingSectionProps {
  control: any;
  disabled?: boolean;
  auditType: 'buy' | 'pawn' | 'price';
}

/**
 * Component for the Pricing section of the audit form
 * This section is shown for all audit types
 */
export function PricingSection({ control, disabled = false, auditType }: PricingSectionProps) {
  // State to track the status of each assessment
  const [pricingStatus, setPricingStatus] = useState('not_assessed');
  const [limitStatus, setLimitStatus] = useState('not_assessed');

  // Watch for changes in the form values
  useEffect(() => {
    const subscription = control._formState.submitCount;
    const pricingValue = control._getWatch('pricing.status');
    const limitValue = control._getWatch('authorizedLimitCheck.status');

    if (pricingValue) {
      setPricingStatus(pricingValue);
    }

    if (limitValue) {
      setLimitStatus(limitValue);
    }

    return () => {};
  }, [control]);

  // Common fail reasons for pricing
  const pricingFailReasons = [
    { id: 'overpriced', label: 'Item overpriced' },
    { id: 'underpriced', label: 'Item underpriced' },
    { id: 'incorrect_market_value', label: 'Incorrect market value assessment' },
    { id: 'missing_price_research', label: 'Missing price research' },
    { id: 'condition_not_factored', label: 'Condition not factored into price' },
  ];

  // Common fail reasons for authorized limit check
  const limitFailReasons = [
    { id: 'exceeded_limit', label: 'Exceeded authorized limit' },
    { id: 'no_approval', label: 'No approval for exceeding limit' },
    { id: 'incorrect_limit_applied', label: 'Incorrect limit applied' },
    { id: 'missing_documentation', label: 'Missing documentation for approval' },
  ];

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle>Pricing Assessment</CardTitle>
        <CardDescription>
          Assess the pricing and authorized limit compliance
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Pricing */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Pricing</h3>

          <FormField
            control={control}
            name="pricing.status"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>Assessment</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={(value) => {
                      field.onChange(value);
                      setPricingStatus(value);
                    }}
                    value={field.value}
                    className="flex flex-col space-y-1"
                    disabled={disabled}
                  >
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="pass" id="pricingPass" />
                      </FormControl>
                      <FormLabel className="font-normal" htmlFor="pricingPass">
                        Pass
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="fail" id="pricingFail" />
                      </FormControl>
                      <FormLabel className="font-normal" htmlFor="pricingFail">
                        Fail
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="not_assessed" id="pricingNotAssessed" />
                      </FormControl>
                      <FormLabel className="font-normal" htmlFor="pricingNotAssessed">
                        Not Assessed
                      </FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Only show fail reasons if status is "fail" */}
          {pricingStatus === 'fail' && (
            <FormField
              control={control}
              name="pricing.failReasons"
              render={({ field }) => (
                <FormItem>
                  <div className="mb-4">
                    <FormLabel>Fail Reasons</FormLabel>
                    <FormDescription>
                      Select all that apply
                    </FormDescription>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {pricingFailReasons.map((reason) => (
                      <FormItem
                        key={reason.id}
                        className="flex flex-row items-start space-x-3 space-y-0"
                      >
                        <FormControl>
                          <Checkbox
                            checked={field.value?.includes(reason.id)}
                            onCheckedChange={(checked) => {
                              const currentValue = field.value || [];
                              if (checked) {
                                field.onChange([...currentValue, reason.id]);
                              } else {
                                field.onChange(
                                  currentValue.filter((value: string) => value !== reason.id)
                                );
                              }
                            }}
                            disabled={disabled}
                          />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {reason.label}
                        </FormLabel>
                      </FormItem>
                    ))}
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}

          <FormField
            control={control}
            name="pricing.suggestedPrice"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Suggested Price</FormLabel>
                <FormControl>
                  <Input
                    type="text"
                    placeholder="Enter suggested price"
                    {...field}
                    disabled={disabled}
                  />
                </FormControl>
                <FormDescription>
                  If the current price is incorrect, enter your suggested price
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {auditType === 'price' && (
            <>
              <FormField
                control={control}
                name="pricing.costPrice"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Cost Price</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="Enter cost price"
                        {...field}
                        disabled={disabled}
                      />
                    </FormControl>
                    <FormDescription>
                      Enter the cost price of the item
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={control}
                name="pricing.ticketPrice"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Ticket Price</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="Enter ticket price"
                        {...field}
                        disabled={disabled}
                      />
                    </FormControl>
                    <FormDescription>
                      Enter the price the item was actually priced for
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </>
          )}

          <FormField
            control={control}
            name="pricing.notes"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Notes</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Enter any additional notes about pricing"
                    className="min-h-[100px]"
                    {...field}
                    disabled={disabled}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Authorized Limit Check - Only for Buy and Pawn audits */}
        {auditType !== 'price' && (
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Authorized Limit Check</h3>

            <FormField
              control={control}
              name="authorizedLimitCheck.status"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>Assessment</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={(value) => {
                        field.onChange(value);
                        setLimitStatus(value);
                      }}
                      value={field.value}
                      className="flex flex-col space-y-1"
                      disabled={disabled}
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="pass" id="limitPass" />
                        </FormControl>
                        <FormLabel className="font-normal" htmlFor="limitPass">
                          Pass
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="fail" id="limitFail" />
                        </FormControl>
                        <FormLabel className="font-normal" htmlFor="limitFail">
                          Fail
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="not_assessed" id="limitNotAssessed" />
                        </FormControl>
                        <FormLabel className="font-normal" htmlFor="limitNotAssessed">
                          Not Assessed
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Only show fail reasons if status is "fail" */}
            {limitStatus === 'fail' && (
              <FormField
                control={control}
                name="authorizedLimitCheck.failReasons"
                render={({ field }) => (
                  <FormItem>
                    <div className="mb-4">
                      <FormLabel>Fail Reasons</FormLabel>
                      <FormDescription>
                        Select all that apply
                      </FormDescription>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {limitFailReasons.map((reason) => (
                        <FormItem
                          key={reason.id}
                          className="flex flex-row items-start space-x-3 space-y-0"
                        >
                          <FormControl>
                            <Checkbox
                              checked={field.value?.includes(reason.id)}
                              onCheckedChange={(checked) => {
                                const currentValue = field.value || [];
                                if (checked) {
                                  field.onChange([...currentValue, reason.id]);
                                } else {
                                  field.onChange(
                                    currentValue.filter((value: string) => value !== reason.id)
                                  );
                                }
                              }}
                              disabled={disabled}
                            />
                          </FormControl>
                          <FormLabel className="font-normal">
                            {reason.label}
                          </FormLabel>
                        </FormItem>
                      ))}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <FormField
              control={control}
              name="authorizedLimitCheck.notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter any additional notes about authorized limit check"
                      className="min-h-[100px]"
                      {...field}
                      disabled={disabled}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        )}
      </CardContent>
    </Card>
  );
}
