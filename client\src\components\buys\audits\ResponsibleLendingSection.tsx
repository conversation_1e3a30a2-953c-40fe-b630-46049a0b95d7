import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface ResponsibleLendingSectionProps {
  control: any;
  disabled?: boolean;
}

/**
 * Component for the Responsible Lending section of the audit form
 * This section is shown only for Pawn audits
 */
export function ResponsibleLendingSection({ control, disabled = false }: ResponsibleLendingSectionProps) {
  // Common fail reasons for POL suitability
  const polFailReasons = [
    { id: 'missing_pol', label: 'Missing POL text' },
    { id: 'incomplete_pol', label: 'Incomplete POL text' },
    { id: 'incorrect_pol', label: 'Incorrect POL text' },
    { id: 'not_suitable', label: 'POL not suitable for customer' },
    { id: 'not_documented', label: 'POL not documented' },
  ];

  // Essential item categories
  const essentialItemCategories = [
    { value: 'mobility', label: 'Mobility Items' },
    { value: 'food', label: 'Food Storage/Preparation' },
    { value: 'income', label: 'Income-Sustaining Items' },
    { value: 'dependent', label: 'Dependent Care Items' },
  ];

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle>Responsible Lending Assessment</CardTitle>
        <CardDescription>
          Assess compliance with responsible lending requirements
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* POL Suitability */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">POL Suitability</h3>

          <FormField
            control={control}
            name="polSuitability.status"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>Assessment</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    value={field.value}
                    className="flex flex-col space-y-1"
                    disabled={disabled}
                  >
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="pass" id="polPass" />
                      </FormControl>
                      <FormLabel className="font-normal" htmlFor="polPass">
                        Pass
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="fail" id="polFail" />
                      </FormControl>
                      <FormLabel className="font-normal" htmlFor="polFail">
                        Fail
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="not_assessed" id="polNotAssessed" />
                      </FormControl>
                      <FormLabel className="font-normal" htmlFor="polNotAssessed">
                        Not Assessed
                      </FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="polSuitability.polText"
            render={({ field }) => (
              <FormItem>
                <FormLabel>POL Text</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Enter the POL text used for this transaction"
                    className="min-h-[100px]"
                    {...field}
                    disabled={disabled}
                  />
                </FormControl>
                <FormDescription>
                  Copy the POL text that was used for this transaction
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="polSuitability.failReasons"
            render={({ field }) => (
              <FormItem>
                <div className="mb-4">
                  <FormLabel>Fail Reasons (if applicable)</FormLabel>
                  <FormDescription>
                    Select all that apply
                  </FormDescription>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {polFailReasons.map((reason) => (
                    <FormItem
                      key={reason.id}
                      className="flex flex-row items-start space-x-3 space-y-0"
                    >
                      <FormControl>
                        <Checkbox
                          checked={field.value?.includes(reason.id)}
                          onCheckedChange={(checked) => {
                            const currentValue = field.value || [];
                            if (checked) {
                              field.onChange([...currentValue, reason.id]);
                            } else {
                              field.onChange(
                                currentValue.filter((value: string) => value !== reason.id)
                              );
                            }
                          }}
                          disabled={disabled}
                        />
                      </FormControl>
                      <FormLabel className="font-normal">
                        {reason.label}
                      </FormLabel>
                    </FormItem>
                  ))}
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="polSuitability.notes"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Notes</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Enter any additional notes about POL suitability"
                    className="min-h-[100px]"
                    {...field}
                    disabled={disabled}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Customer Understanding */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Customer Understanding</h3>

          <FormField
            control={control}
            name="customerUnderstanding.status"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>Assessment</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    value={field.value}
                    className="flex flex-col space-y-1"
                    disabled={disabled}
                  >
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="pass" id="customerUnderstandingPass" />
                      </FormControl>
                      <FormLabel className="font-normal" htmlFor="customerUnderstandingPass">
                        Pass
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="fail" id="customerUnderstandingFail" />
                      </FormControl>
                      <FormLabel className="font-normal" htmlFor="customerUnderstandingFail">
                        Fail
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="not_assessed" id="customerUnderstandingNotAssessed" />
                      </FormControl>
                      <FormLabel className="font-normal" htmlFor="customerUnderstandingNotAssessed">
                        Not Assessed
                      </FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="customerUnderstanding.notes"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Notes</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Enter any notes about customer understanding"
                    className="min-h-[100px]"
                    {...field}
                    disabled={disabled}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Vulnerable Customer */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Vulnerable Customer</h3>

          <FormField
            control={control}
            name="vulnerableCustomer.status"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>Assessment</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    value={field.value}
                    className="flex flex-col space-y-1"
                    disabled={disabled}
                  >
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="pass" id="vulnerableCustomerPass" />
                      </FormControl>
                      <FormLabel className="font-normal" htmlFor="vulnerableCustomerPass">
                        Pass
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="fail" id="vulnerableCustomerFail" />
                      </FormControl>
                      <FormLabel className="font-normal" htmlFor="vulnerableCustomerFail">
                        Fail
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="not_assessed" id="vulnerableCustomerNotAssessed" />
                      </FormControl>
                      <FormLabel className="font-normal" htmlFor="vulnerableCustomerNotAssessed">
                        Not Assessed
                      </FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="vulnerableCustomer.notes"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Notes</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Enter any notes about vulnerable customer assessment"
                    className="min-h-[100px]"
                    {...field}
                    disabled={disabled}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Essential Item Check */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Essential Item Check</h3>

          <FormField
            control={control}
            name="essentialItemCheck.status"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>Assessment</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    value={field.value}
                    className="flex flex-col space-y-1"
                    disabled={disabled}
                  >
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="pass" id="essentialItemPass" />
                      </FormControl>
                      <FormLabel className="font-normal" htmlFor="essentialItemPass">
                        Pass
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="fail" id="essentialItemFail" />
                      </FormControl>
                      <FormLabel className="font-normal" htmlFor="essentialItemFail">
                        Fail
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="not_assessed" id="essentialItemNotAssessed" />
                      </FormControl>
                      <FormLabel className="font-normal" htmlFor="essentialItemNotAssessed">
                        Not Assessed
                      </FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="essentialItemCheck.potentiallyEssentialChecked"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                    disabled={disabled}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>
                    Potentially Essential Item Checked
                  </FormLabel>
                  <FormDescription>
                    Check this if the employee checked whether the item was potentially essential
                  </FormDescription>
                </div>
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="essentialItemCheck.category"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Essential Item Category</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  value={field.value}
                  disabled={disabled}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a category" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {essentialItemCategories.map((category) => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormDescription>
                  If the item is potentially essential, select the category
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="essentialItemCheck.potentiallyEssentialText"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Potentially Essential Item Notes</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Enter notes about the potentially essential item check"
                    className="min-h-[100px]"
                    {...field}
                    disabled={disabled}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="essentialItemCheck.compliance"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>Compliance Assessment</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    value={field.value}
                    className="flex flex-col space-y-1"
                    disabled={disabled}
                  >
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="compliant" id="essentialItemCompliant" />
                      </FormControl>
                      <FormLabel className="font-normal" htmlFor="essentialItemCompliant">
                        Compliant
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="non_compliant" id="essentialItemNonCompliant" />
                      </FormControl>
                      <FormLabel className="font-normal" htmlFor="essentialItemNonCompliant">
                        Non-Compliant
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="not_assessed" id="essentialItemComplianceNotAssessed" />
                      </FormControl>
                      <FormLabel className="font-normal" htmlFor="essentialItemComplianceNotAssessed">
                        Not Assessed
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="not_applicable" id="essentialItemNotApplicable" />
                      </FormControl>
                      <FormLabel className="font-normal" htmlFor="essentialItemNotApplicable">
                        Not Applicable
                      </FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="essentialItemCheck.reasonNotEssential"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Reason Not Essential</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="If item appears essential but is not, explain why"
                    className="min-h-[100px]"
                    {...field}
                    disabled={disabled}
                  />
                </FormControl>
                <FormDescription>
                  If the item appears to be essential but has been determined not to be, explain why
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="essentialItemCheck.notes"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Notes</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Enter any additional notes about essential item check"
                    className="min-h-[100px]"
                    {...field}
                    disabled={disabled}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </CardContent>
    </Card>
  );
}
