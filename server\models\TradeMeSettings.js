const mongoose = require('mongoose');

const tradeMeSettingsSchema = new mongoose.Schema({
  environment: {
    type: String,
    enum: ['production', 'sandbox'],
    default: 'sandbox'
  },
  connected: {
    type: Boolean,
    default: false
  },
  username: {
    type: String,
    default: ''
  },
  accessToken: {
    type: String,
    default: ''
  },
  accessSecret: {
    type: String,
    default: ''
  },
  memberId: {
    type: String,
    default: ''
  },
  lastUpdated: {
    type: Date,
    default: null
  },
  lastUpdatedBy: {
    type: mongoose.Schema.Types.Mixed, // Can be ObjectId (for User) or String (for 'Imported')
    ref: 'User',
    default: null
  },
  createdAt: {
    type: Date,
    default: Date.now,
    immutable: true
  }
}, {
  versionKey: false,
  toJSON: { getters: true },
  toObject: { getters: true }
});

const TradeMeSettings = mongoose.model('TradeMeSettings', tradeMeSettingsSchema);

module.exports = TradeMeSettings;