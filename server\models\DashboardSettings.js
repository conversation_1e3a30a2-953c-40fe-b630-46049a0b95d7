const mongoose = require('mongoose');

const dashboardSettingsSchema = new mongoose.Schema({
  pbiLinks: [{
    name: String,
    url: String,
    enabled: { type: Boolean, default: true },
    screenshot: String
  }],
  showPowerBIImages: { type: Boolean, default: true }, // Control visibility of PowerBI images on dashboard
  screenshotInterval: { type: Number, default: 5 }, // minutes
  screenshotSchedule: {
    enabled: { type: Boolean, default: true },
    startTime: { type: String, default: '08:00' },
    endTime: { type: String, default: '17:30' }
  },
  updatedAt: { type: Date, default: Date.now }
});

module.exports = mongoose.model('DashboardSettings', dashboardSettingsSchema);
