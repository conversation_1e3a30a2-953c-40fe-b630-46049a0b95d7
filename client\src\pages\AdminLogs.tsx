import React, { useState, useEffect } from 'react';
import { getLogs, getLogsStats, AccessLog, LogsQueryParams, clearOldLogs, LogsStatistics } from '@/api/logs';
import { useToast } from '@/hooks/useToast';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { LogsPagination } from '@/components/LogsPagination';
import { Loader2, RefreshCw, Search, Trash2, BarChart3, Filter, ChevronDown, ChevronUp } from 'lucide-react';
import { format, startOfDay, endOfDay } from 'date-fns';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { DateRangePicker } from '@/components/DateRangePicker';
import { DateRange } from 'react-day-picker';
import { LogsDashboard } from '@/components/LogsDashboard';

const AdminLogs: React.FC = () => {
  const { toast } = useToast();
  const [logs, setLogs] = useState<AccessLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    pages: 1,
    limit: 50,
  });
  const [filters, setFilters] = useState<LogsQueryParams>({
    page: 1,
    limit: 50,
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [searchField, setSearchField] = useState('path');
  const [clearDays, setClearDays] = useState(30);
  const [clearDialogOpen, setClearDialogOpen] = useState(false);
  const [dateRange, setDateRange] = useState<DateRange | undefined>();
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [minResponseTime, setMinResponseTime] = useState<string>('');
  const [maxResponseTime, setMaxResponseTime] = useState<string>('');
  const [dashboardStats, setDashboardStats] = useState({
    totalRequests: 0,
    uniqueUsers: 0,
    avgResponseTime: 0,
    errorRate: 0,
    requestsByType: [] as {label: string, value: number, color: string}[],
    requestsByStatus: [] as {label: string, value: number, color: string}[],
    requestsOverTime: [] as {label: string, value: number}[],
  });
  const [showDashboard, setShowDashboard] = useState(true);

  // Fetch logs on component mount and when filters change
  useEffect(() => {
    if (showDashboard) {
      fetchLogsAndStats();
    } else {
      fetchLogs();
    }
  }, [filters, showDashboard]);

  // Fetch dashboard stats when dashboard is shown
  useEffect(() => {
    if (showDashboard) {
      fetchDashboardStats();
    }
  }, [showDashboard]);

  // Update date filter when date range changes
  useEffect(() => {
    if (dateRange?.from) {
      const newFilters = { ...filters, page: 1 };
      newFilters.startDate = startOfDay(dateRange.from).toISOString();

      if (dateRange.to) {
        newFilters.endDate = endOfDay(dateRange.to).toISOString();
      } else {
        // If only 'from' is selected, set 'to' to the end of the same day
        newFilters.endDate = endOfDay(dateRange.from).toISOString();
      }

      setFilters(newFilters);
    } else if (filters.startDate || filters.endDate) {
      // Clear date filters if dateRange is cleared
      const newFilters = { ...filters, page: 1 };
      delete newFilters.startDate;
      delete newFilters.endDate;
      setFilters(newFilters);
    }
  }, [dateRange]);

  // Fetch logs only (without dashboard stats)
  const fetchLogs = async () => {
    setLoading(true);
    try {
      const response = await getLogs(filters);
      setLogs(response.logs);
      setPagination(response.pagination);
    } catch (error) {
      toast({
        title: 'Error',
        description: `Failed to fetch logs: ${error.message}`,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch dashboard statistics from API
  const fetchDashboardStats = async () => {
    try {
      console.log('Fetching dashboard stats from server...');
      const response = await getLogsStats();
      console.log('Received stats from server:', response.stats);
      setDashboardStats(response.stats);
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      toast({
        title: 'Error',
        description: 'Failed to load dashboard statistics from server',
        variant: 'destructive',
      });
      // Set empty stats as fallback
      setDashboardStats({
        totalRequests: 0,
        uniqueUsers: 0,
        avgResponseTime: 0,
        errorRate: 0,
        requestsByType: [],
        requestsByStatus: [],
        requestsOverTime: []
      });
    }
  };

  // Fetch logs and dashboard stats
  const fetchLogsAndStats = async () => {
    setLoading(true);
    try {
      // Fetch logs
      const response = await getLogs(filters);
      setLogs(response.logs);
      setPagination(response.pagination);

      // Fetch dashboard statistics
      await fetchDashboardStats();
    } catch (error) {
      toast({
        title: 'Error',
        description: `Failed to fetch logs: ${error.message}`,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (page: number) => {
    setFilters({ ...filters, page });
  };

  const handleSearch = () => {
    if (!searchTerm) {
      // If search term is empty, remove the filter
      const newFilters = { ...filters };
      delete newFilters[searchField as keyof LogsQueryParams];
      setFilters({ ...newFilters, page: 1 });
      return;
    }

    setFilters({
      ...filters,
      [searchField]: searchTerm,
      page: 1, // Reset to first page on new search
    });
  };

  const handleApplyAdvancedFilters = () => {
    const newFilters = { ...filters, page: 1 };

    if (minResponseTime) {
      newFilters.minResponseTime = parseInt(minResponseTime);
    } else {
      delete newFilters.minResponseTime;
    }

    if (maxResponseTime) {
      newFilters.maxResponseTime = parseInt(maxResponseTime);
    } else {
      delete newFilters.maxResponseTime;
    }

    setFilters(newFilters);
  };

  const handleClearAdvancedFilters = () => {
    setMinResponseTime('');
    setMaxResponseTime('');
    setDateRange(undefined);

    const newFilters = { ...filters, page: 1 };
    delete newFilters.minResponseTime;
    delete newFilters.maxResponseTime;
    delete newFilters.startDate;
    delete newFilters.endDate;

    setFilters(newFilters);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const handleClearLogs = async () => {
    try {
      const response = await clearOldLogs(clearDays);
      toast({
        title: 'Success',
        description: response.message,
      });
      fetchLogs(); // Refresh logs after clearing
      setClearDialogOpen(false);
    } catch (error) {
      toast({
        title: 'Error',
        description: `Failed to clear logs: ${error.message}`,
        variant: 'destructive',
      });
    }
  };

  const handleFilterByStatusType = (statusType: string) => {
    if (statusType === 'all') {
      const newFilters = { ...filters };
      delete newFilters.statusType;
      delete newFilters.statusCode;
      setFilters({ ...newFilters, page: 1 });
    } else {
      setFilters({
        ...filters,
        statusType: statusType as LogsQueryParams['statusType'],
        page: 1,
      });
    }
  };

  const getStatusColor = (statusCode: number): string => {
    if (statusCode >= 200 && statusCode < 300) return 'bg-green-500';
    if (statusCode >= 300 && statusCode < 400) return 'bg-blue-500';
    if (statusCode >= 400 && statusCode < 500) return 'bg-red-500';
    if (statusCode >= 500) return 'bg-red-700';
    return 'bg-gray-500';
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'yyyy-MM-dd HH:mm:ss');
    } catch (error) {
      return dateString;
    }
  };

  return (
    <div className="container mx-auto py-6">
      <Card>
        <CardHeader>
          <div className="flex flex-row justify-between items-center">
            <div>
              <CardTitle className="text-2xl">Access Logs</CardTitle>
              <CardDescription>
                View and filter access logs for the application
              </CardDescription>
            </div>
            <Button
              variant="outline"
              onClick={() => setShowDashboard(!showDashboard)}
              className="ml-auto"
            >
              <BarChart3 className="h-4 w-4 mr-2" />
              {showDashboard ? 'Hide' : 'Show'} Dashboard
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col space-y-4">
            {/* Dashboard */}
            {showDashboard && (
              <div className="space-y-4">
                <LogsDashboard
                  stats={dashboardStats}
                  isLoading={loading}
                />
                <div className="border-t pt-4"></div>
              </div>
            )}

            {/* Search and filter controls */}
            <div className="flex flex-wrap gap-2 items-center">
              <div className="flex-1 flex items-center space-x-2 min-w-[300px]">
                <Select
                  value={searchField}
                  onValueChange={setSearchField}
                >
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Search field" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="path">Path</SelectItem>
                    <SelectItem value="username">Username</SelectItem>
                    <SelectItem value="ipAddress">IP Address</SelectItem>
                    <SelectItem value="method">Method</SelectItem>
                    <SelectItem value="referrer">Referrer</SelectItem>
                  </SelectContent>
                </Select>
                <Input
                  placeholder={`Search by ${searchField}...`}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyPress={handleKeyPress}
                  className="flex-1"
                />
                <Button onClick={handleSearch} variant="secondary">
                  <Search className="h-4 w-4 mr-2" />
                  Search
                </Button>
              </div>

              <div className="flex items-center space-x-2">
                <Select
                  value={filters.statusType || 'all'}
                  onValueChange={handleFilterByStatusType}
                >
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="success">Success (2xx)</SelectItem>
                    <SelectItem value="redirect">Redirect (3xx)</SelectItem>
                    <SelectItem value="client-error">Client Error (4xx)</SelectItem>
                    <SelectItem value="server-error">Server Error (5xx)</SelectItem>
                    <SelectItem value="error">All Errors (4xx-5xx)</SelectItem>
                  </SelectContent>
                </Select>

                <Select
                  value={filters.requestType || 'all'}
                  onValueChange={(value) => {
                    if (value === 'all') {
                      const newFilters = { ...filters };
                      delete newFilters.requestType;
                      setFilters({ ...newFilters, page: 1 });
                    } else {
                      setFilters({
                        ...filters,
                        requestType: value as LogsQueryParams['requestType'],
                        page: 1,
                      });
                    }
                  }}
                >
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Request Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="page">Pages</SelectItem>
                    <SelectItem value="api">API Calls</SelectItem>
                    <SelectItem value="asset">Assets</SelectItem>
                    <SelectItem value="auth">Auth</SelectItem>
                    <SelectItem value="user">User</SelectItem>
                  </SelectContent>
                </Select>

                <Button onClick={fetchLogs} variant="outline" title="Refresh logs">
                  <RefreshCw className="h-4 w-4" />
                </Button>

                <Button
                  variant="outline"
                  onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                  className="gap-1"
                >
                  <Filter className="h-4 w-4" />
                  Filters
                  {showAdvancedFilters ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </Button>

                <Dialog open={clearDialogOpen} onOpenChange={setClearDialogOpen}>
                  <DialogTrigger asChild>
                    <Button variant="destructive" title="Clear old logs">
                      <Trash2 className="h-4 w-4 mr-2" />
                      Clear Old Logs
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Clear Old Logs</DialogTitle>
                      <DialogDescription>
                        This will permanently delete logs older than the specified number of days.
                      </DialogDescription>
                    </DialogHeader>
                    <div className="py-4">
                      <Label htmlFor="days">Days to keep</Label>
                      <Input
                        id="days"
                        type="number"
                        value={clearDays}
                        onChange={(e) => setClearDays(parseInt(e.target.value))}
                        min={1}
                        max={365}
                      />
                    </div>
                    <DialogFooter>
                      <Button variant="outline" onClick={() => setClearDialogOpen(false)}>
                        Cancel
                      </Button>
                      <Button variant="destructive" onClick={handleClearLogs}>
                        Clear Logs
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
            </div>

            {/* Advanced filters */}
            {showAdvancedFilters && (
              <div className="p-4 border rounded-md bg-muted/30 space-y-4">
                <h3 className="text-sm font-medium">Advanced Filters</h3>

                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  <div className="space-y-2">
                    <Label htmlFor="date-range">Date Range</Label>
                    <DateRangePicker
                      dateRange={dateRange}
                      onDateRangeChange={setDateRange}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="min-response-time">Response Time (ms)</Label>
                    <div className="flex space-x-2">
                      <Input
                        id="min-response-time"
                        placeholder="Min"
                        type="number"
                        value={minResponseTime}
                        onChange={(e) => setMinResponseTime(e.target.value)}
                      />
                      <Input
                        id="max-response-time"
                        placeholder="Max"
                        type="number"
                        value={maxResponseTime}
                        onChange={(e) => setMaxResponseTime(e.target.value)}
                      />
                    </div>
                  </div>
                </div>

                <div className="flex justify-end space-x-2 pt-2">
                  <Button variant="outline" onClick={handleClearAdvancedFilters}>
                    Clear Filters
                  </Button>
                  <Button onClick={handleApplyAdvancedFilters}>
                    Apply Filters
                  </Button>
                </div>
              </div>
            )}

            {/* Logs table */}
            {loading ? (
              <div className="flex justify-center items-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : logs.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No logs found matching your criteria
              </div>
            ) : (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Time</TableHead>
                      <TableHead>User</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Method</TableHead>
                      <TableHead className="w-[30%]">Path</TableHead>
                      <TableHead>IP Address</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Time (ms)</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {logs.map((log) => (
                      <TableRow key={log._id}>
                        <TableCell className="font-mono text-xs">
                          {formatDate(log.timestamp)}
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-col">
                            <span className="font-medium">{log.username}</span>
                            {log.fullName && (
                              <span className="text-xs text-muted-foreground">{log.fullName}</span>
                            )}
                            {/* Removed the userRole display */}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant="outline"
                            className={`
                              ${log.requestType === 'page' ? 'bg-green-100 text-green-800 hover:bg-green-200' : ''}
                              ${log.requestType === 'api' ? 'bg-blue-100 text-blue-800 hover:bg-blue-200' : ''}
                              ${log.requestType === 'asset' ? 'bg-gray-100 text-gray-800 hover:bg-gray-200' : ''}
                              ${!log.requestType ? 'bg-gray-100 text-gray-800 hover:bg-gray-200' : ''}
                            `}
                          >
                            {log.requestType ? log.requestType.toUpperCase() : 'UNKNOWN'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline" className="font-mono">
                            {log.method}
                          </Badge>
                        </TableCell>
                        <TableCell className="font-mono text-xs truncate max-w-[300px]" title={log.fullUrl || log.path}>
                          {log.path}
                          {/* Removed the pageName display */}
                          {log.referrer && log.referrer !== '' && (
                            <div className="text-xs text-muted-foreground truncate">
                              Ref: {log.referrer.substring(0, 30)}{log.referrer.length > 30 ? '...' : ''}
                            </div>
                          )}
                        </TableCell>
                        <TableCell className="font-mono text-xs">
                          {log.ipAddress}
                        </TableCell>
                        <TableCell>
                          <Badge
                            className={`${getStatusColor(log.statusCode)} text-white`}
                            title={`HTTP Status ${log.statusCode}`}
                          >
                            {log.statusCode}
                          </Badge>
                        </TableCell>
                        <TableCell className="font-mono text-xs">
                          {log.responseTime || '-'}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}

            {/* Pagination */}
            {pagination.pages > 1 && (
              <div className="flex justify-center mt-4">
                <LogsPagination
                  currentPage={pagination.page}
                  totalPages={pagination.pages}
                  onPageChange={handlePageChange}
                />
              </div>
            )}
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="text-sm text-muted-foreground">
            Showing {logs.length} of {pagination.total} logs
          </div>
        </CardFooter>
      </Card>
    </div>
  );
};

export default AdminLogs;





