```markdown
# gh0st

Ghost is a comprehensive, locally-hosted web application designed specifically for pawn shops. The system serves as a powerful supplement to the mandated POS system, addressing critical operational gaps while providing advanced TradeMe integration, pricing reference tools, and business analytics. With a multi-tenant architecture supporting role-based access control, Ghost enables franchise locations to operate independently while maintaining consistent business processes.

## Overview

Ghost employs a modern web application architecture utilizing Next.js with <PERSON>act on the frontend, and an Express-based server with MongoDB on the backend. The application is designed for self-hosting with Docker containerization for streamlined deployment. 

### Architecture & Technologies:
- **Frontend:** Next.js, React, shadcn-ui, Tailwind CSS, React Router
- **Backend:** Node.js, Express.js, MongoDB with Mongoose
- **Authentication:** JWT-based authentication with role-based access control
- **Deployment:** Docker containerization for streamlined deployment
- **UI Design:** Default dark mode with a responsive design for various device sizes

### Project Structure:
```
client/              # Frontend implementation
  ├── public/        # Public assets
  ├── src/           # Source files
  │   ├── api/       # API functions
  │   ├── components/# UI components
  │   ├── contexts/  # React context providers
  │   ├── hooks/     # Custom hooks
  │   ├── pages/     # Page components
  │   ├── App.tsx    # Main application component
  │   ├── main.tsx   # Entry point
  │   ├── index.css  # Global styles
  └── package.json   # Client dependencies and scripts

server/              # Backend implementation
  ├── config/        # Configuration files
  ├── models/        # Mongoose models 
  ├── routes/        # Express route handlers
  ├── services/      # Service layer
  ├── utils/         # Utility functions
  ├── .env           # Environment variables config
  ├── server.js      # Main server entry point
  └── package.json   # Server dependencies and scripts
```

## Features
Ghost offers a robust set of features to streamline pawnshop operations:

### User & Location Management
- Role-based access control with permissions for Admin, Manager, and Employee.
- Users can log in with either email or username.

### Pricing Database System
- Manager-maintained database of common items with detailed item records, visual freshness indicators, and advanced filtering.

### Gold Pricing Calculator
- Real-time spot price integration, automated price updates, location-specific pricing, and multiple calculation modes.

### TradeMe Integration
- Comprehensive listing manager, synchronization with in-store inventory, media management, AI-assisted descriptions, and more.

### Device Verification
- Integration with multiple service providers for device verification and redundancy.

### User Interface & Navigation
- Dark mode by default, consistent layout, responsive design, integrated analytics with PowerBI.

## Getting Started

### Requirements
Ensure the following are installed on your system:
- Node.js (v14 or newer)
- Docker
- MongoDB

### Quickstart

1. **Clone the Repository**

   ```sh
   git clone https://github.com/your-username/gh0st.git
   cd gh0st
   ```

2. **Environment Configuration**

   Create a `.env` file in the `server` directory based on the provided `.env` template:
   
   ```env
   PORT=3000
   DATABASE_URL=mongodb://localhost/yourdb
   JWT_SECRET=your_jwt_secret
   REFRESH_TOKEN_SECRET=your_refresh_token_secret
   ```

3. **Install Dependencies**

   In both the `client` and `server` directories:
   
   ```sh
   cd client && npm install
   cd ../server && npm install
   ```

4. **Run the Application**

   To start both the frontend and backend concurrently:

   ```sh
   cd client
   npm run start
   ```

   The backend will run on port 3000 and the frontend on port 5173.

5. **Access the Application**

   Open your web browser and navigate to `http://localhost:5173`.

### License
```
Copyright (c) 2024.
```
```