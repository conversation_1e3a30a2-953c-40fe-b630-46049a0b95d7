import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import { useToast } from '@/hooks/useToast';
import { getAudits } from '@/api/buyPawnAudits';
import { AuditNavigation } from './AuditNavigation';
import { Loader2, Search, Flag, FileText, CheckCircle } from 'lucide-react';
import { format } from 'date-fns';

/**
 * Component to display and manage flagged audits that require follow-up
 */
export function FlaggedAudits() {
  const [audits, setAudits] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const navigate = useNavigate();
  const { toast } = useToast();

  // Fetch flagged audits
  useEffect(() => {
    const fetchAudits = async () => {
      try {
        setIsLoading(true);
        const result = await getAudits({
          flaggedForFollowup: true,
          followedUp: false,
          page: currentPage,
          limit: 10,
          search: searchTerm,
        });

        if (result.success) {
          // Ensure data is an array before setting it
          if (Array.isArray(result.data)) {
            setAudits(result.data);
          } else {
            console.error('API response data is not an array:', result.data);
            setAudits([]); // Set to empty array if data is not an array
          }

          // Ensure pagination exists before accessing pages
          if (result.pagination && typeof result.pagination.pages === 'number') {
            setTotalPages(result.pagination.pages);
          } else {
            setTotalPages(1);
          }
        } else {
          toast({
            title: 'Error',
            description: result.error || 'Failed to load flagged audits.',
            variant: 'destructive',
          });
          setAudits([]); // Reset to empty array on error
        }
      } catch (error: any) {
        toast({
          title: 'Error',
          description: error.message || 'An unexpected error occurred.',
          variant: 'destructive',
        });
        setAudits([]); // Reset to empty array on error
      } finally {
        setIsLoading(false);
      }
    };

    fetchAudits();
  }, [currentPage, searchTerm, toast]);

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
  };

  // Format date
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'dd/MM/yyyy');
  };

  // Get audit type display
  const getAuditTypeDisplay = (type: string) => {
    switch (type) {
      case 'buy':
        return 'Buy Deal';
      case 'pawn':
        return 'Pawn Loan';
      case 'price':
        return 'Price';
      default:
        return type;
    }
  };

  // Get compliance badge
  const getComplianceBadge = (compliance: string) => {
    switch (compliance) {
      case 'compliant':
        return <Badge className="bg-green-500">Compliant</Badge>;
      case 'minor_non_compliant':
        return <Badge className="bg-yellow-500">Minor Non-Compliant</Badge>;
      case 'major_non_compliant':
        return <Badge className="bg-red-500">Major Non-Compliant</Badge>;
      default:
        return <Badge variant="outline">{compliance}</Badge>;
    }
  };

  // View audit details
  const viewAuditDetails = (id: string) => {
    navigate(`/buys/audits/${id}`);
  };

  // Record follow-up
  const recordFollowUp = (id: string) => {
    navigate(`/buys/audits/${id}?action=followup`);
  };

  // Show loading state only when initially loading
  if (isLoading && (!Array.isArray(audits) || audits.length === 0)) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex flex-col gap-4 mb-6">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div>
              <h1 className="text-3xl font-bold">Flagged Audits</h1>
              <p className="text-muted-foreground">Audits that require follow-up action</p>
            </div>
          </div>

          <AuditNavigation />
        </div>

        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading flagged audits...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col gap-4 mb-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold">Flagged Audits</h1>
            <p className="text-muted-foreground">Audits that require follow-up action</p>
          </div>
        </div>

        <AuditNavigation />
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Pending Follow-ups</CardTitle>
          <CardDescription>
            These audits have been flagged for follow-up and require attention
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <form onSubmit={handleSearch} className="flex w-full max-w-sm items-center space-x-2">
              <div className="relative flex-1">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by employee or transaction ID..."
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <Button type="submit">Search</Button>
            </form>

            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Transaction ID</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Employee</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Compliance</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {!Array.isArray(audits) || audits.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-4">
                        No flagged audits found
                      </TableCell>
                    </TableRow>
                  ) : (
                    audits.map((audit) => audit && (
                      <TableRow key={audit._id || 'unknown'}>
                        <TableCell className="font-medium">
                          {audit.transactionId || 'N/A'}
                        </TableCell>
                        <TableCell>
                          {getAuditTypeDisplay(audit.auditType || '')}
                        </TableCell>
                        <TableCell>{audit.employeeName || 'Unknown'}</TableCell>
                        <TableCell>{audit.auditDate ? formatDate(audit.auditDate) : 'N/A'}</TableCell>
                        <TableCell>
                          {getComplianceBadge(audit.overallCompliance || 'not_assessed')}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => viewAuditDetails(audit._id)}
                              disabled={!audit._id}
                            >
                              <FileText className="h-4 w-4 mr-1" />
                              View
                            </Button>
                            <Button
                              variant="default"
                              size="sm"
                              onClick={() => recordFollowUp(audit._id)}
                              disabled={!audit._id}
                            >
                              <CheckCircle className="h-4 w-4 mr-1" />
                              Record Follow-up
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>

            {totalPages > 1 && (
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
                      disabled={currentPage === 1}
                    />
                  </PaginationItem>

                  {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                    <PaginationItem key={page}>
                      <PaginationLink
                        onClick={() => setCurrentPage(page)}
                        isActive={currentPage === page}
                      >
                        {page}
                      </PaginationLink>
                    </PaginationItem>
                  ))}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}
                      disabled={currentPage === totalPages}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
