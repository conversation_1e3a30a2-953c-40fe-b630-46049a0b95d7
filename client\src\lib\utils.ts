
import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Format currency values
export function formatCurrency(value: number | undefined | null): string {
  if (value === undefined || value === null) return '$0.00';

  return new Intl.NumberFormat('en-NZ', {
    style: 'currency',
    currency: 'NZD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(value);
}

// Format date and time
export function formatDateTime(date: string | Date): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;

  // Check if the date is today
  const today = new Date();
  const isToday = dateObj.getDate() === today.getDate() &&
                  dateObj.getMonth() === today.getMonth() &&
                  dateObj.getFullYear() === today.getFullYear();

  // Check if the date is yesterday
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);
  const isYesterday = dateObj.getDate() === yesterday.getDate() &&
                      dateObj.getMonth() === yesterday.getMonth() &&
                      dateObj.getFullYear() === yesterday.getFullYear();

  if (isToday) {
    return `Today at ${dateObj.toLocaleTimeString('en-NZ', { hour: '2-digit', minute: '2-digit' })}`;
  } else if (isYesterday) {
    return `Yesterday at ${dateObj.toLocaleTimeString('en-NZ', { hour: '2-digit', minute: '2-digit' })}`;
  } else {
    return dateObj.toLocaleDateString('en-NZ', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
}

// Format date only (no time)
export function formatDate(date: string | Date | undefined | null): string {
  if (!date) return 'N/A';

  const dateObj = typeof date === 'string' ? new Date(date) : date;

  // Check if the date is valid
  if (isNaN(dateObj.getTime())) return 'Invalid date';

  // Check if the date is today
  const today = new Date();
  const isToday = dateObj.getDate() === today.getDate() &&
                  dateObj.getMonth() === today.getMonth() &&
                  dateObj.getFullYear() === today.getFullYear();

  // Check if the date is yesterday
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);
  const isYesterday = dateObj.getDate() === yesterday.getDate() &&
                      dateObj.getMonth() === yesterday.getMonth() &&
                      dateObj.getFullYear() === yesterday.getFullYear();

  if (isToday) {
    return `Today at ${dateObj.toLocaleTimeString('en-NZ', { hour: '2-digit', minute: '2-digit' })}`;
  } else if (isYesterday) {
    return `Yesterday at ${dateObj.toLocaleTimeString('en-NZ', { hour: '2-digit', minute: '2-digit' })}`;
  } else {
    return dateObj.toLocaleDateString('en-NZ', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    }) + ' ' + dateObj.toLocaleTimeString('en-NZ', { hour: '2-digit', minute: '2-digit' });
  }
}

// Calculate time left until a given date
export function calculateTimeLeft(endDate: Date) {
  const total = endDate.getTime() - new Date().getTime();
  const seconds = Math.floor((total / 1000) % 60);
  const minutes = Math.floor((total / 1000 / 60) % 60);
  const hours = Math.floor((total / (1000 * 60 * 60)) % 24);
  const days = Math.floor(total / (1000 * 60 * 60 * 24));

  return {
    total,
    days,
    hours,
    minutes,
    seconds
  };
}
