const mongoose = require('mongoose');

const emailTemplateSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true
  },
  subject: {
    type: String,
    required: true,
    trim: true
  },
  body: {
    type: String,
    required: true
  },
  category: {
    type: String,
    enum: ['Trademe', 'Personal Finance', 'Buys/WIW', 'Other'],
    default: 'Other'
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now,
    immutable: true
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  }
}, {
  versionKey: false
});

// Create indexes for common queries
emailTemplateSchema.index({ category: 1 });
emailTemplateSchema.index({ createdBy: 1 });

// Pre-save middleware to update the updatedAt field
emailTemplateSchema.pre('save', function(next) {
  if (this.isModified() && !this.isNew) {
    this.updatedAt = Date.now();
  }
  next();
});

const EmailTemplate = mongoose.model('EmailTemplate', emailTemplateSchema, 'emailtemplates');

module.exports = EmailTemplate;
