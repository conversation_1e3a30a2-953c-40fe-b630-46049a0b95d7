import {
  DollarSign,
  ShoppingCart,
  TestTube2,
  LayoutDashboard,
  Calculator,
  Book,
  Mail,
  SmilePlus,
  Command,
  ChevronRight,
  Globe
} from "lucide-react";
import { TradeMeIcon } from "./icons/TradeMeIcon";
import { Link, useLocation } from "react-router-dom";

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenuSub,
  SidebarMenuSubItem,
  SidebarMenuSubButton,
  SidebarRail
} from "@/components/ui/sidebar";
import { NavUser } from "./NavUser";
import { cn } from "@/lib/utils";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { ScrollArea } from "@/components/ui/scroll-area";

export function GhostSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const location = useLocation();

  // Define the type for navigation items
  type NavItem = {
    name: string;
    path: string;
    icon: React.ElementType;
    isActive?: boolean;
    items?: {
      name: string;
      path: string;
      disabled?: boolean;
      external?: boolean;
    }[];
  };

  // Main navigation items
  const mainNavItems: NavItem[] = [
    {
      name: "Dashboard",
      path: "/",
      icon: LayoutDashboard,
      isActive: location.pathname === "/"
    },
    {
      name: "Buys and Loans",
      path: "#",
      icon: DollarSign,
      items: [
        { name: "Audits", path: "/buys/audits" },
        { name: "Banned & Restricted Items", path: "/buys/banned-restricted-items" },
        { name: "Game Price Charting", path: "https://www.pricecharting.com/", external: true },
        { name: "Gold Calculators", path: "/gold-calculator" },
        { name: "Pricing Database", path: "/inventory" },
        { name: "Walkers", path: "#", disabled: true }
      ]
    },
    {
      name: "Personal Finance",
      path: "#",
      icon: Calculator,
      items: [
        { name: "Application Tracker", path: "/pf/tracker" },
        { name: "Staff Portal", path: "https://pl.cashconverters.kiwi/user/login", external: true },
        { name: "Staff Portal (UAT)", path: "https://pl-uat.cashconverters.kiwi/user/login", external: true },
        { name: "Kiosk Portal", path: "https://kiosk.cashconverters.co.nz/", external: true },
        { name: "Customer PF Portal", path: "https://loans.cashconverters.co.nz/sign-in/", external: true }
      ]
    },
    {
      name: "Retail",
      path: "#",
      icon: ShoppingCart,
      items: [
        { name: "Winz Quote Generator", path: "#", disabled: true },
        { name: "Spec Card Generator", path: "#", disabled: true }
      ]
    },
    {
      name: "Testing & Info Tools",
      path: "#",
      icon: TestTube2,
      items: [
        { name: "IMEI and iCloud Checker", path: "/tools/device-checker" },
        { name: "Controller Tester", path: "https://hardwaretester.com/gamepad", external: true },
        { name: "Microphone Tester", path: "https://hardwaretester.com/microphone", external: true },
        { name: "MIDI Controller Tester", path: "https://hardwaretester.com/midi", external: true },
        { name: "Apple Devices by Max iOS", path: "https://iosref.com/ios", external: true },

      ]
    },
    {
      name: "Trademe",
      path: "#",
      icon: TradeMeIcon,
      items: [
        { name: "Create New Listing", path: "/trademe/listing/new" },
        { name: "Items For Sale", path: "/trademe/selling" },
        { name: "Sold", path: "/trademe/sold" },
        { name: "Draft Items", path: "/trademe/draft" },
        { name: "Unsold", path: "/trademe/unsold" },
        { name: "Archived", path: "/trademe/archived" },
        { name: "Questions", path: "/trademe/questions" },
        { name: "Feedback", path: "/trademe/feedback" },
        { name: "Template Manager", path: "/trademe/templates" },
        { name: "Sync Status", path: "/trademe/sync" },
        { name: "Blacklist", path: "#", disabled: true }
      ]
    },
    {
      name: "External Websites",
      path: "#",
      icon: Globe,
      items: [
        { name: "Deputy", path: "https://cashconverters.au.deputy.com/", external: true },
        { name: "BrightHR", path: "https://app.brighthr.com/login", external: true },
        { name: "BrightSafe", path: "https://app.brightsafe.com/#/auth/login", external: true },
        { name: "CCNZ Training (ELMO)", path: "https://ccfranchises.elmotalent.co.nz/dashboard", external: true },
        { name: "CCNZ Support Ticket", path: "https://helpdesk.cashconverters.co.nz:8080/", external: true },
        { name: "CCNZ Pawn Portal", path: "https://payments.cashconverters.co.nz/", external: true }
        
      ]
    },
    {
      name: "Address Book",
      path: "/address-book",
      icon: Book,
    },
    {
      name: "Email Templates",
      path: "/email-templates",
      icon: Mail,
    },
    {
      name: "Happy or Not Feedback",
      path: "/feedback",
      icon: SmilePlus,
    }
  ];

  // Business tools as projects
  const businessTools: NavItem[] = [
    // All items have been moved to the main navigation
  ];

  // Support and feedback items
  const supportItems: NavItem[] = [
  ];

  // Helper functions to check if items are active
  const isSubItemActive = (path: string) => {
    if (path === "#") return false;
    if (path.startsWith("http")) return false;
    return location.pathname.startsWith(path);
  };

  const isMainItemActive = (item: any) => {
    if (item.path === "/" && location.pathname !== "/") return false;
    if (item.path === "#" && item.items) {
      return item.items.some((subItem: any) => isSubItemActive(subItem.path));
    }
    return location.pathname.startsWith(item.path);
  };

  return (
    <Sidebar variant="inset" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild>
              <a href="/">
                <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                  <Command className="size-4" />
                </div>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold">Ghost</span>
                  <span className="truncate text-xs">Cash Converters Rotorua</span>
                </div>
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent className="overflow-hidden">
        <ScrollArea className="h-[calc(100vh-8rem)]">
          {/* Main Navigation */}
          <SidebarGroup>
            <SidebarGroupLabel>Menu</SidebarGroupLabel>
            <SidebarMenu>
              {mainNavItems.map((item) => (
                <Collapsible
                  key={item.name}
                  asChild
                  defaultOpen={isMainItemActive(item)}
                  className="group/collapsible"
                >
                  <SidebarMenuItem>
                    <CollapsibleTrigger asChild>
                      {item.items ? (
                        <SidebarMenuButton className={isMainItemActive(item) ? 'font-medium text-primary' : ''}>
                          <item.icon className="size-4" />
                          <span>{item.name}</span>
                          <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                        </SidebarMenuButton>
                      ) : (
                        <SidebarMenuButton asChild>
                          <Link
                            to={item.path}
                            className={isMainItemActive(item) ? 'font-medium text-primary' : ''}
                          >
                            <item.icon className="size-4" />
                            <span>{item.name}</span>
                          </Link>
                        </SidebarMenuButton>
                      )}
                    </CollapsibleTrigger>
                    {item.items && (
                      <CollapsibleContent>
                        <SidebarMenuSub>
                          {item.items.map((subItem) => (
                            <SidebarMenuSubItem key={subItem.name}>
                              <SidebarMenuSubButton asChild>
                                {subItem.external ? (
                                  <a
                                    href={subItem.disabled ? "#" : subItem.path}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    aria-disabled={subItem.disabled ? "true" : undefined}
                                    className={subItem.disabled ? 'opacity-50 cursor-not-allowed' : ''}
                                    onClick={(e) => subItem.disabled && e.preventDefault()}
                                  >
                                    <span>{subItem.name}</span>
                                  </a>
                                ) : (
                                  <Link
                                    to={subItem.disabled ? "#" : subItem.path}
                                    aria-disabled={subItem.disabled ? "true" : undefined}
                                    className={cn(
                                      subItem.disabled ? 'opacity-50 cursor-not-allowed' : '',
                                      isSubItemActive(subItem.path) ? 'font-medium text-primary bg-primary/10' : ''
                                    )}
                                    onClick={(e) => subItem.disabled && e.preventDefault()}
                                  >
                                    <span>{subItem.name}</span>
                                  </Link>
                                )}
                              </SidebarMenuSubButton>
                            </SidebarMenuSubItem>
                          ))}
                        </SidebarMenuSub>
                      </CollapsibleContent>
                    )}
                  </SidebarMenuItem>
                </Collapsible>
              ))}
            </SidebarMenu>
          </SidebarGroup>

          {/* Business Tools as Projects */}
          {businessTools.length > 0 && (
            <SidebarGroup className="group-data-[collapsible=icon]:hidden">
              <SidebarGroupLabel>Other</SidebarGroupLabel>
              <SidebarMenu>
                {businessTools.map((item) => (
                  <SidebarMenuItem key={item.name}>
                    <SidebarMenuButton asChild>
                      <Link to={item.path} className={isSubItemActive(item.path) ? 'font-medium text-primary' : ''}>
                        <item.icon className="size-4" />
                        <span>{item.name}</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroup>
          )}

          {/* Support and Feedback */}
          <SidebarGroup className="mt-auto">
            <SidebarMenu>
              {supportItems.map((item) => (
                <SidebarMenuItem key={item.name}>
                  <SidebarMenuButton asChild size="sm">
                    <a
                      href={item.path}
                      target={item.path.startsWith("http") ? "_blank" : undefined}
                      rel={item.path.startsWith("http") ? "noopener noreferrer" : undefined}
                    >
                      <item.icon className="size-4" />
                      <span>{item.name}</span>
                    </a>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroup>
        </ScrollArea>
      </SidebarContent>
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
