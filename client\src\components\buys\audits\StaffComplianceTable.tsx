import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  ChevronDown,
  ChevronUp,
  Search,
  TrendingUp,
  TrendingDown,
  Minus,
  BarChart2
} from 'lucide-react';

interface StaffCompliance {
  employeeId: string;
  employeeName: string;
  totalAudits: number;
  compliantCount: number;
  minorNonCompliantCount: number;
  majorNonCompliantCount: number;
  complianceRate: number;
  complianceChange: number;
  flaggedCount: number;
}

interface StaffComplianceTableProps {
  data: StaffCompliance[];
}

/**
 * Table component to display staff compliance metrics
 */
export function StaffComplianceTable({ data }: StaffComplianceTableProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<keyof StaffCompliance>('complianceRate');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const navigate = useNavigate();

  // Handle empty data
  const safeData = data || [];

  // Handle sorting
  const handleSort = (field: keyof StaffCompliance) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  // Filter and sort data
  const filteredData = safeData
    .filter(staff =>
      staff.employeeName?.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .sort((a, b) => {
      const aValue = a[sortField];
      const bValue = b[sortField];

      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
      }

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortDirection === 'asc'
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      return 0;
    });

  // Get sort icon
  const getSortIcon = (field: keyof StaffCompliance) => {
    if (field !== sortField) return null;
    return sortDirection === 'asc' ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />;
  };

  // Get compliance trend icon
  const getComplianceTrendIcon = (change: number) => {
    if (change > 0) {
      return <TrendingUp className="h-4 w-4 text-green-500" />;
    } else if (change < 0) {
      return <TrendingDown className="h-4 w-4 text-red-500" />;
    }
    return <Minus className="h-4 w-4 text-muted-foreground" />;
  };

  // View employee details
  const viewEmployeeDetails = (employeeId: string) => {
    navigate(`/buys/audits/staff/${employeeId}`);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search by employee name..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead
                className="cursor-pointer"
                onClick={() => handleSort('employeeName')}
              >
                <div className="flex items-center">
                  Employee {getSortIcon('employeeName')}
                </div>
              </TableHead>
              <TableHead
                className="cursor-pointer text-right"
                onClick={() => handleSort('totalAudits')}
              >
                <div className="flex items-center justify-end">
                  Total Audits {getSortIcon('totalAudits')}
                </div>
              </TableHead>
              <TableHead
                className="cursor-pointer text-right"
                onClick={() => handleSort('complianceRate')}
              >
                <div className="flex items-center justify-end">
                  Compliance Rate {getSortIcon('complianceRate')}
                </div>
              </TableHead>
              <TableHead
                className="cursor-pointer text-right"
                onClick={() => handleSort('flaggedCount')}
              >
                <div className="flex items-center justify-end">
                  Flagged {getSortIcon('flaggedCount')}
                </div>
              </TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredData.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-4">
                  No staff members found
                </TableCell>
              </TableRow>
            ) : (
              filteredData.map((staff) => (
                <TableRow key={staff.employeeId}>
                  <TableCell className="font-medium">
                    {staff.employeeName}
                  </TableCell>
                  <TableCell className="text-right">
                    {staff.totalAudits}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex items-center justify-end space-x-2">
                      <span>{staff.complianceRate}%</span>
                      {getComplianceTrendIcon(staff.complianceChange)}
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    {staff.flaggedCount > 0 ? (
                      <Badge variant="outline" className="bg-yellow-500/10 text-yellow-500">
                        {staff.flaggedCount}
                      </Badge>
                    ) : (
                      <span>0</span>
                    )}
                  </TableCell>
                  <TableCell className="text-right">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => viewEmployeeDetails(staff.employeeId)}
                    >
                      <BarChart2 className="h-4 w-4 mr-1" />
                      Details
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
