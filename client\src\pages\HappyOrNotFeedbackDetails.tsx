import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Loader2, RefreshCw, ArrowLeft } from 'lucide-react';
import { useToast } from '@/hooks/useToast';
import { DateRange } from 'react-day-picker';
import { subDays } from 'date-fns';
import { Link } from 'react-router-dom';
import {
  getHappyOrNotSettings,
  syncHappyOrNotFeedback,
  HappyOrNotSettings
} from '@/api/happyOrNot';
import HappyOrNotFeedbackList from '@/components/HappyOrNotFeedbackList';

export function HappyOrNotFeedbackDetails() {
  const [loading, setLoading] = useState(true);
  const [syncing, setSyncing] = useState(false);
  const [settings, setSettings] = useState<HappyOrNotSettings | null>(null);
  const [dateRange, setDateRange] = useState<DateRange>({
    from: subDays(new Date(), 30),
    to: new Date()
  });

  const { toast } = useToast();

  // Load settings on initial render
  useEffect(() => {
    fetchSettings();
  }, []);

  // Fetch settings
  const fetchSettings = async () => {
    setLoading(true);
    try {
      const response = await getHappyOrNotSettings();

      if (response.success) {
        setSettings(response.data);
      } else {
        toast({
          title: 'Error',
          description: 'Failed to load Happy or Not settings',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error fetching Happy or Not settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to load Happy or Not settings',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Sync feedback data
  const syncFeedbackData = async () => {
    setSyncing(true);
    try {
      const response = await syncHappyOrNotFeedback();

      if (response.success) {
        toast({
          title: 'Success',
          description: 'Feedback data synced successfully',
        });
      } else {
        toast({
          title: 'Error',
          description: 'Failed to sync feedback data',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error syncing feedback data:', error);
      toast({
        title: 'Error',
        description: 'Failed to sync feedback data',
        variant: 'destructive',
      });
    } finally {
      setSyncing(false);
    }
  };

  // Handle date range change
  const handleDateRangeChange = (range: DateRange | undefined) => {
    if (range) {
      setDateRange(range);
    }
  };

  // Format experience point from settings for the component
  const formattedExperiencePoints = settings?.experiencePointName ? [
    {
      id: "1", // Using a placeholder ID since we're only using the name
      name: settings.experiencePointName
    }
  ] : [];

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <div className="flex items-center gap-2 mb-2">
            <Button variant="ghost" size="sm" asChild>
              <Link to="/feedback">
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back to Dashboard
              </Link>
            </Button>
          </div>
          <h1 className="text-3xl font-bold tracking-tight">Feedback Details</h1>
          <p className="text-muted-foreground">
            Search and filter customer feedback comments and responses
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-2">
          <Button
            variant="outline"
            onClick={syncFeedbackData}
            disabled={syncing}
          >
            {syncing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Syncing...
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                Sync Data
              </>
            )}
          </Button>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-80">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </div>
      ) : (
        <HappyOrNotFeedbackList
          experiencePoints={formattedExperiencePoints}
          dateRange={dateRange}
          onDateRangeChange={handleDateRangeChange}
        />
      )}
    </div>
  );
}

export default HappyOrNotFeedbackDetails;
