const axios = require('axios');
const MetalPriceSettings = require('../models/MetalPriceSettings');

/**
 * Service for interacting with the Metal Price API
 */
class MetalApiService {
  /**
   * Get the API key from settings
   * @returns {Promise<string>} The API key
   */
  async getApiKey() {
    const settings = await MetalPriceSettings.getSettings();
    if (!settings.apiKey) {
      throw new Error('Metal Price API key not configured. Please set your API key in the Gold Settings page.');
    }
    return settings.apiKey;
  }

  /**
   * Get gold prices by carat
   * @returns {Promise<Object>} Gold prices by carat
   */
  async getGoldPricesByCarat() {
    try {
      const apiKey = await this.getApiKey();
      const response = await axios.get(`https://api.metalpriceapi.com/v1/carat`, {
        params: {
          api_key: apiKey,
          base: 'NZD',
          currency: 'XAU'
        },
        timeout: 30000 // 30 second timeout
      });

      if (!response.data.success) {
        throw new Error('Failed to fetch gold prices by carat');
      }

      // Convert to per gram (multiply by 5 since 1 carat = 0.2g)
      const pricesPerGram = {};
      Object.entries(response.data.data).forEach(([carat, price]) => {
        pricesPerGram[carat] = price * 5;
      });

      return {
        success: true,
        timestamp: response.data.timestamp,
        prices: pricesPerGram
      };
    } catch (error) {
      console.error('Error fetching gold prices by carat:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get latest prices for Silver, Palladium, and Platinum
   * @returns {Promise<Object>} Latest prices
   */
  async getLatestOtherMetalPrices() {
    try {
      const apiKey = await this.getApiKey();
      const response = await axios.get(`https://api.metalpriceapi.com/v1/latest`, {
        params: {
          api_key: apiKey,
          base: 'NZD',
          currencies: 'XAG,XPD,XPT',
          unit: 'gram'
        },
        timeout: 30000 // 30 second timeout
      });

      if (!response.data.success) {
        throw new Error('Failed to fetch other metal prices');
      }

      // Map API metal codes to readable names
      const metalMap = {
        'XAG': 'Silver',
        'XPD': 'Palladium',
        'XPT': 'Platinum'
      };

      // Convert to our format
      const prices = {};
      Object.entries(response.data.rates).forEach(([code, price]) => {
        const metal = metalMap[code];
        if (metal) {
          // For other metals, we just store the price
          // and invert the rate (API gives metal per NZD, we want NZD per metal)
          prices[metal] = 1 / price;
        }
      });

      return {
        success: true,
        timestamp: response.data.timestamp,
        prices
      };
    } catch (error) {
      console.error('Error fetching other metal prices:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get historical price data for a date range
   * @param {string} startDate - Start date in YYYY-MM-DD format
   * @param {string} endDate - End date in YYYY-MM-DD format
   * @returns {Promise<Object>} Historical price data
   */
  async getHistoricalPrices(startDate, endDate) {
    try {
      console.log(`Fetching historical prices from ${startDate} to ${endDate}`);
      const apiKey = await this.getApiKey();

      if (!apiKey) {
        console.error('No API key available for Metal Price API');
        return {
          success: false,
          error: 'API key not configured'
        };
      }

      console.log(`Making API request to fetch historical data from ${startDate} to ${endDate}`);
      const response = await axios.get(`https://api.metalpriceapi.com/v1/timeframe`, {
        params: {
          api_key: apiKey,
          base: 'NZD',
          currencies: 'XAU,XAG,XPD,XPT',
          start_date: startDate,
          end_date: endDate
        },
        timeout: 30000 // 30 second timeout
      });

      if (!response.data.success) {
        console.error('API returned unsuccessful response:', response.data);
        return {
          success: false,
          error: 'Failed to fetch historical metal prices: API returned unsuccessful status'
        };
      }

      // Map API metal codes to readable names
      const metalMap = {
        'XAU': 'Gold',
        'XAG': 'Silver',
        'XPD': 'Palladium',
        'XPT': 'Platinum'
      };

      // Convert to our format
      const history = [];
      Object.entries(response.data.rates).forEach(([date, rates]) => {
        Object.entries(rates).forEach(([code, price]) => {
          const metal = metalMap[code];
          if (metal) {
            // The API gives metal per NZD, we want NZD per metal
            const pricePerTroyOz = 1 / price;

            history.push({
              metal,
              date,
              price: pricePerTroyOz
            });
          }
        });
      });

      console.log(`Retrieved ${history.length} historical price points from API for date range ${startDate} to ${endDate}`);
      return {
        success: true,
        history
      };
    } catch (error) {
      console.error(`Error fetching historical metal prices from ${startDate} to ${endDate}:`, error);
      return {
        success: false,
        error: `API request failed: ${error.message}`
      };
    }
  }

  /**
   * Get API usage information
   * @returns {Promise<Object>} API usage information
   */
  async getApiUsage() {
    try {
      const apiKey = await this.getApiKey();
      const response = await axios.get(`https://api.metalpriceapi.com/v1/usage`, {
        params: {
          api_key: apiKey
        },
        timeout: 30000 // 30 second timeout
      });

      if (!response.data.success) {
        throw new Error('Failed to fetch API usage information');
      }

      return {
        success: true,
        usage: response.data.result
      };
    } catch (error) {
      console.error('Error fetching API usage:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Mock methods have been removed to ensure we always use real API data
}

module.exports = new MetalApiService();