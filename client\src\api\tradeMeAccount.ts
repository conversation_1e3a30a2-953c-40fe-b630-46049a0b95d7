import api from './api';

/**
 * Get TradeMe connection status
 * @returns Promise with connection status
 */
export const getConnectionStatus = async () => {
  try {
    const response = await api.get('/trademe/account/connection-status');
    return response.data;
  } catch (error: any) {
    console.error('Error getting TradeMe connection status:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

/**
 * Get TradeMe settings
 * @returns Promise with settings
 */
export const getSettings = async () => {
  try {
    const response = await api.get('/trademe/account/settings');
    console.log('API getSettings response:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('Error getting TradeMe settings:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

/**
 * Update TradeMe settings
 * @param settings - Updated settings
 * @returns Promise with result
 */
export const updateSettings = async (settings: { environment: 'production' | 'sandbox' }) => {
  try {
    const response = await api.put('/trademe/account/settings', settings);
    return response.data;
  } catch (error: any) {
    console.error('Error updating TradeMe settings:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

/**
 * Get a request token for OAuth
 * @param environment - TradeMe environment
 * @returns Promise with request token and auth URL
 */
export const getRequestToken = async (environment: 'production' | 'sandbox', callbackUrl?: string, scopes?: string[]) => {
  try {
    // Generate a callback URL based on the current window location if not provided
    if (!callbackUrl) {
      // TradeMe requires HTTPS for the callback URL
      const baseUrl = window.location.origin.replace('http:', 'https:');
      callbackUrl = `${baseUrl}/trademe/callback`;
    }
    console.log(`Using callback URL: ${callbackUrl}`);

    // Default scopes if not provided
    const requestScopes = scopes || ['MyTradeMeRead', 'MyTradeMeWrite'];

    const response = await api.post('/trademe/account/request-token', {
      environment,
      callbackUrl,
      scopes: requestScopes
    });
    return response.data;
  } catch (error: any) {
    console.error('Error getting TradeMe request token:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

/**
 * Exchange a request token for an access token
 * @param environment - TradeMe environment
 * @param oauthToken - OAuth token
 * @param oauthVerifier - OAuth verifier
 * @returns Promise with access token
 */
export const getAccessToken = async (
  environment: 'production' | 'sandbox',
  oauthToken: string,
  oauthVerifier: string
) => {
  try {
    console.log(`Calling API to get access token with environment: ${environment}, token: ${oauthToken}, verifier: ${oauthVerifier}`);

    const response = await api.post('/trademe/account/access-token', {
      environment,
      oauthToken,
      oauthVerifier
    });

    console.log('Access token API response:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('Error getting TradeMe access token:', error);

    // Log more detailed error information
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }

    // Return a structured error response instead of throwing
    return {
      success: false,
      error: error?.response?.data?.error || error.message
    };
  }
};

/**
 * Connect a TradeMe account
 * @param environment - TradeMe environment
 * @param accessToken - Access token
 * @param accessTokenSecret - Access token secret
 * @returns Promise with result
 */
export const connectAccount = async (
  environment: 'production' | 'sandbox',
  accessToken: string,
  accessTokenSecret: string
) => {
  try {
    console.log(`Connecting TradeMe account with environment: ${environment}, token: ${accessToken}`);

    const response = await api.post('/trademe/account/connect', {
      environment,
      accessToken,
      accessTokenSecret
    });

    console.log('Connect account API response:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('Error connecting TradeMe account:', error);

    // Log more detailed error information
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }

    // Return a structured error response instead of throwing
    return {
      success: false,
      error: error?.response?.data?.error || error.message
    };
  }
};

/**
 * Disconnect a TradeMe account
 * @returns Promise with result
 */
export const disconnectAccount = async () => {
  try {
    const response = await api.post('/trademe/account/disconnect');
    return response.data;
  } catch (error: any) {
    console.error('Error disconnecting TradeMe account:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

/**
 * Refresh TradeMe connection
 * @returns Promise with result
 */
export const refreshConnection = async () => {
  try {
    const response = await api.post('/trademe/account/refresh');
    return response.data;
  } catch (error: any) {
    console.error('Error refreshing TradeMe connection:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

/**
 * Check TradeMe token permissions
 * @returns Promise with permissions
 */
export const checkPermissions = async () => {
  try {
    const response = await api.get('/trademe/account/permissions');
    return response.data;
  } catch (error: any) {
    console.error('Error checking TradeMe permissions:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

/**
 * Get TradeMe member summary
 * @returns Promise with member summary
 */
export const getMemberSummary = async () => {
  try {
    const response = await api.get('/trademe/account/member-summary');
    return response.data;
  } catch (error: any) {
    console.error('Error getting TradeMe member summary:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

