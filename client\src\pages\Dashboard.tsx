import { useEffect, useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { getDashboardSettings, DashboardSettings } from "@/api/dashboardSettings";
import { X } from 'lucide-react';
import { format } from "date-fns";
import { Skeleton } from "@/components/ui/skeleton";
import { VisuallyHidden } from "@/components/ui/visually-hidden";
import { PersonalFinanceTrackerDashboard } from "@/components/PersonalFinanceTrackerDashboard";
import { HappyOrNotDashboard } from "@/components/HappyOrNotDashboard";

export function Dashboard() {
  const [loading, setLoading] = useState(true);
  const [settings, setSettings] = useState<DashboardSettings | null>(null);
  const [selectedImage, setSelectedImage] = useState<{ name: string; url: string } | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const settingsData = await getDashboardSettings();
        setSettings(settingsData);
      } catch (error) {
        console.error("Error fetching dashboard data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  return (
    <div className="space-y-8">

      {/* PowerBI Screenshots - Only shown if showPowerBIImages is true */}
      {(loading || settings?.showPowerBIImages) && (
        <div className="grid gap-6 lg:grid-cols-2">
          {loading ? (
            // Skeleton loading state
            <>
              <Card className="overflow-hidden">
                <CardContent className="p-0">
                  <div className="relative aspect-[16/9]">
                    <Skeleton className="absolute inset-0 w-full h-full" />
                  </div>
                </CardContent>
              </Card>
              <Card className="overflow-hidden">
                <CardContent className="p-0">
                  <div className="relative aspect-[16/9]">
                    <Skeleton className="absolute inset-0 w-full h-full" />
                  </div>
                </CardContent>
              </Card>
              <Card className="overflow-hidden">
                <CardContent className="p-0">
                  <div className="relative aspect-[16/9]">
                    <Skeleton className="absolute inset-0 w-full h-full" />
                  </div>
                </CardContent>
              </Card>
              <Card className="overflow-hidden">
                <CardContent className="p-0">
                  <div className="relative aspect-[16/9]">
                    <Skeleton className="absolute inset-0 w-full h-full" />
                  </div>
                </CardContent>
              </Card>
            </>
          ) : (
            // Actual content
            settings?.pbiLinks.map((link, index) => (
              link.enabled && (
                <Card key={index} className="overflow-hidden">
                  <CardContent className="p-0">
                    <div
                      className="relative aspect-[16/9] overflow-hidden cursor-pointer"
                      onClick={() => setSelectedImage({ name: link.name, url: `data:image/png;base64,${link.screenshot}` })}
                    >
                      <img
                        src={`data:image/png;base64,${link.screenshot}`}
                        alt={link.name}
                        className="w-full h-full object-cover hover:opacity-90 transition-opacity"
                      />
                    </div>
                  </CardContent>
                </Card>
              )
            ))
          )}
        </div>
      )}

      {/* Process Step Images */}
      <div className="grid gap-6 md:grid-cols-3 mt-6">
        {/* Buys Process */}
        <Card className="overflow-hidden">
          <CardContent className="p-0">
            <div
              className="relative aspect-[16/9] overflow-hidden cursor-pointer"
              onClick={() => setSelectedImage({ name: "Buys Process", url: "/img/steps/buys.png" })}
            >
              <img
                src="/img/ccnz/steps/buys.png"
                alt="Buys Process"
                className="w-full h-full object-cover hover:opacity-90 transition-opacity"
              />
            </div>
          </CardContent>
        </Card>

        {/* Personal Loans Process */}
        <Card className="overflow-hidden">
          <CardContent className="p-0">
            <div
              className="relative aspect-[16/9] overflow-hidden cursor-pointer"
              onClick={() => setSelectedImage({ name: "Personal Loans Process", url: "/img/steps/pls.png" })}
            >
              <img
                src="/img/ccnz/steps/pls.png"
                alt="Personal Loans Process"
                className="w-full h-full object-cover hover:opacity-90 transition-opacity"
              />
            </div>
          </CardContent>
        </Card>

        {/* Sales Process */}
        <Card className="overflow-hidden">
          <CardContent className="p-0">
            <div
              className="relative aspect-[16/9] overflow-hidden cursor-pointer"
              onClick={() => setSelectedImage({ name: "Sales Process", url: "/img/steps/sales.png" })}
            >
              <img
                src="/img/ccnz/steps/sales.png"
                alt="Sales Process"
                className="w-full h-full object-cover hover:opacity-90 transition-opacity"
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Our Values */}
      <div className="mt-8">
        <div className="grid gap-4 grid-cols-2 md:grid-cols-3 lg:grid-cols-5">
          {/* Community Value */}
          <Card className="overflow-hidden">
            <CardContent className="p-0">
              <div className="relative aspect-[1/1] overflow-hidden">
                <img
                  src="/img/ccnz/Value-Community.png"
                  alt="Community Value"
                  className="w-full h-full object-cover"
                />
              </div>
            </CardContent>
          </Card>

          {/* Equity Value */}
          <Card className="overflow-hidden">
            <CardContent className="p-0">
              <div className="relative aspect-[1/1] overflow-hidden">
                <img
                  src="/img/ccnz/Value-Equity.png"
                  alt="Equity Value"
                  className="w-full h-full object-cover"
                />
              </div>
            </CardContent>
          </Card>

          {/* Perseverance Value */}
          <Card className="overflow-hidden">
            <CardContent className="p-0">
              <div className="relative aspect-[1/1] overflow-hidden">
                <img
                  src="/img/ccnz/Value-Perseverance.png"
                  alt="Perseverance Value"
                  className="w-full h-full object-cover"
                />
              </div>
            </CardContent>
          </Card>

          {/* Potential Value */}
          <Card className="overflow-hidden">
            <CardContent className="p-0">
              <div className="relative aspect-[1/1] overflow-hidden">
                <img
                  src="/img/ccnz/Value-Potential.png"
                  alt="Potential Value"
                  className="w-full h-full object-cover"
                />
              </div>
            </CardContent>
          </Card>

          {/* Responsibility Value */}
          <Card className="overflow-hidden">
            <CardContent className="p-0">
              <div className="relative aspect-[1/1] overflow-hidden">
                <img
                  src="/img/ccnz/Value-Responsibility.png"
                  alt="Responsibility Value"
                  className="w-full h-full object-cover"
                />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Happy or Not Feedback */}
      <div className="mt-6">
        <HappyOrNotDashboard />
      </div>

      {/* Personal Finance Tracker */}
      <div className="mt-6">
        <PersonalFinanceTrackerDashboard />
      </div>

      {/* Image Preview Dialog */}
      <Dialog open={!!selectedImage} onOpenChange={() => setSelectedImage(null)}>
        <DialogContent className="max-w-[1600px] w-[90vw] max-h-[900px] p-0">
          <VisuallyHidden>
            <DialogTitle>
              {selectedImage ? `${selectedImage.name} Preview` : 'Image Preview'}
            </DialogTitle>
          </VisuallyHidden>
          <div className="absolute right-4 top-4 z-50 flex items-center gap-3">
            {settings?.updatedAt && (
              <div className="bg-black/70 text-white px-3 py-1 rounded-md text-sm">
                Last updated: {format(new Date(settings.updatedAt), 'PPpp')}
              </div>
            )}
            <button
              onClick={() => setSelectedImage(null)}
              className="rounded-full bg-black/70 p-2 hover:bg-black/90 transition-colors"
            >
              <X className="h-4 w-4 text-white" />
            </button>
          </div>
          {selectedImage && (
            <div className="relative w-full h-full flex items-center justify-center bg-background">
              <img
                src={selectedImage.url}
                alt={selectedImage.name}
                className="w-full h-full object-contain"
              />
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
