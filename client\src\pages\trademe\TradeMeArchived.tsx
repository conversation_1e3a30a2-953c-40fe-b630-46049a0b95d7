import { useEffect, useState } from 'react';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import { getItems as getListings, TradeMeItem } from '@/api/tradeMeItems';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/useToast';
import { TradeMeListingsLayout } from '@/components/trademe/TradeMeListingsLayout';
import { TradeMeListingCard } from '@/components/trademe/TradeMeListingCard';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from '@/components/ui/input';
import { Search, Filter, RefreshCw } from 'lucide-react';

export function TradeMeArchived() {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const { toast } = useToast();

  // State for listings and loading
  const [listings, setListings] = useState<TradeMeItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [syncing, setSyncing] = useState(false);
  const [totalItems, setTotalItems] = useState(0);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  // State for filters and search
  const [searchTerm, setSearchTerm] = useState('');
  const [filter, setFilter] = useState('all');
  const [sortBy, setSortBy] = useState(searchParams.get('sort') || 'newest');
  const [timeFilter, setTimeFilter] = useState<number | null>(searchParams.get('days') ? parseInt(searchParams.get('days') || '0') : null);

  // Only fetch on initial load
  useEffect(() => {
    fetchListings(1, true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Fetch listings from the API
  const fetchListings = async (pageToFetch = page, reset = false) => {
    try {
      // Set loading state only if we're resetting the list
      if (reset) {
        setLoading(true);
      }

      const queryParams: any = {
        status: 'archived',
        page: pageToFetch,
        limit: 20,
        sort: sortBy,
        search: searchTerm
      };

      // Add filter if set
      if (filter !== 'all') {
        queryParams.filter = filter;
      }

      // Add time filter if set
      if (timeFilter) {
        queryParams.days = timeFilter;
      }

      console.log('Fetching archived listings with params:', queryParams);

      const data = await getListings(queryParams);

      if (reset) {
        setListings(data.items || []);
      } else {
        setListings(prev => [...prev, ...(data.items || [])]);
      }

      setTotalItems(data.pagination?.total || 0);
      setHasMore(data.pagination?.page < data.pagination?.pages);
      setPage(pageToFetch);
    } catch (error: any) {
      console.error('Failed to fetch archived listings:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to fetch archived listings',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle search
  const handleSearch = (term: string) => {
    setSearchTerm(term);
    fetchListings(1, true);
  };

  // Handle filter change
  const handleFilter = (value: string) => {
    setFilter(value);
    fetchListings(1, true);
  };

  // Handle sort change
  const handleSort = (value: string) => {
    setSortBy(value);
    fetchListings(1, true);
  };

  // Handle time filter change
  const handleTimeFilter = (days: number) => {
    setTimeFilter(days || null);
    fetchListings(1, true);
  };

  // Handle sync
  const handleSync = async () => {
    try {
      setSyncing(true);
      toast({
        title: 'Syncing',
        description: 'Syncing archived listings...',
      });

      // In a real implementation, this would call an API endpoint to sync
      await new Promise(resolve => setTimeout(resolve, 2000));

      fetchListings(1, true);

      toast({
        title: 'Success',
        description: 'Archived listings synced successfully',
      });
    } catch (error: any) {
      console.error('Failed to sync archived listings:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to sync archived listings',
        variant: 'destructive',
      });
    } finally {
      setSyncing(false);
    }
  };

  // Handle unarchive
  const handleUnarchive = async (itemId: string) => {
    try {
      // This would call an API endpoint to unarchive the item
      // For now, we'll just show a toast
      toast({
        title: 'Not Implemented',
        description: 'Unarchive functionality is not yet implemented',
      });
    } catch (error: any) {
      console.error('Failed to unarchive item:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to unarchive item',
        variant: 'destructive',
      });
    }
  };

  return (
    <TradeMeListingsLayout
      title="Archived Listings"
      type="archived"
      listings={listings}
      totalItems={totalItems}
      loading={loading}
      syncing={syncing}
      onSync={handleSync}
      onSearch={handleSearch}
      onFilter={handleFilter}
      onSort={handleSort}
      onTimeFilter={handleTimeFilter}
    >
      {listings.length === 0 && !loading ? (
        <div className="text-center py-12">
          <h3 className="text-lg font-medium">No archived listings found</h3>
          <p className="text-muted-foreground mt-2">
            You don't have any archived TradeMe listings yet.
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {listings.map((listing) => (
            <TradeMeListingCard
              key={listing._id}
              listing={listing}
              actions={[
                {
                  label: 'View Details',
                  onClick: () => navigate(`/trademe/listing/${listing._id}`),
                },
                {
                  label: 'Unarchive',
                  onClick: () => handleUnarchive(listing._id),
                },
                {
                  label: 'Relist',
                  onClick: () => navigate(`/trademe/listing/edit/${listing._id}?relist=true`),
                },
              ]}
            />
          ))}
        </div>
      )}
    </TradeMeListingsLayout>
  );
}
