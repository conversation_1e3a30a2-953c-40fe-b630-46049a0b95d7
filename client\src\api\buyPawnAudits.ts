import api from './api';

// Interface for individual items
export interface AuditItem {
  stockcode: string;
  brand: string;
  description: string;
  cost: number;
}

// Interfaces for the new audit types
export interface AuditBase {
  _id: string;
  transactionId: string;
  auditType: 'buy' | 'pawn' | 'price';
  employeeId: string;
  employeeName: string;
  amount: number;
  // Legacy fields for backward compatibility
  itemDescription?: string;
  stockcode?: string;
  brand?: string;
  // New multi-item structure
  items: AuditItem[];
  auditedBy: {
    _id: string;
    fullName: string;
  };
  auditDate: string;
  status: 'draft' | 'completed' | 'flagged' | 'resolved';
  flaggedForFollowup: boolean;
  flagReason?: string;
  followedUp: boolean;
  followupResponse?: string;
  followupNotes?: string;
  flagResolvedAt?: string;
  flagResolvedBy?: {
    _id: string;
    fullName: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface SectionAssessment {
  status: 'pass' | 'fail' | 'not_assessed';
  failReasons: string[];
  notes?: string;
}

export interface PricingAssessment extends SectionAssessment {
  suggestedPrice?: string;
  costPrice?: string;
  ticketPrice?: string;
  // Price audit specific fields
  transactionType?: 'buy' | 'pawn';
  overpaymentReason?: 'paid_over_ghost_price' | 'paid_over_gold_calculator' | 'insufficient_research' | 'other';
  customOverpaymentReason?: string;
}

export interface ResponsibleLendingAssessment extends SectionAssessment {
  polText?: string;
}

export interface EssentialItemCheck {
  status: 'pass' | 'fail' | 'not_assessed' | 'not_applicable';
  category?: string;
  hasEssentialItems: boolean;
  isEssential: boolean;
  potentiallyEssentialChecked: boolean;
  potentiallyEssentialText?: string;
  compliance: 'compliant' | 'non_compliant' | 'not_assessed' | 'not_applicable';
  reasonNotEssential?: string;
  notes?: string;
}

export interface BuyAudit extends AuditBase {
  auditType: 'buy';
  dataEntryQuality: SectionAssessment;
  itemConditionCheck: SectionAssessment;
  pricing: PricingAssessment;
  authorizedLimitCheck: SectionAssessment;
  overallCompliance: 'compliant' | 'minor_non_compliant' | 'major_non_compliant' | 'not_assessed';
  auditNotes?: string;
  overallScore: number;
}

export interface PawnAudit extends AuditBase {
  auditType: 'pawn';
  dataEntryQuality: SectionAssessment;
  itemConditionCheck: SectionAssessment;
  pricing: PricingAssessment;
  authorizedLimitCheck: SectionAssessment;
  polSuitability: ResponsibleLendingAssessment;
  customerUnderstanding: SectionAssessment;
  vulnerableCustomer: SectionAssessment;
  essentialItemCheck: EssentialItemCheck;
  overallCompliance: 'compliant' | 'minor_non_compliant' | 'major_non_compliant' | 'not_assessed';
  auditNotes?: string;
  overallScore: number;
}

export interface PriceAudit extends AuditBase {
  auditType: 'price';
  pricing: PricingAssessment;
  overallCompliance: 'compliant' | 'minor_non_compliant' | 'major_non_compliant' | 'not_assessed';
  auditNotes?: string;
  overallScore: number;
}

export type Audit = BuyAudit | PawnAudit | PriceAudit;

export interface AuditFilterOptions {
  status?: 'draft' | 'completed' | 'flagged' | 'resolved';
  type?: 'buy' | 'pawn' | 'price';
  employeeId?: string;
  flaggedForFollowup?: boolean;
  followedUp?: boolean;
  search?: string;
  fromDate?: string;
  toDate?: string;
  page?: number;
  limit?: number;
  sort?: string;
  sortDirection?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  success: boolean;
  data: {
    audits: T[];
    pagination: {
      total: number;
      page: number;
      limit: number;
      pages: number;
    };
  };
  error?: string;
}

// Create a new audit
export const createAudit = async (auditData: any) => {
  try {
    const response = await api.post('/buys/audits/new', auditData);
    return response.data;
  } catch (error: any) {
    console.error('Error creating audit:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Get an audit by ID
export const getAuditById = async (id: string) => {
  try {
    const response = await api.get(`/buys/audits/${id}`);
    return response.data;
  } catch (error: any) {
    console.error('Error getting audit by ID:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Update an audit
export const updateAudit = async (id: string, auditData: Partial<Audit>) => {
  try {
    const response = await api.put(`/buys/audits/${id}`, auditData);
    return response.data;
  } catch (error: any) {
    console.error('Error updating audit:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Get audits with filtering and pagination
export const getAudits = async (options: AuditFilterOptions = {}): Promise<PaginatedResponse<Audit>> => {
  try {
    const response = await api.get('/buys/audits', { params: options });
    return response.data;
  } catch (error: any) {
    console.error('Error getting audits:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Flag an audit for follow-up
export const flagAudit = async (id: string, flagReason: string) => {
  try {
    const response = await api.put(`/buys/audits/${id}/flag`, { flagReason });
    return response.data;
  } catch (error: any) {
    console.error('Error flagging audit:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Remove flag from an audit
export const unflagAudit = async (id: string) => {
  try {
    const response = await api.put(`/buys/audits/${id}/unflag`);
    return response.data;
  } catch (error: any) {
    console.error('Error unflagging audit:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Complete follow-up for a flagged audit
export const completeFollowup = async (id: string, followupNotes: string) => {
  try {
    const response = await api.put(`/buys/audits/${id}/complete-followup`, { followupNotes });
    return response.data;
  } catch (error: any) {
    console.error('Error completing follow-up:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Mark an audit as followed up (legacy - kept for backward compatibility)
export const markAuditFollowedUp = async (id: string, followupResponse: string) => {
  try {
    const response = await api.put(`/buys/audits/${id}/followup`, { followedUp: true, followupResponse });
    return response.data;
  } catch (error: any) {
    console.error('Error marking audit as followed up:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Create a direct audit (without a deal)
export const createDirectAudit = async (auditData: any) => {
  try {
    const response = await api.post('/buys/audits/direct', auditData);
    return response.data;
  } catch (error: any) {
    console.error('Error creating direct audit:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Get audit statistics
export const getAuditStats = async (options: { timeRange?: string } = {}) => {
  try {
    const response = await api.get('/buys/audits/stats', { params: options });
    return response.data;
  } catch (error: any) {
    console.error('Error getting audit statistics:', error);

    // Return a default response with empty data instead of throwing an error
    // This allows the UI to handle the empty state gracefully
    if (error.response && error.response.status === 500) {
      return {
        success: true,
        data: {
          totalAudits: 0,
          compliantCount: 0,
          nonCompliantCount: 0,
          flaggedCount: 0,
          pendingFollowupCount: 0,
          complianceRate: 0,
          complianceChange: 0,
          recentAudits: 0,
          staffCompliance: [],
          complianceTrends: [],
          auditTypeDistribution: [],
          staffPerformanceTrends: []
        }
      };
    }

    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Get staff performance data
export const getStaffPerformance = async (options: { employeeId?: string, timeRange?: string } = {}) => {
  try {
    const { employeeId, ...params } = options;
    let url = '/buys/audits/staff';

    if (employeeId) {
      url += `/${employeeId}`;
    }

    const response = await api.get(url, { params });
    return response.data;
  } catch (error: any) {
    console.error('Error fetching staff performance:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Export audit data
export const exportAuditData = async (format: string, options: any) => {
  try {
    const response = await api.post('/buys/audits/export', {
      format,
      ...options,
    }, {
      responseType: 'blob',
    });

    // Get the filename from the Content-Disposition header if available
    let filename = `audit-export-${new Date().toISOString().split('T')[0]}.${format}`;
    const contentDisposition = response.headers['content-disposition'];
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="(.+)"/);
      if (filenameMatch && filenameMatch[1]) {
        filename = filenameMatch[1];
      }
    }

    // Create a download link and trigger the download
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', filename);
    document.body.appendChild(link);
    link.click();
    link.remove();

    return {
      success: true,
    };
  } catch (error: any) {
    console.error('Error exporting audit data:', error);
    if (error.response && error.response.data instanceof Blob) {
      // Try to read the error message from the blob
      try {
        const text = await error.response.data.text();
        const errorData = JSON.parse(text);
        throw new Error(errorData.error || 'Failed to export audit data');
      } catch (blobError) {
        throw new Error('Failed to export audit data');
      }
    }
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Get an audit by transaction ID
export const getAuditByTransactionId = async (transactionId: string) => {
  try {
    const response = await api.get(`/buys/audits/transaction/${transactionId}`);
    return response.data;
  } catch (error: any) {
    console.error('Error fetching audit by transaction ID:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Get an audit by deal ID
export const getAuditByDealId = async (dealId: string) => {
  try {
    const response = await api.get(`/buys/audits/deal/${dealId}`);
    return response.data;
  } catch (error: any) {
    console.error('Error fetching audit by deal ID:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Delete an audit
export const deleteAudit = async (id: string) => {
  try {
    const response = await api.delete(`/buys/audits/${id}`);
    return response.data;
  } catch (error: any) {
    console.error('Error deleting audit:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};



// Get audit comments
export const getAuditComments = async (auditId: string) => {
  try {
    const response = await api.get(`/buys/audits/${auditId}/comments`);
    return response.data;
  } catch (error: any) {
    console.error('Error fetching audit comments:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Add audit comment
export const addAuditComment = async (auditId: string, comment: string) => {
  try {
    const response = await api.post(`/buys/audits/${auditId}/comments`, { comment });
    return response.data;
  } catch (error: any) {
    console.error('Error adding audit comment:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Get audit history
export const getAuditHistory = async (auditId: string) => {
  try {
    const response = await api.get(`/buys/audits/${auditId}/history`);
    return response.data;
  } catch (error: any) {
    console.error('Error fetching audit history:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Update audit follow-up status
export const updateAuditFollowup = async (auditId: string, followupData: any) => {
  try {
    const response = await api.put(`/buys/audits/${auditId}/followup-status`, followupData);
    return response.data;
  } catch (error: any) {
    console.error('Error updating audit follow-up:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};
