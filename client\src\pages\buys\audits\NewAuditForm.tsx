import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/hooks/useToast';
import { useAuth } from '@/contexts/AuthContext';
import { getUsers } from '@/api/users';
import { createDirectAudit } from '@/api/buyPawnAudits';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DatePicker } from '@/components/ui/date-picker';
import { Loader2, ArrowRight, ArrowLeft, CheckCircle2 } from 'lucide-react';

// Import components
import { TransactionInfoHeader } from '@/components/buys/audits/TransactionInfoHeader';
import { MultiItemAssessmentSection } from '@/components/buys/audits/MultiItemAssessmentSection';
import { PricingOnlySection } from '@/components/buys/audits/PricingOnlySection';
import { MultiItemPricingSection } from '@/components/buys/audits/MultiItemPricingSection';
import { PriceAuditSection } from '@/components/buys/audits/PriceAuditSection';
import { MultiItemPriceAuditSection } from '@/components/buys/audits/MultiItemPriceAuditSection';
import { LinearResponsibleLendingSection } from '@/components/buys/audits/LinearResponsibleLendingSection';
import { AuditSummarySection } from '@/components/buys/audits/AuditSummarySection';
import { AuthorizedLimitSection } from '@/components/buys/audits/AuthorizedLimitSection';
import { AuditTypeSelector } from '@/components/buys/audits/AuditTypeSelector';
import { FormProgress } from '@/components/buys/audits/FormProgress';
import { ItemsSection } from '@/components/buys/audits/ItemsSection';

// Define the item schema with individual pricing
const itemSchema = z.object({
  stockcode: z.string().min(1, 'Stockcode is required'),
  brand: z.string().min(1, 'Brand is required'),
  description: z.string().min(1, 'Description is required'),
  cost: z.string().min(1, 'Cost is required').refine((val) => {
    const num = parseFloat(val);
    return !isNaN(num) && num > 0;
  }, 'Cost must be a valid positive number'),
  // Individual pricing fields for each item
  pricing: z.object({
    suggestedPrice: z.string().default(''),
    costPrice: z.string().default(''),
    ticketPrice: z.string().default(''),
    notes: z.string().default(''),
  }).optional(),
});

// Define the form schema with conditional validation
const createFormSchema = () => z.object({
  // Basic information
  auditType: z.enum(['buy', 'pawn', 'price']),
  transactionId: z.string().min(1, 'Transaction ID is required'),
  employeeId: z.string().min(1, 'Employee is required'),
  amount: z.string().min(1, 'Amount is required'),
  transactionDate: z.string().min(1, 'Transaction date is required'),
  auditDate: z.string().min(1, 'Audit date is required'),
  // Price audit specific transaction type (top level for Step 1)
  priceAuditTransactionType: z.enum(['buy', 'pawn']).optional(),
  // Legacy field for backward compatibility
  itemDescription: z.string().optional(),
  // New multi-item structure
  items: z.array(itemSchema).min(1, 'At least one item is required').refine((items) => {
    // Validate that sum of item costs equals transaction amount
    return true; // We'll handle this validation in the component
  }, 'Sum of item costs must equal transaction amount'),

  // Transaction section
  dataEntryQuality: z.object({
    status: z.enum(['pass', 'fail', 'not_assessed']).default('not_assessed'),
    failReasons: z.array(z.string()).default([]),
    notes: z.string().optional(),
  }),

  itemConditionCheck: z.object({
    status: z.enum(['pass', 'fail', 'not_assessed']).default('not_assessed'),
    failReasons: z.array(z.string()).default([]),
    notes: z.string().optional(),
  }),

  // Pricing section
  pricing: z.object({
    status: z.enum(['pass', 'fail', 'not_assessed']).default('not_assessed'),
    failReasons: z.array(z.string()).default([]),
    suggestedPrice: z.string().default(''),
    costPrice: z.string().default(''),
    ticketPrice: z.string().default(''),
    // Price audit specific fields
    transactionType: z.enum(['buy', 'pawn']).optional(),
    overpaymentReason: z.enum(['paid_over_ghost_price', 'paid_over_gold_calculator', 'insufficient_research', 'other']).optional(),
    customOverpaymentReason: z.string().default(''),
    notes: z.string().default(''),
  }),

  authorizedLimitCheck: z.object({
    status: z.enum(['pass', 'fail', 'not_assessed']).default('not_assessed'),
    failReasons: z.array(z.string()).default([]),
    notes: z.string().optional(),
  }),

  // Responsible lending section (for pawn only)
  polSuitability: z.object({
    status: z.enum(['pass', 'fail', 'not_assessed']).default('not_assessed'),
    polText: z.string().optional(),
    failReasons: z.array(z.string()).default([]),
    notes: z.string().optional(),
  }),

  customerUnderstanding: z.object({
    status: z.enum(['pass', 'fail', 'not_assessed']).default('not_assessed'),
    notes: z.string().optional(),
  }),

  vulnerableCustomer: z.object({
    status: z.enum(['pass', 'fail', 'not_assessed']).default('not_assessed'),
    notes: z.string().optional(),
  }),

  essentialItemCheck: z.object({
    status: z.enum(['pass', 'fail', 'not_assessed']).default('not_assessed'),
    category: z.string().optional(),
    hasEssentialItems: z.boolean().default(false),
    isEssential: z.boolean().default(false),
    potentiallyEssentialChecked: z.boolean().default(false),
    potentiallyEssentialText: z.string().optional(),
    compliance: z.enum(['compliant', 'non_compliant', 'not_assessed', 'not_applicable']).default('not_assessed'),
    reasonNotEssential: z.string().optional(),
    notes: z.string().optional(),
  }),

  // Summary section
  overallCompliance: z.enum(['compliant', 'minor_non_compliant', 'major_non_compliant', 'not_assessed']).default('not_assessed'),
  auditNotes: z.string().optional(),
  flaggedForFollowup: z.boolean().default(false),
  flagReason: z.string().optional(),
}).refine((data) => {
  // If flagged for follow-up, flagReason is required
  if (data.flaggedForFollowup && (!data.flagReason || data.flagReason.trim() === '')) {
    return false;
  }
  return true;
}, {
  message: "Follow-up reason is required when flagged for follow-up",
  path: ["flagReason"],
});

// Create the form schema
const formSchema = createFormSchema();

type FormValues = z.infer<typeof formSchema>;
/**
 * Component for creating a new audit
 * Implements a single-page flow where sections appear based on previous answers
 */
export function NewAuditForm() {
  const { toast } = useToast();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [users, setUsers] = useState<any[]>([]);
  const [isLoadingUsers, setIsLoadingUsers] = useState(true);
  const [currentStep, setCurrentStep] = useState(1);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  const [showSections, setShowSections] = useState({
    transaction: false,
    pricing: false,
    responsibleLending: false,
    summary: false
  });

  // Stats for scoring
  const [stats, setStats] = useState({
    passCount: 0,
    failCount: 0,
    overallScore: 0
  });

  // Function to generate initial item with pricing structure
  const generateInitialItem = (auditType: string, transactionId: string) => {
    const prefix = auditType === 'pawn' ? 'A' : 'B';
    return {
      stockcode: transactionId ? `${prefix}${transactionId}-1` : '',
      brand: '',
      description: '',
      cost: '',
      pricing: {
        suggestedPrice: '',
        costPrice: '',
        ticketPrice: '',
        notes: '',
      }
    };
  };

  // Initialize form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      auditType: 'buy',
      transactionId: '',
      employeeId: '',
      amount: '',
      transactionDate: '',
      auditDate: new Date().toISOString().split('T')[0], // Default to today's date
      priceAuditTransactionType: undefined,
      itemDescription: '',
      items: [generateInitialItem('buy', '')],
      dataEntryQuality: {
        status: 'not_assessed',
        failReasons: [],
        notes: '',
      },
      itemConditionCheck: {
        status: 'not_assessed',
        failReasons: [],
        notes: '',
      },
      pricing: {
        status: 'not_assessed',
        failReasons: [],
        suggestedPrice: '',
        costPrice: '',
        ticketPrice: '',
        transactionType: undefined,
        overpaymentReason: undefined,
        customOverpaymentReason: '',
        notes: '',
      },
      authorizedLimitCheck: {
        status: 'not_assessed',
        failReasons: [],
        notes: '',
      },
      polSuitability: {
        status: 'not_assessed',
        polText: '',
        failReasons: [],
        notes: '',
      },
      customerUnderstanding: {
        status: 'not_assessed',
        notes: '',
      },
      vulnerableCustomer: {
        status: 'not_assessed',
        notes: '',
      },
      essentialItemCheck: {
        status: 'not_assessed',
        category: '',
        hasEssentialItems: false,
        isEssential: false,
        potentiallyEssentialChecked: false,
        potentiallyEssentialText: '',
        compliance: 'not_assessed',
        reasonNotEssential: '',
        notes: '',
      },
      overallCompliance: 'not_assessed',
      auditNotes: '',
      flaggedForFollowup: false,
      flagReason: '',
    },
  });

  // Watch for changes to auditType and transactionId
  const auditType = form.watch('auditType');
  const transactionId = form.watch('transactionId');
  const priceAuditTransactionType = form.watch('priceAuditTransactionType');

  // Load users for employee selection
  useEffect(() => {
    const loadUsers = async () => {
      setIsLoadingUsers(true);
      try {
        const result = await getUsers();
        if (result.success) {
          // Sort users by full name
          const sortedUsers = [...result.data].sort((a, b) =>
            a.fullName.localeCompare(b.fullName)
          );
          setUsers(sortedUsers);
        } else {
          toast({
            title: 'Error',
            description: result.error || 'Failed to load users.',
            variant: 'destructive',
          });
        }
      } catch (error: any) {
        toast({
          title: 'Error',
          description: error.message || 'An unexpected error occurred.',
          variant: 'destructive',
        });
      } finally {
        setIsLoadingUsers(false);
      }
    };

    loadUsers();
  }, [toast]);

  // Watch for changes to form values to calculate score
  useEffect(() => {
    const subscription = form.watch((value, { name, type }) => {
      if (type === 'change' && name?.includes('status')) {
        calculateScore();
      }
    });
    return () => subscription.unsubscribe();
  }, [form.watch]);

  // Update stockcodes when audit type, transaction ID, or price audit transaction type changes
  useEffect(() => {
    if (auditType && transactionId) {
      const currentItems = form.getValues('items') || [];
      let prefix = 'B'; // Default to buy

      if (auditType === 'price') {
        // For price audits, use the specific transaction type
        prefix = priceAuditTransactionType === 'pawn' ? 'A' : 'B';
      } else {
        // For buy/pawn audits, use the audit type
        prefix = auditType === 'pawn' ? 'A' : 'B';
      }

      currentItems.forEach((item, index) => {
        const newStockcode = `${prefix}${transactionId}-${index + 1}`;
        form.setValue(`items.${index}.stockcode`, newStockcode);
      });
    }
  }, [auditType, transactionId, priceAuditTransactionType, form]);

  // Calculate the audit score based on pass/fail assessments
  const calculateScore = () => {
    const formValues = form.getValues();
    let passCount = 0;
    let failCount = 0;
    let totalAssessments = 0;

    // Check data entry quality
    if (formValues.dataEntryQuality.status === 'pass') passCount++;
    if (formValues.dataEntryQuality.status === 'fail') failCount++;
    if (formValues.dataEntryQuality.status !== 'not_assessed') totalAssessments++;

    // Check item condition
    if (formValues.itemConditionCheck.status === 'pass') passCount++;
    if (formValues.itemConditionCheck.status === 'fail') failCount++;
    if (formValues.itemConditionCheck.status !== 'not_assessed') totalAssessments++;

    // Check pricing
    if (formValues.pricing.status === 'pass') passCount++;
    if (formValues.pricing.status === 'fail') failCount++;
    if (formValues.pricing.status !== 'not_assessed') totalAssessments++;

    // Check authorized limit (for buy and pawn)
    if (auditType !== 'price') {
      if (formValues.authorizedLimitCheck.status === 'pass') passCount++;
      if (formValues.authorizedLimitCheck.status === 'fail') failCount++;
      if (formValues.authorizedLimitCheck.status !== 'not_assessed') totalAssessments++;
    }

    // Check responsible lending (for pawn only)
    if (auditType === 'pawn') {
      // POL Suitability
      if (formValues.polSuitability.status === 'pass') passCount++;
      if (formValues.polSuitability.status === 'fail') failCount++;
      if (formValues.polSuitability.status !== 'not_assessed') totalAssessments++;

      // Customer Understanding
      if (formValues.customerUnderstanding.status === 'pass') passCount++;
      if (formValues.customerUnderstanding.status === 'fail') failCount++;
      if (formValues.customerUnderstanding.status !== 'not_assessed') totalAssessments++;

      // Vulnerable Customer
      if (formValues.vulnerableCustomer.status === 'pass') passCount++;
      if (formValues.vulnerableCustomer.status === 'fail') failCount++;
      if (formValues.vulnerableCustomer.status !== 'not_assessed') totalAssessments++;

      // Essential Item Check
      if (formValues.essentialItemCheck.compliance === 'compliant') passCount++;
      if (formValues.essentialItemCheck.compliance === 'non_compliant') failCount++;
      if (formValues.essentialItemCheck.compliance !== 'not_assessed' &&
          formValues.essentialItemCheck.compliance !== 'not_applicable') totalAssessments++;
    }

    // Calculate overall score
    const overallScore = totalAssessments > 0 ? Math.round((passCount / totalAssessments) * 100) : 0;

    setStats({
      passCount,
      failCount,
      overallScore
    });

    // Set overall compliance based on score
    let compliance = 'not_assessed';
    if (totalAssessments > 0) {
      if (overallScore >= 90) compliance = 'compliant';
      else if (overallScore >= 70) compliance = 'minor_non_compliant';
      else compliance = 'major_non_compliant';
    }

    form.setValue('overallCompliance', compliance as any);
  };
  // Handle form submission
  const onSubmit = async (values: FormValues) => {
    setIsSaving(true);
    try {
      // Validate cost totals
      const transactionAmount = parseFloat(values.amount);
      const itemsTotal = values.items.reduce((total, item) => total + parseFloat(item.cost || '0'), 0);

      if (Math.abs(itemsTotal - transactionAmount) > 0.01) {
        toast({
          title: 'Cost Mismatch',
          description: `Item costs ($${itemsTotal.toFixed(2)}) must equal transaction amount ($${transactionAmount.toFixed(2)})`,
          variant: 'destructive',
        });
        setIsSaving(false);
        return;
      }

      // Convert items costs to numbers for server validation
      const processedItems = values.items.map(item => ({
        stockcode: item.stockcode?.trim() || '',
        brand: item.brand?.trim() || '',
        description: item.description?.trim() || '',
        cost: parseFloat(item.cost || '0')
      }));

      // Validate processed items before submission
      for (const item of processedItems) {
        if (!item.stockcode || !item.brand || !item.description || isNaN(item.cost) || item.cost <= 0) {
          toast({
            title: 'Invalid Item Data',
            description: 'Each item must have stockcode, brand, description, and a valid positive cost.',
            variant: 'destructive',
          });
          setIsSaving(false);
          return;
        }
      }

      // Generate item description from items for backward compatibility
      const itemDescription = values.items.map(item =>
        `${item.brand} - ${item.description}`
      ).join(', ');

      // Calculate final score before submission
      calculateScore();

      // For Price Audits, ensure required fields are properly set in the pricing section
      if (values.auditType === 'price') {
        // Copy transaction type from top-level field
        if (values.priceAuditTransactionType) {
          values.pricing.transactionType = values.priceAuditTransactionType;
        }

        // Ensure overpayment reason is set (should be validated by this point)
        if (!values.pricing.overpaymentReason) {
          toast({
            title: 'Missing Overpayment Reason',
            description: 'Please select the reason for the overpayment.',
            variant: 'destructive',
          });
          return;
        }
      }

      // Validate dates before submission
      if (!values.transactionDate || !values.auditDate) {
        toast({
          title: 'Missing Dates',
          description: 'Please select both transaction date and audit date.',
          variant: 'destructive',
        });
        setIsSaving(false);
        return;
      }

      // Ensure dates are valid
      const transactionDate = new Date(values.transactionDate);
      const auditDate = new Date(values.auditDate);

      if (isNaN(transactionDate.getTime()) || isNaN(auditDate.getTime())) {
        toast({
          title: 'Invalid Dates',
          description: 'Please select valid dates for transaction and audit.',
          variant: 'destructive',
        });
        setIsSaving(false);
        return;
      }

      // Prepare the data for submission
      const auditData = {
        ...values, // Include all other form fields first
        transactionType: values.auditType,
        transactionId: values.transactionId,
        employeeId: values.employeeId,
        amount: transactionAmount,
        transactionDate: values.transactionDate,
        auditDate: values.auditDate,
        // Legacy fields for backward compatibility
        itemDescription: itemDescription,
        stockcode: values.items[0]?.stockcode || '',
        brand: values.items[0]?.brand || '',
        // New multi-item structure with numeric costs (override the string version)
        items: processedItems,
        auditorId: user?._id || '',
        auditorName: user?.fullName || '',
      };

      console.log('Submitting audit data:', JSON.stringify(auditData, null, 2));

      // Submit the form using the proper API function
      const result = await createDirectAudit(auditData);

      if (result.success) {
        toast({
          title: 'Audit Created',
          description: 'The audit has been created successfully.',
        });

        // Navigate to the new audit
        navigate(`/buys/audits/${result.data._id}`);
      } else {
        throw new Error(result.error || 'Failed to create audit');
      }
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'An unexpected error occurred.',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Get total steps based on audit type
  const getTotalSteps = (auditType: string) => {
    if (auditType === 'pawn') return 5; // Type, Details, Assessment, Pawn Questions, Summary
    return 4; // Type, Details, Assessment, Summary
  };

  // Handle continuing to the next section
  const handleContinue = () => {
    const values = form.getValues();

    // Validate current step
    if (currentStep === 1) {
      // Step 1: Validate audit type selection
      if (!values.auditType) {
        toast({
          title: 'Missing Information',
          description: 'Please select an audit type before continuing.',
          variant: 'destructive',
        });
        return;
      }

      // Mark step 1 as completed and move to step 2
      setCompletedSteps(prev => [...prev.filter(s => s !== 1), 1]);
      setCurrentStep(2);
    } else if (currentStep === 2) {
      // Step 2: Validate transaction details and items
      if (!values.transactionId || !values.employeeId || !values.amount || !values.transactionDate || !values.auditDate) {
        toast({
          title: 'Missing Information',
          description: 'Please fill in all required fields before continuing.',
          variant: 'destructive',
        });
        return;
      }

      // Validate items
      if (!values.items || values.items.length === 0) {
        toast({
          title: 'Missing Items',
          description: 'Please add at least one item before continuing.',
          variant: 'destructive',
        });
        return;
      }

      // Validate each item
      for (const item of values.items) {
        if (!item.stockcode || !item.brand || !item.description || !item.cost) {
          toast({
            title: 'Incomplete Items',
            description: 'Please fill in all item details (stockcode, brand, description, cost) before continuing.',
            variant: 'destructive',
          });
          return;
        }
      }

      // Validate cost totals - only if items have costs entered
      const transactionAmount = parseFloat(values.amount);
      const itemsTotal = values.items.reduce((total, item) => total + parseFloat(item.cost || '0'), 0);
      const hasItemCosts = values.items.some(item => parseFloat(item.cost || '0') > 0);

      if (hasItemCosts && Math.abs(itemsTotal - transactionAmount) > 0.01) {
        toast({
          title: 'Cost Mismatch',
          description: `Item costs ($${itemsTotal.toFixed(2)}) must equal transaction amount ($${transactionAmount.toFixed(2)})`,
          variant: 'destructive',
        });
        return;
      }

      // Validate Price Audit specific fields for Step 2 (only transaction type)
      if (values.auditType === 'price') {
        if (!values.priceAuditTransactionType) {
          toast({
            title: 'Missing Transaction Type',
            description: 'Please select whether this was a buy deal or pawn loan.',
            variant: 'destructive',
          });
          return;
        }
      }

      // Show appropriate sections based on audit type
      if (values.auditType === 'buy') {
        setShowSections({
          transaction: true,
          pricing: true,
          responsibleLending: false,
          summary: false
        });
      } else if (values.auditType === 'pawn') {
        setShowSections({
          transaction: true,
          pricing: true,
          responsibleLending: false,
          summary: false
        });
      } else if (values.auditType === 'price') {
        setShowSections({
          transaction: false,
          pricing: true,
          responsibleLending: false,
          summary: false
        });
      }

      // Mark step 2 as completed and move to step 3
      setCompletedSteps(prev => [...prev.filter(s => s !== 2), 2]);
      setCurrentStep(3);
    } else if (currentStep === 3) {
      // Step 3: Item Assessment - validate that assessments are completed

      // Validate Price Audit specific fields for Step 3 (overpayment details)
      if (values.auditType === 'price') {
        if (!values.pricing.overpaymentReason) {
          toast({
            title: 'Missing Overpayment Reason',
            description: 'Please select the reason for the overpayment.',
            variant: 'destructive',
          });
          return;
        }

        if (values.pricing.overpaymentReason === 'other' && !values.pricing.customOverpaymentReason?.trim()) {
          toast({
            title: 'Missing Custom Reason',
            description: 'Please provide a custom reason for the overpayment.',
            variant: 'destructive',
          });
          return;
        }
      }

      setCompletedSteps(prev => [...prev.filter(s => s !== 3), 3]);

      if (values.auditType === 'pawn') {
        // Move to pawn-specific questions
        setShowSections(prev => ({ ...prev, responsibleLending: true }));
        setCurrentStep(4);
      } else {
        // Move directly to summary for buy/price audits
        setShowSections(prev => ({ ...prev, summary: true }));
        setCurrentStep(4);
      }
    } else if (currentStep === 4 && values.auditType === 'pawn') {
      // Step 4: Pawn-specific questions (only for pawn audits)
      setCompletedSteps(prev => [...prev.filter(s => s !== 4), 4]);
      setShowSections(prev => ({ ...prev, summary: true }));
      setCurrentStep(5);
    }

    // Scroll to top
    window.scrollTo(0, 0);
  };

  // Handle going back to previous step
  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);

      // Update sections visibility based on the step we're going back to
      if (currentStep === 2) {
        // Going back to step 1 - hide all sections
        setShowSections({
          transaction: false,
          pricing: false,
          responsibleLending: false,
          summary: false
        });
      } else if (currentStep === 3) {
        // Going back to step 2 - keep transaction details visible
        setShowSections(prev => ({ ...prev, summary: false, responsibleLending: false }));
      } else if (currentStep === 4) {
        // Going back to step 3
        if (auditType === 'pawn') {
          setShowSections(prev => ({ ...prev, responsibleLending: false, summary: false }));
        } else {
          setShowSections(prev => ({ ...prev, summary: false }));
        }
      } else if (currentStep === 5) {
        // Going back to step 4 (pawn questions)
        setShowSections(prev => ({ ...prev, summary: false }));
      }

      window.scrollTo(0, 0);
    }
  };

  // Determine what to show based on current step
  const showTypeSelection = currentStep === 1;
  const showTransactionDetails = currentStep === 2;
  const showItemAssessment = currentStep === 3;
  const showPawnQuestions = currentStep === 4 && auditType === 'pawn';
  const showSummary = (currentStep === 4 && auditType !== 'pawn') || (currentStep === 5 && auditType === 'pawn');

  // Function to handle step navigation
  const handleStepClick = (step: number) => {
    // Only allow navigation to completed steps or the current step
    if (completedSteps.includes(step) || step === currentStep) {
      setCurrentStep(step);

      // Update sections visibility based on the step
      if (step === 1) {
        setShowSections({
          transaction: false,
          pricing: false,
          responsibleLending: false,
          summary: false
        });
      } else if (step === 2) {
        setShowSections({
          transaction: false,
          pricing: false,
          responsibleLending: false,
          summary: false
        });
      } else if (step === 3) {
        const values = form.getValues();
        if (values.auditType === 'buy') {
          setShowSections({
            transaction: true,
            pricing: true,
            responsibleLending: false,
            summary: false
          });
        } else if (values.auditType === 'pawn') {
          setShowSections({
            transaction: true,
            pricing: true,
            responsibleLending: false,
            summary: false
          });
        } else if (values.auditType === 'price') {
          setShowSections({
            transaction: false,
            pricing: true,
            responsibleLending: false,
            summary: false
          });
        }
      } else if (step === 4) {
        const values = form.getValues();
        if (values.auditType === 'pawn') {
          setShowSections(prev => ({ ...prev, responsibleLending: true, summary: false }));
        } else {
          setShowSections(prev => ({ ...prev, summary: true }));
        }
      } else if (step === 5) {
        setShowSections(prev => ({ ...prev, summary: true }));
      }

      window.scrollTo(0, 0);
    }
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col gap-4 mb-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold">Create New Audit</h1>
            <p className="text-muted-foreground">Create a new audit for a buy or pawn transaction</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => navigate('/buys/audits')}>
              Back to Dashboard
            </Button>
          </div>
        </div>


      </div>

      {/* Enhanced Progress indicator */}
      <FormProgress
        auditType={auditType}
        currentStep={currentStep}
        onStepClick={handleStepClick}
        completedSteps={completedSteps}
      />

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Step 1: Select Audit Type */}
          {showTypeSelection && (
            <>
              <AuditTypeSelector control={form.control} disabled={isSaving} />

              <div className="flex justify-end mt-6">
                <Button
                  type="button"
                  onClick={handleContinue}
                  disabled={isSaving || !auditType}
                  className="w-full md:w-auto"
                >
                  Continue to Transaction Details <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </>
          )}

          {/* Step 2: Transaction Details */}
          {showTransactionDetails && (
            <>
              <Card>
                <CardHeader>
                  <CardTitle>Transaction Details</CardTitle>
                  <CardDescription>Enter the details of the transaction and items</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {/* Transaction ID */}
                    <FormField
                      control={form.control}
                      name="transactionId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Transaction ID</FormLabel>
                          <FormControl>
                            <Input placeholder="Enter transaction ID" {...field} disabled={isSaving} />
                          </FormControl>
                          <FormDescription>
                            Enter the unique identifier for this transaction
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Amount */}
                    <FormField
                      control={form.control}
                      name="amount"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Amount</FormLabel>
                          <FormControl>
                            <Input type="text" placeholder="Enter amount" {...field} disabled={isSaving} />
                          </FormControl>
                          <FormDescription>
                            Enter the transaction amount
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Employee */}
                    <FormField
                      control={form.control}
                      name="employeeId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Employee</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                            disabled={isLoadingUsers || isSaving}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select an employee" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {users.map((user) => (
                                <SelectItem key={user._id} value={user._id}>
                                  {user.fullName}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Select the employee who processed this transaction
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Transaction Type - Only for Price Audits */}
                    {auditType === 'price' && (
                      <FormField
                        control={form.control}
                        name="priceAuditTransactionType"
                        render={({ field }) => (
                          <FormItem className="space-y-3">
                            <FormLabel>Transaction Type</FormLabel>
                            <FormControl>
                              <RadioGroup
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                                className="flex flex-col space-y-2"
                                disabled={isSaving}
                              >
                                <div className="flex items-center space-x-2">
                                  <RadioGroupItem value="buy" id="price-audit-buy" />
                                  <FormLabel htmlFor="price-audit-buy" className="cursor-pointer">
                                    Buy Deal
                                  </FormLabel>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <RadioGroupItem value="pawn" id="price-audit-pawn" />
                                  <FormLabel htmlFor="price-audit-pawn" className="cursor-pointer">
                                    Pawn Loan
                                  </FormLabel>
                                </div>
                              </RadioGroup>
                            </FormControl>
                            <FormDescription>
                              Select whether this was a buy deal or pawn loan transaction
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}

                    {/* Transaction Date */}
                    <FormField
                      control={form.control}
                      name="transactionDate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Transaction Date</FormLabel>
                          <FormControl>
                            <DatePicker
                              date={field.value ? new Date(field.value) : undefined}
                              setDate={(date) => {
                                if (date && !isNaN(date.getTime())) {
                                  field.onChange(date.toISOString().split('T')[0]);
                                } else {
                                  field.onChange('');
                                }
                              }}
                              className="w-full"
                            />
                          </FormControl>
                          <FormDescription>
                            Date when the buy/pawn transaction occurred
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Audit Date */}
                    <FormField
                      control={form.control}
                      name="auditDate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Audit Date</FormLabel>
                          <FormControl>
                            <DatePicker
                              date={field.value ? new Date(field.value) : undefined}
                              setDate={(date) => {
                                if (date && !isNaN(date.getTime())) {
                                  field.onChange(date.toISOString().split('T')[0]);
                                } else {
                                  field.onChange('');
                                }
                              }}
                              className="w-full"
                            />
                          </FormControl>
                          <FormDescription>
                            Date when this audit is being performed
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <Separator className="my-6" />

                  <div className="grid grid-cols-1 gap-6">
                    {/* Items Section - Multi-item support */}
                    <div className="col-span-full">
                      <ItemsSection
                        control={form.control}
                        disabled={isSaving}
                        transactionType={auditType}
                        transactionId={transactionId}
                        transactionAmount={parseFloat(form.watch('amount') || '0')}
                      />
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleBack}
                    disabled={isSaving}
                    className="w-full md:w-auto"
                  >
                    <ArrowLeft className="mr-2 h-4 w-4" /> Back to Type Selection
                  </Button>
                  <Button
                    type="button"
                    onClick={handleContinue}
                    disabled={isSaving}
                    className="w-full md:w-auto"
                  >
                    Continue to Assessment <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </CardFooter>
              </Card>
            </>
          )}

          {/* Step 3: Item Assessment */}
          {showItemAssessment && (
            <>
              {/* Transaction Information Header */}
              <TransactionInfoHeader
                transactionId={form.getValues().transactionId}
                buyer={users.find(u => u._id === form.getValues().employeeId)?.fullName || 'Unknown'}
                cost={form.getValues().amount}
                auditType={auditType}
                transactionDate={form.getValues().transactionDate}
              />

              {/* Multi-Item Assessment Section - Only for Buy and Pawn audits */}
              {auditType !== 'price' && (
                <MultiItemAssessmentSection
                  control={form.control}
                  disabled={isSaving}
                  auditType={auditType}
                />
              )}

              {/* Pricing Section - Multi-item pricing for all audit types */}
              {auditType === 'price' ? (
                <MultiItemPriceAuditSection
                  control={form.control}
                  disabled={isSaving}
                />
              ) : (
                <MultiItemPricingSection
                  control={form.control}
                  disabled={isSaving}
                  auditType={auditType}
                />
              )}

              <div className="flex justify-between mt-6">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleBack}
                  disabled={isSaving}
                  className="w-full md:w-auto"
                >
                  <ArrowLeft className="mr-2 h-4 w-4" /> Back to Details
                </Button>
                <Button
                  type="button"
                  onClick={handleContinue}
                  disabled={isSaving}
                  className="w-full md:w-auto"
                >
                  {auditType === 'pawn' ? (
                    <>Continue to Pawn Questions <ArrowRight className="ml-2 h-4 w-4" /></>
                  ) : (
                    <>Continue to Summary <ArrowRight className="ml-2 h-4 w-4" /></>
                  )}
                </Button>
              </div>
            </>
          )}

          {/* Step 4: Pawn-Specific Questions (only for pawn audits) */}
          {showPawnQuestions && (
            <>
              <LinearResponsibleLendingSection control={form.control} disabled={isSaving} />

              <div className="flex justify-between mt-6">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleBack}
                  disabled={isSaving}
                  className="w-full md:w-auto"
                >
                  <ArrowLeft className="mr-2 h-4 w-4" /> Back to Assessment
                </Button>
                <Button
                  type="button"
                  onClick={handleContinue}
                  disabled={isSaving}
                  className="w-full md:w-auto"
                >
                  Continue to Summary <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </>
          )}

          {/* Step 4/5: Authorization & Summary */}
          {showSummary && (
            <>
              {/* Authorized Limit Check - moved to final step */}
              <AuthorizedLimitSection
                control={form.control}
                disabled={isSaving}
                auditType={auditType}
              />

              <AuditSummarySection
                control={form.control}
                disabled={isSaving}
                auditType={auditType}
                overallScore={stats.overallScore}
                failCount={stats.failCount}
                passCount={stats.passCount}
              />

              <div className="flex justify-between mt-6">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleBack}
                  disabled={isSaving}
                  className="w-full md:w-auto"
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  {auditType === 'pawn' ? 'Back to Pawn Questions' : 'Back to Assessment'}
                </Button>
                <Button
                  type="submit"
                  disabled={isSaving}
                  className="w-full md:w-auto"
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <CheckCircle2 className="mr-2 h-4 w-4" /> Create Audit
                    </>
                  )}
                </Button>
              </div>
            </>
          )}


        </form>
      </Form>
    </div>
  );
}

export default NewAuditForm;
