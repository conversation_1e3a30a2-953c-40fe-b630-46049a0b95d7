const { Service } = require('node-windows');
const path = require('path');

// Create a new service object
const svc = new Service({
  name: 'GH0ST Application',
  description: 'GH0ST Business Management Application',
  script: path.join(__dirname, 'server.js'),
  nodeOptions: [
    '--harmony',
    '--max_old_space_size=4096'
  ],
  // Environment variables
  env: [
    {
      name: "NODE_ENV",
      value: "production"
    }
  ]
});

// Listen for the "install" event
svc.on('install', function() {
  console.log('Service installed successfully!');
  svc.start();
});

// Listen for the "start" event
svc.on('start', function() {
  console.log('Service started successfully!');
});

// Listen for the "error" event
svc.on('error', function(err) {
  console.error('Service error:', err);
});

console.log('Installing the service...');
svc.install();
