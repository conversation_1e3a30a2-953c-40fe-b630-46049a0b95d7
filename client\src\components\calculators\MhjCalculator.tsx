import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { MetalPriceSettings } from '@/api/goldPricing';

interface MhjCalculatorProps {
  settings: MetalPriceSettings;
}

export function MhjCalculator({ settings }: MhjCalculatorProps) {
  const [rrpPrice, setRrpPrice] = useState<string>('');
  const [minPrice, setMinPrice] = useState<number | null>(null);
  const [maxPrice, setMaxPrice] = useState<number | null>(null);

  // Calculate prices based on RRP and settings
  useEffect(() => {
    if (!rrpPrice) {
      setMinPrice(null);
      setMaxPrice(null);
      return;
    }

    const rrp = parseFloat(rrpPrice);
    if (isNaN(rrp) || rrp <= 0) {
      setMinPrice(null);
      setMaxPrice(null);
      return;
    }

    setMinPrice(rrp * settings.mhjMinPercentage / 100);
    setMaxPrice(rrp * settings.mhjMaxPercentage / 100);
  }, [rrpPrice, settings]);

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <label className="text-sm font-medium">Full RRP Price (NZD)</label>
        <Input
          type="number"
          value={rrpPrice}
          onChange={(e) => setRrpPrice(e.target.value)}
          placeholder="Enter full retail price"
          min="0.01"
          step="0.01"
        />
      </div>

      {minPrice !== null && maxPrice !== null && (
        <div className="pt-4 border-t">
          <div className="space-y-3">
            <div>
              <div className="text-sm text-muted-foreground">Minimum Buy Price:</div>
              <div className="text-xl font-semibold">${minPrice.toFixed(2)}</div>
              <div className="text-xs text-muted-foreground">
                Based on {settings.mhjMinPercentage}% of Michael Hill Full Retail Price
              </div>
            </div>

            <div>
              <div className="text-sm text-muted-foreground">Maximum Buy Price:</div>
              <div className="text-xl font-semibold">${maxPrice.toFixed(2)}</div>
              <div className="text-xs text-muted-foreground">
                Based on {settings.mhjMaxPercentage}% of Michael Hill Full Retail Price
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}