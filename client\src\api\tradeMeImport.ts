import api from './api';

/**
 * Import active TradeMe listings
 * @param maxPages - Maximum number of pages to import
 * @returns Promise with import result
 */
export const importListings = async (maxPages = 5) => {
  try {
    const response = await api.post('/trademe/import/listings', { maxPages, type: 'active' });
    return response.data;
  } catch (error: any) {
    console.error('Error importing active TradeMe listings:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

/**
 * Import sold TradeMe listings
 * @param maxPages - Maximum number of pages to import
 * @returns Promise with import result
 */
export const importSoldListings = async (maxPages = 5) => {
  try {
    const response = await api.post('/trademe/import/listings', { maxPages, type: 'sold' });
    return response.data;
  } catch (error: any) {
    console.error('Error importing sold TradeMe listings:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

/**
 * Get import log by ID
 * @param logId - Import log ID
 * @returns Promise with import log
 */
export const getImportLog = async (logId: string) => {
  try {
    const response = await api.get(`/trademe/import/logs/${logId}`);
    return response.data;
  } catch (error: any) {
    console.error('Error getting import log:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

/**
 * Get items from an import log
 * @param logId - Import log ID
 * @param page - Page number
 * @param limit - Items per page
 * @returns Promise with items and pagination data
 */
export const getImportLogItems = async (logId: string, page = 1, limit = 50) => {
  try {
    const response = await api.get(`/trademe/import/logs/${logId}/items`, {
      params: { page, limit }
    });
    return response.data;
  } catch (error: any) {
    console.error('Error getting import log items:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

/**
 * Save an item from the import log to the database
 * @param logId - Import log ID
 * @param itemIndex - Item index in the fetchedItems array
 * @param itemData - Updated item data
 * @returns Promise with saved item
 */
export const saveImportedItem = async (logId: string, itemIndex: number, itemData: any) => {
  try {
    const response = await api.post('/trademe/import/save-item', {
      logId,
      itemIndex,
      itemData
    });
    return response.data;
  } catch (error: any) {
    console.error('Error saving imported item:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

/**
 * Skip an item in the import log
 * @param logId - Import log ID
 * @param itemIndex - Item index in the fetchedItems array
 * @returns Promise with result
 */
export const skipImportedItem = async (logId: string, itemIndex: number) => {
  try {
    const response = await api.post('/trademe/import/skip-item', {
      logId,
      itemIndex
    });
    return response.data;
  } catch (error: any) {
    console.error('Error skipping imported item:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

/**
 * Update an imported item
 * @param id - Item ID
 * @param data - Updated item data
 * @returns Promise with updated item
 */
export const updateImportedItem = async (id: string, data: any) => {
  try {
    const response = await api.put(`/trademe/import/items/${id}`, data);
    return response.data;
  } catch (error: any) {
    console.error('Error updating imported item:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

/**
 * Batch update imported items
 * @param ids - Array of item IDs
 * @param data - Updated item data
 * @returns Promise with result
 */
export const batchUpdateImportedItems = async (ids: string[], data: any) => {
  try {
    const response = await api.put('/trademe/import/items/batch', { ids, data });
    return response.data;
  } catch (error: any) {
    console.error('Error batch updating imported items:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

/**
 * Check if a stock code is already in use by another item
 * @param stockCode - Stock code to check
 * @param currentItemId - ID of the current item (to exclude from check)
 * @returns Promise with result
 */
export const checkDuplicateStockCode = async (stockCode: string, currentItemId: string) => {
  try {
    const response = await api.get(`/trademe/import/check-stockcode`, {
      params: { stockCode, currentItemId }
    });
    return response.data;
  } catch (error: any) {
    console.error('Error checking duplicate stock code:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

/**
 * Get import logs
 * @param page - Page number
 * @param limit - Items per page
 * @returns Promise with import logs
 */
export const getImportLogs = async (page = 1, limit = 10) => {
  try {
    const response = await api.get(`/trademe/import/logs?page=${page}&limit=${limit}`);
    return response.data;
  } catch (error: any) {
    console.error('Error getting import logs:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

/**
 * Get imported items
 * @param page - Page number
 * @param limit - Items per page
 * @param filters - Filters to apply
 * @returns Promise with imported items
 */
export const getImportedItems = async (page = 1, limit = 10, filters = {}) => {
  try {
    // If needsUpdate is true, use the new endpoint
    if (filters && (filters as any).needsUpdate === 'true') {
      const response = await api.get('/trademe/import/items-needing-update', {
        params: {
          page,
          limit
        }
      });
      return response.data;
    } else {
      // Otherwise use the original endpoint
      const response = await api.get('/trademe/import/items', {
        params: {
          page,
          limit,
          ...filters
        }
      });
      return response.data;
    }
  } catch (error: any) {
    console.error('Error getting imported items:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

/**
 * Get all locations
 * @returns Promise with locations
 */
export const getLocations = async () => {
  try {
    const response = await api.get('/locations');
    return response.data;
  } catch (error: any) {
    console.error('Error getting locations:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

/**
 * Get in-progress imports
 * @returns Promise with in-progress imports
 */
export const getInProgressImports = async () => {
  try {
    // This function checks localStorage for any saved import progress
    const inProgressImports = [];

    // Scan localStorage for keys that match the pattern
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('trademe-import-progress-')) {
        try {
          // Extract the import log ID from the key
          const importLogId = key.replace('trademe-import-progress-', '');
          const savedProgressJson = localStorage.getItem(key);

          if (savedProgressJson) {
            const savedProgress = JSON.parse(savedProgressJson);

            // Check if progress is recent (within 7 days)
            const isRecent = (Date.now() - savedProgress.timestamp) < 7 * 24 * 60 * 60 * 1000;
            if (!isRecent) {
              // Remove old progress
              localStorage.removeItem(key);
              continue;
            }

            // Try to get the import log details
            try {
              const logResult = await getImportLog(importLogId);
              if (logResult.success) {
                // Check if the import is complete based on the log data
                const totalProcessed = savedProgress.completedItems.length + savedProgress.skippedItems.length;
                const totalItems = logResult.importLog?.stats?.itemsProcessed || 0;

                // If all items are processed, consider it complete
                if (totalItems > 0 && totalProcessed >= totalItems) {
                  console.log(`Import ${importLogId} is complete (${totalProcessed}/${totalItems}), removing from localStorage`);
                  localStorage.removeItem(key);
                  continue;
                }

                inProgressImports.push({
                  importLogId,
                  progress: savedProgress,
                  importLog: logResult.importLog,
                  timestamp: savedProgress.timestamp
                });
              }
            } catch (error) {
              console.error(`Error fetching import log ${importLogId}:`, error);
              // Keep the progress even if we can't fetch the log
              inProgressImports.push({
                importLogId,
                progress: savedProgress,
                timestamp: savedProgress.timestamp
              });
            }
          }
        } catch (error) {
          console.error(`Error parsing progress for ${key}:`, error);
        }
      }
    }

    // Sort by most recent first
    inProgressImports.sort((a, b) => b.timestamp - a.timestamp);

    return {
      success: true,
      inProgressImports
    };
  } catch (error: any) {
    console.error('Error getting in-progress imports:', error);
    return {
      success: false,
      error: error.message,
      inProgressImports: []
    };
  }
};
