import { useState, FormEvent, useMemo, useCallback } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';

import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { User, updateUser, toggleUserStatus, createUser, resetUserPassword } from '@/api/user';
import { useToast } from '@/hooks/useToast';
import { format } from 'date-fns';
import { Plus, Search, Key, ArrowUpDown, UserCog, Power, PowerOff } from 'lucide-react';

interface UserManagementTableProps {
  users: User[];  // Using the User interface
  onUserUpdated: () => void;
  currentUserRole?: 'admin' | 'manager' | 'employee';
}

export function UserManagementTable({ users, onUserUpdated, currentUserRole = 'admin' }: UserManagementTableProps) {
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [resettingUser, setResettingUser] = useState<User | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isResetPasswordDialogOpen, setIsResetPasswordDialogOpen] = useState(false);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [autoGeneratePassword, setAutoGeneratePassword] = useState(false);
  const [passwordError, setPasswordError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<keyof User>('fullName');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const { toast } = useToast();

  // Helper functions to check permissions
  const canEditUser = (userToEdit: User) => {
    if (currentUserRole === 'admin') return true;
    if (currentUserRole === 'manager') {
      // Managers can edit employees and other managers, but not admins
      return userToEdit.role !== 'admin';
    }
    return false;
  };

  const canToggleUserStatus = (userToToggle: User) => {
    if (currentUserRole === 'admin') return true;
    if (currentUserRole === 'manager') {
      // Managers can toggle status for employees and other managers, but not admins
      return userToToggle.role !== 'admin';
    }
    return false;
  };

  const canResetPassword = (userToReset: User) => {
    if (currentUserRole === 'admin') return true;
    if (currentUserRole === 'manager') {
      // Managers can reset passwords for employees and other managers, but not admins
      return userToReset.role !== 'admin';
    }
    return false;
  };

  // Handle sorting when a column header is clicked
  const handleSort = useCallback((field: keyof User) => {
    if (sortField === field) {
      // If already sorting by this field, toggle direction
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // Otherwise, sort by the new field in ascending order
      setSortField(field);
      setSortDirection('asc');
    }
  }, [sortField, sortDirection]);

  // Filter and sort users
  const filteredAndSortedUsers = useMemo(() => {
    // First, filter users based on search term
    const filtered = users.filter(user => {
      const searchLower = searchTerm.toLowerCase();
      return (
        user.fullName?.toLowerCase().includes(searchLower) ||
        user.username?.toLowerCase().includes(searchLower) ||
        user.email?.toLowerCase().includes(searchLower) ||
        user.role?.toLowerCase().includes(searchLower)
      );
    });

    // Then, sort the filtered users
    return [...filtered].sort((a, b) => {
      const aValue = a[sortField];
      const bValue = b[sortField];

      // Handle null or undefined values
      if (!aValue && !bValue) return 0;
      if (!aValue) return sortDirection === 'asc' ? -1 : 1;
      if (!bValue) return sortDirection === 'asc' ? 1 : -1;

      // Compare values based on their type
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortDirection === 'asc'
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      // For dates
      if (aValue instanceof Date && bValue instanceof Date) {
        return sortDirection === 'asc'
          ? aValue.getTime() - bValue.getTime()
          : bValue.getTime() - aValue.getTime();
      }

      // For numbers and other types
      return sortDirection === 'asc'
        ? (aValue > bValue ? 1 : -1)
        : (bValue > aValue ? 1 : -1);
    });
  }, [users, searchTerm, sortField, sortDirection]);

  const handleEditUser = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!editingUser) return;

    try {
      const formData = new FormData(e.currentTarget);
      const data = {
        username: formData.get('username') as string,
        email: formData.get('email') as string,
        fullName: formData.get('fullName') as string,
        role: formData.get('role') as 'admin' | 'manager' | 'employee',
      };

      await updateUser(editingUser._id, data);
      setIsEditDialogOpen(false);
      onUserUpdated();
      toast({
        title: "Success",
        description: "User updated successfully",
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || 'An error occurred',
        variant: "destructive",
      });
    }
  };

  const generateRandomPassword = () => {
    // Generate a more secure random password with letters, numbers, and special characters
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let result = '';
    for (let i = 0; i < 12; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  };

  const [generatedPassword, setGeneratedPassword] = useState('');
  const [showGeneratedPassword, setShowGeneratedPassword] = useState(false);

  const handleResetPassword = async () => {
    if (!resettingUser) return;

    try {
      // Clear any previous errors
      setPasswordError('');

      let passwordToUse = newPassword;

      // If auto-generate is selected, generate a random password
      if (autoGeneratePassword) {
        passwordToUse = generateRandomPassword();
        setGeneratedPassword(passwordToUse);
      } else {
        // Validate passwords if manually entered
        if (!newPassword) {
          setPasswordError('Password is required');
          return;
        }

        if (newPassword.length < 6) {
          setPasswordError('Password must be at least 6 characters long');
          return;
        }

        if (newPassword !== confirmPassword) {
          setPasswordError('Passwords do not match');
          return;
        }
      }

      await resetUserPassword(resettingUser._id, passwordToUse);

      if (autoGeneratePassword) {
        // Show the generated password to the user
        setShowGeneratedPassword(true);
      } else {
        // Close the dialog if not showing generated password
        setIsResetPasswordDialogOpen(false);
        toast({
          title: "Success",
          description: "Password reset successfully",
        });

        // Clear the password fields after successful reset
        setNewPassword('');
        setConfirmPassword('');
        setAutoGeneratePassword(false);
        setPasswordError('');
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || 'An error occurred',
        variant: "destructive",
      });
    }
  };

  const handleToggleStatus = async (user: User) => {
    try {
      await toggleUserStatus(user._id);
      onUserUpdated();
      toast({
        title: "Success",
        description: `User ${user.isActive ? 'deactivated' : 'activated'} successfully`,
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || 'An error occurred',
        variant: "destructive",
      });
    }
  };

  const handleCreateUser = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    try {
      const formData = new FormData(e.currentTarget);
      const data = {
        email: formData.get('email') as string,
        username: formData.get('username') as string,
        fullName: formData.get('fullName') as string,
        password: formData.get('password') as string,
        role: formData.get('role') as 'admin' | 'manager' | 'employee',
      };

      await createUser(data);
      setIsCreateDialogOpen(false);
      onUserUpdated();
      toast({
        title: "Success",
        description: "User created successfully",
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || 'An error occurred',
        variant: "destructive",
      });
    }
  };

  return (
    <>
      <Card className="w-full">
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row justify-between items-center mb-6 gap-4">
            <div className="relative w-full md:w-1/3">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search users..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-9 w-full"
              />
            </div>
            <Button
              onClick={() => setIsCreateDialogOpen(true)}
              className="flex items-center gap-1 w-full md:w-auto"
            >
              <Plus className="h-4 w-4" /> Add New User
            </Button>
          </div>

          {filteredAndSortedUsers.length === 0 ? (
            <div className="text-center py-6 text-muted-foreground">
              {searchTerm ? 'No users match your search' : 'No users found'}
            </div>
          ) : (
            <div className="rounded-md border overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead
                      onClick={() => handleSort('fullName')}
                      className="cursor-pointer hover:bg-muted/50 transition-colors"
                    >
                      <div className="flex items-center">
                        Full Name
                        {sortField === 'fullName' && (
                          <ArrowUpDown className={`ml-2 h-4 w-4 ${sortDirection === 'desc' ? 'rotate-180 transform' : ''}`} />
                        )}
                      </div>
                    </TableHead>
                    <TableHead
                      onClick={() => handleSort('username')}
                      className="cursor-pointer hover:bg-muted/50 transition-colors"
                    >
                      <div className="flex items-center">
                        Username
                        {sortField === 'username' && (
                          <ArrowUpDown className={`ml-2 h-4 w-4 ${sortDirection === 'desc' ? 'rotate-180 transform' : ''}`} />
                        )}
                      </div>
                    </TableHead>
                    <TableHead
                      onClick={() => handleSort('email')}
                      className="cursor-pointer hover:bg-muted/50 transition-colors"
                    >
                      <div className="flex items-center">
                        Email
                        {sortField === 'email' && (
                          <ArrowUpDown className={`ml-2 h-4 w-4 ${sortDirection === 'desc' ? 'rotate-180 transform' : ''}`} />
                        )}
                      </div>
                    </TableHead>
                    <TableHead
                      onClick={() => handleSort('role')}
                      className="cursor-pointer hover:bg-muted/50 transition-colors"
                    >
                      <div className="flex items-center">
                        Role
                        {sortField === 'role' && (
                          <ArrowUpDown className={`ml-2 h-4 w-4 ${sortDirection === 'desc' ? 'rotate-180 transform' : ''}`} />
                        )}
                      </div>
                    </TableHead>
                    <TableHead
                      onClick={() => handleSort('isActive')}
                      className="cursor-pointer hover:bg-muted/50 transition-colors"
                    >
                      <div className="flex items-center">
                        Status
                        {sortField === 'isActive' && (
                          <ArrowUpDown className={`ml-2 h-4 w-4 ${sortDirection === 'desc' ? 'rotate-180 transform' : ''}`} />
                        )}
                      </div>
                    </TableHead>
                    <TableHead
                      onClick={() => handleSort('lastLoginAt')}
                      className="cursor-pointer hover:bg-muted/50 transition-colors"
                    >
                      <div className="flex items-center">
                        Last Login
                        {sortField === 'lastLoginAt' && (
                          <ArrowUpDown className={`ml-2 h-4 w-4 ${sortDirection === 'desc' ? 'rotate-180 transform' : ''}`} />
                        )}
                      </div>
                    </TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredAndSortedUsers.map((user) => (
                    <TableRow key={user._id} className={!user.isActive ? 'opacity-60' : ''}>
                      <TableCell className="font-medium">{user.fullName}</TableCell>
                      <TableCell>{user.username || '—'}</TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>
                        <Badge variant="outline" className="capitalize">
                          {user.role}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={user.isActive ? "success" : "secondary"}>
                          {user.isActive ? 'Active' : 'Inactive'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {user.lastLoginAt
                          ? format(new Date(user.lastLoginAt), 'PPp')
                          : 'Never'}
                      </TableCell>
                      <TableCell className="text-right space-x-2">
                        {canEditUser(user) && (
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => {
                              setEditingUser(user);
                              setIsEditDialogOpen(true);
                            }}
                            className="h-8 w-8"
                            title="Edit User"
                          >
                            <UserCog className="h-4 w-4" />
                          </Button>
                        )}
                        {canToggleUserStatus(user) && (
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleToggleStatus(user)}
                            className="h-8 w-8"
                            title={user.isActive ? 'Deactivate User' : 'Activate User'}
                          >
                            {user.isActive ? (
                              <PowerOff className="h-4 w-4 text-destructive" />
                            ) : (
                              <Power className="h-4 w-4 text-green-500" />
                            )}
                          </Button>
                        )}
                        {canResetPassword(user) && (
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => {
                              setResettingUser(user);
                              setIsResetPasswordDialogOpen(true);
                            }}
                            className="h-8 w-8"
                            title="Reset Password"
                          >
                            <Key className="h-4 w-4" />
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit User</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleEditUser}>
            <div className="space-y-4">
              <div>
                <Label htmlFor="fullName">Full Name</Label>
                <Input
                  id="fullName"
                  name="fullName"
                  defaultValue={editingUser?.fullName}
                  required
                />
              </div>
              <div>
                <Label htmlFor="username">Username</Label>
                <Input
                  id="username"
                  name="username"
                  defaultValue={editingUser?.username || ''}
                />
              </div>
              <div>
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  defaultValue={editingUser?.email}
                  required
                />
              </div>
              <div>
                <Label htmlFor="role">Role</Label>
                <Select name="role" defaultValue={editingUser?.role}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="admin">Admin</SelectItem>
                    <SelectItem value="manager">Manager</SelectItem>
                    <SelectItem value="employee">Employee</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter className="mt-6">
              <Button type="button" variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit">Save Changes</Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      <Dialog open={isResetPasswordDialogOpen} onOpenChange={(open) => {
        setIsResetPasswordDialogOpen(open);
        if (!open) {
          // Reset form state when dialog is closed
          setNewPassword('');
          setConfirmPassword('');
          setAutoGeneratePassword(false);
          setPasswordError('');
          setGeneratedPassword('');
          setShowGeneratedPassword(false);
        }
      }}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reset User Password</DialogTitle>
            <DialogDescription>
              Reset password for {resettingUser?.fullName}.
            </DialogDescription>
          </DialogHeader>

          {showGeneratedPassword ? (
            <div className="space-y-6 py-4">
              <div className="rounded-md bg-muted p-4 border">
                <h4 className="text-sm font-medium mb-2">Password has been reset to:</h4>
                <div className="flex items-center justify-between bg-background p-3 rounded border">
                  <code className="text-sm font-mono">{generatedPassword}</code>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      navigator.clipboard.writeText(generatedPassword);
                      toast({
                        title: "Copied",
                        description: "Password copied to clipboard",
                      });
                    }}
                  >
                    Copy
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground mt-2">
                  Please save this password in a secure location. It will not be shown again.
                </p>
              </div>
              <DialogFooter>
                <Button onClick={() => {
                  setIsResetPasswordDialogOpen(false);
                  setNewPassword('');
                  setConfirmPassword('');
                  setAutoGeneratePassword(false);
                  setPasswordError('');
                  setGeneratedPassword('');
                  setShowGeneratedPassword(false);
                }}>
                  Close
                </Button>
              </DialogFooter>
            </div>
          ) : (
            <>
              <div className="space-y-4 py-4">
                <div className="flex items-center space-x-2 mb-4">
                  <input
                    type="checkbox"
                    id="autoGeneratePassword"
                    checked={autoGeneratePassword}
                    onChange={(e) => setAutoGeneratePassword(e.target.checked)}
                    className="rounded border-gray-300 text-primary focus:ring-primary"
                  />
                  <Label htmlFor="autoGeneratePassword" className="cursor-pointer">
                    Auto-generate secure password
                  </Label>
                </div>

                {!autoGeneratePassword && (
                  <>
                    <div className="space-y-2">
                      <Label htmlFor="newPassword">New Password</Label>
                      <Input
                        id="newPassword"
                        type="password"
                        placeholder="Enter new password"
                        value={newPassword}
                        onChange={(e) => setNewPassword(e.target.value)}
                        disabled={autoGeneratePassword}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="confirmPassword">Confirm Password</Label>
                      <Input
                        id="confirmPassword"
                        type="password"
                        placeholder="Confirm new password"
                        value={confirmPassword}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        disabled={autoGeneratePassword}
                      />
                    </div>
                  </>
                )}

                {passwordError && (
                  <div className="text-sm font-medium text-destructive">{passwordError}</div>
                )}
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => {
                  setIsResetPasswordDialogOpen(false);
                  setNewPassword('');
                  setConfirmPassword('');
                  setAutoGeneratePassword(false);
                  setPasswordError('');
                }}>
                  Cancel
                </Button>
                <Button onClick={handleResetPassword}>
                  {autoGeneratePassword ? 'Generate & Reset' : 'Reset Password'}
                </Button>
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>

      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New User</DialogTitle>
            <DialogDescription>
              Add a new user to the system with a password. No email setup is required.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleCreateUser}>
            <div className="space-y-4">
              <div>
                <Label htmlFor="createFullName">Full Name</Label>
                <Input
                  id="createFullName"
                  name="fullName"
                  required
                  placeholder="Enter full name"
                />
              </div>
              <div>
                <Label htmlFor="createUsername">Username (optional)</Label>
                <Input
                  id="createUsername"
                  name="username"
                  placeholder="Enter username"
                />
              </div>
              <div>
                <Label htmlFor="createEmail">Email</Label>
                <Input
                  id="createEmail"
                  name="email"
                  type="email"
                  required
                  placeholder="Enter email address"
                />
              </div>
              <div>
                <Label htmlFor="createPassword">Password</Label>
                <Input
                  id="createPassword"
                  name="password"
                  type="password"
                  required
                  placeholder="Enter password"
                />
              </div>
              <div>
                <Label htmlFor="createRole">Role</Label>
                <Select name="role" defaultValue="employee">
                  <SelectTrigger>
                    <SelectValue placeholder="Select role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="admin">Admin</SelectItem>
                    <SelectItem value="manager">Manager</SelectItem>
                    <SelectItem value="employee">Employee</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter className="mt-6">
              <Button type="button" variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit">Create User</Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </>
  );
}






