import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { cn } from '@/lib/utils';

export function AuditNavigation() {
  const location = useLocation();
  const { user } = useAuth();
  const canAccessAdminRoutes = user?.role === 'admin' || user?.role === 'manager';

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  return (
    <div className="flex flex-wrap gap-2 mb-6">
      <Link
        to="/buys/audits"
        className={cn(
          "px-4 py-2 rounded-md text-sm font-medium transition-colors",
          isActive('/buys/audits')
            ? "bg-primary text-primary-foreground"
            : "bg-muted hover:bg-muted/80"
        )}
      >
        Dashboard
      </Link>
      {canAccessAdminRoutes && (
        <Link
          to="/buys/audits/new"
          className={cn(
            "px-4 py-2 rounded-md text-sm font-medium transition-colors",
            isActive('/buys/audits/new')
              ? "bg-primary text-primary-foreground"
              : "bg-muted hover:bg-muted/80"
          )}
        >
          New Audit
        </Link>
      )}
      <Link
        to="/buys/audits/flagged"
        className={cn(
          "px-4 py-2 rounded-md text-sm font-medium transition-colors",
          isActive('/buys/audits/flagged')
            ? "bg-primary text-primary-foreground"
            : "bg-muted hover:bg-muted/80"
        )}
      >
        Flagged Audits
      </Link>
      {canAccessAdminRoutes && (
        <Link
          to="/buys/audits/reporting"
          className={cn(
            "px-4 py-2 rounded-md text-sm font-medium transition-colors",
            isActive('/buys/audits/reporting')
              ? "bg-primary text-primary-foreground"
              : "bg-muted hover:bg-muted/80"
          )}
        >
          Reporting
        </Link>
      )}
    </div>
  );
}
