const express = require('express');
const router = express.Router();
const { requireUser } = require('./middleware/auth');
const tradeMeSyncOrchestrator = require('../services/tradeMeSyncOrchestrator');

/**
 * @route GET /api/trademe/sync/status
 * @description Get the status of the TradeMe background sync service
 * @access Private (admin/manager only)
 */
router.get('/status', requireUser, async (req, res) => {
  try {
    // Check if user has permission (admin or manager)
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to view sync status',
      });
    }

    const status = tradeMeSyncOrchestrator.getSyncStatus();
    return res.status(200).json({
      success: true,
      status
    });
  } catch (error) {
    console.error('Error getting TradeMe sync status:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route POST /api/trademe/sync/run
 * @description Manually trigger a TradeMe sync (legacy endpoint)
 * @access Private (admin/manager only)
 */
router.post('/run', requireUser, async (req, res) => {
  try {
    // Check if user has permission (admin or manager)
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to run manual sync',
      });
    }

    // Get parameters from request body or use defaults
    const { syncType = 'listings', maxPages = 10 } = req.body;

    if (!['listings', 'questions', 'feedback', 'categories'].includes(syncType)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid sync type. Must be one of: listings, questions, feedback, categories'
      });
    }

    // Start the sync in the background
    const syncPromise = tradeMeSyncOrchestrator.performSync({
      syncType,
      maxPagesPerEndpoint: parseInt(maxPages)
    });

    // Return immediately to the client
    return res.status(200).json({
      success: true,
      message: `${syncType} sync started`,
      syncType
    });

    // Let the sync continue in the background
    syncPromise.then(result => {
      console.log(`Background ${syncType} sync completed:`, result.success ? 'success' : 'failed');
    }).catch(error => {
      console.error(`Error in background ${syncType} sync:`, error);
    });
  } catch (error) {
    console.error('Error running manual TradeMe sync:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route POST /api/trademe/sync/optimized-categories
 * @description Optimized category sync that checks root categories ETag first
 * @access Private (admin/manager only)
 */
router.post('/optimized-categories', requireUser, async (req, res) => {
  try {
    // Check if user has permission (admin or manager)
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to run manual sync',
      });
    }

    const tradeMeCategoryService = require('../services/tradeMeCategoryService');

    console.log('Triggering optimized category sync...');

    // Start the sync in the background
    const syncPromise = tradeMeCategoryService.optimizedSync();

    // Return immediately to the client
    return res.status(200).json({
      success: true,
      message: 'Optimized category sync started'
    });

    // Let the sync continue in the background
    syncPromise.then(result => {
      console.log('Background optimized category sync completed:', result.success ? 'success' : 'failed');
    }).catch(error => {
      console.error('Error in background optimized category sync:', error);
    });
  } catch (error) {
    console.error('Error running optimized category sync:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route POST /api/trademe/sync/trigger
 * @description Manually trigger a TradeMe sync
 * @access Private (admin/manager only)
 */
router.post('/trigger', requireUser, async (req, res) => {
  try {
    // Check if user has permission (admin or manager)
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to run manual sync',
      });
    }

    // Get parameters from request body or use defaults
    const { syncType = 'listings', maxPagesPerEndpoint = 10 } = req.body;

    if (!['listings', 'questions', 'feedback', 'categories'].includes(syncType)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid sync type. Must be one of: listings, questions, feedback, categories'
      });
    }

    // For feedback sync, use an extremely high page limit by default if not specified
    // This ensures we can retrieve all feedback items (up to 20,000 items)
    let maxPages = maxPagesPerEndpoint;

    if (syncType === 'feedback') {
      // If the client specified a high value, use it, otherwise use 1000 as a default for feedback
      // This allows retrieving up to 20,000 feedback items with a typical page size of 20
      maxPages = maxPagesPerEndpoint > 100 ? maxPagesPerEndpoint : 1000;
      console.log(`Setting maxPages to ${maxPages} for feedback sync to handle up to 20,000 feedback items`);
    }

    console.log(`Triggering manual ${syncType} sync with maxPagesPerEndpoint=${maxPages}...`);

    // Start the sync in the background
    const syncPromise = tradeMeSyncOrchestrator.performSync({
      syncType,
      maxPagesPerEndpoint: parseInt(maxPages)
    });

    // Return immediately to the client
    return res.status(200).json({
      success: true,
      message: `${syncType} sync started`,
      syncType,
      maxPagesPerEndpoint: maxPages
    });

    // Let the sync continue in the background
    syncPromise.then(result => {
      console.log(`Background ${syncType} sync completed:`, result.success ? 'success' : 'failed');
    }).catch(error => {
      console.error(`Error in background ${syncType} sync:`, error);
    });
  } catch (error) {
    console.error('Error triggering manual TradeMe sync:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/trademe/sync/logs
 * @description Get sync logs
 * @access Private (Admin, Manager)
 */
router.get('/logs', requireUser, async (req, res) => {
  try {
    // Check if user has permission (admin or manager)
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to view sync logs'
      });
    }

    const { limit, syncType } = req.query;
    const logs = await tradeMeSyncOrchestrator.getSyncLogs({
      limit: limit ? parseInt(limit) : 20,
      syncType
    });

    return res.status(200).json({
      success: true,
      logs
    });
  } catch (error) {
    console.error('Error getting TradeMe sync logs:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route POST /api/trademe/sync/stop
 * @description Stop all sync operations
 * @access Private (Admin, Manager)
 */
router.post('/stop', requireUser, async (req, res) => {
  try {
    // Check if user has permission (admin or manager)
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to stop sync operations'
      });
    }

    tradeMeSyncOrchestrator.stopAllSyncs();

    return res.status(200).json({
      success: true,
      message: 'All sync operations stopped'
    });
  } catch (error) {
    console.error('Error stopping TradeMe sync operations:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = router;
