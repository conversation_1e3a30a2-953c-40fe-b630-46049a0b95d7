export const updateSaleStatus = async (listingId: string, status: string): Promise<{ success: boolean }> => {
  const response = await authenticatedFetch(`/api/listings/${listingId}/status`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ status }),
  });

  if (!response.ok) {
    throw new Error('Failed to update sale status');
  }

  return { success: true };
};