const express = require('express');
const router = express.Router();
const { requireUser } = require('./middleware/auth');
const tradeMeCategoryService = require('../services/tradeMeCategoryService');

/**
 * @route GET /api/trademe/categories
 * @description Get TradeMe categories
 * @access Private
 */
router.get('/', requireUser, async (req, res) => {
  try {
    const { parentId } = req.query;

    const result = await tradeMeCategoryService.getCategories({
      parentId: parentId === 'null' ? null : parentId
    });

    return res.status(200).json(result);
  } catch (error) {
    console.error('Error getting TradeMe categories:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/trademe/categories/search
 * @description Search TradeMe categories
 * @access Private
 */
router.get('/search', requireUser, async (req, res) => {
  try {
    const { query } = req.query;

    const result = await tradeMeCategoryService.searchCategories(query);

    return res.status(200).json(result);
  } catch (error) {
    console.error('Error searching TradeMe categories:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/trademe/categories/suggestions
 * @description Get category suggestions from TradeMe API based on search term
 * @access Private
 */
router.get('/suggestions', requireUser, async (req, res) => {
  try {
    const { searchString } = req.query;

    if (!searchString || searchString.length < 2) {
      return res.status(400).json({
        success: false,
        error: 'Search string must be at least 2 characters'
      });
    }

    const result = await tradeMeCategoryService.getCategorySuggestions(searchString);

    return res.status(200).json(result);
  } catch (error) {
    console.error('Error getting TradeMe category suggestions:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/trademe/categories/etags
 * @description Check ETags status
 * @access Private
 */
router.get('/etags', requireUser, async (req, res) => {
  try {
    const result = await tradeMeCategoryService.checkETags();
    return res.status(200).json(result);
  } catch (error) {
    console.error('Error checking ETags:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route POST /api/trademe/categories/etags/fix
 * @description Fix ETags in the database
 * @access Private
 */
router.post('/etags/fix', requireUser, async (req, res) => {
  try {
    const result = await tradeMeCategoryService.fixETags();
    return res.status(200).json(result);
  } catch (error) {
    console.error('Error fixing ETags:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route POST /api/trademe/categories/etags/migrate
 * @description Migrate ETags from TradeMeETag collection to TradeMeCategory
 * @access Private
 */
router.post('/etags/migrate', requireUser, async (req, res) => {
  try {
    const result = await tradeMeCategoryService.migrateETags();
    return res.status(200).json(result);
  } catch (error) {
    console.error('Error migrating ETags:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route POST /api/trademe/categories/etags/migrate-details
 * @description Migrate detailsEtag to etag for all categories
 * @access Private
 */
router.post('/etags/migrate-details', requireUser, async (req, res) => {
  try {
    const result = await tradeMeCategoryService.migrateDetailsTags();
    return res.status(200).json(result);
  } catch (error) {
    console.error('Error migrating detailsEtag:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/trademe/categories/success-fees
 * @description Check for categories with success fees
 * @access Private
 */
router.get('/success-fees', requireUser, async (req, res) => {
  try {
    const result = await tradeMeCategoryService.checkSuccessFees();
    return res.status(200).json(result);
  } catch (error) {
    console.error('Error checking success fees:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/trademe/categories/payment-methods
 * @description Check for categories with payment methods
 * @access Private
 */
router.get('/payment-methods', requireUser, async (req, res) => {
  try {
    const result = await tradeMeCategoryService.checkPaymentMethods();
    return res.status(200).json(result);
  } catch (error) {
    console.error('Error checking payment methods:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route POST /api/trademe/categories/payment-methods/update
 * @description Update payment methods for all categories
 * @access Private
 */
router.post('/payment-methods/update', requireUser, async (req, res) => {
  try {
    const result = await tradeMeCategoryService.updatePaymentMethods();
    return res.status(200).json(result);
  } catch (error) {
    console.error('Error updating payment methods:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/trademe/categories/:categoryId
 * @description Get a TradeMe category by ID
 * @access Private
 */
router.get('/:categoryId', requireUser, async (req, res) => {
  try {
    const { categoryId } = req.params;

    const result = await tradeMeCategoryService.getCategoryById(categoryId);

    return res.status(200).json(result);
  } catch (error) {
    console.error('Error getting TradeMe category:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = router;
