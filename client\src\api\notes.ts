import api from './api';

export interface ItemNote {
  _id: string;
  itemId: string;
  userId: {
    _id: string;
    email: string;
    username?: string;
    name?: string;
  };
  content: string;
  createdAt: string;
}

// Description: Get notes for an item
// Endpoint: GET /api/notes/item/:itemId
// Request: {}
// Response: { success: boolean, data: ItemNote[] }
export const getItemNotes = async (itemId: string) => {
  try {
    const response = await api.get(`/notes/item/${itemId}`);
    return response.data;
  } catch (error: any) {
    console.error(error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Description: Add a note to an item
// Endpoint: POST /api/notes/item/:itemId
// Request: { content: string }
// Response: { success: boolean, data: ItemNote }
export const addItemNote = async (itemId: string, content: string) => {
  try {
    const response = await api.post(`/notes/item/${itemId}`, { content });
    return response.data;
  } catch (error: any) {
    console.error(error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Description: Delete a note
// Endpoint: DELETE /api/notes/:noteId
// Request: {}
// Response: { success: boolean, message: string }
export const deleteItemNote = async (noteId: string) => {
  try {
    const response = await api.delete(`/notes/${noteId}`);
    return response.data;
  } catch (error: any) {
    console.error(error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};