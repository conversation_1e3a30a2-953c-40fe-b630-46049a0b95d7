# TradeMe Implementation

## Overview

The TradeMe integration allows users to list items on TradeMe, track sales, and manage questions. The implementation follows a "one item, one database record" approach, where each physical item is represented by a single document in the database, regardless of how many times it has been listed on TradeMe.

## Architecture

The TradeMe integration is split into several main components:

1. **Account Management** - Handles TradeMe account connection, authentication, and settings
2. **Item Management** - Handles creating, updating, listing, and tracking TradeMe items
3. **Sync Services** - Handles synchronization between TradeMe API and our local database
4. **Templates** - Manages reusable templates for questions, footers, and shipping options
5. **Feedback & Categories** - Handles TradeMe feedback and category management

## Models

### TradeMeItems

The `TradeMeItems` model represents a physical item that can be listed on TradeMe. It includes:

- Basic item information (title, description, price, etc.)
- Current listing information (currentListingId, status, etc.)
- Listing history (all previous listings of this item)
- Questions and answers
- Notes
- Fault tracking
- Location tracking
- Metadata (createdBy, createdAt, updatedAt, etc.)

### TradeMeItemAudit

The `TradeMeItemAudit` model tracks all changes to TradeMe items for audit purposes. It includes:

- Reference to the TradeMe item
- Action performed (create, update, delete, etc.)
- User who performed the action
- Timestamp
- Details of the change
- Previous and new values

### TradeMeSettings

The `TradeMeSettings` model stores TradeMe account settings, including:

- Environment (production or sandbox)
- Connection status
- Username
- Access token and secret
- Last updated timestamp

### TradeMeTemplates

The `TradeMeTemplates` model provides a unified way to manage templates for various TradeMe-related features:

- Question templates - Pre-defined responses to common buyer questions
- Footer templates - Standard text to include at the bottom of listings
- Shipping templates - Standard shipping options and prices

### TradeMeFeedback

The `TradeMeFeedback` model stores feedback received from TradeMe buyers, including:

- Feedback text
- Rating
- Buyer information
- Associated listing
- Timestamp

### TradeMeCategory

The `TradeMeCategory` model stores TradeMe category information, including:

- Category ID
- Name
- Path
- Parent category
- Subcategories
- Attributes

## Services

### Account Management

#### tradeMeService.js

Handles TradeMe API interactions, account connection, and authentication. It includes functions for:

- Getting TradeMe settings
- Connecting and disconnecting TradeMe accounts
- Creating listings on TradeMe
- Fetching listings from TradeMe
- Answering questions on TradeMe
- Checking token permissions
- Getting member summary

### Item Management

#### tradeMeItemsService.js

Handles CRUD operations for TradeMe items in our database. It includes functions for:

- Creating and updating items
- Listing and relisting items on TradeMe
- Managing questions and answers
- Tracking item history and audits
- Adding and managing notes

### Sync Services

#### tradeMeItemsSyncService.js

Handles synchronization between TradeMe API and our local database for items and questions. It includes functions for:

- Syncing listings from TradeMe
- Syncing questions from TradeMe
- Downloading and storing images locally

#### tradeMeSyncOrchestrator.js

Handles scheduling and coordination of TradeMe synchronization tasks. It includes functions for:

- Initializing sync schedules
- Performing sync operations
- Getting sync status
- Getting sync logs
- Stopping sync operations

### Templates

#### tradeMeTemplatesService.js

Handles operations related to TradeMe templates. It includes functions for:

- Getting templates with filtering
- Creating templates
- Updating templates
- Deleting templates

### Feedback & Categories

#### tradeMeFeedbackService.js

Handles operations related to TradeMe feedback. It includes functions for:

- Getting feedback
- Syncing feedback from TradeMe
- Responding to feedback

#### tradeMeCategoryService.js

Handles operations related to TradeMe categories. It includes functions for:

- Getting categories
- Syncing categories from TradeMe
- Finding categories by ID or name

### Utilities

#### tradeMeUtils.js

Provides shared utilities for TradeMe API integration. It includes:

- API endpoints for production and sandbox environments
- OAuth utilities for authentication
- Date parsing functions for TradeMe date formats

## Routes

### Account Management

#### tradeMeAccountRoutes.js

Defines the API endpoints for managing TradeMe account settings. It includes endpoints for:

- Getting TradeMe connection status
- Connecting and disconnecting TradeMe accounts
- Refreshing TradeMe connection
- Getting TradeMe settings
- Getting TradeMe member summary
- OAuth flow (request token, access token)

### Item Management

#### tradeMeItemsRoutes.js

Defines the API endpoints for managing TradeMe items. It includes endpoints for:

- Getting all TradeMe items with filtering and pagination
- Getting a single TradeMe item by ID
- Creating a new TradeMe item
- Updating an existing TradeMe item
- Listing an item on TradeMe
- Relisting an item on TradeMe
- Archiving an item
- Getting audit history for an item
- Managing questions and answers
- Managing notes

### Sync Services

#### tradeMeSyncRoutes.js

Defines the API endpoints for synchronizing with TradeMe. It includes endpoints for:

- Getting sync status
- Getting sync logs
- Triggering manual syncs
- Stopping sync operations

### Templates

#### tradeMeTemplatesRoutes.js

Defines the API endpoints for managing TradeMe templates. It includes endpoints for:

- Getting templates with filtering
- Getting a template by ID
- Creating a new template
- Updating a template
- Deleting a template

### Feedback & Categories

#### tradeMeFeedbackRoutes.js

Defines the API endpoints for managing TradeMe feedback. It includes endpoints for:

- Getting feedback
- Responding to feedback
- Syncing feedback from TradeMe

#### tradeMeCategoryRoutes.js

Defines the API endpoints for managing TradeMe categories. It includes endpoints for:

- Getting categories
- Getting a category by ID
- Syncing categories from TradeMe

## Client-Side Components

### Pages

- `TradeMeSelling.tsx` - Displays active TradeMe listings
- `TradeMeSold.tsx` - Displays sold TradeMe items
- `TradeMeUnsold.tsx` - Displays unsold TradeMe items
- `TradeMeArchived.tsx` - Displays archived TradeMe items
- `TradeMeListingDetail.tsx` - Displays details for a single TradeMe item
- `TradeMeListingEdit.tsx` - Allows editing of a TradeMe item
- `TradeMeQuestions.tsx` - Displays and manages questions for TradeMe listings
- `TradeMeFeedback.tsx` - Displays and manages TradeMe feedback
- `TradeMeSyncStatus.tsx` - Displays sync status and logs with options to trigger different types of syncs
- `TradeMeTemplates.tsx` - Manages all types of templates (questions, footers, shipping)

### Components

- `TradeMeLayout.tsx` - Provides a layout for all TradeMe pages
- `TradeMeSettings.tsx` - Allows configuration of TradeMe account settings
- `TradeMeListingCard.tsx` - Displays a TradeMe item in a card format
- `TradeMeListingsLayout.tsx` - Provides a layout for TradeMe listings pages
- `TradeMeIcon.tsx` - Provides the TradeMe logo icon
- `TradeMeTemplateManager.tsx` - Unified template manager for questions, footers, and shipping templates

### API

- `tradeMeItems.ts` - Client-side API functions for TradeMe item operations
- `tradeMeAccount.ts` - Client-side API functions for TradeMe account management
- `tradeMeSync.ts` - Client-side API functions for TradeMe synchronization
- `tradeMeTemplates.ts` - Client-side API functions for TradeMe templates
- `tradeMeFeedback.ts` - Client-side API functions for TradeMe feedback

## Usage Examples

### Creating a TradeMe Item

```javascript
const result = await tradeMeItemsService.createItem({
  title: 'Example Item',
  description: 'This is an example item',
  currentPrice: 10.00,
  category: 'General',
  locationId: '123456789012345678901234'
}, user);
```

### Listing an Item on TradeMe

```javascript
const result = await tradeMeItemsService.listItemOnTradeMe(itemId, user);
```

### Relisting an Item on TradeMe

```javascript
const result = await tradeMeItemsService.relistItemOnTradeMe(itemId, user);
```

### Adding a Note to an Item

```javascript
const result = await tradeMeItemsService.addNote(itemId, 'This is a note', user);
```

### Answering a Question

```javascript
const result = await tradeMeItemsService.answerQuestion(itemId, questionId, 'This is an answer', user);
```

### Creating a Template

```javascript
const result = await tradeMeTemplatesService.createTemplate({
  type: 'question',
  title: 'Standard Response',
  content: 'Thank you for your question. The item is still available.',
  category: 'General'
}, user);
```

### Triggering a Manual Sync

```javascript
const result = await tradeMeSyncOrchestrator.performSync({
  syncType: 'listings',
  maxPagesPerEndpoint: 5
});
```

## Explanation of the Minimum Price Field in Relisting Settings

The minimum price field in the relisting settings is an important part of the automatic relisting feature for TradeMe items. Here's how it works:

Purpose: The minimum price sets a floor for how low the price can go after multiple relistings with price reductions.
How it works:
When an item doesn't sell and is relisted, the price is reduced by the percentage specified in the "Price Reduction Percent" field (default 10%).
This reduction happens each time the item is relisted, up to the maximum number of relistings specified.
The minimum price prevents the price from dropping below a certain threshold, regardless of how many times the item is relisted.
Example:
Original price: $100
Price reduction: 10%
Minimum price: $50
Max relists: 3
In this scenario:

First relisting: Price drops to $90 (10% off $100)
Second relisting: Price drops to $81 (10% off $90)
Third relisting: Price drops to $72.90 (10% off $81)
If the minimum price was set to $80, the third relisting would be $80 instead of $72.90, because the calculated price would be below the minimum.

Business logic:
This helps ensure that items don't get sold for less than what you're willing to accept.
It prevents automatic relistings from reducing the price too far below your cost.
It allows for aggressive initial price reductions while maintaining a profit margin.
The default value is set to 50% of the current price, which is a reasonable starting point for most items, but you should adjust it based on your cost and desired profit margin.

## Recent Improvements

1. **Unified Template System**:
   - Combined question templates, footer templates, and added shipping templates into a single `TradeMeTemplates` model
   - Created a dedicated service and routes for template management
   - Removed the old separate template models and routes
   - Implemented a unified `TradeMeTemplateManager` component for managing all template types
   - Created a dedicated page at `/trademe/templates` for managing all templates

2. **Improved Account Management**:
   - Split account management endpoints into a dedicated `tradeMeAccountRoutes.js` file
   - Improved error handling and validation for account operations
   - Enhanced OAuth implementation with proper scope handling
   - Added better documentation for OAuth flow

3. **Enhanced Sync Architecture**:
   - Created a new `tradeMeSyncOrchestrator.js` to handle scheduling and coordination
   - Improved sync logging and status tracking
   - Added endpoints for manual sync operations and stopping syncs
   - Implemented a new sync status page with options to trigger different types of syncs

4. **Comprehensive Item Model**:
   - Enhanced the `TradeMeItems` model with embedded documents for listing history, questions, and notes
   - Added support for fault tracking, location tracking, and relisting rules
   - Implemented proper status tracking (draft, queued, active, sold, archived, unsold)

5. **Improved Client-Side API Structure**:
   - Split the monolithic `trademe.ts` file into domain-specific API files
   - Created separate API files for items, account, sync, templates, and feedback
   - Added better error handling and type definitions
   - Provided backward compatibility for legacy code

## Planned Improvements

1. **Client-Side Components**: ✅ COMPLETED
   - ✅ Updated listing creation/editing forms to use the new templates
   - ✅ Implemented the new note functionality in the UI
   - ✅ Added support for managing shipping options in the listing form
   - ✅ Created a unified template manager component for all template types

2. **Background Sync Service**:
   - Add more detailed progress tracking for sync operations
   - Implement relisting rules based on item configuration
   - Add support for automatic relisting of unsold items

3. **Image Management**:
   - Enhance image upload and management
   - Implement better organization of images by listing
   - Add support for image reordering and deletion

4. **Reporting and Analytics**:
   - Develop widgets for the dashboard showing TradeMe performance
   - Add charts for sales, views, and watchers
   - Implement trend analysis

5. **Testing and Optimization**:
   - Develop comprehensive tests for the new models and services
   - Implement integration tests for the API endpoints
   - Add end-to-end tests for critical workflows
