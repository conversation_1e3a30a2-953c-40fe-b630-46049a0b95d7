import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { DollarSign, AlertTriangle, Package } from 'lucide-react';
import { useFormContext, useWatch } from 'react-hook-form';
import { useEffect } from 'react';

interface MultiItemPriceAuditSectionProps {
  control: any;
  disabled?: boolean;
}

/**
 * Multi-item Price Audit component for tracking overpayment incidents per item
 * Focuses on documentation and follow-up rather than compliance assessment
 */
export function MultiItemPriceAuditSection({ control, disabled = false }: MultiItemPriceAuditSectionProps) {
  const { setValue, watch } = useFormContext();
  const items = useWatch({ control, name: 'items' }) || [];

  // Auto-populate cost price for each item when item cost changes
  useEffect(() => {
    items.forEach((item: any, index: number) => {
      if (item.cost && (!item.pricing?.costPrice || item.pricing.costPrice === '')) {
        setValue(`items.${index}.pricing.costPrice`, item.cost);
      }
    });
  }, [items, setValue]);

  return (
    <div className="space-y-6">
      {/* Overall Overpayment Reason Selection */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5 text-orange-500" />
            <CardTitle>Overpayment Reason</CardTitle>
          </div>
          <CardDescription>
            Select the primary reason for the overpayment incident
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <FormField
            control={control}
            name="pricing.overpaymentReason"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>Reason for overpayment</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    className="flex flex-col space-y-3"
                    disabled={disabled}
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="paid_over_ghost_price" id="reason-ghost" />
                      <FormLabel htmlFor="reason-ghost" className="cursor-pointer">
                        Paid over Ghost recorded price
                      </FormLabel>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="paid_over_gold_calculator" id="reason-gold" />
                      <FormLabel htmlFor="reason-gold" className="cursor-pointer">
                        Paid over gold calculator value
                      </FormLabel>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="insufficient_research" id="reason-research" />
                      <FormLabel htmlFor="reason-research" className="cursor-pointer">
                        Insufficient market research
                      </FormLabel>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="other" id="reason-other" />
                      <FormLabel htmlFor="reason-other" className="cursor-pointer">
                        Other reason
                      </FormLabel>
                    </div>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Custom reason field - shown when "other" is selected */}
          <FormField
            control={control}
            name="pricing.overpaymentReason"
            render={({ field }) => (
              <div>
                {field.value === 'other' && (
                  <FormField
                    control={control}
                    name="pricing.customOverpaymentReason"
                    render={({ field: customField }) => (
                      <FormItem>
                        <FormLabel>Custom Reason</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Please specify the reason for overpayment"
                            {...customField}
                            disabled={disabled}
                          />
                        </FormControl>
                        <FormDescription>
                          Provide details about the specific reason for overpayment
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </div>
            )}
          />

          {/* Overall notes */}
          <FormField
            control={control}
            name="pricing.notes"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Overall Notes</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Enter any overall notes about the overpayment incident"
                    className="min-h-[80px]"
                    {...field}
                    disabled={disabled}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </CardContent>
      </Card>

      {/* Individual Item Pricing Details */}
      {items.map((item: any, itemIndex: number) => (
        <Card key={itemIndex} className="border-l-4 border-l-orange-500">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Package className="h-5 w-5 text-orange-500" />
                <div>
                  <CardTitle className="text-lg">Item {itemIndex + 1}: {item.brand} - {item.description}</CardTitle>
                  <CardDescription>Stockcode: {item.stockcode} | Transaction Cost: ${item.cost}</CardDescription>
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Suggested Price */}
              <FormField
                control={control}
                name={`items.${itemIndex}.pricing.suggestedPrice`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Suggested Price</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="Enter suggested price"
                        {...field}
                        disabled={disabled}
                      />
                    </FormControl>
                    <FormDescription>
                      What should have been paid
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Cost Price (Auto-populated) */}
              <FormField
                control={control}
                name={`items.${itemIndex}.pricing.costPrice`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Actual Cost Paid</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="Auto-populated from item cost"
                        {...field}
                        disabled={disabled}
                        className="bg-muted/50"
                      />
                    </FormControl>
                    <FormDescription>
                      Auto-populated from transaction cost
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Ticket Price */}
              <FormField
                control={control}
                name={`items.${itemIndex}.pricing.ticketPrice`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Ticket Price</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="Enter ticket price"
                        {...field}
                        disabled={disabled}
                      />
                    </FormControl>
                    <FormDescription>
                      Intended selling price
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Item-specific notes */}
            <FormField
              control={control}
              name={`items.${itemIndex}.pricing.notes`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Item Notes</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter any notes specific to this item's overpayment"
                      className="min-h-[60px]"
                      {...field}
                      disabled={disabled}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
