@echo off
echo Schedule GH0ST Application Backup
echo ==============================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo This script requires administrator privileges.
    echo Please run as administrator.
    pause
    exit /b 1
)

REM Get the full path to the backup script
set BACKUP_SCRIPT=%~dp0backup-app.bat

echo This script will create a scheduled task to back up your GH0ST application.
echo The backup will exclude all node_modules folders.
echo.

REM Ask for schedule frequency
echo How often would you like to schedule the backup?
echo 1. Daily
echo 2. Weekly
echo 3. Monthly
echo.
set /p FREQUENCY_CHOICE=Enter your choice (1-3): 

REM Set schedule based on choice
set SCHEDULE_TYPE=
if "%FREQUENCY_CHOICE%"=="1" set SCHEDULE_TYPE=daily
if "%FREQUENCY_CHOICE%"=="2" set SCHEDULE_TYPE=weekly
if "%FREQUENCY_CHOICE%"=="3" set SCHEDULE_TYPE=monthly

if "%SCHEDULE_TYPE%"=="" (
    echo Invalid choice. Defaulting to daily backup.
    set SCHEDULE_TYPE=daily
)

REM Ask for time
echo.
echo At what time should the backup run? (24-hour format, e.g., 23:00)
set /p BACKUP_TIME=Enter time (HH:MM): 

REM Extract hours and minutes
for /f "tokens=1,2 delims=:" %%a in ("%BACKUP_TIME%") do (
    set HOURS=%%a
    set MINUTES=%%b
)

REM Validate time format
set TIME_VALID=1
if "%HOURS%"=="" set TIME_VALID=0
if "%MINUTES%"=="" set TIME_VALID=0

if %TIME_VALID%==0 (
    echo Invalid time format. Defaulting to 23:00.
    set HOURS=23
    set MINUTES=00
)

REM Create the scheduled task
echo.
echo Creating scheduled task to run %SCHEDULE_TYPE% at %HOURS%:%MINUTES%...

if "%SCHEDULE_TYPE%"=="daily" (
    schtasks /create /tn "GH0ST Application Backup" /tr "%BACKUP_SCRIPT%" /sc daily /st %HOURS%:%MINUTES% /ru SYSTEM /f
) else if "%SCHEDULE_TYPE%"=="weekly" (
    echo On which day of the week?
    echo 1. Monday
    echo 2. Tuesday
    echo 3. Wednesday
    echo 4. Thursday
    echo 5. Friday
    echo 6. Saturday
    echo 7. Sunday
    set /p DAY_CHOICE=Enter your choice (1-7): 
    
    set DAY_OF_WEEK=MON
    if "%DAY_CHOICE%"=="2" set DAY_OF_WEEK=TUE
    if "%DAY_CHOICE%"=="3" set DAY_OF_WEEK=WED
    if "%DAY_CHOICE%"=="4" set DAY_OF_WEEK=THU
    if "%DAY_CHOICE%"=="5" set DAY_OF_WEEK=FRI
    if "%DAY_CHOICE%"=="6" set DAY_OF_WEEK=SAT
    if "%DAY_CHOICE%"=="7" set DAY_OF_WEEK=SUN
    
    schtasks /create /tn "GH0ST Application Backup" /tr "%BACKUP_SCRIPT%" /sc weekly /d %DAY_OF_WEEK% /st %HOURS%:%MINUTES% /ru SYSTEM /f
) else if "%SCHEDULE_TYPE%"=="monthly" (
    echo On which day of the month?
    set /p DAY_OF_MONTH=Enter day (1-31): 
    
    schtasks /create /tn "GH0ST Application Backup" /tr "%BACKUP_SCRIPT%" /sc monthly /d %DAY_OF_MONTH% /st %HOURS%:%MINUTES% /ru SYSTEM /f
)

if %errorLevel% EQU 0 (
    echo Scheduled task created successfully!
) else (
    echo Failed to create scheduled task. Error code: %errorLevel%
)

echo.
echo You can view and modify this task in Task Scheduler.
echo The backup files will be stored in C:\Backups\GH0ST
echo.
pause
