const mongoose = require('mongoose');

const customerSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Name is required'],
    trim: true
  },
  email: {
    type: String,
    trim: true,
    lowercase: true,
    validate: {
      validator: function(email) {
        return !email || email.trim() === '' || /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/.test(email);
      },
      message: 'Please provide a valid email address'
    }
  },
  phone: {
    type: String,
    required: false,
    trim: true
  },
  address: {
    type: String,
    default: '',
    trim: true
  },
  notes: {
    type: String,
    default: ''
  },
  // Add this field
  type: {
    type: String,
    enum: ['customer', 'store', 'repairer', 'utility', 'other'],
    default: 'customer'
  },
  dateAdded: {
    type: Date,
    default: Date.now
  },
  createdAt: {
    type: Date,
    default: Date.now,
    immutable: true
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Custom validator to ensure at least one contact method is provided
customerSchema.pre('validate', function(next) {
  if ((!this.email || this.email.trim() === '') && (!this.phone || this.phone.trim() === '')) {
    this.invalidate('contact', 'At least one contact method (email or phone) is required');
  }
  next();
});

// Only create a unique email index for non-empty emails
customerSchema.index(
  { email: 1 },
  {
    unique: true,
    sparse: true,
    partialFilterExpression: { email: { $exists: true, $ne: null, $ne: "" } }
  }
);

const Customer = mongoose.model('Customer', customerSchema);

module.exports = Customer;