import api from './api';

export interface Customer {
  _id: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  notes: string;
  dateAdded: string;
  type?: string;
}

export interface PaginationData {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface CustomersResponse {
  customers: Customer[];
  pagination: PaginationData;
}

// Description: Get customers list with pagination
// Endpoint: GET /api/address-book
// Request: { page?: number, limit?: number, search?: string }
// Response: { customers: Customer[], pagination: { total: number, page: number, limit: number, totalPages: number } }
export const getCustomers = async (page = 1, limit = 10, search = '') => {
  try {
    const response = await api.get('/address-book', {
      params: { page, limit, search }
    });
    return response.data;
  } catch (error: any) {
    console.error(error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Description: Create a new customer
// Endpoint: POST /api/address-book
// Request: { name: string, email?: string, phone?: string, address?: string, notes?: string }
// Response: { success: boolean, message: string, customer: Customer }
export const createCustomer = async (customerData: {
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  notes?: string;
  type?: string;
}) => {
  try {
    const response = await api.post('/address-book', customerData);
    return response.data;
  } catch (error: any) {
    console.error(error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Description: Get a single customer
// Endpoint: GET /api/address-book/:id
// Request: {}
// Response: { customer: Customer }
export const getCustomer = async (id: string) => {
  try {
    const response = await api.get(`/address-book/${id}`);
    return response.data;
  } catch (error: any) {
    console.error("Error fetching customer:", error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Description: Update a customer
// Endpoint: PUT /api/address-book/:id
// Request: Partial<Customer>
// Response: { success: boolean, message: string, customer: Customer }
export const updateCustomer = async (id: string, customerData: Partial<Omit<Customer, '_id'>>) => {
  try {
    const response = await api.put(`/address-book/${id}`, customerData);
    return response.data;
  } catch (error: any) {
    console.error(error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Description: Delete a customer
// Endpoint: DELETE /api/address-book/:id
// Request: {}
// Response: { success: boolean, message: string }
export const deleteCustomer = async (id: string) => {
  try {
    const response = await api.delete(`/address-book/${id}`);
    return response.data;
  } catch (error: any) {
    console.error(error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};