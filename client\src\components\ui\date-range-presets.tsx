import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { CalendarIcon, Loader2 } from 'lucide-react';
import { getHappyOrNotAvailableYears } from '@/api/happyOrNot';

export interface DateRange {
  startDate: Date | undefined;
  endDate: Date | undefined;
  label?: string;
}

interface DateRangePresetsProps {
  onSelectPreset: (range: DateRange) => void;
  currentPreset?: string;
  showYearsOnly?: boolean;
}

export const DateRangePresets: React.FC<DateRangePresetsProps> = ({
  onSelectPreset,
  currentPreset = 'Custom Range',
  showYearsOnly = false,
}) => {
  const [availableYears, setAvailableYears] = useState<number[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  // Fetch available years on component mount
  useEffect(() => {
    const fetchAvailableYears = async () => {
      setLoading(true);
      try {
        const response = await getHappyOrNotAvailableYears();
        if (response.success) {
          setAvailableYears(response.data);
        } else {
          console.error('Failed to fetch available years');
          // Fallback to default years if API fails
          const currentYear = new Date().getFullYear();
          setAvailableYears(Array.from({ length: currentYear - 2017 }, (_, i) => 2018 + i));
        }
      } catch (error) {
        console.error('Error fetching available years:', error);
        // Fallback to default years if API fails
        const currentYear = new Date().getFullYear();
        setAvailableYears(Array.from({ length: currentYear - 2017 }, (_, i) => 2018 + i));
      } finally {
        setLoading(false);
      }
    };

    fetchAvailableYears();
  }, []);

  // Helper function to get the first day of the current month
  const getFirstDayOfMonth = () => {
    const now = new Date();
    return new Date(now.getFullYear(), now.getMonth(), 1);
  };

  // Helper function to get the first day of the current week (Monday)
  const getFirstDayOfWeek = () => {
    const now = new Date();
    const day = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const diff = now.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
    return new Date(now.setDate(diff));
  };

  // Helper function to get the first day of the current year
  const getFirstDayOfYear = () => {
    const now = new Date();
    return new Date(now.getFullYear(), 0, 1);
  };

  // Helper function to get the start of a specific year
  const getYearStart = (year: number) => {
    return new Date(year, 0, 1);
  };

  // Helper function to get the end of a specific year
  const getYearEnd = (year: number) => {
    const date = new Date(year, 11, 31, 23, 59, 59, 999);
    return date;
  };

  // Get current date with time set to end of day
  const getEndOfToday = () => {
    const now = new Date();
    now.setHours(23, 59, 59, 999);
    return now;
  };

  // Calculate date ranges for presets
  const presets = {
    mtd: {
      label: 'Month to Date',
      startDate: getFirstDayOfMonth(),
      endDate: getEndOfToday(),
    },
    wtd: {
      label: 'Week to Date',
      startDate: getFirstDayOfWeek(),
      endDate: getEndOfToday(),
    },
    last7Days: {
      label: 'Last 7 Days',
      startDate: new Date(new Date().setDate(new Date().getDate() - 7)),
      endDate: getEndOfToday(),
    },
    last14Days: {
      label: 'Last 14 Days',
      startDate: new Date(new Date().setDate(new Date().getDate() - 14)),
      endDate: getEndOfToday(),
    },
    lastMonth: {
      label: 'Last Month',
      startDate: new Date(new Date().setDate(new Date().getDate() - 30)),
      endDate: getEndOfToday(),
    },
    last4Months: {
      label: 'Last 4 Months',
      startDate: new Date(new Date().setMonth(new Date().getMonth() - 4)),
      endDate: getEndOfToday(),
    },
    last6Months: {
      label: 'Last 6 Months',
      startDate: new Date(new Date().setMonth(new Date().getMonth() - 6)),
      endDate: getEndOfToday(),
    },
    lastYear: {
      label: 'Last Year',
      startDate: new Date(new Date().setFullYear(new Date().getFullYear() - 1)),
      endDate: getEndOfToday(),
    },
    ytd: {
      label: 'Year to Date',
      startDate: getFirstDayOfYear(),
      endDate: getEndOfToday(),
    },
  };

  // Generate year options from available years
  const yearOptions = availableYears.map(year => ({
    label: year.toString(),
    startDate: getYearStart(year),
    endDate: getYearEnd(year),
  }));

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="w-full justify-start">
          <CalendarIcon className="mr-2 h-4 w-4" />
          {currentPreset}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56">
        {!showYearsOnly && (
          <>
            <DropdownMenuLabel>Date Range Presets</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              <DropdownMenuItem
                onClick={() => onSelectPreset(presets.mtd)}
              >
                Month to Date
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onSelectPreset(presets.wtd)}
              >
                Week to Date
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onSelectPreset(presets.last7Days)}
              >
                Last 7 Days
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onSelectPreset(presets.last14Days)}
              >
                Last 14 Days
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onSelectPreset(presets.lastMonth)}
              >
                Last Month
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onSelectPreset(presets.last4Months)}
              >
                Last 4 Months
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onSelectPreset(presets.last6Months)}
              >
                Last 6 Months
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onSelectPreset(presets.lastYear)}
              >
                Last Year
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onSelectPreset(presets.ytd)}
              >
                Year to Date
              </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
          </>
        )}

        <DropdownMenuLabel>{showYearsOnly ? 'Select Year' : 'Years'}</DropdownMenuLabel>
        <DropdownMenuSeparator />

        <div className="max-h-40 overflow-y-auto">
          {loading ? (
            <div className="flex items-center justify-center py-2">
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              <span className="text-sm">Loading years...</span>
            </div>
          ) : yearOptions.length > 0 ? (
            yearOptions.map((yearOption) => (
              <DropdownMenuItem
                key={yearOption.label}
                onClick={() => onSelectPreset(yearOption)}
              >
                {yearOption.label}
              </DropdownMenuItem>
            ))
          ) : (
            <div className="text-sm text-muted-foreground text-center py-2">
              No data available
            </div>
          )}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default DateRangePresets;
