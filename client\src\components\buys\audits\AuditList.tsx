import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import { useToast } from '@/hooks/useToast';
import { getAudits, deleteAudit as deleteAuditApi } from '@/api/buyPawnAudits';
import { AuditNavigation } from './AuditNavigation';
import { Loader2, Search, FileText, Edit, Trash2, Plus } from 'lucide-react';
import { format } from 'date-fns';

/**
 * Component to display a list of all audits with filtering and pagination
 */
export function AuditList() {
  const [audits, setAudits] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const navigate = useNavigate();
  const { toast } = useToast();

  // Fetch audits
  useEffect(() => {
    const fetchAudits = async () => {
      try {
        setIsLoading(true);
        const result = await getAudits({
          auditType: filterType !== 'all' ? filterType as any : undefined,
          status: filterStatus !== 'all' ? filterStatus as any : undefined,
          page: currentPage,
          limit: 10,
          search: searchTerm,
        });

        if (result.success) {
          setAudits(result.data.audits || []);
          setTotalPages(result.data.pagination?.pages || 1);
        } else {
          toast({
            title: 'Error',
            description: result.error || 'Failed to load audits.',
            variant: 'destructive',
          });
        }
      } catch (error: any) {
        console.error('Error fetching audits:', error);
        setAudits([]); // Ensure audits is always an array
        toast({
          title: 'Error',
          description: error.message || 'An unexpected error occurred.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchAudits();
  }, [currentPage, filterType, filterStatus, searchTerm, toast]);

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
  };

  // Format date
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'dd/MM/yyyy');
  };

  // Get audit type display
  const getAuditTypeDisplay = (type: string) => {
    switch (type) {
      case 'buy':
        return 'Buy Deal';
      case 'pawn':
        return 'Pawn Loan';
      case 'price':
        return 'Price';
      default:
        return type;
    }
  };

  // Get compliance badge
  const getComplianceBadge = (compliance: string) => {
    switch (compliance) {
      case 'compliant':
        return <Badge className="bg-green-500">Compliant</Badge>;
      case 'minor_non_compliant':
        return <Badge className="bg-yellow-500">Minor Non-Compliant</Badge>;
      case 'major_non_compliant':
        return <Badge className="bg-red-500">Major Non-Compliant</Badge>;
      default:
        return <Badge variant="outline">{compliance}</Badge>;
    }
  };

  // View audit details
  const viewAuditDetails = (id: string) => {
    navigate(`/buys/audits/${id}`);
  };

  // Edit audit
  const editAudit = (id: string) => {
    navigate(`/buys/audits/${id}/edit`);
  };

  // Delete audit
  const deleteAudit = async (id: string) => {
    if (confirm('Are you sure you want to delete this audit? This action cannot be undone.')) {
      try {
        // Call the API to delete the audit
        const result = await deleteAuditApi(id);

        if (result.success) {
          toast({
            title: 'Audit Deleted',
            description: 'The audit has been deleted successfully.',
          });

          // Refresh the list
          setAudits((prevAudits) => (prevAudits || []).filter(audit => audit._id !== id));
        } else {
          toast({
            title: 'Error',
            description: result.error || 'Failed to delete audit.',
            variant: 'destructive',
          });
        }
      } catch (error: any) {
        toast({
          title: 'Error',
          description: error.message || 'Failed to delete audit.',
          variant: 'destructive',
        });
      }
    }
  };

  if (isLoading && (!audits || audits.length === 0)) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col gap-4 mb-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold">Audit List</h1>
            <p className="text-muted-foreground">View and manage all audits</p>
          </div>
          <Button onClick={() => navigate('/buys/audits/new')}>
            <Plus className="mr-2 h-4 w-4" />
            New Audit
          </Button>
        </div>

        <AuditNavigation />
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Audits</CardTitle>
          <CardDescription>
            Browse, filter, and manage audit records
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex flex-col md:flex-row gap-4">
              <form onSubmit={handleSearch} className="flex w-full max-w-sm items-center space-x-2">
                <div className="relative flex-1">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search by employee or transaction ID..."
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <Button type="submit">Search</Button>
              </form>

              <div className="flex items-center space-x-2">
                <Select value={filterType} onValueChange={setFilterType}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Filter by type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="buy">Buy Deal</SelectItem>
                    <SelectItem value="pawn">Pawn Loan</SelectItem>
                    <SelectItem value="price">Price</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={filterStatus} onValueChange={setFilterStatus}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="compliant">Compliant</SelectItem>
                    <SelectItem value="minor_non_compliant">Minor Non-Compliant</SelectItem>
                    <SelectItem value="major_non_compliant">Major Non-Compliant</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Transaction ID</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Employee</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Compliance</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {!audits || audits.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-4">
                        No audits found
                      </TableCell>
                    </TableRow>
                  ) : (
                    audits.map((audit) => (
                      <TableRow key={audit._id}>
                        <TableCell className="font-medium">
                          {audit.transactionId}
                        </TableCell>
                        <TableCell>
                          {getAuditTypeDisplay(audit.auditType)}
                        </TableCell>
                        <TableCell>{audit.employeeName}</TableCell>
                        <TableCell>{formatDate(audit.auditDate)}</TableCell>
                        <TableCell>
                          {getComplianceBadge(audit.overallCompliance)}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => viewAuditDetails(audit._id)}
                              title="View"
                            >
                              <FileText className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => editAudit(audit._id)}
                              title="Edit"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => deleteAudit(audit._id)}
                              title="Delete"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>

            {totalPages > 1 && (
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
                      disabled={currentPage === 1}
                    />
                  </PaginationItem>

                  {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                    <PaginationItem key={page}>
                      <PaginationLink
                        onClick={() => setCurrentPage(page)}
                        isActive={currentPage === page}
                      >
                        {page}
                      </PaginationLink>
                    </PaginationItem>
                  ))}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}
                      disabled={currentPage === totalPages}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
