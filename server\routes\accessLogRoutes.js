const express = require('express');
const router = express.Router();
const { requireUser } = require('./middleware/auth');
const AccessLog = require('../models/AccessLog');

// Helper functions for color coding
function getColorForRequestType(type) {
  switch (type) {
    case 'page': return 'bg-green-500';
    case 'api': return 'bg-blue-500';
    case 'asset': return 'bg-gray-500';
    default: return 'bg-gray-400';
  }
}

function getColorForStatusCode(statusRange) {
  switch (statusRange) {
    case '2xx': return 'bg-green-500';
    case '3xx': return 'bg-blue-500';
    case '4xx': return 'bg-yellow-500';
    case '5xx': return 'bg-red-500';
    default: return 'bg-gray-400';
  }
}

/**
 * Get access logs with filtering and pagination
 * Only accessible to admin users
 */
router.get('/', requireUser, async (req, res) => {
  try {
    // Check if user has admin permission
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to view access logs'
      });
    }

    // Parse query parameters
    const {
      page = 1,
      limit = 50,
      username,
      userId,
      path,
      method,
      ipAddress,
      startDate,
      endDate,
      userRole,
      statusCode,
      statusType, // 'error', 'success', 'redirect', 'client-error', 'server-error'
      requestType, // 'page', 'api', 'asset', 'unknown'
      minResponseTime,
      maxResponseTime,
      referrer
    } = req.query;

    // Build query
    const query = {};

    if (username) {
      query.username = { $regex: username, $options: 'i' };
    }

    if (userId) {
      query.userId = userId;
    }

    if (path) {
      query.path = { $regex: path, $options: 'i' };
    }

    if (method) {
      query.method = method.toUpperCase();
    }

    if (ipAddress) {
      query.ipAddress = { $regex: ipAddress, $options: 'i' };
    }

    if (userRole) {
      query.userRole = userRole;
    }

    // Status code filtering
    if (statusCode) {
      query.statusCode = parseInt(statusCode);
    } else if (statusType) {
      // Filter by status code type
      switch(statusType) {
        case 'success':
          query.statusCode = { $gte: 200, $lt: 300 };
          break;
        case 'redirect':
          query.statusCode = { $gte: 300, $lt: 400 };
          break;
        case 'client-error':
          query.statusCode = { $gte: 400, $lt: 500 };
          break;
        case 'server-error':
          query.statusCode = { $gte: 500, $lt: 600 };
          break;
        case 'error':
          query.statusCode = { $gte: 400 };
          break;
      }
    }

    // Request type filtering
    if (requestType) {
      switch (requestType) {
        case 'auth':
          query.requestType = 'auth';  // Changed from path regex to direct requestType match
          break;
        case 'page':
          query.requestType = 'page';
          break;
        case 'api':
          query.requestType = 'api';
          break;
        case 'asset':
          query.requestType = 'asset';
          break;
      }
    }

    // Response time filtering
    if (minResponseTime || maxResponseTime) {
      query.responseTime = {};
      if (minResponseTime) {
        query.responseTime.$gte = parseInt(minResponseTime);
      }
      if (maxResponseTime) {
        query.responseTime.$lte = parseInt(maxResponseTime);
      }
    }

    // Referrer filtering
    if (referrer) {
      query.referrer = { $regex: referrer, $options: 'i' };
    }

    // Date range filtering
    if (startDate || endDate) {
      query.timestamp = {};

      if (startDate) {
        query.timestamp.$gte = new Date(startDate);
      }

      if (endDate) {
        query.timestamp.$lte = new Date(endDate);
      }
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Get total count for pagination
    const total = await AccessLog.countDocuments(query);

    // Get logs with pagination
    const logs = await AccessLog.find(query)
      .sort({ timestamp: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .lean();

    return res.status(200).json({
      success: true,
      logs,
      pagination: {
        total,
        page: parseInt(page),
        pages: Math.ceil(total / parseInt(limit)),
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Error fetching access logs:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Get dashboard statistics for access logs
 */
router.get('/stats', async (req, res) => {
  try {

    // Get total count
    const totalCount = await AccessLog.countDocuments();

    // Get unique users count
    const uniqueUsers = await AccessLog.distinct('username');
    const uniqueUsersCount = uniqueUsers.length;

    // Get average response time
    let avgResponseTime = 0;
    try {
      const avgResponseTimeResult = await AccessLog.aggregate([
        { $match: { responseTime: { $exists: true, $ne: null } } },
        { $group: { _id: null, avg: { $avg: '$responseTime' } } }
      ]);
      avgResponseTime = avgResponseTimeResult.length > 0 ?
        Math.round(avgResponseTimeResult[0].avg) : 0;
    } catch (error) {
      console.error('Error calculating average response time:', error);
      avgResponseTime = 0;
    }

    // Get error rate (status >= 400)
    const errorCount = await AccessLog.countDocuments({ statusCode: { $gte: 400 } });
    const errorRate = totalCount > 0 ? Math.round((errorCount / totalCount) * 100) : 0;

    // Create placeholder data for charts
    const requestsByType = [
      { label: 'Page', value: await AccessLog.countDocuments({ requestType: 'page' }), color: 'bg-green-500' },
      { label: 'Api', value: await AccessLog.countDocuments({ requestType: 'api' }), color: 'bg-blue-500' },
      { label: 'Asset', value: await AccessLog.countDocuments({ requestType: 'asset' }), color: 'bg-gray-500' },
      { label: 'Unknown', value: await AccessLog.countDocuments({ $or: [{ requestType: 'unknown' }, { requestType: { $exists: false } }] }), color: 'bg-gray-400' }
    ];

    // Get status code counts
    const status2xx = await AccessLog.countDocuments({ statusCode: { $gte: 200, $lt: 300 } });
    const status3xx = await AccessLog.countDocuments({ statusCode: { $gte: 300, $lt: 400 } });
    const status4xx = await AccessLog.countDocuments({ statusCode: { $gte: 400, $lt: 500 } });
    const status5xx = await AccessLog.countDocuments({ statusCode: { $gte: 500, $lt: 600 } });

    const requestsByStatus = [
      { label: '2xx', value: status2xx, color: 'bg-green-500' },
      { label: '3xx', value: status3xx, color: 'bg-blue-500' },
      { label: '4xx', value: status4xx, color: 'bg-yellow-500' },
      { label: '5xx', value: status5xx, color: 'bg-red-500' }
    ];

    // Generate time-based data (last 7 days)
    const requestsOverTime = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const startOfDayDate = new Date(date);
      startOfDayDate.setHours(0, 0, 0, 0);
      const endOfDayDate = new Date(date);
      endOfDayDate.setHours(23, 59, 59, 999);

      const count = await AccessLog.countDocuments({
        timestamp: { $gte: startOfDayDate, $lte: endOfDayDate }
      });

      requestsOverTime.push({
        label: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
        value: count
      });
    }

    return res.status(200).json({
      success: true,
      stats: {
        totalRequests: totalCount,
        uniqueUsers: uniqueUsersCount,
        avgResponseTime,
        errorRate,
        requestsByType,
        requestsByStatus,
        requestsOverTime
      }
    });
  } catch (error) {
    console.error('Error fetching access logs statistics:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Clear old access logs (older than specified days)
 * Only accessible to admin users
 */
router.delete('/clear', requireUser, async (req, res) => {
  try {
    // Check if user has admin permission
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to clear access logs'
      });
    }

    const { days = 30 } = req.body;

    // Calculate the cutoff date
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - parseInt(days));

    // Delete logs older than the cutoff date
    const result = await AccessLog.deleteMany({
      timestamp: { $lt: cutoffDate }
    });

    return res.status(200).json({
      success: true,
      message: `Successfully cleared ${result.deletedCount} access logs older than ${days} days`
    });
  } catch (error) {
    console.error('Error clearing access logs:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = router;


