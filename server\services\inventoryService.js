const Inventory = require('../models/Inventory');
const Location = require('../models/Location');
const auditService = require('./auditService');
const mongoose = require('mongoose');

class InventoryService {
  /**
   * Get all inventory items
   * @returns {Promise<Array>} List of inventory items
   */
  async getAllItems() {
    try {
      const items = await Inventory.find()
        .populate('location', 'name')
        .sort({ lastUpdated: -1 });

      return {
        success: true,
        items: items.map(item => ({
          ...item.toObject(),
          location: item.location?.name,
        })),
      };
    } catch (error) {
      console.error('Error fetching inventory items:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Create a new inventory item
   * @param {Object} itemData - Data for the new inventory item
   * @returns {Promise<Object>} Result with the created item or error
   */
  async createItem(itemData) {
    try {
      console.log('Creating new inventory item:', itemData.name);
      console.log('Item data includes:', Object.keys(itemData).join(', '));

      // Make location check conditional
      if (itemData.location) {
        // Only validate location if it's provided
        const location = await Location.findById(itemData.location);
        if (!location) {
          console.error(`Location with ID ${itemData.location} not found`);
          return {
            success: false,
            error: 'Invalid location selected',
          };
        }
      }

      // Create the item with current timestamp
      const newItem = new Inventory({
        name: itemData.name,
        category: itemData.category,
        brand: itemData.brand || '',
        model: itemData.model || '',
        modelNumber: itemData.modelNumber || '',
        releaseYear: itemData.releaseYear || null,
        lastRRP: itemData.lastRRP || 0,
        currentPrice: itemData.currentPrice,
        location: itemData.location || null, // Allow null location
        status: itemData.status || 'in_stock',
        lastUpdated: new Date(),
      });

      const savedItem = await newItem.save();
      console.log(`Successfully created inventory item with ID: ${savedItem._id}`);

      // Return the saved item without populating location
      return {
        success: true,
        item: savedItem,
      };
    } catch (error) {
      console.error('Error creating inventory item:', error.stack);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Update an existing inventory item
   * @param {string} itemId - The ID of the item to update
   * @param {Object} updateData - The data to update
   * @param {string} userId - The ID of the user making the update
   * @returns {Promise<Object>} Result with the updated item or error
   */
  async updateItem(itemId, updateData, userId) {
    try {

      // Find the item first to make sure it exists
      const existingItem = await Inventory.findById(itemId);
      if (!existingItem) {
        console.error(`Inventory item with ID ${itemId} not found`);
        return {
          success: false,
          error: 'Inventory item not found',
        };
      }

      // Make location check conditional
      if (updateData.location) {
        // Only validate location if it's provided
        const location = await Location.findById(updateData.location);
        if (!location) {
          console.error(`Location with ID ${updateData.location} not found`);
          return {
            success: false,
            error: 'Invalid location selected',
          };
        }
      }

      // Prepare update data with current timestamp
      const itemToUpdate = {
        ...updateData,
        lastUpdated: new Date(),
      };

      // Track changes for audit log
      const changes = {};
      const previousValues = {};
      const newValues = {};

      // Determine which fields have changed
      Object.keys(itemToUpdate).forEach(key => {
        // Skip the lastUpdated field
        if (key === 'lastUpdated') return;

        // Check if the value has changed
        const oldValue = existingItem[key];
        const newValue = itemToUpdate[key];

        // Compare values (handling ObjectId comparison)
        let valueChanged;
        if (oldValue instanceof mongoose.Types.ObjectId && newValue) {
          valueChanged = oldValue.toString() !== newValue.toString();
        } else if (typeof oldValue === 'object' && oldValue !== null && typeof newValue === 'object' && newValue !== null) {
          valueChanged = JSON.stringify(oldValue) !== JSON.stringify(newValue);
        } else {
          valueChanged = oldValue !== newValue;
        }

        if (valueChanged) {
          changes[key] = true;
          previousValues[key] = oldValue;
          newValues[key] = newValue;
        }
      });

      // Update the item
      const updatedItem = await Inventory.findByIdAndUpdate(
        itemId,
        itemToUpdate,
        { new: true } // Return the updated document
      ).populate('location', 'name');

      console.log(`Successfully updated inventory item: ${updatedItem.name}`);

      // Create audit log if there are changes
      if (Object.keys(changes).length > 0) {
        await auditService.createLog(
          itemId,
          userId,
          'update',
          changes,
          previousValues,
          newValues
        );
        console.log('Created audit log for item update');
      }

      return {
        success: true,
        item: {
          ...updatedItem.toObject(),
          location: updatedItem.location?.name,
        },
      };
    } catch (error) {
      console.error(`Error updating inventory item ${itemId}:`, error.stack);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Delete an inventory item by ID
   * @param {string} itemId - The ID of the item to delete
   * @param {string} userId - The ID of the user performing the deletion
   * @returns {Promise<Object>} Result of the deletion operation
   */
  async deleteItem(itemId, userId) {
    try {
      console.log(`Deleting inventory item with ID: ${itemId} by user: ${userId}`);

      // Find the item first to make sure it exists and to get its data for audit
      const existingItem = await Inventory.findById(itemId);
      if (!existingItem) {
        console.error(`Inventory item with ID ${itemId} not found`);
        return {
          success: false,
          error: 'Inventory item not found',
        };
      }

      // Delete the item
      const result = await Inventory.findByIdAndDelete(itemId);

      if (!result) {
        console.error(`Failed to delete inventory item with ID: ${itemId}`);
        return {
          success: false,
          error: 'Failed to delete inventory item',
        };
      }

      // Create audit log for the deletion
      await auditService.createLog(
        itemId,
        userId,
        'delete',
        { item: true },
        { item: existingItem.toObject() },
        { item: null }
      );

      console.log(`Successfully deleted inventory item with ID: ${itemId}`);

      return {
        success: true,
        message: 'Inventory item deleted successfully',
      };
    } catch (error) {
      console.error(`Error deleting inventory item ${itemId}:`, error.stack);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Seeds the database with initial inventory items
   * @returns {Promise<Object>} Result of the seeding operation
   */
  async seedInventory() {
    try {
      console.log('Starting inventory seeding process');
      // Check if inventory items already exist
      const existingCount = await Inventory.countDocuments();
      console.log(`Found ${existingCount} existing inventory items`);

      if (existingCount > 0) {
        console.log('Skipping seed as inventory items already exist');
        return {
          success: true,
          message: `Database already has ${existingCount} inventory items. Skipping seed.`,
        };
      }

      // Get all active locations to associate items with
      console.log('Fetching active locations');
      const locations = await Location.find({ isActive: true });
      console.log(`Found ${locations.length} active locations`);

      if (locations.length === 0) {
        console.error('No active locations found for inventory seeding');
        return {
          success: false,
          error: 'No active locations found. Please seed locations first.',
        };
      }

      // Sample inventory items data
      const sampleItems = [
        {
          name: 'iPhone 13',
          category: 'Electronics',
          brand: 'Apple',
          model: 'iPhone 13',
          modelNumber: 'A2482',
          releaseYear: 2021,
          lastRRP: 799.99,
          currentPrice: 599.99,
          status: 'in_stock',
        },
        {
          name: 'Gold Chain',
          category: 'Jewelry',
          brand: '18K Gold',
          model: 'Cuban Link',
          modelNumber: 'CL-1420',
          releaseYear: null,
          lastRRP: 1200.00,
          currentPrice: 899.99,
          status: 'listed',
        },
        {
          name: 'PlayStation 5',
          category: 'Electronics',
          brand: 'Sony',
          model: 'PlayStation 5',
          modelNumber: 'CFI-1215A',
          releaseYear: 2020,
          lastRRP: 499.99,
          currentPrice: 399.99,
          status: 'in_stock',
        },
        {
          name: 'Diamond Ring',
          category: 'Jewelry',
          brand: '14K White Gold',
          model: 'Princess Cut',
          modelNumber: 'DC-150',
          releaseYear: null,
          lastRRP: 3500.00,
          currentPrice: 2999.99,
          status: 'in_stock',
        },
        {
          name: 'Rolex Watch',
          category: 'Watches',
          brand: 'Rolex',
          model: 'Submariner',
          modelNumber: '126610LN',
          releaseYear: 2020,
          lastRRP: 10000.00,
          currentPrice: 8999.99,
          status: 'listed',
        },
        {
          name: 'MacBook Pro',
          category: 'Electronics',
          brand: 'Apple',
          model: 'MacBook Pro',
          modelNumber: 'M1 Pro, 14"',
          releaseYear: 2021,
          lastRRP: 1999.99,
          currentPrice: 1299.99,
          status: 'in_stock',
        }
      ];

      // Distribute items across locations
      console.log('Preparing inventory items for insertion');
      const itemsToInsert = sampleItems.map((item, index) => ({
        ...item,
        location: locations[index % locations.length]._id,
        lastUpdated: new Date(),
      }));

      // Insert the items
      console.log(`Inserting ${itemsToInsert.length} inventory items`);
      const result = await Inventory.insertMany(itemsToInsert);
      console.log(`Successfully seeded ${result.length} inventory items`);

      return {
        success: true,
        message: `Successfully seeded ${result.length} inventory items`,
        count: result.length,
      };
    } catch (error) {
      console.error('Error seeding inventory:', error.stack);
      return {
        success: false,
        error: error.message,
      };
    }
  }
}

module.exports = new InventoryService();