@echo off
echo MongoDB Backup Script
echo ===================
echo.

REM Set variables
set DB_NAME=pythagora
set BACKUP_PATH="X:\Web Design\Ghost\Database Backups\ghost_v2"
set TIMESTAMP=%date:~-4,4%%date:~-7,2%%date:~-10,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set TIMESTAMP=%TIMESTAMP: =0%

REM Create backup directory if it doesn't exist
if not exist "%BACKUP_PATH%" mkdir "%BACKUP_PATH%"
if not exist "%BACKUP_PATH%\%TIMESTAMP%" mkdir "%BACKUP_PATH%\%TIMESTAMP%"

echo Creating backup of %DB_NAME% database...
echo Backup location: %BACKUP_PATH%\%TIMESTAMP%
echo.

REM Perform the backup
mongodump --db %DB_NAME% --out %BACKUP_PATH%\%TIMESTAMP%

echo.
if %ERRORLEVEL% EQU 0 (
    echo Backup completed successfully!
) else (
    echo Backup failed with error code %ERRORLEVEL%
)

REM Keep only the last 7 backups
echo.
echo Cleaning up old backups...
powershell -Command "Get-ChildItem -Path '%BACKUP_PATH%' -Directory | Sort-Object CreationTime -Descending | Select-Object -Skip 7 | Remove-Item -Recurse -Force"

echo.
echo Backup process complete!
echo.
pause
