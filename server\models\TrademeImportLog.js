const mongoose = require('mongoose');

/**
 * TrademeImportLog Schema
 * Tracks import operations for TradeMe items
 */
const trademeImportLogSchema = new mongoose.Schema({
  // Import statistics
  itemsProcessed: {
    type: Number,
    default: 0
  },
  newItems: {
    type: Number,
    default: 0
  },
  updatedItems: {
    type: Number,
    default: 0
  },
  duplicateItems: {
    type: Number,
    default: 0
  },
  failedItems: {
    type: Number,
    default: 0
  },
  apiCallsMade: {
    type: Number,
    default: 0
  },

  // Import status
  status: {
    type: String,
    enum: ['pending', 'in_progress', 'completed', 'failed'],
    default: 'pending'
  },

  // Error information
  error: {
    type: String
  },

  // Import details
  importType: {
    type: String,
    enum: ['manual', 'automatic'],
    default: 'manual'
  },

  // Listing type (active or sold)
  listingType: {
    type: String,
    enum: ['active', 'sold'],
    default: 'active'
  },

  // Start and end times
  startTime: {
    type: Date,
    default: Date.now
  },
  endTime: {
    type: Date
  },

  // Duration in milliseconds
  duration: {
    type: Number
  },

  // User who initiated the import
  initiatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },

  // Environment (production or sandbox)
  environment: {
    type: String,
    enum: ['production', 'sandbox'],
    default: 'production'
  },

  // Detailed logs
  logs: [{
    timestamp: {
      type: Date,
      default: Date.now
    },
    message: {
      type: String,
      required: true
    },
    level: {
      type: String,
      enum: ['info', 'warning', 'error'],
      default: 'info'
    },
    data: {
      type: mongoose.Schema.Types.Mixed
    }
  }],

  // Fetched items (stored temporarily and not saved to TradeMeItems collection until explicitly saved)
  fetchedItems: [{
    type: mongoose.Schema.Types.Mixed
  }],

  // Saved item IDs (references to items that have been saved to the database)
  savedItemIds: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'TradeMeItems'
  }],

  // Skipped item indices (indices of items in fetchedItems that were skipped)
  skippedItemIndices: [{
    type: Number
  }]
}, {
  timestamps: true
});

// Pre-save middleware to calculate duration
trademeImportLogSchema.pre('save', function(next) {
  if (this.endTime && this.startTime) {
    this.duration = this.endTime.getTime() - this.startTime.getTime();
  }
  next();
});

// Method to add a log entry
trademeImportLogSchema.methods.addLog = function(message, level = 'info', data = null) {
  this.logs.push({
    timestamp: new Date(),
    message,
    level,
    data
  });
  return this;
};

// Method to update import statistics
trademeImportLogSchema.methods.updateStats = function(stats) {
  Object.assign(this, stats);
  return this;
};

// Method to complete the import
trademeImportLogSchema.methods.complete = function(success = true) {
  this.status = success ? 'completed' : 'failed';
  this.endTime = new Date();
  return this;
};

// Method to fail the import with an error
trademeImportLogSchema.methods.fail = function(error) {
  this.status = 'failed';
  this.error = error.message || error;
  this.endTime = new Date();
  return this;
};

const TrademeImportLog = mongoose.model('TrademeImportLog', trademeImportLogSchema);

module.exports = TrademeImportLog;
