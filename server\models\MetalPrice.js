const mongoose = require('mongoose');

const metalPriceSchema = new mongoose.Schema({
  metal: {
    type: String,
    required: true,
    enum: ['Gold', 'Silver', 'Platinum', 'Palladium'],
    index: true
  },
  purity: {
    type: String,
    required: true,
    index: true
  },
  spotPrice: {
    type: Number,
    required: true,
    min: 0
  },
  minBuyPrice: {
    type: Number,
    required: true,
    min: 0
  },
  maxBuyPrice: {
    type: Number,
    required: true,
    min: 0
  },
  meltPrice: {
    type: Number,
    required: true,
    min: 0
  },
  lastUpdated: {
    type: Date,
    default: Date.now,
    index: true
  },
  nextUpdateAt: {
    type: Date,
    required: true
  }
}, {
  versionKey: false
});

// Create a compound index for fast lookups
metalPriceSchema.index({ metal: 1, purity: 1 }, { unique: true });

const MetalPrice = mongoose.model('MetalPrice', metalPriceSchema);

module.exports = MetalPrice;