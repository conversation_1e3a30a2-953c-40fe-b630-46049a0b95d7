const { randomUUID } = require('crypto');

const User = require('../models/User.js');
const { generatePasswordHash, validatePassword } = require('../utils/password.js');

class UserService {
  static async list() {
    try {
      return User.find();
    } catch (err) {
      throw new Error(`Database error while listing users: ${err}`);
    }
  }

  static async get(id) {
    try {
      return User.findOne({ _id: id }).exec();
    } catch (err) {
      throw new Error(`Database error while getting the user by their ID: ${err}`);
    }
  }

  static async getByEmail(email) {
    try {
      return User.findOne({ email }).exec();
    } catch (err) {
      throw new Error(`Database error while getting the user by their email: ${err}`);
    }
  }

  static async getByUsername(username) {
    try {
      return User.findOne({ username }).exec();
    } catch (err) {
      throw new Error(`Database error while getting the user by their username: ${err}`);
    }
  }

  static async isUsernameUnique(username, excludeUserId = null) {
    try {
      if (!username) return true;

      // Use a case-insensitive regex pattern to match the username
      const query = {
        username: { $regex: new RegExp(`^${username}$`, 'i') }
      };

      if (excludeUserId) {
        query._id = { $ne: excludeUserId };
      }

      const count = await User.countDocuments(query);
      return count === 0;
    } catch (err) {
      throw new Error(`Database error while checking username uniqueness: ${err}`);
    }
  }

  static async update(id, data) {
    try {
      // If username is being updated, check for uniqueness
      if (data.username !== undefined) {
        const isUnique = await this.isUsernameUnique(data.username, id);
        if (!isUnique) {
          throw new Error('Username already taken');
        }
      }

      return User.findOneAndUpdate({ _id: id }, data, { new: true, upsert: false });
    } catch (err) {
      throw new Error(`Database error while updating user ${id}: ${err}`);
    }
  }

  static async delete(id) {
    try {
      const result = await User.deleteOne({ _id: id }).exec();
      return (result.deletedCount === 1);
    } catch (err) {
      throw new Error(`Database error while deleting user ${id}: ${err}`);
    }
  }

  static async authenticateWithPassword(emailOrUsername, password) {
    if (!emailOrUsername) throw new Error('Email or username is required');
    if (!password) throw new Error('Password is required');

    try {
      // Try to find user by email first
      let user = await User.findOne({ email: emailOrUsername.toLowerCase() }).exec();

      // If not found by email, try by username with case-insensitive search
      if (!user && emailOrUsername) {
        user = await User.findOne({
          username: { $regex: new RegExp(`^${emailOrUsername}$`, 'i') }
        }).exec();
      }

      if (!user) return null;

      const passwordValid = await validatePassword(password, user.password);
      if (!passwordValid) return null;

      user.lastLoginAt = Date.now();
      const updatedUser = await user.save();
      return updatedUser;
    } catch (err) {
      throw new Error(`Database error while authenticating user with password: ${err}`);
    }
  }

  static async create({ email, username, password, fullName = '', name = '', role = 'employee', location = null }) {
    if (!email) throw new Error('Email is required');
    if (!password) throw new Error('Password is required');

    const existingUser = await UserService.getByEmail(email);
    if (existingUser) throw new Error('User with this email already exists');

    // Check username uniqueness if provided
    if (username) {
      const isUnique = await this.isUsernameUnique(username);
      if (!isUnique) {
        throw new Error('Username already taken');
      }
    }

    const hash = await generatePasswordHash(password);

    try {
      const user = new User({
        email,
        username,
        password: hash,
        fullName: fullName || name, // Use fullName if provided, otherwise fall back to name
        role,
        location,
      });

      await user.save();
      return user;
    } catch (err) {
      throw new Error(`Database error while creating new user: ${err}`);
    }
  }

  static async setPassword(user, password) {
    if (!password) throw new Error('Password is required');
    user.password = await generatePasswordHash(password); // eslint-disable-line

    try {
      if (!user.isNew) {
        await user.save();
      }

      return user;
    } catch (err) {
      throw new Error(`Database error while setting user password: ${err}`);
    }
  }
}

module.exports = UserService;