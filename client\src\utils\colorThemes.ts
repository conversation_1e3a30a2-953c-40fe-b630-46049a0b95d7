export interface ColorTheme {
  id: string;
  name: string;
  color: string;
  gradientFrom: string;
  gradientVia: string;
  gradientTo: string;
  overlayOpacity?: number;
}

export const colorThemes: ColorTheme[] = [
  // Default - Grey
  {
    id: 'ccgrey',
    name: '<PERSON> <PERSON>',
    color: '#57585a',
    gradientFrom: '#1a1a1b',
    gradientVia: '#57585a',
    gradientTo: '#2d2d2e',
    overlayOpacity: 0.7
  },
  // Original colors from the list
  {
    id: 'ccmaroon',
    name: 'CC Maroon',
    color: '#902b34',
    gradientFrom: '#3d1215',
    gradientVia: '#902b34',
    gradientTo: '#4a1619',
    overlayOpacity: 0.65
  },
  {
    id: 'ccyellow',
    name: 'CC Yellow',
    color: '#efb211',
    gradientFrom: '#8a6507',
    gradientVia: '#efb211',
    gradientTo: '#9e7308',
    overlayOpacity: 0.75
  },
  {
    id: 'ccbuysblue',
    name: '<PERSON> <PERSON><PERSON> Blue',
    color: '#136cb3',
    gradientFrom: '#0a3b66',
    gradientVia: '#136cb3',
    gradientTo: '#0b4675',
    overlayOpacity: 0.7
  },
  {
    id: 'ccpfblue',
    name: 'CC PF Blue',
    color: '#4fb4ce',
    gradientFrom: '#2a6b7a',
    gradientVia: '#4fb4ce',
    gradientTo: '#307d8d',
    overlayOpacity: 0.7
  },
  {
    id: 'ccpurple',
    name: 'CC Purple',
    color: '#575686',
    gradientFrom: '#2e2d40',
    gradientVia: '#575686',
    gradientTo: '#363552',
    overlayOpacity: 0.65
  },
  {
    id: 'ccgrey2',
    name: 'CC Grey 2',
    color: '#69696c',
    gradientFrom: '#3d3d3f',
    gradientVia: '#69696c',
    gradientTo: '#4a4a4c',
    overlayOpacity: 0.7
  },
  {
    id: 'purple',
    name: 'Purple',
    color: '#571c86',
    gradientFrom: '#2d0f45',
    gradientVia: '#571c86',
    gradientTo: '#3a1359',
    overlayOpacity: 0.65
  },

  // Additional 5 colors
  {
    id: 'emerald',
    name: 'Emerald',
    color: '#047857',
    gradientFrom: '#023b2b',
    gradientVia: '#047857',
    gradientTo: '#034a37',
    overlayOpacity: 0.7
  },
  {
    id: 'amber',
    name: 'Amber',
    color: '#d97706',
    gradientFrom: '#854804',
    gradientVia: '#d97706',
    gradientTo: '#9a5504',
    overlayOpacity: 0.75
  },
  {
    id: 'rose',
    name: 'Rose',
    color: '#be123c',
    gradientFrom: '#7f0b28',
    gradientVia: '#be123c',
    gradientTo: '#8f0e2d',
    overlayOpacity: 0.7
  },
  {
    id: 'slate',
    name: 'Slate',
    color: '#334155',
    gradientFrom: '#0f172a',
    gradientVia: '#334155',
    gradientTo: '#1e293b',
    overlayOpacity: 0.65
  },
  {
    id: 'indigo',
    name: 'Indigo',
    color: '#4f46e5',
    gradientFrom: '#3730a3',
    gradientVia: '#4f46e5',
    gradientTo: '#3730a3',
    overlayOpacity: 0.75
  }
];

export const getThemeById = (id: string): ColorTheme => {
  return colorThemes.find(theme => theme.id === id) || colorThemes[0];
};

export const getNextTheme = (currentThemeId: string): ColorTheme => {
  const currentIndex = colorThemes.findIndex(theme => theme.id === currentThemeId);
  const nextIndex = (currentIndex + 1) % colorThemes.length;
  return colorThemes[nextIndex];
};

// Local storage key
export const COLOR_THEME_STORAGE_KEY = 'gh0st-color-theme';

// Get theme from local storage or use default
export const getStoredTheme = (): ColorTheme => {
  if (typeof window === 'undefined') return colorThemes[0];

  const storedThemeId = localStorage.getItem(COLOR_THEME_STORAGE_KEY);
  return storedThemeId ? getThemeById(storedThemeId) : colorThemes[0];
};

// Save theme to local storage
export const saveThemeToStorage = (themeId: string): void => {
  if (typeof window !== 'undefined') {
    localStorage.setItem(COLOR_THEME_STORAGE_KEY, themeId);
  }
};
