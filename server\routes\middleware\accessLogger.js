/**
 * Enhanced Access Logger Middleware
 *
 * This middleware logs user access information including:
 * - User ID and username (if authenticated)
 * - User role (if authenticated)
 * - IP address
 * - Request method
 * - URL path accessed
 * - Query parameters
 * - User agent
 * - Timestamp
 * - Response time
 * - Request type (page, API, asset)
 * - Referrer
 * - Full URL
 * - Response status code
 *
 * Logs are both printed to the console and stored in the database
 */

const AccessLog = require('../../models/AccessLog');
const jwt = require('jsonwebtoken');
const UserService = require('../../services/userService');

const accessLogger = async (req, res, next) => {
  // Record start time for response time calculation
  const startTime = process.hrtime();

  // Get the request timestamp
  const timestamp = new Date().toISOString();

  // Get the IP address (considering all possible proxy headers)
  const getIpAddress = (req) => {
    // Since we're trusting the proxy, we can use Express's built-in IP detection
    const realIp = req.ip || 
                   req.connection.remoteAddress || 
                   req.socket.remoteAddress || 
                   req.connection.socket?.remoteAddress;
                 
    // Clean up IPv6 localhost and IPv4 mapped addresses
    // This will remove ::ffff: prefix and any other IPv6 formatting
    return realIp?.replace(/^::ffff:/i, '')  // Remove IPv6 prefix
                ?.replace(/^::1$/, '127.0.0.1')  // Replace IPv6 localhost with IPv4 localhost
                || 'unknown';
  };

  // Update the ip constant to use the new function
  const ip = getIpAddress(req);

  // Get the request method and path
  const method = req.method;
  const path = req.originalUrl || req.url;

  // Get the full URL
  const protocol = req.headers['x-forwarded-proto'] || req.protocol;
  const host = req.headers['host'] || 'unknown';
  const fullUrl = `${protocol}://${host}${path}`;

  // Get referrer
  const referrer = req.headers['referer'] || req.headers['referrer'] || '';

  // Extract page name from referrer
  let pageName = '';
  let pageUrl = '';
  if (referrer) {
    try {
      const referrerUrl = new URL(referrer);
      // Extract the pathname without leading slash
      let pathname = referrerUrl.pathname;
      pageUrl = pathname; // Store the original page URL

      if (pathname.startsWith('/')) {
        pathname = pathname.substring(1);
      }
      // If it's empty, it's the home page
      if (!pathname) {
        pageName = 'Home';
      } else {
        // Split by slashes and capitalize each segment
        const segments = pathname.split('/');
        pageName = segments.map(segment => {
          // Capitalize first letter of each segment
          if (segment) {
            return segment.charAt(0).toUpperCase() + segment.slice(1);
          }
          return '';
        }).join(' > ');
      }

    } catch (error) {
      console.error('Error extracting page name from referrer:', error);
    }
  }

  // Determine request type (page, API, asset, auth, user)
  let requestType = 'unknown';
  try {
    if (path.startsWith('/api/auth/login') || path.startsWith('/api/auth/logout')) {
      requestType = 'auth';
    } else if (path.startsWith('/api/users') && (method === 'POST' || method === 'PUT')) {
      requestType = 'user';
    } else if (path.startsWith('/api/')) {
      requestType = 'api';
    } else if (path.match(/\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot)$/i)) {
      requestType = 'asset';
    } else {
      requestType = 'page';
    }
  } catch (error) {
    console.error('Error determining request type:', error);
    // Default to unknown if there's an error
    requestType = 'unknown';
  }

  // User information (if authenticated)
  // Enhanced user detection - check multiple possible locations for user info
  let userId = 'unauthenticated';
  let username = 'unauthenticated';
  let fullName = '';
  let userRole = 'none';

  // Check for JWT token in Authorization header
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    try {
      // Extract the token
      const token = authHeader.split(' ')[1];

      // Try to verify the token
      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET);

        if (decoded && decoded.sub) {
          // We have a user ID from the token
          userId = decoded.sub;

          // Since we have a valid token, let's try to get the user from the database
          try {
            // Use UserService to get the user
            const user = await UserService.get(decoded.sub);
            if (user) {
              username = user.username || user.email || 'authenticated-user';
              fullName = user.fullName || '';
              userRole = user.role || 'unknown';
            } else {
              username = 'authenticated-user';
            }
          } catch (userError) {
            console.error('Error finding user for log:', userError);
            username = 'authenticated-user';
          }
        }
      } catch (jwtError) {
        // If token verification fails, try to decode it without verification
        // This is just for logging purposes, not for authentication
        try {
          const decoded = jwt.decode(token);
          if (decoded && decoded.sub) {
            userId = decoded.sub;
            username = 'authenticated-user';
          }
        } catch (decodeError) {
          console.error('Error decoding token for logging:', decodeError);
        }
      }
    } catch (error) {
      // If token parsing fails, just continue with unauthenticated
      console.error('Error parsing auth token for logging:', error);
    }
  }

  // Check req.user (standard Express auth) as fallback
  if (req.user) {
    userId = req.user._id || 'unknown-id';

    // Get username - prioritize actual username field
    if (req.user.username) {
      username = req.user.username;
    } else if (req.user.email) {
      username = req.user.email;
    } else if (req.user.name) {
      username = req.user.name;
    } else if (req.user.displayName) {
      username = req.user.displayName;
    }

    // Get full name
    if (req.user.fullName) {
      fullName = req.user.fullName;
    } else if (req.user.firstName && req.user.lastName) {
      fullName = `${req.user.firstName} ${req.user.lastName}`;
    } else if (req.user.name) {
      fullName = req.user.name;
    }

    userRole = req.user.role || req.user.userRole || 'unknown';
  }

  // Check session (if no user found yet)
  else if (req.session && req.session.user) {
    userId = req.session.user._id || 'unknown-session-id';
    username = req.session.user.username || req.session.user.email || 'unknown-session-user';
    fullName = req.session.user.fullName || '';
    userRole = req.session.user.role || 'unknown-session-role';
  }

  // Get user agent
  const userAgent = req.headers['user-agent'] || 'unknown';

  // Get query parameters (if any)
  const hasQueryParams = Object.keys(req.query).length > 0;
  const queryParams = hasQueryParams ? JSON.stringify(req.query) : 'none';

  // Format the log message with color coding for request type
  let requestTypeLabel;
  switch (requestType) {
    case 'api':
      requestTypeLabel = '\x1b[36m[API]\x1b[0m'; // Cyan for API
      break;
    case 'page':
      requestTypeLabel = '\x1b[32m[PAGE]\x1b[0m'; // Green for pages
      break;
    case 'asset':
      requestTypeLabel = '\x1b[90m[ASSET]\x1b[0m'; // Gray for assets
      break;
    case 'auth':
      requestTypeLabel = '\x1b[35m[AUTH]\x1b[0m'; // Magenta for authentication
      break;
    case 'user':
      requestTypeLabel = '\x1b[33m[USER]\x1b[0m'; // Yellow for user management
      break;
    default:
      requestTypeLabel = '\x1b[37m[UNKNOWN]\x1b[0m'; // White for unknown
  }

  const logMessage = [
    `\x1b[33m[ACCESS]\x1b[0m ${timestamp}`,
    `User: ${username}`,
    `IP: ${ip}`,
    `${method} ${path}`,
  ].filter(Boolean).join(' | ');

  // Create a log object to store
  const logData = {
    timestamp: new Date(timestamp),
    userId: userId !== 'unauthenticated' ? userId : null,
    username,
    fullName,
    userRole,
    ipAddress: ip,
    method,
    path,
    fullUrl,
    queryParams: hasQueryParams ? req.query : {},
    userAgent,
    requestType,
    referrer,
    pageName
  };

  // Capture the response status code after the response is sent
  const originalEnd = res.end;
  res.end = function(chunk, encoding) {
    // Restore the original end function
    res.end = originalEnd;
    // Call the original end function
    res.end(chunk, encoding);

    // Calculate response time
    const hrTime = process.hrtime(startTime);
    const responseTimeMs = Math.round(hrTime[0] * 1000 + hrTime[1] / 1000000);
    logData.responseTime = responseTimeMs;

    // Now capture the status code
    logData.statusCode = res.statusCode;

    // Color code for status
    let statusColor = '\x1b[32m'; // Green for success (2xx)
    if (res.statusCode >= 300 && res.statusCode < 400) {
      statusColor = '\x1b[36m'; // Cyan for redirects (3xx)
    } else if (res.statusCode >= 400 && res.statusCode < 500) {
      statusColor = '\x1b[33m'; // Yellow for client errors (4xx)
    } else if (res.statusCode >= 500) {
      statusColor = '\x1b[31m'; // Red for server errors (5xx)
    }

    // Add status code and response time to console log
    console.log(`${logMessage} | Status: ${statusColor}${res.statusCode}\x1b[0m | Time: ${responseTimeMs}ms`);

    // Save to database
    try {
      const accessLog = new AccessLog(logData);
      accessLog.save().catch(err => {
        console.error('Error saving access log to database:', err);
      });

      // If this is an API request and we have a page name, create a separate page view log
      if (requestType === 'api' && pageName && !req.originalUrl.includes('/access-logs')) {
        // Only log page views for non-access-logs endpoints to avoid recursive logging
        const pageViewLog = new AccessLog({
          timestamp: new Date(timestamp),
          userId: userId !== 'unauthenticated' ? userId : null,
          username,
          fullName,
          userRole,
          ipAddress: ip,
          method: 'GET',
          path: pageUrl || '/',
          fullUrl: referrer,
          queryParams: {},
          userAgent,
          requestType: 'page',
          referrer: '',
          pageName,
          statusCode: 200, // Assume page view was successful
          responseTime: 0  // No response time for page views
        });

        // Save the page view log to the database
        pageViewLog.save()
          .then(() => {
            console.log(`\x1b[32m[PAGE VIEW]\x1b[0m ${timestamp} | User: ${username} | Page: ${pageName}`);
          })
          .catch(err => {
            console.error('Error saving page view log:', err);
          });
      }
    } catch (error) {
      console.error('Error creating access log:', error);
    }
  };

  // Continue with the request
  next();
};

module.exports = accessLogger;



