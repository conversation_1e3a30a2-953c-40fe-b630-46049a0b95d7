const express = require('express');
const router = express.Router();
const CustomerService = require('../services/customerService');
const { requireUser } = require('./middleware/auth');

// Get all customers with pagination
router.get('/', requireUser, async (req, res) => {
  try {
    // Extract pagination parameters from query string
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const search = req.query.search || '';

    console.log(`Requesting customers - page: ${page}, limit: ${limit}, search term: ${search ? `"${search}"` : 'none'}`);
    
    const result = await CustomerService.getAll(page, limit, search);
    
    console.log(`Retrieved ${result.customers.length} customers. Total: ${result.pagination.total}, Pages: ${result.pagination.totalPages}`);
    
    res.status(200).json(result);
  } catch (error) {
    console.error(`Error fetching customers: ${error.stack}`);
    res.status(500).json({ error: error.message });
  }
});

// Get a single customer by ID
router.get('/:id', requireUser, async (req, res) => {
  try {
    const customer = await CustomerService.getById(req.params.id);
    res.status(200).json({ customer });
  } catch (error) {
    console.error(`Error fetching customer: ${error.stack}`);
    if (error.message === 'Customer not found') {
      return res.status(404).json({ error: error.message });
    }
    res.status(500).json({ error: error.message });
  }
});

// Create a new customer
router.post('/', requireUser, async (req, res) => {
  try {
    const customer = await CustomerService.create(req.body);
    res.status(201).json({
      success: true,
      message: 'Customer created successfully',
      customer
    });
  } catch (error) {
    console.error(`Error creating customer: ${error.stack}`);
    if (error.message.includes('already exists')) {
      return res.status(400).json({ error: error.message });
    }
    res.status(500).json({ error: error.message });
  }
});

// Update an existing customer
router.put('/:id', requireUser, async (req, res) => {
  try {
    const customer = await CustomerService.update(req.params.id, req.body);
    res.status(200).json({
      success: true,
      message: 'Customer updated successfully',
      customer
    });
  } catch (error) {
    console.error(`Error updating customer: ${error.stack}`);
    if (error.message === 'Customer not found') {
      return res.status(404).json({ error: error.message });
    }
    if (error.message.includes('already exists')) {
      return res.status(400).json({ error: error.message });
    }
    res.status(500).json({ error: error.message });
  }
});

// Delete a customer
router.delete('/:id', requireUser, async (req, res) => {
  try {
    await CustomerService.delete(req.params.id);
    res.status(200).json({
      success: true,
      message: 'Customer deleted successfully'
    });
  } catch (error) {
    console.error(`Error deleting customer: ${error.stack}`);
    if (error.message === 'Customer not found') {
      return res.status(404).json({ error: error.message });
    }
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;