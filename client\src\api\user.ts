import api from './api';

export interface User {
  _id: string;
  email: string;
  username?: string;
  fullName: string;
  role: 'admin' | 'manager' | 'employee';
  location: {
    _id: string;
    name: string;
    address: string;
    phone: string;
    isActive: boolean;
  } | null;
  createdAt: string;
  lastLoginAt: string;
  isActive: boolean;
  deactivatedAt?: string;
  refreshToken?: string;
}

export interface UserListItem extends User {
  lastLoginAt: string;
}

// Auth-related endpoints
export const getCurrentUser = async (): Promise<User> => {
  try {
    const response = await api.get('/auth/me');
    return response.data;
  } catch (error) {
    console.error('Error fetching current user:', error);
    throw new Error(error?.response?.data?.message || error.message);
  }
};

export const updateCurrentUser = async (data: {
  role?: string;
  location?: string | null;
  username?: string;
  email?: string;
  fullName?: string;
}): Promise<User> => {
  try {
    const response = await api.patch('/auth/me', data);
    return response.data;
  } catch (error) {
    console.error('Error updating user:', error);
    throw new Error(error?.response?.data?.message || error.message);
  }
};

export const updateUserPassword = async (data: {
  currentPassword: string;
  newPassword: string;
}): Promise<{ message: string }> => {
  try {
    const response = await api.patch('/auth/me/password', data);
    return response.data;
  } catch (error) {
    console.error('Error updating password:', error);
    throw new Error(error?.response?.data?.message || error.message);
  }
};

export const checkUsernameAvailability = async (
  username: string,
  userId?: string
): Promise<boolean> => {
  try {
    const response = await api.get(`/auth/check-username/${username}`, {
      params: userId ? { userId } : {}
    });
    return response.data.available;
  } catch (error) {
    console.error('Error checking username availability:', error);
    throw new Error(error?.response?.data?.message || error.message);
  }
};

// User Management endpoints for Settings
export const getAllUsers = async (): Promise<UserListItem[]> => {
  try {
    const response = await api.get('/auth/users');
    return response.data;
  } catch (error) {
    console.error('Error fetching users:', error);
    throw new Error(error?.response?.data?.message || error.message);
  }
};

export const createUser = async (userData: {
  email: string;
  username?: string;
  fullName: string;
  password: string;
  role: 'admin' | 'manager' | 'employee';
  location?: string;
}): Promise<User> => {
  try {
    // Send the data directly without modification
    const response = await api.post('/auth/register', userData);
    return response.data;
  } catch (error) {
    console.error('Error creating user:', error);
    throw new Error(error?.response?.data?.message || error.message);
  }
};

export const updateUser = async (userId: string, data: {
  email?: string;
  username?: string;
  fullName?: string;
  role?: 'admin' | 'manager' | 'employee';
  location?: string | null;
}): Promise<User> => {
  try {
    const response = await api.patch(`/auth/users/${userId}`, data);
    return response.data;
  } catch (error) {
    console.error('Error updating user:', error);
    throw new Error(error?.response?.data?.message || error.message);
  }
};

export const deleteUser = async (userId: string): Promise<void> => {
  try {
    await api.delete(`/auth/users/${userId}`);
  } catch (error) {
    console.error('Error deleting user:', error);
    throw new Error(error?.response?.data?.message || error.message);
  }
};

export const resetUserPassword = async (userId: string, newPassword: string): Promise<{ message: string }> => {
  try {
    const response = await api.post(`/auth/users/${userId}/reset-password`, { newPassword });
    return response.data;
  } catch (error) {
    console.error('Error resetting user password:', error);
    throw new Error(error?.response?.data?.message || error.message);
  }
};

export const toggleUserStatus = async (userId: string): Promise<User> => {
  try {
    const response = await api.patch(`/auth/users/${userId}/toggle-status`);
    return response.data;
  } catch (error) {
    console.error('Error toggling user status:', error);
    throw new Error(error?.response?.data?.message || error.message);
  }
};
