import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import {
  getDeviceCheckerSettings,
  updateDeviceCheckerSettings,
  verifyDeviceCheckerConnection,
  refreshDeviceCheckerServices,
  DeviceCheckerSettings as DeviceCheckerSettingsType
} from '@/api/deviceChecker';
import { useToast } from '@/hooks/useToast';
import { CheckIcon, Loader2, RefreshCw, ExternalLink } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

export function DeviceCheckerSettings() {
  const [settings, setSettings] = useState<DeviceCheckerSettingsType>({
    url: 'https://api.ifreeicloud.co.uk',
    apiKey: '',
    enabledServices: [],
    accountBalance: 0,
    lastUpdated: new Date().toISOString()
  });

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [verifying, setVerifying] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'none' | 'success' | 'error'>('none');
  const [connectionMessage, setConnectionMessage] = useState('');
  const [refreshingServices, setRefreshingServices] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      const response = await getDeviceCheckerSettings();
      if (response.success) {
        setSettings(response.settings);
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to fetch device checker settings",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSaveSettings = async () => {
    try {
      setSaving(true);

      // Create a copy of settings to send to the server
      let settingsToSave: Partial<DeviceCheckerSettingsType> = { ...settings };

      // If the API key is masked (contains X's and has the exact format of a masked key),
      // don't send it to the server
      if (settingsToSave.apiKey && /^[X-]+[^X]{1,4}$/.test(settingsToSave.apiKey)) {
        console.log('API key is masked, not sending to server');
        // Create a new object without the apiKey
        const { apiKey, ...rest } = settingsToSave;
        settingsToSave = rest;
      } else if (settingsToSave.apiKey) {
        console.log('Sending new API key to server');
      }

      const response = await updateDeviceCheckerSettings(settingsToSave);
      if (response.success) {
        // Update the settings with the response, but preserve the original API key if it was masked
        setSettings(prev => ({
          ...response.settings,
          // If we didn't send the API key, keep the current one in the UI
          apiKey: settingsToSave.apiKey === undefined ? prev.apiKey : response.settings.apiKey
        }));

        toast({
          title: "Success",
          description: "Device checker settings updated successfully",
        });
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to update device checker settings",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  const handleVerifyConnection = async () => {
    try {
      // Reset connection status
      setConnectionStatus('none');
      setConnectionMessage('');
      setVerifying(true);

      const response = await verifyDeviceCheckerConnection();

      if (response.success) {
        // Set success status and message
        setConnectionStatus('success');
        setConnectionMessage(`Connection verified successfully.`);

        // Update the balance in settings
        setSettings(prev => ({
          ...prev,
          accountBalance: response.balance || 0,
          lastUpdated: new Date().toISOString()
        }));
      } else {
        // Set error status and message
        setConnectionStatus('error');
        setConnectionMessage(response.error || "Failed to verify connection");
      }
    } catch (error: any) {
      // Set error status and message
      setConnectionStatus('error');
      setConnectionMessage(error.message || "Failed to verify connection");
    } finally {
      setVerifying(false);
    }
  };

  const handleRefreshServices = async () => {
    try {
      setRefreshingServices(true);
      // Use the new refreshDeviceCheckerServices function to get services from the external API
      const response = await refreshDeviceCheckerServices();
      if (response.success && response.services) {
        setSettings(prev => ({
          ...prev,
          enabledServices: response.services || [],
          lastUpdated: new Date().toISOString()
        }));
        toast({
          title: "Success",
          description: `Retrieved ${response.services.length} services from external API successfully`,
        });
      } else {
        toast({
          title: "Error",
          description: response.error || "Failed to refresh services from external API",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to refresh services from external API",
        variant: "destructive",
      });
    } finally {
      setRefreshingServices(false);
    }
  };

  const toggleServiceEnabled = async (serviceId: string, enabled: boolean) => {
    // Update local state first for immediate UI feedback
    const updatedSettings = {
      ...settings,
      enabledServices: settings.enabledServices.map(service =>
        service.serviceId === serviceId ? { ...service, enabled } : service
      )
    };

    setSettings(updatedSettings);

    // Create a copy of settings to send to the server
    let settingsToSave: Partial<DeviceCheckerSettingsType> = { ...updatedSettings };

    // If the API key is masked (contains X's and has the exact format of a masked key),
    // don't send it to the server
    if (settingsToSave.apiKey && /^[X-]+[^X]{1,4}$/.test(settingsToSave.apiKey)) {
      console.log('API key is masked, not sending to server (toggle)');
      // Create a new object without the apiKey
      const { apiKey, ...rest } = settingsToSave;
      settingsToSave = rest;
    } else if (settingsToSave.apiKey) {
      console.log('Sending API key to server (toggle)');
    }

    // Then save to the database
    try {
      await updateDeviceCheckerSettings(settingsToSave);
    } catch (error: any) {
      // If there's an error, revert the change
      toast({
        title: "Error",
        description: error.message || "Failed to update service status",
        variant: "destructive",
      });

      // Revert the local state
      setSettings(settings);
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Device Lookup Tool Settings</CardTitle>
        <CardDescription>Configure API settings for the device lookup tool</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Two-column layout for API settings and Account Balance */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Left column - API Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">API Settings</h3>

            <div className="space-y-2">
              <div className="flex justify-between">
                <Label htmlFor="apiKey">API Key</Label>
                {settings.apiKey && /^[X-]+[^X]{1,4}$/.test(settings.apiKey) && (
                  <span className="text-xs text-muted-foreground">
                    Masked key with last 4 characters visible
                  </span>
                )}
              </div>
              <Input
                id="apiKey"
                type="text" // Use text type to show the X's and last 4 characters
                value={settings.apiKey}
                onChange={(e) => {
                  // Always allow changes to the API key field
                  // The server will handle masking when returning the data
                  setSettings({ ...settings, apiKey: e.target.value });
                }}
                onPaste={() => {
                  // Handle paste events normally
                  // The server will handle masking when returning the data
                }}
                placeholder="API Key (format: XXX-XXX-XXX-XXX-XXX-XXX-XXX-XXXX)"
                className="font-mono" // Use monospace font for better readability
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="url">API URL</Label>
              <Input
                id="url"
                value={settings.url}
                onChange={(e) => setSettings({ ...settings, url: e.target.value })}
                placeholder="API URL"
              />
            </div>

            <div className="space-y-2">
              <div className="flex space-x-4 items-center">
                <Button
                  onClick={handleVerifyConnection}
                  variant={connectionStatus === 'error' ? 'destructive' : 'outline'}
                  className={connectionStatus === 'success' ? 'bg-green-500 text-white hover:bg-green-600' : ''}
                  disabled={verifying}
                >
                  {verifying ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <RefreshCw className="mr-2 h-4 w-4" />}
                  Verify Connection
                </Button>
                <Button onClick={handleSaveSettings} disabled={saving}>
                  {saving ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <CheckIcon className="mr-2 h-4 w-4" />}
                  Save Settings
                </Button>
              </div>

              {/* Connection status message */}
              {connectionStatus !== 'none' && (
                <div className={`text-sm mt-2 ${connectionStatus === 'success' ? 'text-green-500' : 'text-red-500'}`}>
                  {connectionMessage}
                </div>
              )}
            </div>
          </div>

          {/* Right column - Account Balance */}
          <div className="space-y-4 border rounded-lg p-4">
            <h3 className="text-lg font-medium">Account Balance</h3>

            <div className="flex flex-col items-center justify-center py-4">
              <div className="text-3xl font-bold mb-2">${settings.accountBalance.toFixed(2)}</div>
              <p className="text-sm text-muted-foreground">
                Last updated: {settings.lastUpdated ? formatDistanceToNow(new Date(settings.lastUpdated), { addSuffix: true }) : 'Never'}
              </p>
            </div>

            <Button variant="outline" className="w-full" asChild>
              <a href="https://ifreeicloud.co.uk/client-area/add-funds" target="_blank" rel="noopener noreferrer">
                Top Up Balance <ExternalLink className="ml-2 h-4 w-4" />
              </a>
            </Button>
          </div>
        </div>

        <Separator className="my-4" />

        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">Available Services</h3>
            <Button onClick={handleRefreshServices} variant="outline" size="sm" disabled={refreshingServices}>
              {refreshingServices ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <RefreshCw className="mr-2 h-4 w-4" />}
              Refresh Services
            </Button>
          </div>

          {settings.enabledServices.length === 0 ? (
            <p className="text-sm text-muted-foreground">No services available. Click "Refresh Services" to fetch available services.</p>
          ) : (
            <div className="border rounded-md">
              <table className="w-full">
                <thead>
                  <tr className="border-b bg-muted/50">
                    <th className="text-left p-2 pl-4">Service Name</th>
                    <th className="text-center p-2">Cost</th>
                    <th className="text-center p-2">Status</th>
                    <th className="text-right p-2 pr-4">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {/* Sort services alphabetically by name */}
                  {[...settings.enabledServices]
                    .sort((a, b) => a.name.localeCompare(b.name))
                    .map((service, index) => (
                    <tr key={service.serviceId} className={index % 2 === 0 ? 'bg-background' : 'bg-muted/20'}>
                      <td className="p-2 pl-4">
                        <Input
                          value={service.name}
                          onChange={(e) => {
                            const updatedSettings = {
                              ...settings,
                              enabledServices: settings.enabledServices.map(s =>
                                s.serviceId === service.serviceId ? { ...s, name: e.target.value } : s
                              )
                            };
                            setSettings(updatedSettings);
                          }}
                          onBlur={() => handleSaveSettings()}
                          className="h-8"
                        />
                      </td>
                      <td className="text-center p-2">${service.cost.toFixed(2)}</td>
                      <td className="text-center p-2">
                        <div className="flex justify-center">
                          <Switch
                            id={`service-${service.serviceId}`}
                            checked={service.enabled}
                            onCheckedChange={(checked) => toggleServiceEnabled(service.serviceId, checked)}
                          />
                        </div>
                      </td>
                      <td className="text-right p-2 pr-4">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleSaveSettings()}
                          title="Save changes to this service"
                        >
                          <CheckIcon className="h-4 w-4" />
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
