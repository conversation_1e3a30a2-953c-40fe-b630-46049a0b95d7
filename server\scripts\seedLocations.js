require('dotenv').config();
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configuration
const API_URL = 'http://localhost:3000/api/locations/seed';
const TOKEN_FILE = path.join(__dirname, 'token.txt');

async function seedLocations() {
  try {
    // Check if token file exists
    let token = '';
    if (fs.existsSync(TOKEN_FILE)) {
      token = fs.readFileSync(TOKEN_FILE, 'utf8').trim();
    }

    // If no token saved, prompt user
    if (!token) {
      console.log('No access token found in token.txt file.');
      console.log('Please login through the web interface and then paste your access token here:');
      // In a real implementation, you'd want to set up a proper input mechanism
      console.log('(Create a token.txt file in the scripts directory with your token)');
      return;
    }

    console.log('Seeding locations...');

    // Make the request to seed locations
    const response = await axios.post(API_URL, {}, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    console.log('Success!');
    console.log(JSON.stringify(response.data, null, 2));

  } catch (error) {
    console.error('Error seeding locations:');
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error('Response:', error.response.data);
    } else {
      console.error(error.message);
    }
  }
}

seedLocations();