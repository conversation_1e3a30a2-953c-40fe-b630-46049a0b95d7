import { useState } from 'react';
import { format } from 'date-fns';
import { Trash2 } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { ItemNote as ItemNoteType } from '@/api/notes';
import { useAuth } from '@/contexts/AuthContext';

interface ItemNoteProps {
  note: ItemNoteType;
  onDelete: (noteId: string) => Promise<void>;
}

export function ItemNote({ note, onDelete }: ItemNoteProps) {
  const { user } = useAuth();
  const [isDeleting, setIsDeleting] = useState(false);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return format(date, 'PPp'); // e.g. "Mar 15, 2023, 2:30 PM"
  };

  const handleDelete = async () => {
    try {
      setIsDeleting(true);
      await onDelete(note._id);
    } catch (error) {
      console.error('Error deleting note:', error);
      setIsDeleting(false);
    }
  };

  // Check if current user can delete this note
  const canDelete =
    (user?._id && note.userId && user._id === note.userId._id) || // Creator can delete
    user?.role === 'admin' ||       // Admin can delete
    user?.role === 'manager';       // Manager can delete

  return (
    <div className="border-b border-border/40 pb-2 last:border-0 last:pb-0">
      <div className="whitespace-pre-wrap text-sm">{note.content}</div>
      <div className="flex justify-between items-center text-xs text-muted-foreground mt-1">
        <div>
          {note.userId ? (note.userId.name || note.userId.username || note.userId.email || 'Unknown User') : 'Unknown User'} • {formatDate(note.createdAt)}
        </div>
        {canDelete && (
          <Button
            variant="ghost"
            size="icon"
            className="h-5 w-5 -mr-1"
            onClick={handleDelete}
            disabled={isDeleting}
          >
            <Trash2 className="h-3 w-3" />
          </Button>
        )}
      </div>
    </div>
  );
}