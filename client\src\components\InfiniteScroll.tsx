import { useEffect, useRef, useState } from 'react';

interface InfiniteScrollProps {
  loadMore: () => Promise<boolean>; // Function to load more data, returns true if more data available
  hasMore: boolean; // Whether there is more data to load
  isLoading: boolean; // Whether data is currently being loaded
  threshold?: number; // Distance from bottom to trigger load (in pixels)
  children: React.ReactNode;
  loadingComponent?: React.ReactNode; // Custom loading component
}

export function InfiniteScroll({
  loadMore,
  hasMore,
  isLoading,
  threshold = 200,
  children,
  loadingComponent
}: InfiniteScrollProps) {
  const sentinel = useRef<HTMLDivElement>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      async (entries) => {
        const [entry] = entries;

        // If the sentinel is visible and we're not already loading and there's more to load
        if (entry.isIntersecting && !loading && hasMore && !isLoading) {
          setLoading(true);
          try {
            await loadMore();
          } finally {
            setLoading(false);
          }
        }
      },
      {
        root: null,
        rootMargin: `0px 0px ${threshold}px 0px`,
        threshold: 0.1
      }
    );

    if (sentinel.current) {
      observer.observe(sentinel.current);
    }

    return () => {
      if (sentinel.current) {
        observer.unobserve(sentinel.current);
      }
    };
  }, [loadMore, hasMore, loading, isLoading, threshold]);

  return (
    <>
      {children}
      <div ref={sentinel} className="h-1 w-full" />
      {loading && (
        <div className="py-4">
          {loadingComponent ? (
            loadingComponent
          ) : (
            <div className="flex justify-center">
              <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-primary"></div>
            </div>
          )}
        </div>
      )}
    </>
  );
}