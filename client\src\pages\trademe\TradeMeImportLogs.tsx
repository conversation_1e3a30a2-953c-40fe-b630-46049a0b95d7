import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, AlertCircle, ArrowLeft, Eye } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { getImportLogs } from '@/api/tradeMeImport';
import { formatDate } from '@/lib/utils';
import { Pagination } from '@/components/CustomPagination';

const TradeMeImportLogs: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [logs, setLogs] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 1
  });

  // Check if user has permission to access this page
  const hasPermission = user?.role === 'admin' || user?.role === 'manager';

  useEffect(() => {
    fetchLogs();
  }, [pagination.page, pagination.limit]);

  const fetchLogs = async () => {
    try {
      setIsLoading(true);
      const result = await getImportLogs(pagination.page, pagination.limit);

      if (result.success) {
        setLogs(result.logs);
        setPagination(result.pagination);
      } else {
        setError(result.error || 'Failed to load import logs');
      }
    } catch (error: any) {
      console.error('Error loading import logs:', error);
      setError(error.message || 'Failed to load import logs');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline">Pending</Badge>;
      case 'in_progress':
        return <Badge variant="secondary">In Progress</Badge>;
      case 'completed':
        return <Badge variant="success">Completed</Badge>;
      case 'failed':
        return <Badge variant="destructive">Failed</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  if (!hasPermission) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Import Logs</CardTitle>
          <CardDescription>View history of TradeMe import operations</CardDescription>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Access Denied</AlertTitle>
            <AlertDescription>
              You do not have permission to access this page.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Import Logs</CardTitle>
          <CardDescription>View history of TradeMe import operations</CardDescription>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm" onClick={() => navigate('/trademe/import')}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Import
          </Button>
          <Button variant="default" size="sm" onClick={() => navigate('/trademe/import/items')}>
            Manage Imported Items
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        ) : error ? (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        ) : logs.length === 0 ? (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>No Logs Found</AlertTitle>
            <AlertDescription>No import logs have been created yet.</AlertDescription>
          </Alert>
        ) : (
          <div className="space-y-4">
            <div className="border rounded-md overflow-hidden">
              <table className="w-full">
                <thead className="bg-muted">
                  <tr>
                    <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground">Date</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground">Status</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground">Environment</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground">Items</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground">Initiated By</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground">Duration</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y">
                  {logs.map((log) => (
                    <tr key={log._id} className="hover:bg-muted/50">
                      <td className="px-4 py-2 text-sm">{formatDate(log.startTime)}</td>
                      <td className="px-4 py-2">{getStatusBadge(log.status)}</td>
                      <td className="px-4 py-2 text-sm">
                        <Badge variant={log.environment === 'production' ? 'default' : 'outline'}>
                          {log.environment === 'production' ? 'Production' : 'Sandbox'}
                        </Badge>
                      </td>
                      <td className="px-4 py-2 text-sm">{log.itemsProcessed || 0}</td>
                      <td className="px-4 py-2 text-sm">
                        {log.initiatedBy?.fullName || log.initiatedBy?.username || 'Unknown'}
                      </td>
                      <td className="px-4 py-2 text-sm">
                        {log.duration ? `${(log.duration / 1000).toFixed(2)}s` : 'N/A'}
                      </td>
                      <td className="px-4 py-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => navigate(`/trademe/import/logs/${log._id}`)}
                        >
                          <Eye className="h-4 w-4" />
                          <span className="sr-only">View</span>
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {pagination.pages > 1 && (
              <div className="flex justify-center mt-4">
                <Pagination
                  currentPage={pagination.page}
                  totalPages={pagination.pages}
                  onPageChange={handlePageChange}
                />
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default TradeMeImportLogs;
