import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { DatePicker } from '@/components/ui/date-picker';
import { CalendarIcon, Loader2 } from 'lucide-react';
import { getHappyOrNotAvailableYears } from '@/api/happyOrNot';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export interface DateRange {
  startDate: Date | undefined;
  endDate: Date | undefined;
  label?: string;
}

interface EnhancedDateRangeProps {
  onSelectDateRange: (range: DateRange) => void;
  currentDateRange: DateRange;
  currentPreset: string;
}

export const EnhancedDateRange: React.FC<EnhancedDateRangeProps> = ({
  onSelectDateRange,
  currentDateRange,
  currentPreset
}) => {
  const [availableYears, setAvailableYears] = useState<number[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [localStartDate, setLocalStartDate] = useState<Date | undefined>(currentDateRange.startDate);
  const [localEndDate, setLocalEndDate] = useState<Date | undefined>(currentDateRange.endDate);
  const [activeTab, setActiveTab] = useState<string>('presets');

  // Fetch available years on component mount
  useEffect(() => {
    const fetchAvailableYears = async () => {
      setLoading(true);
      try {
        const response = await getHappyOrNotAvailableYears();
        if (response.success) {
          setAvailableYears(response.data);
        } else {
          console.error('Failed to fetch available years');
          // Fallback to default years if API fails
          const currentYear = new Date().getFullYear();
          setAvailableYears(Array.from({ length: currentYear - 2017 }, (_, i) => 2018 + i));
        }
      } catch (error) {
        console.error('Error fetching available years:', error);
        // Fallback to default years if API fails
        const currentYear = new Date().getFullYear();
        setAvailableYears(Array.from({ length: currentYear - 2017 }, (_, i) => 2018 + i));
      } finally {
        setLoading(false);
      }
    };

    fetchAvailableYears();
  }, []);

  // Update local dates when currentDateRange changes
  useEffect(() => {
    setLocalStartDate(currentDateRange.startDate);
    setLocalEndDate(currentDateRange.endDate);
  }, [currentDateRange]);

  // Helper function to get the first day of the current month
  const getFirstDayOfMonth = () => {
    const now = new Date();
    return new Date(now.getFullYear(), now.getMonth(), 1);
  };

  // Helper function to get the first day of the current week (Monday)
  const getFirstDayOfWeek = () => {
    const now = new Date();
    const day = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const diff = now.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
    return new Date(now.setDate(diff));
  };

  // Helper function to get the first day of the current year
  const getFirstDayOfYear = () => {
    const now = new Date();
    return new Date(now.getFullYear(), 0, 1);
  };

  // Helper function to get the start of a specific year
  const getYearStart = (year: number) => {
    return new Date(year, 0, 1);
  };

  // Helper function to get the end of a specific year
  const getYearEnd = (year: number) => {
    const date = new Date(year, 11, 31, 23, 59, 59, 999);
    return date;
  };

  // Get current date with time set to end of day
  const getEndOfToday = () => {
    const now = new Date();
    now.setHours(23, 59, 59, 999);
    return now;
  };

  // Calculate date ranges for presets
  const presets = {
    mtd: {
      label: 'Month to Date',
      startDate: getFirstDayOfMonth(),
      endDate: getEndOfToday(),
    },
    wtd: {
      label: 'Week to Date',
      startDate: getFirstDayOfWeek(),
      endDate: getEndOfToday(),
    },
    last7Days: {
      label: 'Last 7 Days',
      startDate: new Date(new Date().setDate(new Date().getDate() - 7)),
      endDate: getEndOfToday(),
    },
    last14Days: {
      label: 'Last 14 Days',
      startDate: new Date(new Date().setDate(new Date().getDate() - 14)),
      endDate: getEndOfToday(),
    },
    lastMonth: {
      label: 'Last Month',
      startDate: new Date(new Date().setDate(new Date().getDate() - 30)),
      endDate: getEndOfToday(),
    },
    last4Months: {
      label: 'Last 4 Months',
      startDate: new Date(new Date().setMonth(new Date().getMonth() - 4)),
      endDate: getEndOfToday(),
    },
    last6Months: {
      label: 'Last 6 Months',
      startDate: new Date(new Date().setMonth(new Date().getMonth() - 6)),
      endDate: getEndOfToday(),
    },
    lastYear: {
      label: 'Last Year',
      startDate: new Date(new Date().setFullYear(new Date().getFullYear() - 1)),
      endDate: getEndOfToday(),
    },
    ytd: {
      label: 'Year to Date',
      startDate: getFirstDayOfYear(),
      endDate: getEndOfToday(),
    },
  };

  // Handle preset selection
  const handlePresetSelect = (preset: keyof typeof presets) => {
    const selectedPreset = presets[preset];
    onSelectDateRange({
      startDate: selectedPreset.startDate,
      endDate: selectedPreset.endDate,
      label: selectedPreset.label
    });
    setIsOpen(false);
  };

  // Handle year selection
  const handleYearSelect = (year: number) => {
    onSelectDateRange({
      startDate: getYearStart(year),
      endDate: getYearEnd(year),
      label: year.toString()
    });
    setIsOpen(false);
  };

  // Handle custom date range
  const handleApplyCustomRange = () => {
    if (localStartDate && localEndDate) {
      onSelectDateRange({
        startDate: localStartDate,
        endDate: localEndDate,
        label: 'Custom Range'
      });
      setIsOpen(false);
    }
  };

  return (
    <div className="w-full">
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button variant="outline" className="w-full justify-between">
            <div className="flex items-center">
              <CalendarIcon className="mr-2 h-4 w-4" />
              <span>{currentPreset}</span>
            </div>
            <div className="text-xs text-muted-foreground">
              {currentDateRange.startDate && currentDateRange.endDate ? (
                `${currentDateRange.startDate.toLocaleDateString()} - ${currentDateRange.endDate.toLocaleDateString()}`
              ) : (
                'Select date range'
              )}
            </div>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <div className="p-4 space-y-4">
            <Tabs defaultValue="presets" value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="presets">Presets</TabsTrigger>
                <TabsTrigger value="years">Years</TabsTrigger>
                <TabsTrigger value="custom">Custom</TabsTrigger>
              </TabsList>

              <TabsContent value="presets" className="space-y-2">
                <div className="grid grid-cols-2 gap-2">
                  <Button
                    variant="outline"
                    onClick={() => handlePresetSelect('mtd')}
                    className={currentPreset === 'Month to Date' ? 'border-primary' : ''}
                  >
                    Month to Date
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => handlePresetSelect('wtd')}
                    className={currentPreset === 'Week to Date' ? 'border-primary' : ''}
                  >
                    Week to Date
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => handlePresetSelect('last7Days')}
                    className={currentPreset === 'Last 7 Days' ? 'border-primary' : ''}
                  >
                    Last 7 Days
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => handlePresetSelect('last14Days')}
                    className={currentPreset === 'Last 14 Days' ? 'border-primary' : ''}
                  >
                    Last 14 Days
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => handlePresetSelect('lastMonth')}
                    className={currentPreset === 'Last Month' ? 'border-primary' : ''}
                  >
                    Last Month
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => handlePresetSelect('last4Months')}
                    className={currentPreset === 'Last 4 Months' ? 'border-primary' : ''}
                  >
                    Last 4 Months
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => handlePresetSelect('last6Months')}
                    className={currentPreset === 'Last 6 Months' ? 'border-primary' : ''}
                  >
                    Last 6 Months
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => handlePresetSelect('lastYear')}
                    className={currentPreset === 'Last Year' ? 'border-primary' : ''}
                  >
                    Last Year
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => handlePresetSelect('ytd')}
                    className={`col-span-2 ${currentPreset === 'Year to Date' ? 'border-primary' : ''}`}
                  >
                    Year to Date
                  </Button>
                </div>
              </TabsContent>

              <TabsContent value="years" className="space-y-2">
                {loading ? (
                  <div className="flex items-center justify-center py-4">
                    <Loader2 className="h-6 w-6 animate-spin mr-2" />
                    <span>Loading years...</span>
                  </div>
                ) : (
                  <div className="grid grid-cols-3 gap-2 max-h-60 overflow-y-auto">
                    {availableYears.map(year => (
                      <Button
                        key={year}
                        variant="outline"
                        onClick={() => handleYearSelect(year)}
                        className={currentPreset === year.toString() ? 'border-primary' : ''}
                      >
                        {year}
                      </Button>
                    ))}
                  </div>
                )}
              </TabsContent>

              <TabsContent value="custom" className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground mb-2">Start Date</p>
                    <DatePicker
                      date={localStartDate}
                      setDate={setLocalStartDate}
                      className="w-full"
                    />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground mb-2">End Date</p>
                    <DatePicker
                      date={localEndDate}
                      setDate={setLocalEndDate}
                      className="w-full"
                    />
                  </div>
                </div>
                <Button
                  onClick={handleApplyCustomRange}
                  disabled={!localStartDate || !localEndDate}
                  className="w-full"
                >
                  Apply Custom Range
                </Button>
              </TabsContent>
            </Tabs>

          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default EnhancedDateRange;
