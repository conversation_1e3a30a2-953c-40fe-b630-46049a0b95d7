import { useEffect, useState } from 'react';
import { getContacts, Contact, PaginationData } from '@/api/contacts';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { But<PERSON> } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Search, UserPlus, Mail, Phone, MapPin, Filter } from 'lucide-react';
import { ContactForm } from '@/components/ContactForm';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogDescription } from '@/components/ui/dialog';
import { useToast } from '@/hooks/useToast';
import { PaginationControls } from '@/components/ui/pagination-controls';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

export function AddressBook() {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearch, setDebouncedSearch] = useState('');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [contactType, setContactType] = useState<string | undefined>(undefined);
  const [pagination, setPagination] = useState<PaginationData>({
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 0
  });

  const { toast } = useToast();
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();

  // Initialize state from URL parameters
  useEffect(() => {
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const type = searchParams.get('type') || undefined;

    setPagination(prev => ({
      ...prev,
      page,
      limit
    }));

    setSearchTerm(search);
    setDebouncedSearch(search);
    setContactType(type);
  }, [searchParams]);

  // Debounce search input
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(searchTerm);
      // Update URL with search term
      handleUrlUpdate(pagination.page, pagination.limit, searchTerm, contactType);
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Fetch contacts when page, limit, or search changes
  useEffect(() => {
    fetchContacts(pagination.page, pagination.limit, debouncedSearch, contactType);
  }, [pagination.page, pagination.limit, debouncedSearch, contactType]);

  const handleUrlUpdate = (page: number, limit: number, search: string, type?: string) => {
    const params = new URLSearchParams();

    if (page !== 1) params.set('page', page.toString());
    if (limit !== 10) params.set('limit', limit.toString());
    if (search) params.set('search', search);
    if (type) params.set('type', type);

    setSearchParams(params);
  };

  const fetchContacts = async (page: number, limit: number, search: string, type?: string) => {
    setLoading(true);
    try {
      const data = await getContacts(page, limit, search, type);
      setContacts(data.contacts);
      setPagination(data.pagination);
    } catch (error) {
      console.error('Failed to fetch contacts:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.message || 'Failed to fetch contacts',
      });
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
    handleUrlUpdate(page, pagination.limit, searchTerm, contactType);
  };

  const handleLimitChange = (limit: number) => {
    console.log(`AddressBook: Changing limit to ${limit} (${typeof limit})`);
    // When changing limit, reset to page 1
    setPagination(prev => ({ ...prev, limit, page: 1 }));
    // Use setTimeout to ensure state is updated before fetching
    setTimeout(() => {
      // Ensure limit is a number
      const numericLimit = Number(limit);
      handleUrlUpdate(1, numericLimit, searchTerm, contactType);
      fetchContacts(1, numericLimit, debouncedSearch, contactType);
    }, 0);
  };

  const handleContactCreated = () => {
    setIsDialogOpen(false);
    fetchContacts(pagination.page, pagination.limit, debouncedSearch, contactType);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleTypeChange = (value: string) => {
    setContactType(value === 'all' ? undefined : value);
    // Reset to page 1 when changing type
    setPagination(prev => ({ ...prev, page: 1 }));
    handleUrlUpdate(1, pagination.limit, searchTerm, value === 'all' ? undefined : value);
  };

  if (loading && contacts.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Loading contacts...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Address Book</h1>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button className="gap-2">
              <UserPlus className="h-4 w-4" />
              Add Contact
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add New Contact</DialogTitle>
              <DialogDescription>
                Create a new contact record. At least one contact method (email or phone) is required.
              </DialogDescription>
            </DialogHeader>
            <ContactForm onSuccess={handleContactCreated} />
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Search & Filter</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search contacts..."
                value={searchTerm}
                onChange={handleSearchChange}
                className="pl-9"
              />
            </div>
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <Select value={contactType || 'all'} onValueChange={handleTypeChange}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Filter by type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Contacts</SelectItem>
                  <SelectItem value="customer">Customers</SelectItem>
                  <SelectItem value="store">Stores</SelectItem>
                  <SelectItem value="repairer">Repairers</SelectItem>
                  <SelectItem value="utility">Utilities</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {loading && contacts.length > 0 ? (
        <Card className="relative min-h-[300px] opacity-60">
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
          </div>
          <CardContent>
            <div className="opacity-0">
              {/* Invisible content to maintain layout */}
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Phone</TableHead>
                    <TableHead>Address</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {contacts.map((contact) => (
                    <TableRow key={contact._id}></TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      ) : contacts.length > 0 ? (
        <Card>
          <CardContent className="pt-6">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Phone</TableHead>
                  <TableHead>Address</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {contacts.map((contact) => (
                  <TableRow key={contact._id} className="cursor-pointer hover:bg-muted/50" onClick={() => navigate(`/address-book/${contact._id}`)}>
                    <TableCell className="font-medium">{contact.name}</TableCell>
                    <TableCell>
                      <span className="capitalize">{contact.type}</span>
                    </TableCell>
                    <TableCell>
                      {contact.email ? (
                        <div className="flex items-center gap-1">
                          <Mail className="h-3 w-3 text-muted-foreground" />
                          <span>{contact.email}</span>
                        </div>
                      ) : (
                        <span className="text-muted-foreground">-</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {contact.phone ? (
                        <div className="flex items-center gap-1">
                          <Phone className="h-3 w-3 text-muted-foreground" />
                          <span>{contact.phone}</span>
                        </div>
                      ) : (
                        <span className="text-muted-foreground">-</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {contact.address ? (
                        <div className="flex items-center gap-1">
                          <MapPin className="h-3 w-3 text-muted-foreground" />
                          <span className="truncate max-w-[200px]">{contact.address}</span>
                        </div>
                      ) : (
                        <span className="text-muted-foreground">-</span>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            {/* Pagination Controls */}
            <div className="mt-6">
              <PaginationControls
                currentPage={pagination.page}
                totalPages={pagination.totalPages}
                totalItems={pagination.total}
                limit={pagination.limit}
                onPageChange={handlePageChange}
                onLimitChange={handleLimitChange}
              />
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <div className="text-muted-foreground text-center">
              <p className="mb-4">No contacts found</p>
              <Button variant="outline" onClick={() => setIsDialogOpen(true)}>
                Add your first contact
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}