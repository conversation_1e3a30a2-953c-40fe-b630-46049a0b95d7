/**
 * BuyPawnCatchup Model
 * 
 * Represents staff catch-up notes related to audited buy/pawn transactions.
 * This model stores documentation of discussions with staff about specific audited transactions.
 */

const mongoose = require('mongoose');

/**
 * MAIN SCHEMA
 * BuyPawnCatchup Schema - Represents staff catch-up notes for audited transactions
 */
const buyPawnCatchupSchema = new mongoose.Schema({
  /**
   * REFERENCES
   */
  auditId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'BuyPawnAudit',
    required: true,
    index: true
  },
  dealId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'BuyPawnDeal',
    required: true,
    index: true
  },
  transactionId: {
    type: String,
    required: true
  },
  
  /**
   * EMPLOYEE REFERENCE
   */
  employee: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  /**
   * CATCH-UP DETAILS
   */
  date: {
    type: Date,
    required: true,
    default: Date.now
  },
  conductedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  /**
   * DISCUSSION CONTENT
   */
  findingsDiscussed: {
    type: String,
    required: true
  },
  commentsBefore: {
    type: String
  },
  discussionPoints: {
    type: String,
    required: true
  },
  employeeResponse: {
    type: String
  },
  agreedActions: {
    type: String
  },
  commentsAfter: {
    type: String
  },
  
  /**
   * STATUS
   */
  status: {
    type: String,
    enum: ['draft', 'finalized'],
    default: 'draft'
  },
  
  /**
   * METADATA
   */
  createdAt: {
    type: Date,
    default: Date.now,
    immutable: true
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  finalizedAt: {
    type: Date
  }
}, {
  toJSON: { getters: true },
  toObject: { getters: true }
});

// Pre-save middleware to update timestamps
buyPawnCatchupSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

module.exports = mongoose.model('BuyPawnCatchup', buyPawnCatchupSchema);
