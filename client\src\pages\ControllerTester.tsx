import { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Loader2, Gamepad, AlertCircle, Info, Usb,
  Volume2, VolumeX, Sliders, RotateCw, Vibrate,
  Lightbulb, Zap, Smartphone
} from 'lucide-react';
import { useToast } from '@/hooks/useToast';
import { hidManager, HIDDeviceInfo as HIDDeviceInfoType, DualSense } from '@/utils/hidUtils';
import HIDControllerFeatures from '@/components/HIDControllerFeatures';
import HIDDeviceInfoCard from '@/components/HIDDeviceInfo';
import ControllerSVG from '@/components/ControllerSVG';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';

interface GamepadState {
  id: string;
  index: number;
  buttons: {
    pressed: boolean;
    value: number;
    touched?: boolean;
  }[];
  axes: number[];
  timestamp: number;
  mapping: string;
  connected: boolean;
}

interface StickDriftData {
  leftStick: {
    x: number;
    y: number;
    driftDetected: boolean;
  };
  rightStick: {
    x: number;
    y: number;
    driftDetected: boolean;
  };
}

interface GyroscopeData {
  pitch: number; // x-axis rotation
  yaw: number;   // y-axis rotation
  roll: number;  // z-axis rotation
}

interface AccelerometerData {
  x: number;
  y: number;
  z: number;
}

const ControllerTester = () => {
  const [gamepads, setGamepads] = useState<GamepadState[]>([]);
  const [activeGamepad, setActiveGamepad] = useState<number | null>(null);
  const [controllerType, setControllerType] = useState<string>('dualsense');
  const [stuckButtons, setStuckButtons] = useState<number[]>([]);
  const [stickDrift, setStickDrift] = useState<StickDriftData>({
    leftStick: { x: 0, y: 0, driftDetected: false },
    rightStick: { x: 0, y: 0, driftDetected: false }
  });
  const [isCalibrating, setIsCalibrating] = useState(false);
  const [calibrationCountdown, setCalibrationCountdown] = useState(0);
  const [deadzone, setDeadzone] = useState(0.05); // Default deadzone value
  const [activeTab, setActiveTab] = useState('gamepad');
  const [hidDevice, setHidDevice] = useState<HIDDeviceInfoType | null>(null);
  const [isHIDSupported, setIsHIDSupported] = useState(false);
  const [gyroscope, setGyroscope] = useState<GyroscopeData>({ pitch: 0, yaw: 0, roll: 0 });
  const [accelerometer, setAccelerometer] = useState<AccelerometerData>({ x: 0, y: 0, z: 0 });
  const [batteryLevel, setBatteryLevel] = useState(100);
  const [isCharging, setIsCharging] = useState(false);
  const [micStatus, setMicStatus] = useState(false);
  const [headphoneStatus, setHeadphoneStatus] = useState(false);
  const [valueDisplayOff, setValueDisplayOff] = useState(true);
  const [audioTestActive, setAudioTestActive] = useState(false);
  const [lightbarColor, setLightbarColor] = useState({ r: 0, g: 0, b: 255 }); // Default blue
  const [rumbleHeavy, setRumbleHeavy] = useState(0);
  const [rumbleSoft, setRumbleSoft] = useState(0);
  const [leftTriggerMode, setLeftTriggerMode] = useState("off");
  const [rightTriggerMode, setRightTriggerMode] = useState("off");
  const [speakerVolume, setSpeakerVolume] = useState(0);
  const [headphoneVolume, setHeadphoneVolume] = useState(0);
  const [micLightEnabled, setMicLightEnabled] = useState(false);
  const animationRef = useRef<number>();
  const stuckButtonTimers = useRef<{ [key: number]: number }>({});
  const audioContext = useRef<AudioContext | null>(null);
  const oscillatorRef = useRef<OscillatorNode | null>(null);
  const { toast } = useToast();

  // Initialize gamepad detection and HID support
  useEffect(() => {
    // Check if WebHID is supported
    setIsHIDSupported(hidManager.isWebHIDSupported());

    // Set up standard gamepad API
    window.addEventListener('gamepadconnected', handleGamepadConnected);
    window.addEventListener('gamepaddisconnected', handleGamepadDisconnected);

    // Start the animation loop for standard gamepad API
    animationRef.current = requestAnimationFrame(updateGamepadState);

    // Set up HID event listeners
    if (hidManager.isWebHIDSupported()) {
      // Check for previously authorized devices
      hidManager.getDevices().then(devices => {
        if (devices.length > 0) {
          setHidDevice(devices[0]);

          // Determine controller type based on vendor/product ID
          const vendorId = devices[0].vendorId;
          const productId = devices[0].productId;

          if (vendorId === 0x054c) { // Sony
            if (productId === 0x0ce6 || productId === 0x0df2) {
              setControllerType('dualsense');
            } else if (productId === 0x09cc || productId === 0x05c4) {
              setControllerType('dualshock');
            }
          }
        }
      });

      // Set up HID device connection/disconnection listeners
      hidManager.onDeviceConnected((device) => {
        setHidDevice(device);

        // Determine controller type based on vendor/product ID
        const vendorId = device.vendorId;
        const productId = device.productId;

        if (vendorId === 0x054c) { // Sony
          if (productId === 0x0ce6 || productId === 0x0df2) {
            setControllerType('dualsense');
          } else if (productId === 0x09cc || productId === 0x05c4) {
            setControllerType('dualshock');
          }
        }
      });

      hidManager.onDeviceDisconnected((device) => {
        if (hidDevice && hidDevice.device === device.device) {
          setHidDevice(null);
        }
      });

      // Set up input report handler for DualSense controller
      hidManager.onInputReport((device, reportId, data) => {
        // Only process if it's a Sony DualSense controller
        if (device.vendorId === 0x054c && (device.productId === 0x0ce6 || device.productId === 0x0df2)) {
          // Process DualSense input report
          if (reportId === 0x01) { // Standard input report
            // Battery level and status (byte 53 for DualSense)
            const batteryData = data.getUint8(53);
            const batteryStatus = (batteryData >> 4) & 0x0F; // Upper 4 bits
            const batteryLevel = Math.min(batteryData & 0x0F, 10) * 10; // Lower 4 bits, scale to percentage

            setBatteryLevel(batteryLevel);
            setIsCharging(batteryStatus === 0x01); // 0x01 = charging, 0x02 = charged, 0x0F = full

            // Headphone and mic status (byte 54)
            const peripheralStatus = data.getUint8(54);
            setHeadphoneStatus((peripheralStatus & 0x01) !== 0);
            setMicStatus((peripheralStatus & 0x04) !== 0);

            // Gyroscope data (bytes 16-21)
            // Convert from 16-bit signed integers
            const gyroX = data.getInt16(16, true) / 1024; // Pitch
            const gyroY = data.getInt16(18, true) / 1024; // Yaw
            const gyroZ = data.getInt16(20, true) / 1024; // Roll

            setGyroscope({
              pitch: gyroX,
              yaw: gyroY,
              roll: gyroZ
            });

            // Accelerometer data (bytes 22-27)
            const accelX = data.getInt16(22, true) / 8192; // X
            const accelY = data.getInt16(24, true) / 8192; // Y
            const accelZ = data.getInt16(26, true) / 8192; // Z

            setAccelerometer({
              x: accelX,
              y: accelY,
              z: accelZ
            });
          }
        }
      });
    }

    return () => {
      window.removeEventListener('gamepadconnected', handleGamepadConnected);
      window.removeEventListener('gamepaddisconnected', handleGamepadDisconnected);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [hidDevice]);

  const handleGamepadConnected = (event: GamepadEvent) => {
    const gamepad = event.gamepad;

    toast({
      title: "Gamepad Connected",
      description: `${gamepad.id} connected at index ${gamepad.index}`,
    });

    // Create a new gamepad state object
    const newGamepadState: GamepadState = {
      id: gamepad.id,
      index: gamepad.index,
      buttons: gamepad.buttons.map(button => ({
        pressed: button.pressed,
        value: button.value,
        touched: button.touched
      })),
      axes: Array.from(gamepad.axes),
      timestamp: gamepad.timestamp,
      mapping: gamepad.mapping,
      connected: true
    };

    // Update the gamepads state
    setGamepads(prevGamepads => {
      const updatedGamepads = [...prevGamepads];
      updatedGamepads[gamepad.index] = newGamepadState;
      return updatedGamepads;
    });

    // Set as active gamepad if none is selected
    if (activeGamepad === null) {
      setActiveGamepad(gamepad.index);
    }

    // Determine controller type based on ID
    const gamepadId = gamepad.id.toLowerCase();
    if (gamepadId.includes('dualsense') || gamepadId.includes('ps5')) {
      setControllerType('dualsense');
    } else if (gamepadId.includes('dualshock') || gamepadId.includes('ps4')) {
      setControllerType('dualshock');
    }
  };

  const handleGamepadDisconnected = (event: GamepadEvent) => {
    toast({
      title: "Gamepad Disconnected",
      description: `${event.gamepad.id} disconnected from index ${event.gamepad.index}`,
      variant: "destructive",
    });

    if (activeGamepad === event.gamepad.index) {
      // Find another connected gamepad if available
      const availableGamepads = navigator.getGamepads();
      let nextGamepad = null;

      for (let i = 0; i < availableGamepads.length; i++) {
        if (availableGamepads[i] && i !== event.gamepad.index) {
          nextGamepad = i;
          break;
        }
      }

      setActiveGamepad(nextGamepad);
    }
  };

  const updateGamepadState = () => {
    const availableGamepads = navigator.getGamepads();
    const updatedGamepads: GamepadState[] = [];
    const newStuckButtons: number[] = [...stuckButtons];

    for (let i = 0; i < availableGamepads.length; i++) {
      const gamepad = availableGamepads[i];

      if (gamepad) {
        // Convert gamepad to a serializable object
        const gamepadState: GamepadState = {
          id: gamepad.id,
          index: gamepad.index,
          buttons: gamepad.buttons.map(button => ({
            pressed: button.pressed,
            value: button.value,
            touched: 'touched' in button ? button.touched : undefined
          })),
          axes: Array.from(gamepad.axes),
          timestamp: gamepad.timestamp,
          mapping: gamepad.mapping,
          connected: gamepad.connected
        };

        updatedGamepads[gamepad.index] = gamepadState;

        // Check for active gamepad
        if (activeGamepad === gamepad.index) {
          // Check for stuck buttons
          gamepadState.buttons.forEach((button, buttonIndex) => {
            if (button.pressed) {
              if (!stuckButtonTimers.current[buttonIndex]) {
                // Start timer for this button
                stuckButtonTimers.current[buttonIndex] = Date.now();
              } else if (Date.now() - stuckButtonTimers.current[buttonIndex] > 3000) {
                // Button has been pressed for more than 3 seconds
                if (!newStuckButtons.includes(buttonIndex)) {
                  newStuckButtons.push(buttonIndex);
                }
              }
            } else {
              // Button is not pressed, clear timer
              delete stuckButtonTimers.current[buttonIndex];

              // Remove from stuck buttons if it was there
              const stuckIndex = newStuckButtons.indexOf(buttonIndex);
              if (stuckIndex !== -1) {
                newStuckButtons.splice(stuckIndex, 1);
              }
            }
          });

          // Check for stick drift
          if (!isCalibrating) {
            const leftX = gamepadState.axes[0];
            const leftY = gamepadState.axes[1];
            const rightX = gamepadState.axes[2] || gamepadState.axes[3] || 0;
            const rightY = gamepadState.axes[3] || gamepadState.axes[4] || 0;

            // Detect drift (if stick is slightly moved without user input)
            const newStickDrift = {
              leftStick: {
                x: leftX,
                y: leftY,
                driftDetected: Math.abs(leftX) > deadzone && Math.abs(leftX) < 0.25 ||
                               Math.abs(leftY) > deadzone && Math.abs(leftY) < 0.25
              },
              rightStick: {
                x: rightX,
                y: rightY,
                driftDetected: Math.abs(rightX) > deadzone && Math.abs(rightX) < 0.25 ||
                                Math.abs(rightY) > deadzone && Math.abs(rightY) < 0.25
              }
            };

            setStickDrift(newStickDrift);
          }
        }
      }
    }

    setGamepads(updatedGamepads);
    setStuckButtons(newStuckButtons);

    // Continue the animation loop
    animationRef.current = requestAnimationFrame(updateGamepadState);
  };

  const startCalibration = () => {
    setIsCalibrating(true);
    setCalibrationCountdown(5);

    const countdownInterval = setInterval(() => {
      setCalibrationCountdown(prev => {
        if (prev <= 1) {
          clearInterval(countdownInterval);
          finishCalibration();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const finishCalibration = () => {
    setIsCalibrating(false);
    toast({
      title: "Calibration Complete",
      description: "Controller has been calibrated. Drift detection is now active.",
    });
  };

  const selectGamepad = (index: number) => {
    setActiveGamepad(index);
    setStuckButtons([]);
    stuckButtonTimers.current = {};
  };

  const getButtonName = (index: number): string => {
    // Standard mapping for Xbox-like controllers
    const buttonNames: { [key: number]: string } = {
      0: 'A',
      1: 'B',
      2: 'X',
      3: 'Y',
      4: 'LB',
      5: 'RB',
      6: 'LT',
      7: 'RT',
      8: 'Back',
      9: 'Start',
      10: 'LS',
      11: 'RS',
      12: 'DPad Up',
      13: 'DPad Down',
      14: 'DPad Left',
      15: 'DPad Right',
      16: 'Home'
    };

    return buttonNames[index] || `Button ${index}`;
  };

  const getAxisName = (index: number): string => {
    // Standard mapping for axes
    const axisNames: { [key: number]: string } = {
      0: 'Left Stick X',
      1: 'Left Stick Y',
      2: 'Right Stick X',
      3: 'Right Stick Y'
    };

    return axisNames[index] || `Axis ${index}`;
  };

  const renderGamepadSelector = () => {
    return (
      <div className="flex flex-wrap gap-2 mb-4">
        {gamepads.map((gamepad, index) => {
          if (gamepad && gamepad.connected) {
            return (
              <Button
                key={index}
                variant={activeGamepad === index ? "default" : "outline"}
                onClick={() => selectGamepad(index)}
              >
                <Gamepad className="mr-2 h-4 w-4" />
                Gamepad {index}
              </Button>
            );
          }
          return null;
        })}

        {!gamepads.some(gamepad => gamepad && gamepad.connected) && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              No gamepads detected. Connect a controller and press any button to activate it.
            </AlertDescription>
          </Alert>
        )}
      </div>
    );
  };

  const renderActiveGamepad = () => {
    if (activeGamepad === null || !gamepads[activeGamepad]) {
      return (
        <Card>
          <CardHeader>
            <CardTitle>No Active Gamepad</CardTitle>
            <CardDescription>
              Connect a gamepad and press any button to begin testing.
            </CardDescription>
          </CardHeader>
        </Card>
      );
    }

    const gamepad = gamepads[activeGamepad];

    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>Gamepad: {gamepad.id}</CardTitle>
              <Badge variant="outline">
                {gamepad.mapping === 'standard' ? 'Standard Mapping' : 'Non-standard Mapping'}
              </Badge>
            </div>
            <CardDescription>
              Index: {gamepad.index} | Buttons: {gamepad.buttons.length} | Axes: {gamepad.axes.length}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-6 items-center">
              <div className="w-full md:w-1/2">
                {isCalibrating ? (
                  <div className="text-center p-6">
                    <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2">Calibrating Controller</h3>
                    <p className="text-muted-foreground mb-4">
                      Please don't touch any buttons or sticks for {calibrationCountdown} seconds...
                    </p>
                  </div>
                ) : (
                  <Button onClick={startCalibration}>Calibrate Controller</Button>
                )}
              </div>
              <div className="w-full md:w-1/2">
                <ControllerSVG
                  controllerType={controllerType}
                  className="w-auto h-40 mx-auto"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Buttons Display */}
        <Card>
          <CardHeader>
            <CardTitle>Buttons</CardTitle>
            <CardDescription>
              Press buttons to test their functionality
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
              {gamepad.buttons.map((button, index) => (
                <div
                  key={index}
                  className={`p-4 border rounded-lg flex flex-col items-center justify-center transition-colors ${
                    button.pressed
                      ? 'bg-primary text-primary-foreground'
                      : 'bg-card'
                  } ${
                    stuckButtons.includes(index)
                      ? 'border-destructive'
                      : 'border-border'
                  }`}
                >
                  <span className="text-lg font-medium">{getButtonName(index)}</span>
                  <span className="text-sm mt-1">
                    {button.pressed ? 'Pressed' : 'Released'}
                    {button.value !== 0 && button.value !== 1 && ` (${button.value.toFixed(2)})`}
                  </span>
                  {stuckButtons.includes(index) && (
                    <Badge variant="destructive" className="mt-2">Stuck!</Badge>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Axes Display */}
        <Card>
          <CardHeader>
            <CardTitle>Analog Sticks & Axes</CardTitle>
            <CardDescription>
              Move sticks to test their range and check for drift
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Left Stick */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Left Stick</h3>
                <div className="relative w-40 h-40 mx-auto border border-border rounded-full">
                  <div
                    className={`absolute w-8 h-8 rounded-full transform -translate-x-1/2 -translate-y-1/2 ${
                      stickDrift.leftStick.driftDetected
                        ? 'bg-destructive'
                        : 'bg-primary'
                    }`}
                    style={{
                      left: `${(gamepad.axes[0] * 50) + 50}%`,
                      top: `${(gamepad.axes[1] * 50) + 50}%`
                    }}
                  />
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-1 h-full bg-muted-foreground/20" />
                  </div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-full h-1 bg-muted-foreground/20" />
                  </div>
                  <div
                    className="absolute rounded-full border border-dashed border-muted-foreground/50"
                    style={{
                      width: `${deadzone * 100}%`,
                      height: `${deadzone * 100}%`,
                      left: '50%',
                      top: '50%',
                      transform: 'translate(-50%, -50%)'
                    }}
                  />
                </div>
                <div className="text-center space-y-1">
                  <div>X: {gamepad.axes[0].toFixed(4)}</div>
                  <div>Y: {gamepad.axes[1].toFixed(4)}</div>
                  {stickDrift.leftStick.driftDetected && (
                    <Badge variant="destructive">Drift Detected!</Badge>
                  )}
                </div>
              </div>

              {/* Right Stick */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Right Stick</h3>
                <div className="relative w-40 h-40 mx-auto border border-border rounded-full">
                  <div
                    className={`absolute w-8 h-8 rounded-full transform -translate-x-1/2 -translate-y-1/2 ${
                      stickDrift.rightStick.driftDetected
                        ? 'bg-destructive'
                        : 'bg-primary'
                    }`}
                    style={{
                      left: `${((gamepad.axes[2] || gamepad.axes[3] || 0) * 50) + 50}%`,
                      top: `${((gamepad.axes[3] || gamepad.axes[4] || 0) * 50) + 50}%`
                    }}
                  />
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-1 h-full bg-muted-foreground/20" />
                  </div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-full h-1 bg-muted-foreground/20" />
                  </div>
                  <div
                    className="absolute rounded-full border border-dashed border-muted-foreground/50"
                    style={{
                      width: `${deadzone * 100}%`,
                      height: `${deadzone * 100}%`,
                      left: '50%',
                      top: '50%',
                      transform: 'translate(-50%, -50%)'
                    }}
                  />
                </div>
                <div className="text-center space-y-1">
                  <div>X: {(gamepad.axes[2] || gamepad.axes[3] || 0).toFixed(4)}</div>
                  <div>Y: {(gamepad.axes[3] || gamepad.axes[4] || 0).toFixed(4)}</div>
                  {stickDrift.rightStick.driftDetected && (
                    <Badge variant="destructive">Drift Detected!</Badge>
                  )}
                </div>
              </div>
            </div>

            {/* Additional Axes (if any) */}
            {gamepad.axes.length > 4 && (
              <div className="mt-6">
                <h3 className="text-lg font-medium mb-4">Additional Axes</h3>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                  {gamepad.axes.slice(4).map((value, index) => (
                    <div key={index + 4} className="p-4 border rounded-lg">
                      <div className="text-sm font-medium">{getAxisName(index + 4)}</div>
                      <div className="mt-2">
                        <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                          <div
                            className="h-full bg-primary transition-all"
                            style={{ width: `${((value + 1) / 2) * 100}%` }}
                          />
                        </div>
                        <div className="text-sm mt-1 text-center">{value.toFixed(4)}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Raw Data Display */}
        <Card>
          <CardHeader>
            <CardTitle>Raw Gamepad Data</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="bg-muted p-4 rounded-md text-sm overflow-auto max-h-60">
              {JSON.stringify(gamepad, null, 2)}
            </pre>
          </CardContent>
        </Card>
      </div>
    );
  };

  // Connect to HID device
  const connectHIDDevice = async () => {
    const device = await hidManager.requestDevice();
    if (device) {
      setHidDevice(device);
      setActiveTab('hid');
    }
  };

  // Audio test functions
  const startAudioTest = (type: 'headphone' | 'speaker', frequency: number) => {
    if (!audioContext.current) {
      try {
        audioContext.current = new (window.AudioContext || (window as any).webkitAudioContext)();
      } catch (error) {
        console.error('Failed to create AudioContext:', error);
        toast({
          title: "Audio Error",
          description: "Your browser doesn't support Web Audio API.",
          variant: "destructive",
        });
        return;
      }
    }

    // Stop any existing oscillator
    stopAudioTest();

    try {
      // Create oscillator
      const oscillator = audioContext.current.createOscillator();
      oscillator.type = 'sine';
      oscillator.frequency.setValueAtTime(frequency, audioContext.current.currentTime);

      // Create gain node for volume control
      const gainNode = audioContext.current.createGain();
      gainNode.gain.setValueAtTime(0.5, audioContext.current.currentTime);

      // Connect nodes
      oscillator.connect(gainNode);
      gainNode.connect(audioContext.current.destination);

      // Start oscillator
      oscillator.start();
      oscillatorRef.current = oscillator;
      setAudioTestActive(true);

      toast({
        title: `${type === 'headphone' ? 'Headphone' : 'Speaker'} Test`,
        description: `Playing ${frequency}Hz sine wave`,
      });
    } catch (error) {
      console.error('Failed to start audio test:', error);
      toast({
        title: "Audio Error",
        description: "Failed to start audio test.",
        variant: "destructive",
      });
    }
  };

  const stopAudioTest = () => {
    if (oscillatorRef.current) {
      try {
        oscillatorRef.current.stop();
        oscillatorRef.current.disconnect();
        oscillatorRef.current = null;
        setAudioTestActive(false);
      } catch (error) {
        console.error('Failed to stop audio test:', error);
      }
    }
  };

  // Controller feature control functions
  const handleLightbarColorChange = (color: { r: number, g: number, b: number }) => {
    setLightbarColor(color);

    if (hidDevice && hidDevice.vendorId === 0x054c) {
      // For DualSense
      if (hidDevice.productId === 0x0ce6 || hidDevice.productId === 0x0df2) {
        DualSense.setControllerState(hidDevice, {
          lightR: color.r,
          lightG: color.g,
          lightB: color.b
        });
      }
      // For DualShock 4
      else if (hidDevice.productId === 0x09cc || hidDevice.productId === 0x05c4) {
        DualSense.setLightbarColor(hidDevice, color.r, color.g, color.b);
      }
    }
  };

  const handleRumbleChange = (type: 'heavy' | 'soft', value: number) => {
    if (type === 'heavy') {
      setRumbleHeavy(value);
    } else {
      setRumbleSoft(value);
    }

    if (hidDevice && hidDevice.vendorId === 0x054c) {
      // Scale value from 0-100 to 0-255
      const scaledValue = Math.floor((value / 100) * 255);

      // For DualSense
      if (hidDevice.productId === 0x0ce6 || hidDevice.productId === 0x0df2) {
        DualSense.setControllerState(hidDevice, {
          leftVibration: type === 'heavy' ? scaledValue : rumbleHeavy * 2.55,
          rightVibration: type === 'soft' ? scaledValue : rumbleSoft * 2.55
        });
      }
      // For DualShock 4
      else if (hidDevice.productId === 0x09cc || hidDevice.productId === 0x05c4) {
        DualSense.setVibration(
          hidDevice,
          type === 'heavy' ? scaledValue : rumbleHeavy * 2.55,
          type === 'soft' ? scaledValue : rumbleSoft * 2.55
        );
      }
    }
  };

  const handleTriggerModeChange = (trigger: 'left' | 'right', mode: string) => {
    if (trigger === 'left') {
      setLeftTriggerMode(mode);
    } else {
      setRightTriggerMode(mode);
    }

    if (hidDevice && hidDevice.vendorId === 0x054c &&
        (hidDevice.productId === 0x0ce6 || hidDevice.productId === 0x0df2)) {

      // Map mode string to DualSense trigger mode
      let triggerMode = DualSense.TRIGGER_EFFECT_MODE.OFF;
      let triggerForce = 0;
      let triggerStart = 0;

      switch (mode) {
        case 'resistance':
          triggerMode = DualSense.TRIGGER_EFFECT_MODE.RESISTANCE;
          triggerForce = 128; // Medium resistance
          triggerStart = 0;   // Start from beginning
          break;
        case 'vibration':
          triggerMode = DualSense.TRIGGER_EFFECT_MODE.VIBRATION;
          triggerForce = 128; // Medium vibration
          triggerStart = 0;   // Start from beginning
          break;
        case 'weapon':
          triggerMode = DualSense.TRIGGER_EFFECT_MODE.WEAPON;
          triggerForce = 128; // Medium force
          triggerStart = 64;  // Start halfway
          break;
        default:
          triggerMode = DualSense.TRIGGER_EFFECT_MODE.OFF;
      }

      // Apply trigger effect
      if (trigger === 'left') {
        DualSense.setControllerState(hidDevice, {
          leftTriggerMode: triggerMode,
          leftTriggerStart: triggerStart,
          leftTriggerForce: triggerForce
        });
      } else {
        DualSense.setControllerState(hidDevice, {
          rightTriggerMode: triggerMode,
          rightTriggerStart: triggerStart,
          rightTriggerForce: triggerForce
        });
      }
    }
  };

  const handleVolumeChange = (type: 'speaker' | 'headphone', value: number) => {
    if (type === 'speaker') {
      setSpeakerVolume(value);
    } else {
      setHeadphoneVolume(value);
    }

    if (hidDevice && hidDevice.vendorId === 0x054c &&
        (hidDevice.productId === 0x0ce6 || hidDevice.productId === 0x0df2)) {

      // Scale value from 0-100 to 0-255
      const scaledValue = Math.floor((value / 100) * 255);

      // Apply volume setting
      DualSense.setControllerState(hidDevice, {
        [type === 'speaker' ? 'speakerVolume' : 'headphoneVolume']: scaledValue
      });
    }
  };

  const handleMicLightToggle = (enabled: boolean) => {
    setMicLightEnabled(enabled);

    if (hidDevice && hidDevice.vendorId === 0x054c &&
        (hidDevice.productId === 0x0ce6 || hidDevice.productId === 0x0df2)) {

      // Apply mic light setting
      DualSense.setControllerState(hidDevice, {
        microphoneLed: enabled ? 1 : 0
      });
    }
  };

  return (
    <div className="space-y-6">
      <div className="border-b">
        <div className="flex h-16 items-center px-4 justify-between">
          <div className="flex items-center">
            <Gamepad className="h-6 w-6 mr-2" />
            <h1 className="text-2xl font-bold">DualSense Tester</h1>
          </div>
          <div className="flex items-center space-x-2">
            <Select defaultValue="en-US">
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Language" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="en-US">English (US)</SelectItem>
                <SelectItem value="zh-CN">中文 (简体)</SelectItem>
                <SelectItem value="ja-JP">日本語</SelectItem>
                <SelectItem value="ko-KR">한국어</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
        {/* Left Panel - Device Info & Controls */}
        <div className="lg:col-span-4 space-y-6">
          {!hidDevice ? (
            <Card>
              <CardHeader>
                <CardTitle>Connect Device</CardTitle>
                <CardDescription>
                  Connect your DualSense controller to begin testing
                </CardDescription>
              </CardHeader>
              <CardContent className="flex flex-col space-y-4">
                <Button onClick={connectHIDDevice} className="w-full">
                  <Usb className="mr-2 h-4 w-4" />
                  Add Device
                </Button>
                {!isHIDSupported && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      WebHID API is not supported in your browser. Please use Chrome or Edge.
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          ) : (
            <>
              {/* Device Info */}
              <Card>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <Badge variant="outline" className="mr-2">#{1}</Badge>
                      <CardTitle>DualSense Wireless Controller</CardTitle>
                    </div>
                    <Button variant="ghost" size="icon" onClick={() => {}}>
                      <span className="sr-only">Add device</span>
                      <Usb className="h-4 w-4" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>Sequence No.</div>
                      <div className="text-right">2D</div>
                    </div>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>Charge Status</div>
                      <div className="text-right text-blue-500">Charging complete</div>
                    </div>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>Battery Level</div>
                      <div className="text-right">{batteryLevel}%</div>
                    </div>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>Microphone</div>
                      <div className="text-right">Not connected</div>
                    </div>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>Headphone</div>
                      <div className="text-right">Not connected</div>
                    </div>
                  </div>

                  <div className="mt-4 border-t pt-4">
                    <h3 className="text-sm font-medium mb-2">Factory Info</h3>
                    {/* Factory info would go here */}
                  </div>
                </CardContent>
              </Card>

              {/* Output Controls */}
              <Card>
                <CardHeader>
                  <CardTitle>Output</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-2 items-center">
                    <Label htmlFor="mic-light">Mic status light</Label>
                    <div className="flex justify-end">
                      <Switch id="mic-light" />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-2 items-center">
                    <Label htmlFor="lightbar-color">Lightbar color</Label>
                    <div className="h-6 w-6 rounded-full bg-blue-500 ml-auto" />
                  </div>

                  <div className="grid grid-cols-2 gap-2 items-center">
                    <Label htmlFor="player-light">Player light</Label>
                    <div className="flex justify-end space-x-1">
                      {[1, 2, 3, 4, 5].map(num => (
                        <div
                          key={num}
                          className={`h-4 w-4 rounded-full ${num === 1 ? 'bg-blue-500' : 'bg-gray-300 dark:bg-gray-700'}`}
                        />
                      ))}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="brightness">Brightness</Label>
                    <div className="flex justify-between text-xs">
                      <span>High</span>
                      <span>Medium</span>
                      <span>Low</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="rumble-heavy">Rumble (heavy)</Label>
                    <Slider
                      id="rumble-heavy"
                      defaultValue={[0]}
                      max={100}
                      step={1}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="rumble-soft">Rumble (soft)</Label>
                    <Slider
                      id="rumble-soft"
                      defaultValue={[0]}
                      max={100}
                      step={1}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-2 items-center">
                    <Label htmlFor="left-trigger">Left trigger</Label>
                    <Select defaultValue="off">
                      <SelectTrigger id="left-trigger">
                        <SelectValue placeholder="Off" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="off">Off</SelectItem>
                        <SelectItem value="resistance">Resistance</SelectItem>
                        <SelectItem value="vibration">Vibration</SelectItem>
                        <SelectItem value="weapon">Weapon</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="grid grid-cols-2 gap-2 items-center">
                    <Label htmlFor="right-trigger">Right trigger</Label>
                    <Select defaultValue="off">
                      <SelectTrigger id="right-trigger">
                        <SelectValue placeholder="Off" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="off">Off</SelectItem>
                        <SelectItem value="resistance">Resistance</SelectItem>
                        <SelectItem value="vibration">Vibration</SelectItem>
                        <SelectItem value="weapon">Weapon</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="grid grid-cols-2 gap-2 items-center">
                    <Label htmlFor="speaker-volume">Speaker volume</Label>
                    <Slider
                      id="speaker-volume"
                      defaultValue={[0]}
                      max={100}
                      step={1}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-2 items-center">
                    <Label htmlFor="headphone-volume">Headphone volume</Label>
                    <Slider
                      id="headphone-volume"
                      defaultValue={[0]}
                      max={100}
                      step={1}
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Audio Test */}
              <Card>
                <CardHeader>
                  <CardTitle>Audio</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() => startAudioTest('headphone', 1000)}
                    >
                      Headphone 1kHz sine wave
                    </Button>
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() => startAudioTest('speaker', 1000)}
                    >
                      Speaker 1kHz sine wave
                    </Button>
                  </div>
                  {audioTestActive && (
                    <Button
                      variant="destructive"
                      className="w-full"
                      onClick={stopAudioTest}
                    >
                      Stop Audio Test
                    </Button>
                  )}
                </CardContent>
              </Card>
            </>
          )}
        </div>

        {/* Right Panel - Controller Visualization & Data */}
        <div className="lg:col-span-8 space-y-6">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Basic Buttons</CardTitle>
                <Select defaultValue="off">
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Value display off" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="off">Value display off</SelectItem>
                    <SelectItem value="on">Value display on</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardHeader>
            <CardContent className="flex justify-center">
              <ControllerSVG
                controllerType={controllerType}
                className="w-auto h-64 mx-auto"
              />
            </CardContent>
          </Card>

          {/* Gyroscope Data */}
          <Card>
            <CardHeader>
              <CardTitle>Gyroscope</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-blue-500">Pitch</span>
                    <span>{gyroscope.pitch.toFixed(1)}°/S</span>
                  </div>
                  <div className="relative w-full h-4 bg-muted rounded-full overflow-hidden">
                    <div
                      className="absolute top-0 bottom-0 left-1/2 bg-blue-500"
                      style={{ width: '2px', transform: 'translateX(-50%)' }}
                    />
                    <div
                      className="absolute top-0 bottom-0 bg-blue-500 opacity-20"
                      style={{
                        width: `${Math.min(Math.abs(gyroscope.pitch) * 5, 100)}%`,
                        left: `${50 - (Math.min(Math.abs(gyroscope.pitch) * 2.5, 50) * (gyroscope.pitch < 0 ? 1 : -1))}%`
                      }}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-red-500">Yaw</span>
                    <span>{gyroscope.yaw.toFixed(1)}°/S</span>
                  </div>
                  <div className="relative w-full h-4 bg-muted rounded-full overflow-hidden">
                    <div
                      className="absolute top-0 bottom-0 left-1/2 bg-red-500"
                      style={{ width: '2px', transform: 'translateX(-50%)' }}
                    />
                    <div
                      className="absolute top-0 bottom-0 bg-red-500 opacity-20"
                      style={{
                        width: `${Math.min(Math.abs(gyroscope.yaw) * 5, 100)}%`,
                        left: `${50 - (Math.min(Math.abs(gyroscope.yaw) * 2.5, 50) * (gyroscope.yaw < 0 ? 1 : -1))}%`
                      }}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-yellow-500">Roll</span>
                    <span>{gyroscope.roll.toFixed(1)}°/S</span>
                  </div>
                  <div className="relative w-full h-4 bg-muted rounded-full overflow-hidden">
                    <div
                      className="absolute top-0 bottom-0 left-1/2 bg-yellow-500"
                      style={{ width: '2px', transform: 'translateX(-50%)' }}
                    />
                    <div
                      className="absolute top-0 bottom-0 bg-yellow-500 opacity-20"
                      style={{
                        width: `${Math.min(Math.abs(gyroscope.roll) * 5, 100)}%`,
                        left: `${50 - (Math.min(Math.abs(gyroscope.roll) * 2.5, 50) * (gyroscope.roll < 0 ? 1 : -1))}%`
                      }}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Accelerometer Data */}
          <Card>
            <CardHeader>
              <CardTitle>Accelerometer</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-blue-500 font-medium">X</div>
                  <div className="text-lg">{accelerometer.x.toFixed(5)} m/s²</div>
                </div>
                <div className="text-center">
                  <div className="text-red-500 font-medium">Y</div>
                  <div className="text-lg">{accelerometer.y.toFixed(5)} m/s²</div>
                </div>
                <div className="text-center">
                  <div className="text-yellow-500 font-medium">Z</div>
                  <div className="text-lg">{accelerometer.z.toFixed(5)} m/s²</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ControllerTester;
