/**
 * Buy/Pawn Catchup Routes
 * 
 * API routes for buy/pawn staff catch-up notes
 */

const express = require('express');
const { requireUser } = require('./middleware/auth');
const buyPawnCatchupService = require('../services/buyPawnCatchupService');
const router = express.Router();

/**
 * @route POST /api/buys/catchups
 * @description Create a new staff catch-up note for an audit
 * @access Private (Admin, Manager)
 */
router.post('/', requireUser, async (req, res) => {
  try {
    // Check if user has permission (admin or manager)
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to create catch-up notes'
      });
    }

    const { auditId } = req.body;
    if (!auditId) {
      return res.status(400).json({
        success: false,
        error: 'Audit ID is required'
      });
    }

    const result = await buyPawnCatchupService.createCatchup(auditId, req.body, req.user);

    if (result.success) {
      return res.status(201).json(result);
    } else {
      return res.status(400).json(result);
    }
  } catch (error) {
    console.error('Error creating buy/pawn catchup note:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/buys/catchups
 * @description Get catch-up notes with filtering and pagination
 * @access Private
 */
router.get('/', requireUser, async (req, res) => {
  try {
    // Employees can only see catch-up notes related to them
    if (req.user.role === 'employee') {
      req.query.employee = req.user._id;
    }

    const result = await buyPawnCatchupService.getCatchups(req.query);

    if (result.success) {
      return res.status(200).json(result);
    } else {
      return res.status(400).json(result);
    }
  } catch (error) {
    console.error('Error getting buy/pawn catchup notes:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/buys/catchups/:id
 * @description Get a catch-up note by ID
 * @access Private
 */
router.get('/:id', requireUser, async (req, res) => {
  try {
    const result = await buyPawnCatchupService.getCatchupById(req.params.id);

    if (result.success) {
      // Employees can only view catch-up notes for themselves
      if (req.user.role === 'employee') {
        const employeeId = result.data.employee._id.toString();
        if (employeeId !== req.user._id.toString()) {
          return res.status(403).json({
            success: false,
            error: 'You do not have permission to view this catch-up note'
          });
        }
      }

      return res.status(200).json(result);
    } else {
      return res.status(404).json(result);
    }
  } catch (error) {
    console.error('Error getting buy/pawn catchup note by ID:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/buys/catchups/audit/:auditId
 * @description Get catch-up notes by audit ID
 * @access Private
 */
router.get('/audit/:auditId', requireUser, async (req, res) => {
  try {
    const result = await buyPawnCatchupService.getCatchupsByAuditId(req.params.auditId);

    if (result.success) {
      // Employees can only view catch-up notes for themselves
      if (req.user.role === 'employee') {
        result.data = result.data.filter(catchup => 
          catchup.employee._id.toString() === req.user._id.toString()
        );
      }

      return res.status(200).json(result);
    } else {
      return res.status(404).json(result);
    }
  } catch (error) {
    console.error('Error getting buy/pawn catchup notes by audit ID:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/buys/catchups/employee/:employeeId
 * @description Get catch-up notes by employee ID
 * @access Private (Admin, Manager, or Self)
 */
router.get('/employee/:employeeId', requireUser, async (req, res) => {
  try {
    // Employees can only view their own catch-up notes
    if (req.user.role === 'employee' && req.params.employeeId !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to view these catch-up notes'
      });
    }

    const result = await buyPawnCatchupService.getCatchupsByEmployeeId(req.params.employeeId);

    if (result.success) {
      return res.status(200).json(result);
    } else {
      return res.status(404).json(result);
    }
  } catch (error) {
    console.error('Error getting buy/pawn catchup notes by employee ID:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route PUT /api/buys/catchups/:id
 * @description Update a catch-up note
 * @access Private (Admin, Manager)
 */
router.put('/:id', requireUser, async (req, res) => {
  try {
    // Check if user has permission (admin or manager)
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to update catch-up notes'
      });
    }

    const result = await buyPawnCatchupService.updateCatchup(req.params.id, req.body, req.user);

    if (result.success) {
      return res.status(200).json(result);
    } else {
      return res.status(400).json(result);
    }
  } catch (error) {
    console.error('Error updating buy/pawn catchup note:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = router;
