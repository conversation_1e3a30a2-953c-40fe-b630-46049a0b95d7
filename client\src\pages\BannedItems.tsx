import { useEffect, useState } from 'react';
import {
  getBannedItems,
  createBannedItem,
  updateBannedItem,
  deleteBannedItem,
  BannedItem,
  BannedItemInput
} from '@/api/bannedItems';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/useToast';
import { useAuth } from '@/contexts/AuthContext';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  Search,
  Plus,
  Edit,
  Trash2,
  AlertTriangle,
  Ban,
  ShieldAlert,
  Loader2
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';
import { Separator } from '@/components/ui/separator';

export function BannedItems() {
  const [items, setItems] = useState<BannedItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [currentItem, setCurrentItem] = useState<BannedItem | null>(null);
  const [formData, setFormData] = useState<BannedItemInput>({
    title: '',
    description: '',
    category: 'banned'
  });

  const { toast } = useToast();
  const { user } = useAuth();
  const isAdminOrManager = user?.role === 'admin' || user?.role === 'manager';

  // Fetch banned items
  const fetchItems = async () => {
    try {
      setLoading(true);
      const response = await getBannedItems(activeTab === 'all' ? undefined : activeTab, searchTerm);
      if (response.success) {
        // Sort items alphabetically by title
        const sortedItems = [...response.data].sort((a, b) =>
          a.title.localeCompare(b.title, undefined, { sensitivity: 'base' })
        );
        setItems(sortedItems);
      } else {
        toast({
          title: 'Error',
          description: response.error || 'Failed to fetch banned items',
          variant: 'destructive'
        });
      }
    } catch (error: any) {
      console.error('Error fetching banned items:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to fetch banned items',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchItems();
  }, [activeTab]);

  // Handle search
  const handleSearch = () => {
    fetchItems();
  };

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle category select change
  const handleCategoryChange = (value: 'banned' | 'restricted') => {
    setFormData(prev => ({ ...prev, category: value }));
  };

  // Reset form data
  const resetFormData = () => {
    setFormData({
      title: '',
      description: '',
      category: 'banned'
    });
    setCurrentItem(null);
  };

  // Open edit dialog
  const openEditDialog = (item: BannedItem) => {
    setCurrentItem(item);
    setFormData({
      title: item.title,
      description: item.description,
      category: item.category
    });
    setIsEditDialogOpen(true);
  };

  // Handle add item
  const handleAddItem = async () => {
    try {
      if (!formData.title || !formData.description) {
        toast({
          title: 'Validation Error',
          description: 'Title and description are required',
          variant: 'destructive'
        });
        return;
      }

      const response = await createBannedItem(formData);
      if (response.success) {
        toast({
          title: 'Success',
          description: 'Item added successfully'
        });
        setIsAddDialogOpen(false);
        resetFormData();
        fetchItems();
      } else {
        toast({
          title: 'Error',
          description: response.error || 'Failed to add item',
          variant: 'destructive'
        });
      }
    } catch (error: any) {
      console.error('Error adding banned item:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to add item',
        variant: 'destructive'
      });
    }
  };

  // Handle update item
  const handleUpdateItem = async () => {
    try {
      if (!currentItem || !formData.title || !formData.description) {
        toast({
          title: 'Validation Error',
          description: 'Title and description are required',
          variant: 'destructive'
        });
        return;
      }

      const response = await updateBannedItem(currentItem._id, formData);
      if (response.success) {
        toast({
          title: 'Success',
          description: 'Item updated successfully'
        });
        setIsEditDialogOpen(false);
        resetFormData();
        fetchItems();
      } else {
        toast({
          title: 'Error',
          description: response.error || 'Failed to update item',
          variant: 'destructive'
        });
      }
    } catch (error: any) {
      console.error('Error updating banned item:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to update item',
        variant: 'destructive'
      });
    }
  };

  // Handle delete item
  const handleDeleteItem = async (id: string) => {
    try {
      const response = await deleteBannedItem(id);
      if (response.success) {
        toast({
          title: 'Success',
          description: 'Item deleted successfully'
        });
        fetchItems();
      } else {
        toast({
          title: 'Error',
          description: response.error || 'Failed to delete item',
          variant: 'destructive'
        });
      }
    } catch (error: any) {
      console.error('Error deleting banned item:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete item',
        variant: 'destructive'
      });
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'dd MMM yyyy, h:mm a');
  };

  // Render description with line breaks and links
  const renderDescription = (description: string) => {
    // Replace URLs with clickable links
    const urlRegex = /(https?:\/\/[^\s]+)/g;
    const parts = description.split(urlRegex);

    return parts.map((part, index) => {
      if (part.match(urlRegex)) {
        return (
          <a
            key={index}
            href={part}
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-500 hover:underline"
          >
            {part}
          </a>
        );
      }
      // Replace line breaks with <br /> elements
      return part.split('\n').map((line, i) => (
        <span key={`${index}-${i}`}>
          {line}
          {i < part.split('\n').length - 1 && <br />}
        </span>
      ));
    });
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Banned & Restricted Items</CardTitle>
          <CardDescription className="space-y-3 mt-2">
            <p>
              Employees are responsible for ensuring no items with a current ban in place by Consumer Affairs NZ or the Company are bought or loaned against, processed, priced or placed on sale.
            </p>

            <div className="pl-4 border-l-2 border-muted">
              <p className="mb-2">
                <span className="font-medium">Banned items</span> are to be discussed with the Store Manager upon identification and addressed with the team immediately to prevent future banned goods buys and/or loans.
              </p>
              <p className="mb-2">
                Banned items are to be disassembled (where possible), and written off/placed in the rubbish by the Store Manager.
              </p>
              <p>
                Supplementary to the above lists of banned goods, in order to comply with the Responsible Lending Code, we will not buy or loan against items that are considered to be 'essential':
              </p>
              <ul className="list-disc pl-6 mt-1">
                <li>Critical medical equipment (nebuliser, blood sugar tester, oxygen masks, defibrillator)</li>
                <li>Fridges and ovens (unless you can confidently ascertain that the customer has more than one or an available substitute e.g. food fridge and beer fridge)</li>
              </ul>
            </div>
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Search and Add */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-2 w-full max-w-sm">
              <Input
                placeholder="Search items..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
              />
              <Button variant="outline" onClick={handleSearch}>
                <Search className="h-4 w-4 mr-2" />
                Search
              </Button>
            </div>

            {isAdminOrManager && (
              <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Item
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Add Banned/Restricted Item</DialogTitle>
                    <DialogDescription>
                      Add a new item that cannot be purchased or loaned against
                    </DialogDescription>
                  </DialogHeader>

                  <div className="space-y-4 py-4">
                    <div className="space-y-2">
                      <Label htmlFor="title">Title</Label>
                      <Input
                        id="title"
                        name="title"
                        placeholder="Enter item title"
                        value={formData.title}
                        onChange={handleInputChange}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="category">Category</Label>
                      <Select
                        value={formData.category}
                        onValueChange={(value: 'banned' | 'restricted') => handleCategoryChange(value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="banned">Banned Goods</SelectItem>
                          <SelectItem value="restricted">Restricted Goods</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="description">Description</Label>
                      <Textarea
                        id="description"
                        name="description"
                        placeholder="Enter description"
                        value={formData.description}
                        onChange={handleInputChange}
                        rows={5}
                      />
                      <p className="text-sm text-muted-foreground">
                        Line breaks and URLs will be preserved.
                      </p>
                    </div>
                  </div>

                  <DialogFooter>
                    <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleAddItem}>
                      Add Item
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            )}
          </div>

          {/* Tabs */}
          <Tabs value={activeTab} onValueChange={handleTabChange}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="all">All Items</TabsTrigger>
              <TabsTrigger value="banned">Banned Goods</TabsTrigger>
              <TabsTrigger value="restricted">Restricted Goods</TabsTrigger>
            </TabsList>

            <TabsContent value="all" className="mt-6">
              {renderItemsList(items, 'all')}
            </TabsContent>

            <TabsContent value="banned" className="mt-6">
              {renderItemsList(items.filter(item => item.category === 'banned'), 'banned')}
            </TabsContent>

            <TabsContent value="restricted" className="mt-6">
              {renderItemsList(items.filter(item => item.category === 'restricted'), 'restricted')}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Edit Dialog */}
      {currentItem && (
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit Item</DialogTitle>
              <DialogDescription>
                Update the details of this banned/restricted item
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="edit-title">Title</Label>
                <Input
                  id="edit-title"
                  name="title"
                  placeholder="Enter item title"
                  value={formData.title}
                  onChange={handleInputChange}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-category">Category</Label>
                <Select
                  value={formData.category}
                  onValueChange={(value: 'banned' | 'restricted') => handleCategoryChange(value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="banned">Banned Goods</SelectItem>
                    <SelectItem value="restricted">Restricted Goods</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-description">Description</Label>
                <Textarea
                  id="edit-description"
                  name="description"
                  placeholder="Enter description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={5}
                />
                <p className="text-sm text-muted-foreground">
                  Line breaks and URLs will be preserved.
                </p>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleUpdateItem}>
                Update Item
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );

  // Helper function to render items list
  function renderItemsList(items: BannedItem[], category: string) {
    if (loading) {
      return (
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      );
    }

    if (items.length === 0) {
      return (
        <div className="text-center py-12 text-muted-foreground">
          {searchTerm ? (
            <p>No items found matching your search criteria.</p>
          ) : (
            <p>
              {category === 'all'
                ? 'No banned or restricted items found.'
                : category === 'banned'
                  ? 'No banned items found.'
                  : 'No restricted items found.'}
            </p>
          )}
        </div>
      );
    }

    return (
      <div className="space-y-6">
        {items.map((item) => (
          <Card key={item._id} className="overflow-hidden">
            <CardContent className="p-6">
              <div className="flex justify-between items-start">
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    {item.category === 'banned' ? (
                      <Ban className="h-5 w-5 text-destructive" />
                    ) : (
                      <ShieldAlert className="h-5 w-5 text-yellow-500" />
                    )}
                    <h3 className="text-lg font-semibold">{item.title}</h3>
                    <Badge variant={item.category === 'banned' ? 'destructive' : 'warning'}>
                      {item.category === 'banned' ? 'Banned' : 'Restricted'}
                    </Badge>
                  </div>

                  <div className="text-sm text-muted-foreground">
                    Added by {item.createdBy?.fullName || 'Unknown'} on {formatDate(item.createdAt)}
                    {item.updatedBy && item.createdBy?._id !== item.updatedBy._id && (
                      <div className="mt-1">Last updated by {item.updatedBy.fullName} on {formatDate(item.updatedAt)}</div>
                    )}
                  </div>
                </div>

                {isAdminOrManager && (
                  <div className="flex space-x-2">
                    <Button variant="ghost" size="icon" onClick={() => openEditDialog(item)}>
                      <Edit className="h-4 w-4" />
                    </Button>

                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <Trash2 className="h-4 w-4 text-destructive" />
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                          <AlertDialogDescription>
                            This action cannot be undone. This will permanently delete the item.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction onClick={() => handleDeleteItem(item._id)}>
                            Delete
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                )}
              </div>

              <Separator className="my-4" />

              <div className="prose prose-sm dark:prose-invert max-w-none">
                <div className="text-foreground">
                  {renderDescription(item.description)}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }
}
