import api from './api';

/**
 * Get the status of the TradeMe sync service
 * @returns Promise with sync status
 */
export const getStatus = async () => {
  try {
    const response = await api.get('/trademe/sync/status');
    return response.data;
  } catch (error: any) {
    console.error('Error getting TradeMe sync status:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

/**
 * Get sync logs
 * @param options - Options for fetching logs
 * @returns Promise with logs
 */
export const getLogs = async (options?: { limit?: number; syncType?: string }) => {
  try {
    const params = new URLSearchParams();
    if (options?.limit) params.append('limit', options.limit.toString());
    if (options?.syncType) params.append('syncType', options.syncType);

    const response = await api.get(`/trademe/sync/logs?${params.toString()}`);
    return response.data;
  } catch (error: any) {
    console.error('Error getting TradeMe sync logs:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

/**
 * Trigger a manual sync
 * @param options - Options for the sync operation
 * @returns Promise with result
 */
export const triggerSync = async (options?: {
  syncType?: 'listings' | 'questions' | 'feedback' | 'categories';
  maxPagesPerEndpoint?: number;
}) => {
  try {
    const response = await api.post('/trademe/sync/trigger', options || {});
    return response.data;
  } catch (error: any) {
    console.error('Error triggering TradeMe sync:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

/**
 * Trigger an optimized category sync
 * @returns Promise with result
 */
export const triggerOptimizedCategorySync = async () => {
  try {
    const response = await api.post('/trademe/sync/optimized-categories');
    return response.data;
  } catch (error: any) {
    console.error('Error triggering optimized category sync:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

/**
 * Stop all sync operations
 * @returns Promise with result
 */
export const stopSync = async () => {
  try {
    const response = await api.post('/trademe/sync/stop');
    return response.data;
  } catch (error: any) {
    console.error('Error stopping TradeMe sync:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};
