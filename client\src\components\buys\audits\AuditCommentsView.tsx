import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback, AvatarInitials } from '@/components/ui/avatar';
import { MessageSquare, Send, Clock, User } from 'lucide-react';
import { format, formatDistanceToNow } from 'date-fns';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/useToast';
import { getAuditComments, addAuditComment } from '@/api/buyPawnAudits';

interface Comment {
  _id: string;
  auditId: string;
  userId: string;
  userName: string;
  userRole: string;
  comment: string;
  createdAt: string;
  updatedAt: string;
}

interface AuditCommentsViewProps {
  auditId: string;
  onCommentAdded?: () => void;
}

export function AuditCommentsView({ auditId, onCommentAdded }: AuditCommentsViewProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const [comments, setComments] = useState<Comment[]>([]);
  const [newComment, setNewComment] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Load comments
  useEffect(() => {
    loadComments();
  }, [auditId]);

  const loadComments = async () => {
    try {
      setIsLoading(true);
      const response = await getAuditComments(auditId);
      if (response.success) {
        // Transform the data to match our interface
        const transformedComments = response.data.map((comment: any) => ({
          _id: comment._id,
          auditId,
          userId: comment.addedBy._id,
          userName: comment.addedBy.fullName,
          userRole: comment.addedBy.role,
          comment: comment.comment,
          createdAt: comment.addedAt,
          updatedAt: comment.addedAt,
        }));
        setComments(transformedComments);
      } else {
        throw new Error(response.error || 'Failed to load comments');
      }
    } catch (error: any) {
      console.error('Error loading comments:', error);
      toast({
        title: 'Error',
        description: 'Failed to load comments.',
        variant: 'destructive',
      });
      setComments([]); // Set empty array on error
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmitComment = async () => {
    if (!newComment.trim()) return;

    try {
      setIsSubmitting(true);

      const response = await addAuditComment(auditId, newComment.trim());

      if (response.success) {
        // Transform the response data to match our interface
        const newCommentData: Comment = {
          _id: response.data._id,
          auditId,
          userId: response.data.addedBy._id,
          userName: response.data.addedBy.fullName,
          userRole: response.data.addedBy.role,
          comment: response.data.comment,
          createdAt: response.data.addedAt,
          updatedAt: response.data.addedAt,
        };

        setComments(prev => [newCommentData, ...prev]);
        setNewComment('');

        toast({
          title: 'Comment Added',
          description: 'Your comment has been added successfully.',
        });

        if (onCommentAdded) {
          onCommentAdded();
        }
      } else {
        throw new Error(response.error || 'Failed to add comment');
      }
    } catch (error: any) {
      console.error('Error adding comment:', error);
      toast({
        title: 'Error',
        description: 'Failed to add comment.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'bg-red-100 text-red-800';
      case 'manager':
        return 'bg-blue-100 text-blue-800';
      case 'employee':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div className="space-y-6">
      {/* Add New Comment */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Add Comment
          </CardTitle>
          <CardDescription>
            Add a comment to track discussions and follow-up actions for this audit
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Textarea
            placeholder="Enter your comment here..."
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            className="min-h-[100px]"
            disabled={isSubmitting}
          />
          <div className="flex justify-end">
            <Button 
              onClick={handleSubmitComment}
              disabled={!newComment.trim() || isSubmitting}
            >
              <Send className="h-4 w-4 mr-2" />
              {isSubmitting ? 'Adding...' : 'Add Comment'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Comments List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Comments ({comments.length})
          </CardTitle>
          <CardDescription>
            All comments and discussions related to this audit
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8 text-muted-foreground">
              Loading comments...
            </div>
          ) : comments.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No comments yet. Be the first to add a comment!
            </div>
          ) : (
            <div className="space-y-4">
              {comments.map((comment, index) => (
                <div key={comment._id}>
                  <div className="flex items-start space-x-3">
                    <Avatar className="h-8 w-8">
                      <AvatarFallback>
                        {getInitials(comment.userName)}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 space-y-2">
                      <div className="flex items-center space-x-2">
                        <span className="font-medium text-sm">{comment.userName}</span>
                        <Badge 
                          variant="secondary" 
                          className={`text-xs ${getRoleColor(comment.userRole)}`}
                        >
                          {comment.userRole.charAt(0).toUpperCase() + comment.userRole.slice(1)}
                        </Badge>
                        <div className="flex items-center text-xs text-muted-foreground">
                          <Clock className="h-3 w-3 mr-1" />
                          <span title={format(new Date(comment.createdAt), 'PPpp')}>
                            {formatDistanceToNow(new Date(comment.createdAt), { addSuffix: true })}
                          </span>
                        </div>
                      </div>
                      <div className="text-sm text-gray-700 bg-gray-50 rounded-lg p-3">
                        {comment.comment}
                      </div>
                    </div>
                  </div>
                  {index < comments.length - 1 && <Separator className="mt-4" />}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Comment Guidelines */}
      <Card className="border-dashed">
        <CardContent className="pt-6">
          <div className="text-sm text-muted-foreground space-y-2">
            <h4 className="font-medium text-foreground">Comment Guidelines:</h4>
            <ul className="list-disc list-inside space-y-1">
              <li>Use comments to document follow-up actions and discussions</li>
              <li>Be professional and constructive in your feedback</li>
              <li>Include specific details about training or corrective actions taken</li>
              <li>Comments are visible to all managers and admins</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
