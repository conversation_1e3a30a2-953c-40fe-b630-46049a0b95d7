import api from './api';
import { User } from './user';

// Separate the refresh token API call to avoid circular dependency
export const refreshTokenApi = async (refreshToken: string) => {
  try {
    // The baseURL is '/api', so we need to use '/auth/refresh' not '/api/auth/refresh'
    const response = await api.post('/auth/refresh', { refreshToken });
    return response.data;
  } catch (error: any) {
    console.error('Token refresh error:', error);
    throw new Error(error?.response?.data?.message || error.message || 'Token refresh failed');
  }
};

interface LoginResponse {
  success: boolean;
  user: User;
  accessToken: string;
  refreshToken: string;
  message?: string;
}

export const login = async (credentials: {
  emailOrUsername?: string;
  password?: string;
  pin?: string;
}): Promise<LoginResponse> => {
  try {
    // The baseURL is '/api', so we need to use '/auth/login' not '/api/auth/login'
    const response = await api.post<LoginResponse>('/auth/login', {
      emailOrUsername: credentials.emailOrUsername?.trim(),
      password: credentials.password,
      pin: credentials.pin
    });

    if (response.data?.accessToken) {
      localStorage.setItem('accessToken', response.data.accessToken);
      localStorage.setItem('refreshToken', response.data.refreshToken);
      api.defaults.headers.common['Authorization'] = `Bearer ${response.data.accessToken}`;
    }

    return response.data;
  } catch (error: any) {
    const errorMessage = error.response?.data?.message || 'Login failed';
    throw new Error(errorMessage);
  }
};

export const getCurrentUser = async () => {
  try {
    const token = localStorage.getItem('accessToken');
    if (token) {
      api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    }

    // The baseURL is '/api', so we need to use '/auth/me' not '/api/auth/me'
    const response = await api.get('/auth/me');
    return response.data;
  } catch (error: any) {
    console.error('Get current user error:', error.response?.data || error);
    localStorage.removeItem('accessToken');
    throw error;
  }
};

export const register = async (email: string, password: string, username?: string) => {
  try {
    // The baseURL is '/api', so we need to use '/auth/register' not '/api/auth/register'
    const response = await api.post('/auth/register', { email, password, username });
    return response.data;
  } catch (error: any) {
    console.error('Registration error:', error);
    throw new Error(error?.response?.data?.message || error.message || 'Registration failed');
  }
};

export const logout = async () => {
  try {
    // The baseURL is '/api', so we need to use '/auth/logout' not '/api/auth/logout'
    const response = await api.post('/auth/logout', {
      email: localStorage.getItem('userEmail')
    });
    return response.data;
  } catch (error: any) {
    console.error('Logout error:', error);
    throw new Error(error?.response?.data?.message || error.message || 'Logout failed');
  }
};

export const refreshToken = async (refreshToken: string) => {
  try {
    // The baseURL is '/api', so we need to use '/auth/refresh' not '/api/auth/refresh'
    const response = await api.post('/auth/refresh', { refreshToken });
    return response.data;
  } catch (error: any) {
    console.error('Token refresh error:', error);
    throw new Error(error?.response?.data?.message || error.message || 'Token refresh failed');
  }
};
