/**
 * TradeMe Account Routes
 *
 * Handles TradeMe account management, including:
 * - Connection status
 * - OAuth flow
 * - Account settings
 * - Member information
 */

const express = require('express');
const router = express.Router();
const { requireUser } = require('./middleware/auth');
const tradeMeService = require('../services/tradeMeService');
const TradeMeSettings = require('../models/TradeMeSettings');

/**
 * @route GET /api/trademe/account/connection-status
 * @description Get TradeMe connection status
 * @access Private (All authenticated users)
 */
router.get('/connection-status', requireUser, async (req, res) => {
  try {
    const settings = await tradeMeService.getSettings();
    return res.status(200).json({
      success: true,
      status: {
        connected: settings.accountStatus.connected,
        environment: settings.accountStatus.environment
      }
    });
  } catch (error) {
    console.error('Error getting TradeMe connection status:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/trademe/account/permissions
 * @description Check TradeMe token permissions
 * @access Private (Admin, Manager)
 */
router.get('/permissions', requireUser, async (req, res) => {
  try {
    // Check if user has permission (admin or manager)
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to check TradeMe permissions',
      });
    }

    const result = await tradeMeService.checkTokenPermissions();

    if (result.success) {
      return res.status(200).json(result);
    } else {
      console.error(`Failed to check TradeMe permissions: ${result.error}`);
      return res.status(400).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error('Error checking TradeMe permissions:', error);
    return res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

/**
 * @route GET /api/trademe/account/member-summary
 * @description Get TradeMe member summary
 * @access Private (Admin, Manager)
 */
router.get('/member-summary', requireUser, async (req, res) => {
  try {
    // Check if user has permission (admin or manager)
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to view TradeMe member summary',
      });
    }

    const result = await tradeMeService.getMemberSummary();

    if (result.success) {
      return res.status(200).json(result);
    } else {
      console.error(`Failed to get TradeMe member summary: ${result.error}`);
      return res.status(400).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error('Error getting TradeMe member summary:', error);
    return res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

/**
 * @route GET /api/trademe/account/settings
 * @description Get TradeMe settings
 * @access Private (Admin, Manager)
 */
router.get('/settings', requireUser, async (req, res) => {
  try {
    // Check if user has permission (admin or manager)
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to view TradeMe settings',
      });
    }

    const settings = await tradeMeService.getSettings();

    return res.status(200).json({
      success: true,
      accountStatus: settings.accountStatus
    });
  } catch (error) {
    console.error('Error getting TradeMe settings:', error);
    return res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

/**
 * @route PUT /api/trademe/account/settings
 * @description Update TradeMe settings
 * @access Private (Admin, Manager)
 */
router.put('/settings', requireUser, async (req, res) => {
  try {
    // Check if user has permission (admin or manager)
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to update TradeMe settings',
      });
    }

    const { environment } = req.body;

    if (!environment || (environment !== 'production' && environment !== 'sandbox')) {
      return res.status(400).json({
        success: false,
        error: 'Invalid environment. Must be "production" or "sandbox"',
      });
    }

    const result = await tradeMeService.updateSettings({ environment });

    if (result.success) {
      return res.status(200).json(result);
    } else {
      console.error(`Failed to update TradeMe settings: ${result.error}`);
      return res.status(400).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error('Error updating TradeMe settings:', error);
    return res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

/**
 * @route POST /api/trademe/account/request-token
 * @description Get a request token for OAuth
 * @access Private (Admin, Manager)
 */
router.post('/request-token', requireUser, async (req, res) => {
  try {
    // Check if user has permission (admin or manager)
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to connect TradeMe account',
      });
    }

    const { environment, callbackUrl, scopes } = req.body;

    if (!environment || (environment !== 'production' && environment !== 'sandbox')) {
      return res.status(400).json({
        success: false,
        error: 'Invalid environment. Must be "production" or "sandbox"',
      });
    }

    if (!callbackUrl) {
      return res.status(400).json({
        success: false,
        error: 'Callback URL is required',
      });
    }

    // Ensure the callback URL uses HTTPS (TradeMe requirement)
    if (callbackUrl.startsWith('http:')) {
      callbackUrl = callbackUrl.replace('http:', 'https:');
    }

    try {
      // Use getRequestToken function
      console.log(`Getting request token for environment: ${environment}, callbackUrl: ${callbackUrl}`);
      const result = await tradeMeService.getRequestToken(environment, callbackUrl, scopes);
      console.log('Request token result:', result);
      return res.json(result);
    } catch (error) {
      console.error('Error getting request token:', error);
      return res.status(500).json({
        success: false,
        error: error.message || 'Failed to get request token',
      });
    }

    // The response is already sent in the try-catch block
  } catch (error) {
    console.error('Error getting request token:', error);
    return res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

/**
 * @route POST /api/trademe/account/access-token
 * @description Exchange a request token for an access token
 * @access Private (Admin, Manager)
 */
router.post('/access-token', requireUser, async (req, res) => {
  try {
    // Check if user has permission (admin or manager)
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to connect TradeMe account',
      });
    }

    const { environment, oauthToken, oauthVerifier } = req.body;

    if (!environment || (environment !== 'production' && environment !== 'sandbox')) {
      return res.status(400).json({
        success: false,
        error: 'Invalid environment. Must be "production" or "sandbox"',
      });
    }

    if (!oauthToken || !oauthVerifier) {
      return res.status(400).json({
        success: false,
        error: 'OAuth token and verifier are required',
      });
    }

    console.log(`Getting access token with environment: ${environment}, token: ${oauthToken}, verifier: ${oauthVerifier}`);

    try {
      const result = await tradeMeService.getAccessToken(environment, oauthToken, oauthVerifier);

      if (result.success) {
        console.log('Successfully got access token');
        return res.status(200).json(result);
      } else {
        console.error(`Failed to get access token: ${result.error}`);
        return res.status(400).json({
          success: false,
          error: result.error,
        });
      }
    } catch (error) {
      console.error('Error in access token route:', error);
      return res.status(500).json({
        success: false,
        error: error.message || 'An unexpected error occurred',
      });
    }
  } catch (error) {
    console.error('Error getting access token:', error);
    return res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

/**
 * @route POST /api/trademe/account/connect
 * @description Connect TradeMe account
 * @access Private (Admin, Manager)
 */
router.post('/connect', requireUser, async (req, res) => {
  try {
    // Check if user has permission (admin or manager)
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to connect TradeMe account',
      });
    }

    const { environment, accessToken, accessTokenSecret } = req.body;

    if (!environment || (environment !== 'production' && environment !== 'sandbox')) {
      return res.status(400).json({
        success: false,
        error: 'Invalid environment. Must be "production" or "sandbox"',
      });
    }

    if (!accessToken || !accessTokenSecret) {
      return res.status(400).json({
        success: false,
        error: 'Access token and secret are required',
      });
    }

    // Call completeConnection instead of connectAccount
    const result = await tradeMeService.completeConnection(accessToken, accessTokenSecret, environment, req.user);

    if (result.success) {
      return res.status(200).json(result);
    } else {
      console.error(`Failed to connect TradeMe account: ${result.error}`);
      return res.status(400).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error('Error connecting TradeMe account:', error);
    return res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

/**
 * @route POST /api/trademe/account/disconnect
 * @description Disconnect TradeMe account
 * @access Private (Admin, Manager)
 */
router.post('/disconnect', requireUser, async (req, res) => {
  try {
    // Check if user has permission (admin or manager)
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to disconnect TradeMe account',
      });
    }

    const result = await tradeMeService.disconnectAccount(req.user);

    if (result.success) {
      return res.status(200).json(result);
    } else {
      console.error(`Failed to disconnect TradeMe account: ${result.error}`);
      return res.status(400).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error('Error disconnecting TradeMe account:', error);
    return res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

/**
 * @route POST /api/trademe/account/refresh
 * @description Refresh TradeMe connection
 * @access Private (Admin, Manager)
 */
router.post('/refresh', requireUser, async (req, res) => {
  try {
    // Check if user has permission (admin or manager)
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to refresh TradeMe connection',
      });
    }

    const result = await tradeMeService.refreshConnection();

    if (result.success) {
      return res.status(200).json(result);
    } else {
      console.error(`Failed to refresh TradeMe connection: ${result.error}`);
      return res.status(400).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error('Error refreshing TradeMe connection:', error);
    return res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

module.exports = router;
