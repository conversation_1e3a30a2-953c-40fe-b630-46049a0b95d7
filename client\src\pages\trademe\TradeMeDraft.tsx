import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { TradeMeItem } from '@/api/tradeMeItems';
import { getItems } from '@/api/tradeMeItems';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Loader2, Search, Plus, AlertCircle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { TradeMeListingCard } from '@/components/trademe/TradeMeListingCard';
import { useToast } from '@/hooks/useToast';

const TradeMeDraft: React.FC = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [draftItems, setDraftItems] = useState<TradeMeItem[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [totalItems, setTotalItems] = useState(0);

  useEffect(() => {
    fetchDraftItems();
  }, []);

  const fetchDraftItems = async (search = '') => {
    try {
      setLoading(true);
      const result = await getItems({
        status: 'draft',
        search,
        page: 1,
        limit: 50
      });

      if (result.success) {
        setDraftItems(result.items);
        setTotalItems(result.totalItems);
      } else {
        toast({
          title: 'Error',
          description: result.error || 'Failed to fetch draft items',
          variant: 'destructive'
        });
      }
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to fetch draft items',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchDraftItems(searchTerm);
  };

  const handleCreateNew = () => {
    navigate('/trademe/listing/new');
  };

  const handleEdit = (id: string) => {
    navigate(`/trademe/listing/edit/${id}`);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Draft Items</CardTitle>
        <CardDescription>Manage your draft TradeMe listings</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <form onSubmit={handleSearch} className="flex w-full max-w-sm items-center space-x-2">
            <Input
              type="search"
              placeholder="Search drafts..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <Button type="submit">
              <Search className="h-4 w-4" />
            </Button>
          </form>

          {loading ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : draftItems.length === 0 ? (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>No draft items found</AlertTitle>
              <AlertDescription>
                You don't have any draft items. Create a new draft to get started.
              </AlertDescription>
            </Alert>
          ) : (
            <div className="grid gap-6">
              <p className="text-sm text-muted-foreground">
                Showing {draftItems.length} of {totalItems} draft items
              </p>
              {draftItems.map((item) => (
                <TradeMeListingCard
                  key={item._id}
                  listing={item}
                  type="draft"
                  onEdit={() => handleEdit(item._id)}
                />
              ))}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default TradeMeDraft;
