import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { useAuth } from "@/contexts/AuthContext";
import { ProfileEditModal } from "@/components/ProfileEditModal";
import { PasswordChangeModal } from "@/components/PasswordChangeModal";
import { PinSetupModal } from "@/components/PinSetupModal";
import { Edit, Key, Mail, User, Calendar, Clock, UserCircle, ShieldCheck, KeyRound } from "lucide-react";
import { format } from "date-fns";

export function Profile() {
  const { user } = useAuth();
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isPasswordModalOpen, setIsPasswordModalOpen] = useState(false);
  const [isPinModalOpen, setIsPinModalOpen] = useState(false);

  if (!user) {
    return <div className="container mx-auto py-6">Loading user information...</div>;
  }

  // Format date in 12-hour time format
  const formatDate = (date: Date | string | null | undefined) => {
    if (!date) return 'Not available';
    try {
      const dateObj = date instanceof Date ? date : new Date(date);
      // Check if the date is valid
      if (isNaN(dateObj.getTime())) return 'Not available';
      return format(dateObj, 'MMM d, yyyy');
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Not available';
    }
  };

  // Format time in 12-hour format
  const formatTime = (date: Date | string | null | undefined) => {
    if (!date) return 'Not available';
    try {
      const dateObj = date instanceof Date ? date : new Date(date);
      // Check if the date is valid
      if (isNaN(dateObj.getTime())) return 'Not available';
      return format(dateObj, 'h:mm a'); // 12-hour format with am/pm
    } catch (error) {
      console.error('Error formatting time:', error);
      return 'Not available';
    }
  };

  // Get user initials for avatar
  const getUserInitials = () => {
    if (user.fullName) {
      const nameParts = user.fullName.split(' ');
      if (nameParts.length >= 2) {
        return `${nameParts[0][0]}${nameParts[nameParts.length - 1][0]}`.toUpperCase();
      }
      return user.fullName[0].toUpperCase();
    }
    return user.email[0].toUpperCase();
  };

  // Get role color
  const getRoleColor = () => {
    switch (user.role) {
      case 'admin':
        return 'bg-red-500/10 text-red-500 border-red-500/20';
      case 'manager':
        return 'bg-blue-500/10 text-blue-500 border-blue-500/20';
      default:
        return 'bg-green-500/10 text-green-500 border-green-500/20';
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <h1 className="text-2xl font-bold">Profile</h1>

      <Card className="border-none shadow-md overflow-hidden">
        <div className="bg-gradient-to-r from-primary/20 to-primary/5 p-6">
          <div className="flex flex-col md:flex-row items-center gap-4">
            <Avatar className="h-20 w-20 border-4 border-background shadow-md">
              <AvatarFallback className="text-xl font-semibold bg-primary/10">
                {getUserInitials()}
              </AvatarFallback>
            </Avatar>
            <div className="text-center md:text-left">
              <h2 className="text-2xl font-bold">{user.fullName || user.username || user.email.split('@')[0]}</h2>
              <p className="text-muted-foreground flex items-center gap-1 mt-1 justify-center md:justify-start">
                <Mail className="h-4 w-4" />
                {user.email}
              </p>
              <div className="mt-2 flex justify-center md:justify-start">
                <Badge className={`${getRoleColor()} capitalize px-2 py-0.5`}>
                  <ShieldCheck className="h-3.5 w-3.5 mr-1" />
                  {user.role}
                </Badge>
              </div>
            </div>
            <div className="mt-4 md:mt-0 md:ml-auto flex flex-wrap justify-center md:justify-end gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsPinModalOpen(true)}
                className="flex items-center gap-1"
              >
                <KeyRound className="h-4 w-4" />
                Manage PIN
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsPasswordModalOpen(true)}
                className="flex items-center gap-1"
              >
                <Key className="h-4 w-4" />
                Change Password
              </Button>
              <Button
                variant="default"
                size="sm"
                onClick={() => setIsEditModalOpen(true)}
                className="flex items-center gap-1"
              >
                <Edit className="h-4 w-4" />
                Edit Profile
              </Button>
            </div>
          </div>
        </div>

        <CardContent className="p-6">
          <div className="space-y-6">
            <div>
              <h3 className="text-base font-medium mb-3">Account Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4">
                <div className="flex items-start gap-2">
                  <UserCircle className="h-5 w-5 text-muted-foreground mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Username</p>
                    <p className="text-base">{user.username || "Not set"}</p>
                  </div>
                </div>

                <div className="flex items-start gap-2">
                  <User className="h-5 w-5 text-muted-foreground mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Full Name</p>
                    <p className="text-base">{user.fullName || "Not set"}</p>
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            <div>
              <h3 className="text-base font-medium mb-3">Account Activity</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4">
                <div className="flex items-start gap-2">
                  <Calendar className="h-5 w-5 text-muted-foreground mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Account Created</p>
                    <p className="text-base">{formatDate(user.createdAt)}</p>
                  </div>
                </div>

                <div className="flex items-start gap-2">
                  <Clock className="h-5 w-5 text-muted-foreground mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Last Login</p>
                    <div className="flex flex-col">
                      <p className="text-base">{formatDate(user.lastLoginAt)}</p>
                      <p className="text-sm text-muted-foreground">{formatTime(user.lastLoginAt)}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <ProfileEditModal
        open={isEditModalOpen}
        onOpenChange={setIsEditModalOpen}
      />

      <PasswordChangeModal
        open={isPasswordModalOpen}
        onOpenChange={setIsPasswordModalOpen}
      />

      <PinSetupModal
        open={isPinModalOpen}
        onOpenChange={setIsPinModalOpen}
      />
    </div>
  );
}