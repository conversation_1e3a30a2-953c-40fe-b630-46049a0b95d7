const ItemNote = require('../models/ItemNote');

class NoteService {
  /**
   * Add a note to an item
   * @param {string} itemId - The ID of the item
   * @param {string} userId - The ID of the user adding the note
   * @param {string} content - The content of the note
   * @returns {Promise<Object>} - The created note
   */
  async addNote(itemId, userId, content) {
    try {
      console.log(`Adding note to item ${itemId}`);

      const note = new ItemNote({
        itemId,
        userId,
        content,
        createdAt: new Date(),
      });

      await note.save();
      console.log(`Created note: ${note._id}`);

      return {
        success: true,
        note,
      };
    } catch (error) {
      console.error('Error adding note:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Get notes for a specific item
   * @param {string} itemId - The ID of the item
   * @returns {Promise<Object>} - The notes for the item
   */
  async getNotesByItem(itemId) {
    try {
      console.log(`Fetching notes for item ${itemId}`);

      const notes = await ItemNote.find({ itemId })
        .sort({ createdAt: -1 })
        .populate('userId', 'email username name')
        .lean();

      console.log(`Found ${notes.length} notes for item ${itemId}`);

      return {
        success: true,
        notes,
      };
    } catch (error) {
      console.error('Error fetching notes:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Delete a note
   * @param {string} noteId - The ID of the note to delete
   * @param {string} userId - The ID of the user attempting to delete the note
   * @param {string} userRole - The role of the user attempting to delete the note
   * @returns {Promise<Object>} - Result of the deletion
   */
  async deleteNote(noteId, userId, userRole) {
    try {
      console.log(`Attempting to delete note ${noteId}`);

      // Find the note
      const note = await ItemNote.findById(noteId);

      if (!note) {
        console.error(`Note ${noteId} not found`);
        return {
          success: false,
          error: 'Note not found',
        };
      }

      // Check permission to delete:
      // 1. User is admin or manager
      // 2. User is the creator of the note
      const isAdmin = userRole === 'admin';
      const isManager = userRole === 'manager';
      const isCreator = note.userId.toString() === userId;

      if (!(isAdmin || isManager || isCreator)) {
        console.error(`User ${userId} is not authorized to delete note ${noteId}`);
        return {
          success: false,
          error: 'You do not have permission to delete this note',
        };
      }

      // Delete the note
      await ItemNote.findByIdAndDelete(noteId);
      console.log(`Deleted note ${noteId}`);

      return {
        success: true,
        message: 'Note deleted successfully',
      };
    } catch (error) {
      console.error('Error deleting note:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }
}

module.exports = new NoteService();