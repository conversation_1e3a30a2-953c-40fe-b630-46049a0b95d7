const mongoose = require('mongoose');

// Define schema for attribute options
const attributeOptionSchema = new mongoose.Schema({
  value: String,
  display: String
}, { _id: false });

// Define schema for attribute units
const attributeUnitSchema = new mongoose.Schema({
  display: String,
  multiplier: Number
}, { _id: false });

// Define schema for attribute range
const attributeRangeSchema = new mongoose.Schema({
  lower: String,
  upper: String
}, { _id: false });

// Define schema for attributes
const attributeSchema = new mongoose.Schema({
  name: String,
  displayName: String,
  type: {
    type: Number,
    enum: [0, 1, 2, 3, 4, 5], // None, Boolean, Integer, Decimal, String, DateTime
    default: 0
  },
  range: attributeRangeSchema,
  maxStringLength: Number,
  options: [attributeOptionSchema],
  units: [attributeUnitSchema],
  isRequiredForSell: {
    type: Boolean,
    default: false
  },
  groupName: String
}, { _id: false });

// Define schema for fee tiers
const feeTierSchema = new mongoose.Schema({
  minimumTierPrice: Number,
  fixedFee: Number,
  percentageFee: Number
}, { _id: false });

// Define schema for payment methods
const paymentMethodSchema = new mongoose.Schema({
  id: {
    type: Number,
    enum: [0, 1, 2, 4, 8, 16, 32, 64] // None, BankDeposit, CreditCard, Cash, SafeTrader, Other, Ping, Afterpay
  },
  name: String,
  defaultsToOn: Boolean,
  logoUrl: String,
  sellerFeePercentage: Number
}, { _id: false });

// Define schema for fees
const feesSchema = new mongoose.Schema({
  bold: Number,
  bundle: Number,
  endDate: Number,
  feature: Number,
  gallery: Number,
  galleryPlus: Number,
  highlight: Number,
  homepage: Number,
  listing: Number,
  multiPhoto: Number,
  reserve: Number,
  subtitle: Number,
  tenDays: Number,
  withdrawal: Number,
  superFeature: Number,
  superFeatureBundle: Number,
  highVolume: Number,
  listingFeeTiers: [feeTierSchema],
  minimumSuccessFee: Number,
  maximumSuccessFee: Number,
  successFeeTiers: [feeTierSchema],
  branding: Number,
  secondCategory: Number
}, { _id: false });

const tradeMeCategorySchema = new mongoose.Schema({
  categoryId: {
    type: String,
    required: true,
    unique: true
  },
  name: {
    type: String,
    required: true
  },
  path: {
    type: String,
    required: true
  },
  parentId: {
    type: String
  },
  isLeaf: {
    type: Boolean,
    default: false
  },
  hasLegalNotice: {
    type: Boolean,
    default: false
  },
  legalNotice: {
    type: String
  },
  count: {
    type: Number,
    default: 0
  },
  // ETag for caching API responses
  etag: {
    type: String
  },
  // Last synced timestamp
  lastSynced: {
    type: Date,
    default: Date.now
  },
  // Additional fields from the detailed category API
  isRestricted: {
    type: Boolean,
    default: false
  },
  isWine: {
    type: Boolean,
    default: false
  },
  canListAuctions: {
    type: Boolean,
    default: true
  },
  canListClassifieds: {
    type: Boolean,
    default: true
  },
  canRelist: {
    type: Boolean,
    default: true
  },
  authenticatedBidsOnly: {
    type: Boolean,
    default: false
  },
  defaultDuration: {
    type: Number,
    default: 7 // Default to 7 days
  },
  allowedDurations: {
    type: [Number],
    default: [2, 3, 4, 5, 6, 7]
  },
  fees: {
    type: feesSchema,
    default: {}
  },
  freePhotoCount: {
    type: Number,
    default: 10
  },
  maximumPhotoCount: {
    type: Number,
    default: 20
  },
  isFreeToRelist: {
    type: Boolean,
    default: false
  },
  attributes: {
    type: [attributeSchema],
    default: []
  },
  hasAutomaticallyCreatedTitle: {
    type: Boolean,
    default: false
  },
  canUseTradeMeShipping: {
    type: Boolean,
    default: false
  },
  maximumTitleLength: {
    type: Number,
    default: 80
  },
  areaOfBusiness: {
    type: Number,
    enum: [0, 1, 2, 3, 4, 5], // NotSpecified, Marketplace, Property, Motors, Jobs, Services
    default: 0
  },
  extensionPeriod: {
    type: Number,
    default: 0
  },
  defaultRelistDuration: {
    type: Number,
    default: 7
  },
  canHaveSecondCategory: {
    type: Boolean,
    default: false
  },
  canBeSecondCategory: {
    type: Boolean,
    default: false
  },
  isFirearms: {
    type: Boolean,
    default: false
  },
  paymentMethods: {
    type: [paymentMethodSchema],
    default: []
  },
  subcategories: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'TradeMeCategory'
  }]
}, {
  timestamps: true
});

const TradeMeCategory = mongoose.model('TradeMeCategory', tradeMeCategorySchema);

module.exports = TradeMeCategory;
