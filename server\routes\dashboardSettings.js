const express = require('express');
const router = express.Router();
const DashboardSettings = require('../models/DashboardSettings');
const { scheduleScreenshots, takeScreenshots, getScreenshotStatus } = require('../services/screenshotService');

router.get('/', async (req, res) => {
  try {
    let settings = await DashboardSettings.findOne();
    if (!settings) {
      settings = await DashboardSettings.create({
        pbiLinks: [],
        screenshotInterval: 5,
        screenshotSchedule: {
          enabled: true,
          startTime: '08:00',
          endTime: '17:30'
        }
      });
    }
    res.json(settings);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

router.put('/', async (req, res) => {
  try {
    const settings = await DashboardSettings.findOneAndUpdate(
      {},
      req.body,
      { new: true, upsert: true }
    );
    await scheduleScreenshots();
    res.json(settings);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

router.post('/force-update', async (req, res) => {
  try {
    // Get current settings
    const settings = await DashboardSettings.findOne();
    if (!settings) {
      return res.status(404).json({ error: 'Dashboard settings not found' });
    }

    // Count enabled links for progress tracking
    const enabledLinks = settings.pbiLinks.filter(link => link.enabled);
    const totalLinks = enabledLinks.length;

    // Return immediately with the number of links to update
    // This allows the frontend to start showing progress right away
    res.json({
      success: true,
      message: 'Dashboard update triggered successfully',
      totalLinks: totalLinks,
      dashboardNames: enabledLinks.map(link => link.name)
    });

    // Force take screenshots after sending the response
    // This way the client doesn't have to wait for the entire process to complete
    // Pass manual: true to bypass schedule time window check
    await takeScreenshots(settings, { manual: true });

  } catch (error) {
    console.error('Error during force update:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get the current status of the screenshot process
router.get('/screenshot-status', async (req, res) => {
  try {
    const status = getScreenshotStatus();
    res.json(status);
  } catch (error) {
    console.error('Error getting screenshot status:', error);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;


