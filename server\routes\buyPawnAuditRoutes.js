/**
 * Buy/Pawn Audit Routes
 *
 * API routes for buy/pawn audits
 * This file has been updated to use the new audit implementation
 */

const express = require('express');
const { requireUser } = require('./middleware/auth');
const { getDb } = require('../db');
const { ObjectId } = require('mongodb');
const User = require('../models/User');
const buyPawnAuditService = require('../services/buyPawnAuditService');
const router = express.Router();

/**
 * @route POST /api/buys/audits
 * @description Create a new audit
 * @access Private (Admin, Manager)
 */
router.post('/', requireUser, async (req, res) => {
  try {
    // Check if user has permission (admin or manager)
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to create audits'
      });
    }

    const db = getDb();

    // Add metadata to the audit
    const audit = {
      ...req.body,
      createdBy: req.user._id,
      createdByName: `${req.user.firstName} ${req.user.lastName}`,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const result = await db.collection('audits').insertOne(audit);

    return res.status(201).json({
      success: true,
      id: result.insertedId,
      message: 'Audit created successfully'
    });
  } catch (error) {
    console.error('Error creating audit:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route POST /api/buys/audits/direct
 * @description Create a new audit directly with transaction details
 * @access Private (Admin, Manager)
 */
router.post('/direct', requireUser, async (req, res) => {
  try {
    // Check if user has permission (admin or manager)
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to create audits'
      });
    }

    const { transactionType, transactionId, employeeId, amount, itemDescription, stockcode, brand, items } = req.body;

    if (!transactionType || !transactionId || !employeeId || !amount) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: transactionType, transactionId, employeeId, amount'
      });
    }

    // Validate items array (new structure) or itemDescription (legacy)
    if (!items && !itemDescription) {
      return res.status(400).json({
        success: false,
        error: 'Either items array or itemDescription is required'
      });
    }

    // If items array is provided, validate it
    if (items) {
      if (!Array.isArray(items) || items.length === 0) {
        return res.status(400).json({
          success: false,
          error: 'Items must be a non-empty array'
        });
      }

      // Validate each item
      for (const item of items) {
        if (!item.stockcode || !item.brand || !item.description || typeof item.cost !== 'number') {
          return res.status(400).json({
            success: false,
            error: 'Each item must have stockcode, brand, description, and cost'
          });
        }
      }

      // Validate that sum of item costs equals transaction amount
      const itemsTotal = items.reduce((total, item) => total + item.cost, 0);
      if (Math.abs(itemsTotal - amount) > 0.01) {
        return res.status(400).json({
          success: false,
          error: `Sum of item costs (${itemsTotal}) must equal transaction amount (${amount})`
        });
      }
    }

    // Fetch employee details from the database
    const employee = await User.findById(employeeId);
    if (!employee) {
      return res.status(400).json({
        success: false,
        error: 'Employee not found'
      });
    }

    // Pass all the audit data to the service
    const result = await buyPawnAuditService.createDirectAudit({
      ...req.body, // Include all form data
      employeeName: employee.fullName, // Override with fetched employee name
    }, req.user);

    if (result.success) {
      return res.status(201).json(result);
    } else {
      return res.status(400).json(result);
    }
  } catch (error) {
    console.error('Error creating direct buy/pawn audit:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/buys/audits
 * @description Get audits with filtering and pagination
 * @access Private
 */
router.get('/', requireUser, async (req, res) => {
  try {
    const db = getDb();
    const {
      type,
      status,
      employeeId,
      fromDate,
      toDate,
      search,
      page = 1,
      limit = 10
    } = req.query;

    // Build query
    const query = {};

    // Employees can only see audits related to their transactions
    if (req.user.role === 'employee') {
      query.employeeId = req.user._id.toString();
    }

    if (type && type !== 'all') {
      query.auditType = type;
    }

    if (status && status !== 'all') {
      query['summary.overallAssessment'] = status;
    }

    if (employeeId && req.user.role !== 'employee') {
      query.employeeId = employeeId;
    }

    // Date range filter
    if (fromDate || toDate) {
      query.createdAt = {};

      if (fromDate) {
        query.createdAt.$gte = new Date(fromDate);
      }

      if (toDate) {
        query.createdAt.$lte = new Date(toDate);
      }
    }

    // Search by transaction ID or employee name
    if (search) {
      query.$or = [
        { transactionId: { $regex: search, $options: 'i' } },
        { employeeName: { $regex: search, $options: 'i' } }
      ];
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Get total count for pagination
    const total = await db.collection('buypawnaudits').countDocuments(query);

    // Get audits with pagination
    const audits = await db
      .collection('buypawnaudits')
      .find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .toArray();

    return res.status(200).json({
      success: true,
      data: {
        audits,
        pagination: {
          total,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    });
  } catch (error) {
    console.error('Error getting audits:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/buys/audits/deal/:dealId
 * @description Get an audit by deal ID
 * @access Private
 */
router.get('/deal/:dealId', requireUser, async (req, res) => {
  try {
    const db = getDb();

    // Find audit by dealId
    const audit = await db.collection('buypawnaudits').findOne({ dealId: req.params.dealId });

    if (!audit) {
      return res.status(404).json({
        success: false,
        error: 'Audit not found'
      });
    }

    // Employees can only view audits for their own transactions
    if (req.user.role === 'employee' && audit.employeeId !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to view this audit'
      });
    }

    return res.status(200).json({
      success: true,
      data: audit
    });
  } catch (error) {
    console.error('Error getting buy/pawn audit by deal ID:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/buys/audits/transaction/:transactionId
 * @description Get an audit by transaction ID
 * @access Private
 */
router.get('/transaction/:transactionId', requireUser, async (req, res) => {
  try {
    const db = getDb();

    // Find audit by transactionId
    const audit = await db.collection('buypawnaudits').findOne({ transactionId: req.params.transactionId });

    if (!audit) {
      return res.status(404).json({
        success: false,
        error: 'Audit not found'
      });
    }

    // Employees can only view audits for their own transactions
    if (req.user.role === 'employee' && audit.employeeId !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to view this audit'
      });
    }

    return res.status(200).json({
      success: true,
      data: audit
    });
  } catch (error) {
    console.error('Error getting buy/pawn audit by transaction ID:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/buys/audits/stats
 * @description Get audit statistics
 * @access Private
 */
router.get('/stats', requireUser, async (req, res) => {
  try {
    const db = getDb();
    const timeRange = req.query.timeRange || '30days';

    // Calculate date range based on timeRange
    const now = new Date();
    let startDate = new Date();

    switch (timeRange) {
      case '7days':
        startDate.setDate(now.getDate() - 7);
        break;
      case '30days':
        startDate.setDate(now.getDate() - 30);
        break;
      case '90days':
        startDate.setDate(now.getDate() - 90);
        break;
      case 'year':
        startDate.setFullYear(now.getFullYear() - 1);
        break;
      case 'all':
        startDate = new Date(0); // Beginning of time
        break;
      default:
        startDate.setDate(now.getDate() - 30);
    }

    // Get total audits
    const totalAudits = await db.collection('buypawnaudits').countDocuments({
      createdAt: { $gte: startDate }
    });

    // Get compliant audits
    const compliantCount = await db.collection('buypawnaudits').countDocuments({
      createdAt: { $gte: startDate },
      overallCompliance: 'compliant'
    });

    // Get non-compliant audits
    const nonCompliantCount = await db.collection('buypawnaudits').countDocuments({
      createdAt: { $gte: startDate },
      overallCompliance: { $in: ['minor_non_compliant', 'major_non_compliant'] }
    });

    // Get flagged audits
    const flaggedCount = await db.collection('buypawnaudits').countDocuments({
      createdAt: { $gte: startDate },
      flaggedForFollowup: true
    });

    // Get pending follow-up audits
    const pendingFollowupCount = await db.collection('buypawnaudits').countDocuments({
      createdAt: { $gte: startDate },
      flaggedForFollowup: true,
      followedUp: false
    });

    // Calculate compliance rate
    const complianceRate = totalAudits > 0
      ? Math.round((compliantCount / totalAudits) * 100)
      : 0;

    // Get recent audits (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(now.getDate() - 30);
    const recentAudits = await db.collection('buypawnaudits').countDocuments({
      createdAt: { $gte: thirtyDaysAgo }
    });

    // Calculate compliance change (compared to previous period)
    const previousPeriodStart = new Date(startDate);
    previousPeriodStart.setDate(previousPeriodStart.getDate() - (now.getTime() - startDate.getTime()) / (24 * 60 * 60 * 1000));

    const previousPeriodAudits = await db.collection('buypawnaudits').countDocuments({
      createdAt: { $gte: previousPeriodStart, $lt: startDate }
    });

    const previousPeriodCompliant = await db.collection('buypawnaudits').countDocuments({
      createdAt: { $gte: previousPeriodStart, $lt: startDate },
      overallCompliance: 'compliant'
    });

    const previousComplianceRate = previousPeriodAudits > 0
      ? Math.round((previousPeriodCompliant / previousPeriodAudits) * 100)
      : 0;

    const complianceChange = complianceRate - previousComplianceRate;

    // Calculate compliance trends over time
    let complianceTrendsAggregation = [];

    // Only run the aggregation if there are audits
    if (totalAudits > 0) {
      complianceTrendsAggregation = await db.collection('buypawnaudits').aggregate([
        {
          $match: {
            createdAt: { $gte: startDate }
          }
        },
        {
          $group: {
            _id: {
              $dateToString: { format: '%Y-%m-%d', date: '$createdAt' }
            },
            compliantCount: {
              $sum: { $cond: [{ $eq: ['$overallCompliance', 'compliant'] }, 1, 0] }
            },
            minorNonCompliantCount: {
              $sum: { $cond: [{ $eq: ['$overallCompliance', 'minor_non_compliant'] }, 1, 0] }
            },
            majorNonCompliantCount: {
              $sum: { $cond: [{ $eq: ['$overallCompliance', 'major_non_compliant'] }, 1, 0] }
            },
            totalCount: { $sum: 1 }
          }
        },
        {
          $project: {
            _id: 0,
            date: '$_id',
            compliantRate: {
              $cond: [
                { $eq: ['$totalCount', 0] },
                0,
                { $multiply: [{ $divide: ['$compliantCount', '$totalCount'] }, 100] }
              ]
            },
            minorNonCompliantRate: {
              $cond: [
                { $eq: ['$totalCount', 0] },
                0,
                { $multiply: [{ $divide: ['$minorNonCompliantCount', '$totalCount'] }, 100] }
              ]
            },
            majorNonCompliantRate: {
              $cond: [
                { $eq: ['$totalCount', 0] },
                0,
                { $multiply: [{ $divide: ['$majorNonCompliantCount', '$totalCount'] }, 100] }
              ]
            },
            totalAudits: '$totalCount'
          }
        },
        {
          $sort: { date: 1 }
        }
      ]).toArray();
    }

    // Calculate audit type distribution
    let auditTypeDistributionAggregation = [];

    // Only run the aggregation if there are audits
    if (totalAudits > 0) {
      auditTypeDistributionAggregation = await db.collection('buypawnaudits').aggregate([
        {
          $match: {
            createdAt: { $gte: startDate }
          }
        },
        {
          $group: {
            _id: '$auditType',
            compliant: {
              $sum: { $cond: [{ $eq: ['$overallCompliance', 'compliant'] }, 1, 0] }
            },
            minorNonCompliant: {
              $sum: { $cond: [{ $eq: ['$overallCompliance', 'minor_non_compliant'] }, 1, 0] }
            },
            majorNonCompliant: {
              $sum: { $cond: [{ $eq: ['$overallCompliance', 'major_non_compliant'] }, 1, 0] }
            },
            total: { $sum: 1 }
          }
        },
        {
          $project: {
            _id: 0,
            type: '$_id',
            compliant: 1,
            minorNonCompliant: 1,
            majorNonCompliant: 1,
            total: 1
          }
        }
      ]).toArray();
    }

    // Calculate staff compliance
    let staffComplianceAggregation = [];

    // Only run the aggregation if there are audits
    if (totalAudits > 0) {
      staffComplianceAggregation = await db.collection('buypawnaudits').aggregate([
        {
          $match: {
            createdAt: { $gte: startDate }
          }
        },
        {
          $group: {
            _id: '$employeeId',
            employeeName: { $first: '$employeeName' },
            totalAudits: { $sum: 1 },
            compliantCount: {
              $sum: { $cond: [{ $eq: ['$overallCompliance', 'compliant'] }, 1, 0] }
            },
            minorNonCompliantCount: {
              $sum: { $cond: [{ $eq: ['$overallCompliance', 'minor_non_compliant'] }, 1, 0] }
            },
            majorNonCompliantCount: {
              $sum: { $cond: [{ $eq: ['$overallCompliance', 'major_non_compliant'] }, 1, 0] }
            },
            flaggedCount: {
              $sum: { $cond: ['$flaggedForFollowup', 1, 0] }
            }
          }
        },
        {
          $project: {
            _id: 0,
            employeeId: '$_id',
            employeeName: 1,
            totalAudits: 1,
            compliantCount: 1,
            minorNonCompliantCount: 1,
            majorNonCompliantCount: 1,
            flaggedCount: 1,
            complianceRate: {
              $cond: [
                { $eq: ['$totalAudits', 0] },
                0,
                { $multiply: [{ $divide: ['$compliantCount', '$totalAudits'] }, 100] }
              ]
            }
          }
        }
      ]).toArray();

      // Calculate previous period compliance for each staff member
      for (const staff of staffComplianceAggregation) {
        // Skip if employeeId is not a valid ObjectId
        if (!staff.employeeId || !ObjectId.isValid(staff.employeeId)) {
          staff.complianceChange = 0;
          continue;
        }

        const previousPeriodAudits = await db.collection('buypawnaudits').countDocuments({
          employeeId: staff.employeeId,
          createdAt: { $gte: previousPeriodStart, $lt: startDate }
        });

        const previousPeriodCompliant = await db.collection('buypawnaudits').countDocuments({
          employeeId: staff.employeeId,
          createdAt: { $gte: previousPeriodStart, $lt: startDate },
          overallCompliance: 'compliant'
        });

        const previousComplianceRate = previousPeriodAudits > 0
          ? Math.round((previousPeriodCompliant / previousPeriodAudits) * 100)
          : 0;

        staff.complianceChange = Math.round(staff.complianceRate) - previousComplianceRate;
      }
    }

    // Calculate audit volume over time
    let auditVolumeAggregation = [];

    // Only run the aggregation if there are audits
    if (totalAudits > 0) {
      auditVolumeAggregation = await db.collection('buypawnaudits').aggregate([
        {
          $match: {
            createdAt: { $gte: startDate }
          }
        },
        {
          $group: {
            _id: {
              $dateToString: { format: '%Y-%m-%d', date: '$createdAt' }
            },
            buyCount: {
              $sum: { $cond: [{ $eq: ['$auditType', 'buy'] }, 1, 0] }
            },
            pawnCount: {
              $sum: { $cond: [{ $eq: ['$auditType', 'pawn'] }, 1, 0] }
            },
            priceCount: {
              $sum: { $cond: [{ $eq: ['$auditType', 'price'] }, 1, 0] }
            }
          }
        },
        {
          $project: {
            _id: 0,
            date: '$_id',
            buyCount: 1,
            pawnCount: 1,
            priceCount: 1,
            totalCount: { $add: ['$buyCount', '$pawnCount', '$priceCount'] }
          }
        },
        {
          $sort: { date: 1 }
        }
      ]).toArray();
    }

    // Calculate staff performance trends over time
    let staffPerformanceTrendsAggregation = [];

    // Only run the aggregation if there are audits
    if (totalAudits > 0) {
      staffPerformanceTrendsAggregation = await db.collection('buypawnaudits').aggregate([
        {
          $match: {
            createdAt: { $gte: startDate }
          }
        },
        {
          $group: {
            _id: {
              date: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
              employeeId: '$employeeId'
            },
            compliantCount: {
              $sum: { $cond: [{ $eq: ['$overallCompliance', 'compliant'] }, 1, 0] }
            },
            totalCount: { $sum: 1 }
          }
        },
        {
          $group: {
            _id: '$_id.date',
            employeeRates: {
              $push: {
                employeeId: '$_id.employeeId',
                complianceRate: {
                  $cond: [
                    { $eq: ['$totalCount', 0] },
                    0,
                    { $multiply: [{ $divide: ['$compliantCount', '$totalCount'] }, 100] }
                  ]
                }
              }
            }
          }
        },
        {
          $project: {
            _id: 0,
            date: '$_id',
            averageComplianceRate: { $avg: '$employeeRates.complianceRate' },
            highestComplianceRate: { $max: '$employeeRates.complianceRate' },
            lowestComplianceRate: { $min: '$employeeRates.complianceRate' }
          }
        },
        {
          $sort: { date: 1 }
        }
      ]).toArray();
    }

    return res.status(200).json({
      success: true,
      data: {
        totalAudits,
        compliantCount,
        nonCompliantCount,
        flaggedCount,
        pendingFollowupCount,
        complianceRate,
        complianceChange,
        recentAudits,
        complianceTrends: complianceTrendsAggregation,
        auditTypeDistribution: auditTypeDistributionAggregation,
        staffCompliance: staffComplianceAggregation,
        auditVolume: auditVolumeAggregation,
        staffPerformanceTrends: staffPerformanceTrendsAggregation
      }
    });
  } catch (error) {
    console.error('Error getting audit statistics:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/buys/audits/staff
 * @description Get staff performance data
 * @access Private (Admin, Manager)
 */
router.get('/staff', requireUser, async (req, res) => {
  try {
    // Check if user has permission (admin or manager)
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to view staff performance data'
      });
    }

    const db = getDb();
    const timeRange = req.query.timeRange || '30days';

    // Calculate date range based on timeRange
    const now = new Date();
    let startDate = new Date();

    switch (timeRange) {
      case '7days':
        startDate.setDate(now.getDate() - 7);
        break;
      case '30days':
        startDate.setDate(now.getDate() - 30);
        break;
      case '90days':
        startDate.setDate(now.getDate() - 90);
        break;
      case 'year':
        startDate.setFullYear(now.getFullYear() - 1);
        break;
      case 'all':
        startDate = new Date(0); // Beginning of time
        break;
      default:
        startDate.setDate(now.getDate() - 30);
    }

    // Time range parameters already processed above

    // Calculate previous period for comparison
    const periodLength = now.getTime() - startDate.getTime();
    const previousPeriodStart = new Date(startDate.getTime() - periodLength);

    // Get staff performance data
    const staffPerformance = await db.collection('audits').aggregate([
      {
        $match: {
          createdAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: '$employeeId',
          employeeName: { $first: '$employeeName' },
          totalAudits: { $sum: 1 },
          compliantCount: {
            $sum: { $cond: [{ $eq: ['$overallCompliance', 'compliant'] }, 1, 0] }
          },
          minorNonCompliantCount: {
            $sum: { $cond: [{ $eq: ['$overallCompliance', 'minor_non_compliant'] }, 1, 0] }
          },
          majorNonCompliantCount: {
            $sum: { $cond: [{ $eq: ['$overallCompliance', 'major_non_compliant'] }, 1, 0] }
          },
          flaggedCount: {
            $sum: { $cond: ['$flaggedForFollowup', 1, 0] }
          }
        }
      },
      {
        $project: {
          _id: 0,
          employeeId: '$_id',
          employeeName: 1,
          totalAudits: 1,
          compliantCount: 1,
          minorNonCompliantCount: 1,
          majorNonCompliantCount: 1,
          flaggedCount: 1,
          complianceRate: {
            $cond: [
              { $eq: ['$totalAudits', 0] },
              0,
              { $multiply: [{ $divide: ['$compliantCount', '$totalAudits'] }, 100] }
            ]
          }
        }
      }
    ]).toArray();

    // For each staff member, get additional data
    for (const staff of staffPerformance) {
      // Calculate compliance change
      const previousPeriodAudits = await db.collection('audits').countDocuments({
        employeeId: staff.employeeId,
        createdAt: { $gte: previousPeriodStart, $lt: startDate }
      });

      const previousPeriodCompliant = await db.collection('audits').countDocuments({
        employeeId: staff.employeeId,
        createdAt: { $gte: previousPeriodStart, $lt: startDate },
        overallCompliance: 'compliant'
      });

      const previousComplianceRate = previousPeriodAudits > 0
        ? Math.round((previousPeriodCompliant / previousPeriodAudits) * 100)
        : 0;

      staff.complianceChange = Math.round(staff.complianceRate) - previousComplianceRate;

      // Get recent audits
      const recentAudits = await db.collection('audits')
        .find({ employeeId: staff.employeeId })
        .sort({ createdAt: -1 })
        .limit(5)
        .toArray();

      staff.recentAudits = recentAudits.map(audit => ({
        id: audit._id.toString(),
        transactionId: audit.transactionId,
        date: audit.auditDate.toISOString().split('T')[0],
        compliance: audit.overallCompliance,
        score: audit.overallScore
      }));

      // Get compliance history
      const complianceHistory = await db.collection('audits').aggregate([
        {
          $match: {
            employeeId: staff.employeeId,
            createdAt: { $gte: new Date(now.getFullYear() - 1, now.getMonth(), 1) } // Last year by month
          }
        },
        {
          $group: {
            _id: {
              $dateToString: { format: '%Y-%m-%d', date: { $dateFromParts: { year: { $year: '$createdAt' }, month: { $month: '$createdAt' }, day: 1 } } }
            },
            compliantCount: {
              $sum: { $cond: [{ $eq: ['$overallCompliance', 'compliant'] }, 1, 0] }
            },
            totalCount: { $sum: 1 }
          }
        },
        {
          $project: {
            _id: 0,
            date: '$_id',
            complianceRate: {
              $cond: [
                { $eq: ['$totalCount', 0] },
                0,
                { $multiply: [{ $divide: ['$compliantCount', '$totalCount'] }, 100] }
              ]
            }
          }
        },
        {
          $sort: { date: 1 }
        }
      ]).toArray();

      staff.complianceHistory = complianceHistory;
    }

    return res.status(200).json({
      success: true,
      data: staffPerformance
    });
  } catch (error) {
    console.error('Error getting staff performance data:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/buys/audits/staff/:employeeId
 * @description Get performance data for a specific staff member
 * @access Private (Admin, Manager)
 */
router.get('/staff/:employeeId', requireUser, async (req, res) => {
  try {
    // Check if user has permission (admin or manager)
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to view staff performance data'
      });
    }

    const db = getDb();
    const { employeeId } = req.params;
    const timeRangeParam = req.query.timeRange || '30days';

    // Calculate date range based on timeRange
    const currentDate = new Date();
    let periodStartDate = new Date();

    switch (timeRangeParam) {
      case '7days':
        periodStartDate.setDate(currentDate.getDate() - 7);
        break;
      case '30days':
        periodStartDate.setDate(currentDate.getDate() - 30);
        break;
      case '90days':
        periodStartDate.setDate(currentDate.getDate() - 90);
        break;
      case 'year':
        periodStartDate.setFullYear(currentDate.getFullYear() - 1);
        break;
      case 'all':
        periodStartDate = new Date(0); // Beginning of time
        break;
      default:
        periodStartDate.setDate(currentDate.getDate() - 30);
    }

    // Time range parameters already processed above

    // Get employee data
    const employee = await User.findById(employeeId);
    if (!employee) {
      return res.status(404).json({
        success: false,
        error: 'Employee not found'
      });
    }

    // Get audit counts
    const totalAudits = await db.collection('audits').countDocuments({
      employeeId: employeeId,
      createdAt: { $gte: periodStartDate }
    });

    const compliantCount = await db.collection('audits').countDocuments({
      employeeId: employeeId,
      createdAt: { $gte: periodStartDate },
      overallCompliance: 'compliant'
    });

    const minorNonCompliantCount = await db.collection('audits').countDocuments({
      employeeId: employeeId,
      createdAt: { $gte: periodStartDate },
      overallCompliance: 'minor_non_compliant'
    });

    const majorNonCompliantCount = await db.collection('audits').countDocuments({
      employeeId: employeeId,
      createdAt: { $gte: periodStartDate },
      overallCompliance: 'major_non_compliant'
    });

    const complianceRate = totalAudits > 0
      ? Math.round((compliantCount / totalAudits) * 100)
      : 0;

    // Get compliance history
    const complianceHistory = await db.collection('audits').aggregate([
      {
        $match: {
          employeeId: employeeId,
          createdAt: { $gte: new Date(currentDate.getFullYear() - 1, currentDate.getMonth(), 1) } // Last year by month
        }
      },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: { $dateFromParts: { year: { $year: '$createdAt' }, month: { $month: '$createdAt' }, day: 1 } } }
          },
          compliantCount: {
            $sum: { $cond: [{ $eq: ['$overallCompliance', 'compliant'] }, 1, 0] }
          },
          totalCount: { $sum: 1 }
        }
      },
      {
        $project: {
          _id: 0,
          date: '$_id',
          complianceRate: {
            $cond: [
              { $eq: ['$totalCount', 0] },
              0,
              { $multiply: [{ $divide: ['$compliantCount', '$totalCount'] }, 100] }
            ]
          }
        }
      },
      {
        $sort: { date: 1 }
      }
    ]).toArray();

    // Get recent audits
    const recentAudits = await db.collection('audits')
      .find({ employeeId: employeeId })
      .sort({ createdAt: -1 })
      .limit(5)
      .toArray();

    const formattedRecentAudits = recentAudits.map(audit => ({
      id: audit._id.toString(),
      transactionId: audit.transactionId,
      date: audit.auditDate.toISOString().split('T')[0],
      compliance: audit.overallCompliance,
      score: audit.overallScore
    }));

    // Prepare employee data
    const employeeData = {
      id: employeeId,
      name: employee.fullName,
      totalAudits,
      complianceRate,
      complianceHistory,
      auditBreakdown: {
        compliant: compliantCount,
        minorNonCompliant: minorNonCompliantCount,
        majorNonCompliant: majorNonCompliantCount,
      },
      recentAudits: formattedRecentAudits,
    };

    return res.status(200).json({
      success: true,
      data: employeeData
    });
  } catch (error) {
    console.error('Error getting staff performance data:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route POST /api/buys/audits/export
 * @description Export audit data in various formats
 * @access Private (Admin, Manager)
 */
// Helper function to get audit stats for export
const getAuditStats = async (db, timeRange) => {
  // Get time range parameters
  const now = new Date();
  let startDate = new Date(now);
  startDate.setDate(startDate.getDate() - 30); // Default to last 30 days

  if (timeRange) {
    switch (timeRange) {
      case '7days':
        startDate = new Date(now);
        startDate.setDate(startDate.getDate() - 7);
        break;
      case '90days':
        startDate = new Date(now);
        startDate.setDate(startDate.getDate() - 90);
        break;
      case 'year':
        startDate = new Date(now);
        startDate.setFullYear(startDate.getFullYear() - 1);
        break;
      case 'all':
        startDate = new Date(0); // Beginning of time
        break;
    }
  }

  // Calculate previous period for comparison
  const periodLength = now.getTime() - startDate.getTime();
  const previousPeriodStart = new Date(startDate.getTime() - periodLength);

  // Get basic audit counts
  const totalAudits = await db.collection('audits').countDocuments({
    createdAt: { $gte: startDate }
  });

  const compliantCount = await db.collection('audits').countDocuments({
    createdAt: { $gte: startDate },
    overallCompliance: 'compliant'
  });

  const nonCompliantCount = await db.collection('audits').countDocuments({
    createdAt: { $gte: startDate },
    overallCompliance: { $ne: 'compliant' }
  });

  const flaggedCount = await db.collection('audits').countDocuments({
    createdAt: { $gte: startDate },
    flaggedForFollowup: true
  });

  const pendingFollowupCount = await db.collection('audits').countDocuments({
    createdAt: { $gte: startDate },
    flaggedForFollowup: true,
    followedUp: false
  });

  // Calculate compliance rate
  const complianceRate = totalAudits > 0 ? Math.round((compliantCount / totalAudits) * 100) : 0;

  // Calculate compliance change from previous period
  const previousPeriodAudits = await db.collection('audits').countDocuments({
    createdAt: { $gte: previousPeriodStart, $lt: startDate }
  });

  const previousPeriodCompliant = await db.collection('audits').countDocuments({
    createdAt: { $gte: previousPeriodStart, $lt: startDate },
    overallCompliance: 'compliant'
  });

  const previousComplianceRate = previousPeriodAudits > 0
    ? Math.round((previousPeriodCompliant / previousPeriodAudits) * 100)
    : 0;

  const complianceChange = complianceRate - previousComplianceRate;

  // Get recent audits
  const recentAudits = await db.collection('audits')
    .find({ createdAt: { $gte: startDate } })
    .sort({ createdAt: -1 })
    .limit(5)
    .toArray();

  // Calculate compliance trends over time
  const complianceTrends = await db.collection('audits').aggregate([
    {
      $match: {
        createdAt: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: {
          $dateToString: { format: '%Y-%m-%d', date: '$createdAt' }
        },
        compliantCount: {
          $sum: { $cond: [{ $eq: ['$overallCompliance', 'compliant'] }, 1, 0] }
        },
        minorNonCompliantCount: {
          $sum: { $cond: [{ $eq: ['$overallCompliance', 'minor_non_compliant'] }, 1, 0] }
        },
        majorNonCompliantCount: {
          $sum: { $cond: [{ $eq: ['$overallCompliance', 'major_non_compliant'] }, 1, 0] }
        },
        totalCount: { $sum: 1 }
      }
    },
    {
      $project: {
        _id: 0,
        date: '$_id',
        compliantRate: {
          $cond: [
            { $eq: ['$totalCount', 0] },
            0,
            { $multiply: [{ $divide: ['$compliantCount', '$totalCount'] }, 100] }
          ]
        },
        minorNonCompliantRate: {
          $cond: [
            { $eq: ['$totalCount', 0] },
            0,
            { $multiply: [{ $divide: ['$minorNonCompliantCount', '$totalCount'] }, 100] }
          ]
        },
        majorNonCompliantRate: {
          $cond: [
            { $eq: ['$totalCount', 0] },
            0,
            { $multiply: [{ $divide: ['$majorNonCompliantCount', '$totalCount'] }, 100] }
          ]
        },
        totalAudits: '$totalCount'
      }
    },
    {
      $sort: { date: 1 }
    }
  ]).toArray();

  // Calculate audit type distribution
  const auditTypeDistribution = await db.collection('audits').aggregate([
    {
      $match: {
        createdAt: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: '$auditType',
        compliant: {
          $sum: { $cond: [{ $eq: ['$overallCompliance', 'compliant'] }, 1, 0] }
        },
        minorNonCompliant: {
          $sum: { $cond: [{ $eq: ['$overallCompliance', 'minor_non_compliant'] }, 1, 0] }
        },
        majorNonCompliant: {
          $sum: { $cond: [{ $eq: ['$overallCompliance', 'major_non_compliant'] }, 1, 0] }
        },
        total: { $sum: 1 }
      }
    },
    {
      $project: {
        _id: 0,
        type: '$_id',
        compliant: 1,
        minorNonCompliant: 1,
        majorNonCompliant: 1,
        total: 1
      }
    }
  ]).toArray();

  // Return the stats
  return {
    totalAudits,
    compliantCount,
    nonCompliantCount,
    flaggedCount,
    pendingFollowupCount,
    complianceRate,
    complianceChange,
    recentAudits,
    complianceTrends,
    auditTypeDistribution
  };
};

// Helper function to get staff performance data for export
const getStaffPerformance = async (db, timeRange) => {
  // Get time range parameters
  const now = new Date();
  let startDate = new Date(now);
  startDate.setDate(startDate.getDate() - 30); // Default to last 30 days

  if (timeRange) {
    switch (timeRange) {
      case '7days':
        startDate = new Date(now);
        startDate.setDate(startDate.getDate() - 7);
        break;
      case '90days':
        startDate = new Date(now);
        startDate.setDate(startDate.getDate() - 90);
        break;
      case 'year':
        startDate = new Date(now);
        startDate.setFullYear(startDate.getFullYear() - 1);
        break;
      case 'all':
        startDate = new Date(0); // Beginning of time
        break;
    }
  }

  // Calculate previous period for comparison
  const periodLength = now.getTime() - startDate.getTime();
  const previousPeriodStart = new Date(startDate.getTime() - periodLength);

  // Get staff performance data
  const staffPerformance = await db.collection('audits').aggregate([
    {
      $match: {
        createdAt: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: '$employeeId',
        employeeName: { $first: '$employeeName' },
        totalAudits: { $sum: 1 },
        compliantCount: {
          $sum: { $cond: [{ $eq: ['$overallCompliance', 'compliant'] }, 1, 0] }
        },
        minorNonCompliantCount: {
          $sum: { $cond: [{ $eq: ['$overallCompliance', 'minor_non_compliant'] }, 1, 0] }
        },
        majorNonCompliantCount: {
          $sum: { $cond: [{ $eq: ['$overallCompliance', 'major_non_compliant'] }, 1, 0] }
        },
        flaggedCount: {
          $sum: { $cond: ['$flaggedForFollowup', 1, 0] }
        }
      }
    },
    {
      $project: {
        _id: 0,
        employeeId: '$_id',
        employeeName: 1,
        totalAudits: 1,
        compliantCount: 1,
        minorNonCompliantCount: 1,
        majorNonCompliantCount: 1,
        flaggedCount: 1,
        complianceRate: {
          $cond: [
            { $eq: ['$totalAudits', 0] },
            0,
            { $multiply: [{ $divide: ['$compliantCount', '$totalAudits'] }, 100] }
          ]
        }
      }
    }
  ]).toArray();

  // For each staff member, calculate compliance change
  for (const staff of staffPerformance) {
    const previousPeriodAudits = await db.collection('audits').countDocuments({
      employeeId: staff.employeeId,
      createdAt: { $gte: previousPeriodStart, $lt: startDate }
    });

    const previousPeriodCompliant = await db.collection('audits').countDocuments({
      employeeId: staff.employeeId,
      createdAt: { $gte: previousPeriodStart, $lt: startDate },
      overallCompliance: 'compliant'
    });

    const previousComplianceRate = previousPeriodAudits > 0
      ? Math.round((previousPeriodCompliant / previousPeriodAudits) * 100)
      : 0;

    staff.complianceChange = Math.round(staff.complianceRate) - previousComplianceRate;
  }

  return staffPerformance;
};

// Helper function to get employee performance data for export
const getEmployeePerformance = async (db, employeeId, timeRange) => {
  // Get time range parameters
  const now = new Date();
  let startDate = new Date(now);
  startDate.setDate(startDate.getDate() - 30); // Default to last 30 days

  if (timeRange) {
    switch (timeRange) {
      case '7days':
        startDate = new Date(now);
        startDate.setDate(startDate.getDate() - 7);
        break;
      case '90days':
        startDate = new Date(now);
        startDate.setDate(startDate.getDate() - 90);
        break;
      case 'year':
        startDate = new Date(now);
        startDate.setFullYear(startDate.getFullYear() - 1);
        break;
      case 'all':
        startDate = new Date(0); // Beginning of time
        break;
    }
  }

  // Get employee data
  let employee;
  try {
    // Check if employeeId is a valid ObjectId
    if (ObjectId.isValid(employeeId)) {
      employee = await User.findById(employeeId);
    }

    if (!employee) {
      throw new Error('Employee not found');
    }
  } catch (error) {
    throw new Error(`Error finding employee: ${error.message}`);
  }

  // Get audit counts
  const totalAudits = await db.collection('audits').countDocuments({
    employeeId: employeeId,
    createdAt: { $gte: startDate }
  });

  const compliantCount = await db.collection('audits').countDocuments({
    employeeId: employeeId,
    createdAt: { $gte: startDate },
    overallCompliance: 'compliant'
  });

  const minorNonCompliantCount = await db.collection('audits').countDocuments({
    employeeId: employeeId,
    createdAt: { $gte: startDate },
    overallCompliance: 'minor_non_compliant'
  });

  const majorNonCompliantCount = await db.collection('audits').countDocuments({
    employeeId: employeeId,
    createdAt: { $gte: startDate },
    overallCompliance: 'major_non_compliant'
  });

  const complianceRate = totalAudits > 0
    ? Math.round((compliantCount / totalAudits) * 100)
    : 0;

  // Get compliance history
  const complianceHistory = await db.collection('audits').aggregate([
    {
      $match: {
        employeeId: employeeId,
        createdAt: { $gte: new Date(now.getFullYear() - 1, now.getMonth(), 1) } // Last year by month
      }
    },
    {
      $group: {
        _id: {
          $dateToString: { format: '%Y-%m-%d', date: { $dateFromParts: { year: { $year: '$createdAt' }, month: { $month: '$createdAt' }, day: 1 } } }
        },
        compliantCount: {
          $sum: { $cond: [{ $eq: ['$overallCompliance', 'compliant'] }, 1, 0] }
        },
        totalCount: { $sum: 1 }
      }
    },
    {
      $project: {
        _id: 0,
        date: '$_id',
        complianceRate: {
          $cond: [
            { $eq: ['$totalCount', 0] },
            0,
            { $multiply: [{ $divide: ['$compliantCount', '$totalCount'] }, 100] }
          ]
        }
      }
    },
    {
      $sort: { date: 1 }
    }
  ]).toArray();

  // Get recent audits
  const recentAudits = await db.collection('audits')
    .find({ employeeId: employeeId })
    .sort({ createdAt: -1 })
    .limit(5)
    .toArray();

  const formattedRecentAudits = recentAudits.map(audit => ({
    id: audit._id.toString(),
    transactionId: audit.transactionId,
    date: audit.auditDate.toISOString().split('T')[0],
    compliance: audit.overallCompliance,
    score: audit.overallScore
  }));

  // Prepare employee data
  return {
    id: employeeId,
    name: employee.fullName,
    totalAudits,
    complianceRate,
    complianceHistory,
    auditBreakdown: {
      compliant: compliantCount,
      minorNonCompliant: minorNonCompliantCount,
      majorNonCompliant: majorNonCompliantCount,
    },
    recentAudits: formattedRecentAudits,
  };
};

router.post('/export', requireUser, async (req, res) => {
  try {
    // Check if user has permission (admin or manager)
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to export audit data'
      });
    }

    const { format, type, timeRange, employeeId, includeCharts = false, includeDetails = false } = req.body;

    // Set the appropriate content type based on the format
    switch (format) {
      case 'pdf':
        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader('Content-Disposition', `attachment; filename=audit-export-${new Date().toISOString().split('T')[0]}.pdf`);
        break;
      case 'excel':
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename=audit-export-${new Date().toISOString().split('T')[0]}.xlsx`);
        break;
      case 'csv':
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', `attachment; filename=audit-export-${new Date().toISOString().split('T')[0]}.csv`);
        break;
      default:
        return res.status(400).json({
          success: false,
          error: 'Invalid export format'
        });
    }

    // Generate the actual file based on the format and type
    try {
      // Get the data based on the export type
      let exportData;
      switch (type) {
        case 'audit_metrics':
          // Get audit metrics data
          exportData = await getAuditStats(db, timeRange);

          // Remove charts or details if not requested
          if (!includeCharts) {
            delete exportData.complianceTrends;
            delete exportData.auditTypeDistribution;
          }

          if (!includeDetails) {
            delete exportData.recentAudits;
          }
          break;

        case 'staff_performance':
          // Get staff performance data
          exportData = await getStaffPerformance(db, timeRange);

          // Filter data based on includeDetails
          if (!includeDetails) {
            // Only include summary data, not individual staff details
            exportData = exportData.map(staff => ({
              employeeId: staff.employeeId,
              employeeName: staff.employeeName,
              totalAudits: staff.totalAudits,
              complianceRate: staff.complianceRate,
              complianceChange: staff.complianceChange
            }));
          }
          break;

        case 'employee_performance':
          // Get employee performance data
          if (!employeeId) {
            return res.status(400).json({
              success: false,
              error: 'Employee ID is required for employee performance export'
            });
          }
          exportData = await getEmployeePerformance(db, employeeId, timeRange);

          // Remove charts or details if not requested
          if (!includeCharts) {
            delete exportData.complianceHistory;
          }

          if (!includeDetails) {
            delete exportData.recentAudits;
          }
          break;

        case 'audit_reporting':
          // Get audit reporting data
          exportData = await getAuditStats(db, timeRange);

          // Remove charts or details if not requested
          if (!includeCharts) {
            delete exportData.complianceTrends;
            delete exportData.auditTypeDistribution;
          }

          if (!includeDetails) {
            delete exportData.recentAudits;
          }
          break;

        default:
          // Get all audits
          exportData = await db.collection('audits')
            .find({})
            .sort({ createdAt: -1 })
            .limit(1000)
            .toArray();
      }

      // Generate the file based on the format
      switch (format) {
        case 'pdf':
          // In a real implementation, this would generate a PDF file using exportData
          // For now, return a mock PDF with some data
          res.send(Buffer.from(`Mock PDF data for ${type} with ${exportData ? Object.keys(exportData).length : 0} records`));
          break;
        case 'excel':
          // In a real implementation, this would generate an Excel file using exportData
          // For now, return a mock Excel file with some data
          res.send(Buffer.from(`Mock Excel data for ${type} with ${exportData ? Object.keys(exportData).length : 0} records`));
          break;
        case 'csv':
          // In a real implementation, this would generate a CSV file using exportData
          // For now, return a mock CSV file with some data
          res.send(Buffer.from(`Mock CSV data for ${type} with ${exportData ? Object.keys(exportData).length : 0} records`));
          break;
      }
    } catch (exportError) {
      console.error('Error generating export:', exportError);
      return res.status(500).json({
        success: false,
        error: exportError.message
      });
    }
  } catch (error) {
    console.error('Error exporting audit data:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/buys/audits/:id
 * @description Get an audit by ID
 * @access Private
 */
router.get('/:id', requireUser, async (req, res) => {
  try {
    const db = getDb();
    const audit = await db.collection('buypawnaudits').findOne({ _id: new ObjectId(req.params.id) });

    if (!audit) {
      return res.status(404).json({
        success: false,
        error: 'Audit not found'
      });
    }

    // Employees can only view audits for their own transactions
    if (req.user.role === 'employee' && audit.employeeId !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to view this audit'
      });
    }

    return res.status(200).json({
      success: true,
      data: audit
    });
  } catch (error) {
    console.error('Error getting audit by ID:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route PUT /api/buys/audits/:id/flag
 * @description Flag an audit for follow-up
 * @access Private (Admin, Manager)
 */
router.put('/:id/flag', requireUser, async (req, res) => {
  try {
    // Check if user has permission (admin or manager)
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to flag audits'
      });
    }

    const { flagReason } = req.body;
    if (!flagReason) {
      return res.status(400).json({
        success: false,
        error: 'Flag reason is required'
      });
    }

    const result = await buyPawnAuditService.flagAudit(req.params.id, flagReason, req.user);

    if (result.success) {
      return res.status(200).json(result);
    } else {
      return res.status(400).json(result);
    }
  } catch (error) {
    console.error('Error flagging audit:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route PUT /api/buys/audits/:id/unflag
 * @description Remove flag from an audit
 * @access Private (Admin, Manager)
 */
router.put('/:id/unflag', requireUser, async (req, res) => {
  try {
    // Check if user has permission (admin or manager)
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to unflag audits'
      });
    }

    const result = await buyPawnAuditService.unflagAudit(req.params.id, req.user);

    if (result.success) {
      return res.status(200).json(result);
    } else {
      return res.status(400).json(result);
    }
  } catch (error) {
    console.error('Error unflagging audit:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route PUT /api/buys/audits/:id/complete-followup
 * @description Mark flagged audit follow-up as complete
 * @access Private (Admin, Manager)
 */
router.put('/:id/complete-followup', requireUser, async (req, res) => {
  try {
    // Check if user has permission (admin or manager)
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to complete follow-ups'
      });
    }

    const { followupNotes } = req.body;
    if (!followupNotes) {
      return res.status(400).json({
        success: false,
        error: 'Follow-up notes are required'
      });
    }

    const result = await buyPawnAuditService.completeFollowup(req.params.id, followupNotes, req.user);

    if (result.success) {
      return res.status(200).json(result);
    } else {
      return res.status(400).json(result);
    }
  } catch (error) {
    console.error('Error completing follow-up:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route PUT /api/buys/audits/:id
 * @description Update an audit
 * @access Private (Admin, Manager)
 */
router.put('/:id', requireUser, async (req, res) => {
  try {
    // Check if user has permission (admin or manager)
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to update audits'
      });
    }

    const db = getDb();

    // Check if audit exists
    const existingAudit = await db.collection('buypawnaudits').findOne({ _id: new ObjectId(req.params.id) });

    if (!existingAudit) {
      return res.status(404).json({
        success: false,
        error: 'Audit not found'
      });
    }

    // Update audit
    const updatedAudit = {
      ...req.body,
      updatedBy: req.user._id,
      updatedByName: `${req.user.firstName} ${req.user.lastName}`,
      updatedAt: new Date(),
      createdAt: existingAudit.createdAt,
      createdBy: existingAudit.createdBy,
      createdByName: existingAudit.createdByName
    };

    await db.collection('buypawnaudits').updateOne(
      { _id: new ObjectId(req.params.id) },
      { $set: updatedAudit }
    );

    return res.status(200).json({
      success: true,
      id: req.params.id,
      message: 'Audit updated successfully'
    });
  } catch (error) {
    console.error('Error updating audit:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route DELETE /api/buys/audits/:id
 * @description Delete an audit
 * @access Private (Admin, Manager)
 */
router.delete('/:id', requireUser, async (req, res) => {
  try {
    // Check if user has permission (admin or manager)
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to delete audits'
      });
    }

    const db = getDb();

    // Check if audit exists
    const existingAudit = await db.collection('buypawnaudits').findOne({ _id: new ObjectId(req.params.id) });

    if (!existingAudit) {
      return res.status(404).json({
        success: false,
        error: 'Audit not found'
      });
    }

    // Delete audit
    await db.collection('buypawnaudits').deleteOne({ _id: new ObjectId(req.params.id) });

    return res.status(200).json({
      success: true,
      message: 'Audit deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting audit:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route PUT /api/buys/audits/:id
 * @description Update an audit
 * @access Private (Admin, Manager)
 */
router.put('/:id', requireUser, async (req, res) => {
  try {
    // Check if user has permission (admin or manager)
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to update audits'
      });
    }

    const result = await buyPawnAuditService.updateAudit(req.params.id, req.body, req.user);

    if (result.success) {
      return res.status(200).json(result);
    } else {
      return res.status(400).json(result);
    }
  } catch (error) {
    console.error('Error updating audit:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/buys/audits/:id/comments
 * @description Get comments for an audit
 * @access Private
 */
router.get('/:id/comments', requireUser, async (req, res) => {
  try {
    const { ObjectId } = require('mongodb');
    const db = getDb();

    // Find the audit
    const audit = await db.collection('buypawnaudits').findOne({
      _id: new ObjectId(req.params.id)
    });

    if (!audit) {
      return res.status(404).json({
        success: false,
        error: 'Audit not found'
      });
    }

    // Check permissions
    if (req.user.role === 'employee' && audit.employeeId !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to view comments for this audit'
      });
    }

    return res.status(200).json({
      success: true,
      data: audit.comments || []
    });
  } catch (error) {
    console.error('Error fetching audit comments:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to fetch audit comments'
    });
  }
});

/**
 * @route POST /api/buys/audits/:id/comments
 * @description Add a comment to an audit
 * @access Private
 */
router.post('/:id/comments', requireUser, async (req, res) => {
  try {
    const { ObjectId } = require('mongodb');
    const db = getDb();
    const { comment } = req.body;

    if (!comment || comment.trim().length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Comment text is required'
      });
    }

    // Find the audit
    const audit = await db.collection('buypawnaudits').findOne({
      _id: new ObjectId(req.params.id)
    });

    if (!audit) {
      return res.status(404).json({
        success: false,
        error: 'Audit not found'
      });
    }

    // Check permissions
    if (req.user.role === 'employee' && audit.employeeId !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to comment on this audit'
      });
    }

    const newComment = {
      _id: new ObjectId(),
      comment: comment.trim(),
      addedBy: {
        _id: new ObjectId(req.user._id),
        fullName: req.user.fullName,
        role: req.user.role
      },
      addedAt: new Date()
    };

    const newHistoryEntry = {
      _id: new ObjectId(),
      action: 'comment_added',
      description: `Comment added: "${comment.trim().substring(0, 50)}${comment.trim().length > 50 ? '...' : ''}"`,
      details: {
        commentId: newComment._id,
        comment: comment.trim()
      },
      performedBy: {
        _id: new ObjectId(req.user._id),
        fullName: req.user.fullName,
        role: req.user.role
      },
      performedAt: new Date()
    };

    // Add comment and history entry
    await db.collection('buypawnaudits').updateOne(
      { _id: new ObjectId(req.params.id) },
      {
        $push: {
          comments: newComment,
          history: newHistoryEntry
        },
        $set: { updatedAt: new Date() }
      }
    );

    return res.status(201).json({
      success: true,
      data: newComment
    });
  } catch (error) {
    console.error('Error adding audit comment:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to add comment'
    });
  }
});

/**
 * @route GET /api/buys/audits/:id/history
 * @description Get history for an audit
 * @access Private
 */
router.get('/:id/history', requireUser, async (req, res) => {
  try {
    const { ObjectId } = require('mongodb');
    const db = getDb();

    // Find the audit
    const audit = await db.collection('buypawnaudits').findOne({
      _id: new ObjectId(req.params.id)
    });

    if (!audit) {
      return res.status(404).json({
        success: false,
        error: 'Audit not found'
      });
    }

    // Check permissions
    if (req.user.role === 'employee' && audit.employeeId !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to view history for this audit'
      });
    }

    return res.status(200).json({
      success: true,
      data: audit.history || []
    });
  } catch (error) {
    console.error('Error fetching audit history:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to fetch audit history'
    });
  }
});

module.exports = router;
