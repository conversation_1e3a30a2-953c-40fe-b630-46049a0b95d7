import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { DollarSign, AlertTriangle } from 'lucide-react';

interface PriceAuditSectionProps {
  control: any;
  disabled?: boolean;
}

/**
 * Specialized Price Audit component for tracking overpayment incidents
 * Focuses on documentation and follow-up rather than compliance assessment
 */
export function PriceAuditSection({ control, disabled = false }: PriceAuditSectionProps) {
  
  return (
    <div className="space-y-6">

      {/* Overpayment Reason Selection */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5 text-orange-500" />
            <CardTitle>Overpayment Reason</CardTitle>
          </div>
          <CardDescription>
            Select the primary reason for the overpayment incident
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <FormField
            control={control}
            name="pricing.overpaymentReason"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>Reason for overpayment</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    className="flex flex-col space-y-3"
                    disabled={disabled}
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="paid_over_ghost_price" id="reason-ghost" />
                      <FormLabel htmlFor="reason-ghost" className="cursor-pointer">
                        Paid over Ghost recorded price
                      </FormLabel>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="paid_over_gold_calculator" id="reason-gold" />
                      <FormLabel htmlFor="reason-gold" className="cursor-pointer">
                        Paid over gold calculator price
                      </FormLabel>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="insufficient_research" id="reason-research" />
                      <FormLabel htmlFor="reason-research" className="cursor-pointer">
                        Didn't conduct enough online research
                      </FormLabel>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="other" id="reason-other" />
                      <FormLabel htmlFor="reason-other" className="cursor-pointer">
                        Other
                      </FormLabel>
                    </div>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Custom reason text area - shown when "Other" is selected */}
          <FormField
            control={control}
            name="pricing.overpaymentReason"
            render={({ field }) => (
              <div>
                {field.value === 'other' && (
                  <FormField
                    control={control}
                    name="pricing.customOverpaymentReason"
                    render={({ field: customField }) => (
                      <FormItem>
                        <FormLabel>Custom Reason</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Please describe the specific reason for overpayment..."
                            className="min-h-[80px]"
                            {...customField}
                            disabled={disabled}
                          />
                        </FormControl>
                        <FormDescription>
                          Provide details about the specific circumstances that led to overpayment
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </div>
            )}
          />
        </CardContent>
      </Card>

      {/* Pricing Details */}
      <Card>
        <CardHeader>
          <CardTitle>Pricing Details</CardTitle>
          <CardDescription>
            Record the pricing information for this overpayment incident
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FormField
              control={control}
              name="pricing.suggestedPrice"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Suggested Price</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter suggested price"
                      {...field}
                      disabled={disabled}
                    />
                  </FormControl>
                  <FormDescription>
                    Recommended market price
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={control}
              name="pricing.costPrice"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Cost Price</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter cost price"
                      {...field}
                      disabled={disabled}
                    />
                  </FormControl>
                  <FormDescription>
                    Actual cost paid
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={control}
              name="pricing.ticketPrice"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Ticket Price</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter ticket price"
                      {...field}
                      disabled={disabled}
                    />
                  </FormControl>
                  <FormDescription>
                    Intended selling price
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </CardContent>
      </Card>

      {/* Additional Notes */}
      <Card>
        <CardHeader>
          <CardTitle>Additional Notes</CardTitle>
          <CardDescription>
            Record any additional information about this overpayment incident
          </CardDescription>
        </CardHeader>
        <CardContent>
          <FormField
            control={control}
            name="pricing.notes"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Notes</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Enter any additional notes about this overpayment incident..."
                    className="min-h-[100px]"
                    {...field}
                    disabled={disabled}
                  />
                </FormControl>
                <FormDescription>
                  Include any relevant context, circumstances, or follow-up actions needed
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </CardContent>
      </Card>
    </div>
  );
}
