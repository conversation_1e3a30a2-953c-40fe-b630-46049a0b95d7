import { useState, useEffect } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { getHistoricalPrices } from '@/api/goldPricing';
import { useToast } from '@/hooks/useToast';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts';
import { Maximize, ArrowLeft, ArrowRight, BarChart2, RefreshCw } from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';

interface HistoricalPrice {
  metal: string;
  date: string;
  price: number;
}

interface PriceHistoryChartProps {
  defaultMetal?: string;
}

const COLORS = {
  Gold: '#FFD700',
  Silver: '#C0C0C0',
  Platinum: '#E5E4E2',
  Palladium: '#CBA135'
};

export function PriceHistoryChart({ defaultMetal = 'Gold' }: PriceHistoryChartProps) {
  const [historicalData, setHistoricalData] = useState<HistoricalPrice[]>([]);
  const [loading, setLoading] = useState(false);
  const [timeRange, setTimeRange] = useState<string>('1095'); // Default to 3 years
  const [isExpanded, setIsExpanded] = useState(false);
  const [visibleRange, setVisibleRange] = useState({ start: 0, end: 90 }); // Show 90 days by default
  const [activeMetal, setActiveMetal] = useState<string>(defaultMetal);
  const [compareMode, setCompareMode] = useState<boolean>(false);
  const [selectedMetals, setSelectedMetals] = useState<Record<string, boolean>>({
    Gold: true,
    Silver: true,
    Platinum: true,
    Palladium: true
  });
  const { toast } = useToast();

  const fetchHistoricalData = async () => {
    setLoading(true);
    try {
      const days = parseInt(timeRange);

      const data = await getHistoricalPrices(
        compareMode ? 'all' : activeMetal,
        days
      );

      setHistoricalData(data);

      // Reset visible range to the latest days when data changes
      if (data.length > 0) {
        const windowSize = Math.min(90, data.length);
        setVisibleRange({
          start: Math.max(0, data.length - windowSize),
          end: data.length
        });
      }
    } catch (error) {
      toast({
        title: "Error fetching historical data",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchHistoricalData();
  }, [activeMetal, timeRange, compareMode]);

  const handlePrevious = () => {
    const windowSize = visibleRange.end - visibleRange.start;
    setVisibleRange({
      start: Math.max(0, visibleRange.start - windowSize),
      end: Math.max(windowSize, visibleRange.end - windowSize)
    });
  };

  const handleNext = () => {
    const windowSize = visibleRange.end - visibleRange.start;
    setVisibleRange({
      start: Math.min(historicalData.length - windowSize, visibleRange.start + windowSize),
      end: Math.min(historicalData.length, visibleRange.end + windowSize)
    });
  };

  // Filter data by selected metals
  const getFilteredData = () => {
    if (!compareMode) {
      return historicalData.slice(visibleRange.start, visibleRange.end);
    }

    // For compare mode, filter by selected metals
    return historicalData
      .filter(item => selectedMetals[item.metal])
      .slice(visibleRange.start, visibleRange.end);
  };

  const visibleData = getFilteredData();

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  const handleMetalToggle = (metal: string) => {
    setSelectedMetals(prev => ({
      ...prev,
      [metal]: !prev[metal]
    }));
  };

  const renderChart = (height: number = 300) => (
    <div className="w-full" style={{ height: `${height}px` }}>
      {loading ? (
        <div className="flex h-full items-center justify-center">
          <RefreshCw className="h-5 w-5 animate-spin mr-2" />
          Loading chart data...
        </div>
      ) : historicalData.length === 0 ? (
        <div className="flex h-full items-center justify-center">No historical data available</div>
      ) : (
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={visibleData} margin={{ top: 5, right: 20, bottom: 5, left: 10 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              dataKey="date"
              tickFormatter={formatDate}
              stroke="#888888"
            />
            <YAxis
              stroke="#888888"
              domain={['auto', 'auto']}
              tickFormatter={(value) => `$${value.toFixed(0)}`}
            />
            <Tooltip
              formatter={(value: number, name: string) => [`$${value.toFixed(2)}`, name]}
              labelFormatter={formatDate}
              contentStyle={{ backgroundColor: '#111', border: '1px solid #333' }}
            />
            <Legend />

            {!compareMode ? (
              <Line
                type="monotone"
                dataKey="price"
                name={activeMetal}
                stroke={COLORS[activeMetal] || '#8884d8'}
                activeDot={{ r: 8 }}
                strokeWidth={2}
              />
            ) : (
              Object.keys(selectedMetals).map(metal =>
                selectedMetals[metal] && (
                  <Line
                    key={metal}
                    type="monotone"
                    dataKey={(item) => item.metal === metal ? item.price : null}
                    name={metal}
                    stroke={COLORS[metal] || '#8884d8'}
                    activeDot={{ r: 6 }}
                    strokeWidth={2}
                    connectNulls={true}
                  />
                )
              )
            )}
          </LineChart>
        </ResponsiveContainer>
      )}
    </div>
  );

  return (
    <>
      <Card className="w-full">
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle className="text-lg">Metal Price History</CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={fetchHistoricalData}
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? "animate-spin" : ""}`} />
              Refresh
            </Button>

            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-[120px]">
                <SelectValue placeholder="Time Range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="30">30 Days</SelectItem>
                <SelectItem value="90">90 Days</SelectItem>
                <SelectItem value="180">6 Months</SelectItem>
                <SelectItem value="365">1 Year</SelectItem>
                <SelectItem value="730">2 Years</SelectItem>
                <SelectItem value="1095">3 Years</SelectItem>
              </SelectContent>
            </Select>

            <Button
              variant="outline"
              size="icon"
              onClick={() => setIsExpanded(true)}
            >
              <Maximize className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs
            defaultValue={compareMode ? "compare" : "single"}
            onValueChange={(v) => setCompareMode(v === "compare")}
            className="mb-4"
          >
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="single">Single Metal</TabsTrigger>
              <TabsTrigger value="compare">Compare Metals</TabsTrigger>
            </TabsList>

            <TabsContent value="single" className="mt-2">
              <Select
                value={activeMetal}
                onValueChange={setActiveMetal}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select Metal" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Gold">Gold</SelectItem>
                  <SelectItem value="Silver">Silver</SelectItem>
                  <SelectItem value="Palladium">Palladium</SelectItem>
                  <SelectItem value="Platinum">Platinum</SelectItem>
                </SelectContent>
              </Select>
            </TabsContent>

            <TabsContent value="compare" className="mt-2">
              <div className="grid grid-cols-2 gap-2">
                {Object.keys(selectedMetals).map(metal => (
                  <div key={metal} className="flex items-center space-x-2">
                    <Checkbox
                      id={`metal-${metal}`}
                      checked={selectedMetals[metal]}
                      onCheckedChange={() => handleMetalToggle(metal)}
                    />
                    <Label
                      htmlFor={`metal-${metal}`}
                      className="flex items-center gap-2"
                    >
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: COLORS[metal] }}
                      />
                      {metal}
                    </Label>
                  </div>
                ))}
              </div>
            </TabsContent>
          </Tabs>

          {renderChart()}

          <div className="flex items-center justify-between mt-4">
            <Button
              variant="outline"
              size="sm"
              onClick={handlePrevious}
              disabled={visibleRange.start <= 0 || loading}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Previous
            </Button>
            <span className="text-sm text-muted-foreground">
              Showing {visibleRange.start + 1} - {visibleRange.end} of {historicalData.length} days
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={handleNext}
              disabled={visibleRange.end >= historicalData.length || loading}
            >
              Next
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          </div>
        </CardContent>
      </Card>

      <Dialog open={isExpanded} onOpenChange={setIsExpanded}>
        <DialogContent className="sm:max-w-[900px] sm:max-h-[800px]">
          <div className="p-4">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold">Metal Price History</h2>
              <div className="flex gap-2">
                <Select value={timeRange} onValueChange={setTimeRange}>
                  <SelectTrigger className="w-[120px]">
                    <SelectValue placeholder="Time Range" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="30">30 Days</SelectItem>
                    <SelectItem value="90">90 Days</SelectItem>
                    <SelectItem value="180">6 Months</SelectItem>
                    <SelectItem value="365">1 Year</SelectItem>
                    <SelectItem value="730">2 Years</SelectItem>
                    <SelectItem value="1095">3 Years</SelectItem>
                  </SelectContent>
                </Select>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={fetchHistoricalData}
                  disabled={loading}
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${loading ? "animate-spin" : ""}`} />
                  Refresh
                </Button>
              </div>
            </div>

            <Tabs
              defaultValue={compareMode ? "compare" : "single"}
              onValueChange={(v) => setCompareMode(v === "compare")}
              className="mb-4"
            >
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="single">Single Metal</TabsTrigger>
                <TabsTrigger value="compare">Compare Metals</TabsTrigger>
              </TabsList>

              <TabsContent value="single" className="mt-2">
                <Select
                  value={activeMetal}
                  onValueChange={setActiveMetal}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select Metal" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Gold">Gold</SelectItem>
                    <SelectItem value="Silver">Silver</SelectItem>
                    <SelectItem value="Palladium">Palladium</SelectItem>
                    <SelectItem value="Platinum">Platinum</SelectItem>
                  </SelectContent>
                </Select>
              </TabsContent>

              <TabsContent value="compare" className="mt-2">
                <div className="grid grid-cols-4 gap-2">
                  {Object.keys(selectedMetals).map(metal => (
                    <div key={metal} className="flex items-center space-x-2">
                      <Checkbox
                        id={`expanded-metal-${metal}`}
                        checked={selectedMetals[metal]}
                        onCheckedChange={() => handleMetalToggle(metal)}
                      />
                      <Label
                        htmlFor={`expanded-metal-${metal}`}
                        className="flex items-center gap-2"
                      >
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: COLORS[metal] }}
                        />
                        {metal}
                      </Label>
                    </div>
                  ))}
                </div>
              </TabsContent>
            </Tabs>

            {renderChart(500)}

            <div className="flex items-center justify-between mt-4">
              <Button
                variant="outline"
                onClick={handlePrevious}
                disabled={visibleRange.start <= 0 || loading}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Previous
              </Button>
              <span className="text-sm text-muted-foreground">
                Showing {visibleRange.start + 1} - {visibleRange.end} of {historicalData.length} days
              </span>
              <Button
                variant="outline"
                onClick={handleNext}
                disabled={visibleRange.end >= historicalData.length || loading}
              >
                Next
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}