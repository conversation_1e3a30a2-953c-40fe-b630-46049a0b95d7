const mongoose = require('mongoose');

const bannedItemSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true
  },
  category: {
    type: String,
    enum: ['banned', 'restricted'],
    required: true
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now,
    immutable: true
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  versionKey: false
});

// Add indexes for efficient queries
bannedItemSchema.index({ category: 1 });
bannedItemSchema.index({ createdAt: -1 });
bannedItemSchema.index({ title: 'text' });

const BannedItem = mongoose.model('BannedItem', bannedItemSchema);

module.exports = BannedItem;
