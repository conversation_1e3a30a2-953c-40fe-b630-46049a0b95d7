import api from './api';

export interface MetalPrice {
  _id?: string;
  metal: string;
  purity: string;
  spotPrice: number;
  minBuyPrice: number;
  maxBuyPrice: number;
  meltPrice: number;
  lastUpdated: string;
  nextUpdateAt: string;
}

export interface MetalPriceSettings {
  minBuyPercentage: number;
  maxBuyPercentage: number;
  meltPercentage: number;
  mhjMinPercentage: number;
  mhjMaxPercentage: number;
  updateFrequencyMinutes: number;
  apiKey?: string;
  lastUpdated: string;
}

// Description: Get current metal prices
// Endpoint: GET /api/metal-prices
// Request: { metal?: string } (optional filter)
// Response: { success: boolean, data: { prices: MetalPrice[], nextUpdateAt: string } }
export const getMetalPrices = async (metal?: string) => {
  try {
    const params = metal ? { metal } : {};
    const response = await api.get('/metal-prices', { params });
    return response.data.data;
  } catch (error) {
    console.error(error);
    throw new Error(error?.response?.data?.message || error.message);
  }
};

// Description: Update a specific metal price
// Endpoint: PUT /api/metal-prices/:id
// Request: { spotPrice?: number, minBuyPrice?: number, maxBuyPrice?: number, meltPrice?: number, nextUpdateAt?: string }
// Response: { success: boolean, data: MetalPrice }
export const updateMetalPrice = async (id: string, data: Partial<Omit<MetalPrice, '_id' | 'metal' | 'purity' | 'lastUpdated'>>) => {
  try {
    const response = await api.put(`/metal-prices/${id}`, data);
    return response.data.data;
  } catch (error) {
    console.error(error);
    throw new Error(error?.response?.data?.message || error.message);
  }
};

// Description: Get metal price settings
// Endpoint: GET /api/metal-prices/settings
// Request: {}
// Response: { success: boolean, data: MetalPriceSettings }
export const getMetalPriceSettings = async () => {
  try {
    const response = await api.get('/metal-prices/settings');
    return response.data.data;
  } catch (error) {
    console.error(error);
    throw new Error(error?.response?.data?.message || error.message);
  }
};

// Description: Update metal price settings
// Endpoint: PUT /api/metal-prices/settings
// Request: Partial<MetalPriceSettings>
// Response: { success: boolean, data: MetalPriceSettings }
export const updateMetalPriceSettings = async (settings: Partial<MetalPriceSettings>) => {
  try {
    const response = await api.put('/metal-prices/settings', settings);
    return response.data.data;
  } catch (error) {
    console.error(error);
    throw new Error(error?.response?.data?.message || error.message);
  }
};

// Description: Get historical metal price data
// Endpoint: GET /api/metal-prices/history/:metal
// Request: { days?: number, compare?: boolean }
// Response: { success: boolean, data: Array<{ metal: string, date: string, price: number }>, warning?: string }
export const getHistoricalPrices = async (metal: string = 'Gold', days: number = 365, compare: boolean = false) => {
  try {
    // Limit days to 365 to match API limitations
    const validDays = Math.min(days, 365);

    const params: any = { days: validDays };
    if (compare) {
      params.compare = true;
    }

    const response = await api.get(`/metal-prices/history/${metal}`, { params });

    // Include any warnings in the response
    const result = response.data.data;
    if (response.data.warning) {
      result.warning = response.data.warning;
    }

    return result;
  } catch (error: any) {
    console.error('Error fetching historical price data:', error);
    throw new Error(error?.response?.data?.message || error.message);
  }
};

// Description: Get API usage information
// Endpoint: GET /api/metal-prices/api-usage
// Request: {}
// Response: { success: boolean, data: { plan: string, used: number, total: number, remaining: number } }
export interface ApiUsage {
  plan: string;
  used: number;
  total: number;
  remaining: number;
}

export const getApiUsage = async () => {
  try {
    const response = await api.get('/metal-prices/api-usage');
    return response.data.data as ApiUsage;
  } catch (error: any) {
    console.error('Error fetching API usage:', error);
    throw new Error(error?.response?.data?.message || error.message);
  }
};