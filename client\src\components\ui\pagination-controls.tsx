import React from 'react';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface PaginationControlsProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  onLimitChange?: (limit: number) => void;
  limit?: number;
  totalItems?: number;
}

export function PaginationControls({
  currentPage,
  totalPages,
  onPageChange,
  onLimitChange,
  limit = 10,
  totalItems
}: PaginationControlsProps) {
  const pageNumbers = Array.from({ length: totalPages }, (_, i) => i + 1);

  // Handle limit change
  const handleLimitChange = (value: string) => {
    if (onLimitChange) {
      const numericValue = Number(value);
      console.log(`PaginationControls: Changing limit to ${value} (${numericValue})`);
      onLimitChange(numericValue);
    }
  };

  // Generate displayed page numbers with ellipsis for large page counts
  const getPageNumbers = () => {
    if (totalPages <= 5) {
      return pageNumbers;
    }

    const result = [];

    // Always show first page
    result.push(1);

    // Calculate range around current page
    const startPage = Math.max(2, currentPage - 1);
    const endPage = Math.min(totalPages - 1, currentPage + 1);

    // Add ellipsis after first page if needed
    if (startPage > 2) {
      result.push('ellipsis1');
    }

    // Add pages around current page
    for (let i = startPage; i <= endPage; i++) {
      result.push(i);
    }

    // Add ellipsis before last page if needed
    if (endPage < totalPages - 1) {
      result.push('ellipsis2');
    }

    // Always show last page
    if (totalPages > 1) {
      result.push(totalPages);
    }

    return result;
  };

  return (
    <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mt-4">
      <div className="flex items-center gap-1">
        <span className="text-sm text-muted-foreground">
          {totalItems !== undefined && (
            <>
              Showing {(currentPage - 1) * limit + 1}-
              {Math.min(currentPage * limit, totalItems)} of {totalItems} items
            </>
          )}
        </span>
      </div>

      <div className="flex items-center gap-2">
        <div className="flex items-center mr-4">
          <span className="text-sm text-muted-foreground mr-2">Items per page</span>
          <Select
            value={String(limit)}
            onValueChange={handleLimitChange}
          >
            <SelectTrigger className="w-[70px]">
              <SelectValue placeholder={limit} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="5">5</SelectItem>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="20">20</SelectItem>
              <SelectItem value="50">50</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Button
          variant="outline"
          size="icon"
          onClick={() => onPageChange(1)}
          disabled={currentPage === 1}
        >
          <ChevronsLeft className="h-4 w-4" />
        </Button>

        <Button
          variant="outline"
          size="icon"
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 1}
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>

        <div className="flex gap-1">
          {getPageNumbers().map((page, idx) => (
            page === 'ellipsis1' || page === 'ellipsis2' ? (
              <div key={`ellipsis-${idx}`} className="px-2 flex items-center">...</div>
            ) : (
              <Button
                key={`page-${page}`}
                variant={currentPage === page ? "default" : "outline"}
                size="icon"
                onClick={() => onPageChange(Number(page))}
                className="w-8 h-8"
              >
                {page}
              </Button>
            )
          ))}
        </div>

        <Button
          variant="outline"
          size="icon"
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
        >
          <ChevronRight className="h-4 w-4" />
        </Button>

        <Button
          variant="outline"
          size="icon"
          onClick={() => onPageChange(totalPages)}
          disabled={currentPage === totalPages}
        >
          <ChevronsRight className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}