const express = require('express');
const router = express.Router();
const locationService = require('../services/locationService');
const { requireUser } = require('./middleware/auth');

/**
 * @route GET /api/locations
 * @description Get all locations
 * @access Private
 */
router.get('/', requireUser, async (req, res) => {
  try {
    const locations = await locationService.getAllLocations();
    res.status(200).json({ locations });
  } catch (error) {
    console.error('Error getting locations:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * @route POST /api/locations
 * @description Create a new location
 * @access Private (Admin and Manager only)
 */
router.post('/', requireUser, async (req, res) => {
  try {
    // Check if user has permission to create locations
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      console.log(`Unauthorized location creation attempt by user ${req.user._id}`);
      return res.status(403).json({ error: 'Only admins and managers can create locations' });
    }

    const { name, address, phone } = req.body;

    if (!name) {
      console.log('Location creation failed: missing name');
      return res.status(400).json({ error: 'Location name is required' });
    }

    console.log(`Creating new location: ${name}`);
    const location = await locationService.createLocation({ name, address, phone });
    console.log(`Location created with ID: ${location._id}`);
    res.status(201).json(location);
  } catch (error) {
    console.error('Error creating location:', error);

    // Handle duplicate key error
    if (error.code === 11000) {
      return res.status(400).json({ error: 'A location with that name already exists' });
    }

    res.status(500).json({ error: error.message });
  }
});

/**
 * @route DELETE /api/locations/:id
 * @description Delete a location
 * @access Private (Admin only)
 */
router.delete('/:id', requireUser, async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      console.log(`Unauthorized location deletion attempt by user ${req.user._id}`);
      return res.status(403).json({ error: 'Only admins can delete locations' });
    }

    console.log(`Attempting to delete location with ID: ${req.params.id}`);
    const result = await locationService.deleteLocation(req.params.id);
    console.log(`Location ${req.params.id} deleted successfully`);
    res.status(200).json(result);
  } catch (error) {
    console.error('Error deleting location:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * @route POST /api/locations/seed
 * @description Seed initial locations
 * @access Private (Admin only)
 */
router.post('/seed', requireUser, async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      console.log(`Unauthorized location seeding attempt by user ${req.user._id}`);
      return res.status(403).json({ error: 'Only admins can seed locations' });
    }

    console.log('Seeding locations database...');
    const result = await locationService.seedLocations();

    if (result.success) {
      console.log(`Locations seeded successfully: ${result.message}`);
      res.status(200).json(result);
    } else {
      console.error(`Failed to seed locations: ${result.message}`);
      res.status(500).json({ error: result.message });
    }
  } catch (error) {
    console.error('Error seeding locations:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * @route PATCH /api/locations/:id/toggle-status
 * @description Toggle a location's active status
 * @access Private (Admin only)
 */
router.patch('/:id/toggle-status', requireUser, async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      console.log(`Unauthorized location status update attempt by user ${req.user._id}`);
      return res.status(403).json({ error: 'Only admins can update location status' });
    }

    const { id } = req.params;
    const { isActive } = req.body;

    if (isActive === undefined) {
      return res.status(400).json({ error: 'isActive status is required' });
    }

    console.log(`Updating location ${id} active status to: ${isActive}`);
    const location = await locationService.toggleLocationStatus(id, isActive);

    res.status(200).json(location);
  } catch (error) {
    console.error('Error updating location status:', error);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;