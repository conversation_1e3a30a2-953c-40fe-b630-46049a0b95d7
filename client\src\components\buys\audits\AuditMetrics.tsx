import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/useToast';
import { getAuditStats } from '@/api/buyPawnAudits';
import { AuditNavigation } from './AuditNavigation';
import { Loader2, Download } from 'lucide-react';
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';

/**
 * Component for detailed audit metrics and visualizations
 */
export function AuditMetrics() {
  const [stats, setStats] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isExporting, setIsExporting] = useState(false);
  const [timeRange, setTimeRange] = useState('30days');
  const { toast } = useToast();

  // Fetch audit statistics
  useEffect(() => {
    const fetchStats = async () => {
      try {
        setIsLoading(true);
        // Pass the timeRange to the API
        const result = await getAuditStats({ timeRange });

        if (result.success) {
          setStats(result.data);
        } else {
          toast({
            title: 'Error',
            description: result.error || 'Failed to load audit metrics.',
            variant: 'destructive',
          });
        }
      } catch (error: any) {
        toast({
          title: 'Error',
          description: error.message || 'An unexpected error occurred.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchStats();
  }, [timeRange, toast]);

  // Handle export to PDF
  const handleExportPDF = async () => {
    try {
      setIsExporting(true);
      toast({
        title: 'Export Started',
        description: 'Generating PDF report...',
      });

      // Call the API to generate and download the PDF
      const response = await fetch('/api/buys/audits/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          format: 'pdf',
          type: 'audit_metrics',
          timeRange,
          includeCharts: true,
          includeDetails: true,
        }),
      });

      setIsExporting(false);

      if (response.ok) {
        toast({
          title: 'Export Complete',
          description: 'PDF report has been downloaded.',
        });
      } else {
        const errorData = await response.json();
        toast({
          title: 'Export Failed',
          description: errorData.error || 'Failed to generate PDF report.',
          variant: 'destructive',
        });
      }
    } catch (error: any) {
      setIsExporting(false);
      toast({
        title: 'Export Failed',
        description: error.message || 'An unexpected error occurred.',
        variant: 'destructive',
      });
    }
  };

  if (isLoading && !stats) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  // Colors for pie charts
  const COLORS = ['#22c55e', '#eab308', '#ef4444', '#3b82f6'];

  // Prepare audit type data from stats
  const auditTypeData = stats?.auditTypeDistribution ?
    stats.auditTypeDistribution.map((item: any) => ({
      name: item.type === 'buy' ? 'Buy Deal' : item.type === 'pawn' ? 'Pawn Loan' : 'Price',
      value: item.total
    })) : [];

  // Prepare compliance status data from stats
  const complianceStatusData = stats ? [
    { name: 'Compliant', value: stats.compliantCount },
    { name: 'Minor Non-Compliant', value: stats.minorNonCompliantCount || 0 },
    { name: 'Major Non-Compliant', value: stats.majorNonCompliantCount || 0 },
  ] : [];

  // Prepare non-compliance reasons data from stats
  // This would typically come from the API, but for now we'll use placeholder data
  // In a real implementation, this would be calculated from the database
  const nonComplianceReasonsData = [
    { name: 'Missing Documentation', value: stats?.nonComplianceReasons?.missingDocumentation || 0 },
    { name: 'Incorrect Pricing', value: stats?.nonComplianceReasons?.incorrectPricing || 0 },
    { name: 'Incomplete Assessment', value: stats?.nonComplianceReasons?.incompleteAssessment || 0 },
    { name: 'Policy Violation', value: stats?.nonComplianceReasons?.policyViolation || 0 },
    { name: 'Other', value: stats?.nonComplianceReasons?.other || 0 },
  ];

  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col gap-4 mb-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold">Audit Metrics</h1>
            <p className="text-muted-foreground">Detailed metrics and visualizations for audits</p>
          </div>
          <Button
            onClick={handleExportPDF}
            disabled={isExporting}
          >
            {isExporting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Exporting...
              </>
            ) : (
              <>
                <Download className="mr-2 h-4 w-4" />
                Export Report
              </>
            )}
          </Button>
        </div>

        <AuditNavigation />
      </div>

      <div className="flex justify-end mb-4">
        <Select value={timeRange} onValueChange={setTimeRange}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select time range" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7days">Last 7 days</SelectItem>
            <SelectItem value="30days">Last 30 days</SelectItem>
            <SelectItem value="90days">Last 90 days</SelectItem>
            <SelectItem value="year">Last year</SelectItem>
            <SelectItem value="all">All time</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Audit Type Distribution</CardTitle>
              <CardDescription>
                Breakdown of audits by type
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={auditTypeData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {auditTypeData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Compliance Status Distribution</CardTitle>
              <CardDescription>
                Breakdown of audits by compliance status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={complianceStatusData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {complianceStatusData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Common Non-Compliance Reasons</CardTitle>
            <CardDescription>
              Most frequent reasons for non-compliance
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[400px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={nonComplianceReasonsData}
                  layout="vertical"
                  margin={{
                    top: 5,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" opacity={0.1} horizontal={false} />
                  <XAxis
                    type="number"
                    tick={{ fill: 'hsl(var(--muted-foreground))' }}
                  />
                  <YAxis
                    dataKey="name"
                    type="category"
                    tick={{ fill: 'hsl(var(--muted-foreground))' }}
                    width={150}
                  />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="value" name="Count" fill="hsl(var(--primary))" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Audit Volume Over Time</CardTitle>
            <CardDescription>
              Number of audits conducted over time
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[400px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={stats?.auditVolume || []}
                  margin={{
                    top: 5,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" opacity={0.1} />
                  <XAxis
                    dataKey="date"
                    tick={{ fill: 'hsl(var(--muted-foreground))' }}
                  />
                  <YAxis
                    tick={{ fill: 'hsl(var(--muted-foreground))' }}
                    label={{
                      value: 'Number of Audits',
                      angle: -90,
                      position: 'insideLeft',
                      style: { fill: 'hsl(var(--muted-foreground))' }
                    }}
                  />
                  <Tooltip />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="buyCount"
                    name="Buy Deals"
                    stroke="#3b82f6"
                    strokeWidth={2}
                  />
                  <Line
                    type="monotone"
                    dataKey="pawnCount"
                    name="Pawn Loans"
                    stroke="#22c55e"
                    strokeWidth={2}
                  />
                  <Line
                    type="monotone"
                    dataKey="priceCount"
                    name="Price Audits"
                    stroke="#eab308"
                    strokeWidth={2}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
