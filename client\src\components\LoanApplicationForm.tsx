import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { parseISO, format } from 'date-fns';
import { Loader2, CalendarIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAuth } from '@/contexts/AuthContext';
import { LoanApplicationFormData, getActiveUsersForLoanApplications } from '@/api/loanApplications';
import { User } from '@/api/user';

interface LoanApplicationFormProps {
  onSubmit: (data: LoanApplicationFormData) => void;
  initialData?: LoanApplicationFormData;
}

export function LoanApplicationForm({ onSubmit, initialData }: LoanApplicationFormProps) {
  const { user } = useAuth();
  const isAdminOrManager = user?.role === 'admin' || user?.role === 'manager';

  const [formData, setFormData] = useState<LoanApplicationFormData>({
    loanId: initialData?.loanId || '',
    loanAmount: initialData?.loanAmount || 0,
    customerId: initialData?.customerId || '',
    customerName: initialData?.customerName || '',
    status: initialData?.status || 'awaitingDecision',
    submittedDate: initialData?.submittedDate || new Date().toISOString(),
    submittedBy: initialData?.submittedBy || user?._id || '',
    notes: initialData?.notes || '',
  });

  const [submittedDate, setSubmittedDate] = useState<Date | undefined>(
    initialData?.submittedDate ? parseISO(initialData.submittedDate) : new Date()
  );

  const [users, setUsers] = useState<User[]>([]);
  const [loadingUsers, setLoadingUsers] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  // Load users for the submitter dropdown
  useEffect(() => {
    fetchUsers();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Fetch all active users
  const fetchUsers = async () => {
    setLoadingUsers(true);
    try {
      // Use the new API function that works for all user roles
      const activeUsers = await getActiveUsersForLoanApplications();
      // Sort alphabetically by fullName
      const sortedUsers = [...activeUsers].sort((a, b) => a.fullName.localeCompare(b.fullName));

      setUsers(sortedUsers);

      // If no submittedBy is set or it's empty, default to current user
      if ((!formData.submittedBy || formData.submittedBy === '') && user?._id) {
        setFormData(prev => ({ ...prev, submittedBy: user._id }));
      }
    } catch (error) {
      console.error('Error fetching users:', error);
    } finally {
      setLoadingUsers(false);
    }
  };

  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle date change
  const handleDateChange = (date: Date | undefined) => {
    console.log('Date selected:', date); // Debug log
    if (date) {
      setSubmittedDate(date);
      setFormData(prev => ({ ...prev, submittedDate: date.toISOString() }));
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);

    try {
      await onSubmit(formData);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Loan ID */}
        <div className="space-y-2">
          <Label htmlFor="loanId">Loan ID</Label>
          <Input
            id="loanId"
            name="loanId"
            value={formData.loanId}
            onChange={handleChange}
            required
          />
        </div>

        {/* Loan Amount */}
        <div className="space-y-2">
          <Label htmlFor="loanAmount">Loan Amount ($)</Label>
          <Input
            id="loanAmount"
            name="loanAmount"
            type="number"
            min="0"
            step="0.01"
            value={formData.loanAmount}
            onChange={handleChange}
            required
          />
        </div>

        {/* Customer ID */}
        <div className="space-y-2">
          <Label htmlFor="customerId">Customer ID</Label>
          <Input
            id="customerId"
            name="customerId"
            value={formData.customerId}
            onChange={handleChange}
            required
          />
        </div>

        {/* Customer Name */}
        <div className="space-y-2">
          <Label htmlFor="customerName">Customer Name</Label>
          <Input
            id="customerName"
            name="customerName"
            value={formData.customerName}
            onChange={handleChange}
            required
          />
        </div>

        {/* Status - Only shown when editing, not for new applications */}
        {initialData ? (
          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Select
              value={formData.status}
              onValueChange={(value) => handleSelectChange('status', value)}
              disabled={!isAdminOrManager && (formData.status === 'approvedPaid' || formData.status === 'declined' || formData.status === 'cancelled')}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="awaitingDecision">Awaiting Decision</SelectItem>
                <SelectItem value="declined">Declined</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
                {isAdminOrManager && (
                  <>
                    <SelectItem value="approvedPaid">Approved & Paid</SelectItem>
                  </>
                )}
              </SelectContent>
            </Select>

          </div>
        ) : (
          <input type="hidden" name="status" value="awaitingDecision" />
        )}

        {/* Submitted Date */}
        <div className="space-y-2">
          <Label htmlFor="submittedDate">Submitted Date</Label>
          <Popover modal={true}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !submittedDate && "text-muted-foreground"
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {submittedDate ? format(submittedDate, "PPP") : "Select date"}
              </Button>
            </PopoverTrigger>
            <PopoverContent
              className="w-auto p-0 z-[9999]"
              align="start"
              onOpenAutoFocus={(e) => e.preventDefault()}
            >
              <Calendar
                mode="single"
                selected={submittedDate}
                onSelect={handleDateChange}
                initialFocus
                disabled={false}
                className="pointer-events-auto"
              />
            </PopoverContent>
          </Popover>
        </div>

        {/* Submitted By */}
        <div className="space-y-2">
          <Label htmlFor="submittedBy">Submitted By</Label>
          {loadingUsers ? (
            <div className="flex items-center space-x-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Loading users...</span>
            </div>
          ) : (
            <Select
              value={formData.submittedBy}
              onValueChange={(value) => handleSelectChange('submittedBy', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select user" />
              </SelectTrigger>
              <SelectContent>
                {users.map((user) => (
                  <SelectItem key={user._id} value={user._id}>
                    {user.fullName}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        </div>

        {/* Notes */}
        <div className="space-y-2 md:col-span-2">
          <Label htmlFor="notes">Notes</Label>
          <Textarea
            id="notes"
            name="notes"
            value={formData.notes}
            onChange={handleChange}
            rows={3}
          />
        </div>
      </div>

      {/* Submit Button */}
      <div className="flex justify-end">
        <Button type="submit" disabled={submitting}>
          {submitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {initialData ? 'Update Application' : 'Add Application'}
        </Button>
      </div>
    </form>
  );
}
