const express = require('express');
const router = express.Router();

// Root path response
router.get("/", (req, res) => {
  res.status(200).send("Hello World");
});

router.get("/ping", (req, res) => {
  res.status(200).send("pong");
});

// CSRF token endpoint - this will be used by the client to get a CSRF token
router.get("/api/csrf-token", (req, res) => {
  // Only return a token if the csrfToken function exists (it won't for excluded routes)
  if (typeof req.csrfToken === 'function') {
    try {
      const token = req.csrfToken();

      // Set the token as a cookie as well
      res.cookie('XSRF-TOKEN', token, {
        httpOnly: false, // Client-side JavaScript needs to read this
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax', // Changed from 'strict' to 'lax' to allow redirects
        path: '/' // Ensure the cookie is available across the entire site
      });

      res.json({ success: true, csrfToken: token });
    } catch (error) {
      console.error('Error generating CSRF token:', error);
      res.status(500).json({ success: false, error: 'Failed to generate CSRF token' });
    }
  } else {
    // For routes that don't have CSRF protection
    res.json({ success: true, csrfToken: null, message: 'CSRF protection not required for this route' });
  }
});

module.exports = router;
