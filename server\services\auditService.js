const AuditLog = require('../models/AuditLog');

class AuditService {
  /**
   * Create a new audit log entry
   * @param {string} itemId - The ID of the item
   * @param {string} userId - The ID of the user performing the action
   * @param {string} action - The action performed (create, update, delete)
   * @param {Object} changes - The fields that were changed
   * @param {Object} previousValues - The previous values of the changed fields
   * @param {Object} newValues - The new values of the changed fields
   * @returns {Promise<Object>} - The created audit log
   */
  async createLog(itemId, userId, action, changes = {}, previousValues = {}, newValues = {}) {
    try {
      console.log(`Creating audit log for item ${itemId}, action: ${action}`);

      const log = new AuditLog({
        itemId,
        userId,
        action,
        changes,
        previousValues,
        newValues,
        timestamp: new Date(),
      });

      await log.save();
      console.log(`Created audit log: ${log._id}`);

      return {
        success: true,
        log,
      };
    } catch (error) {
      console.error('Error creating audit log:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Get audit logs for a specific item
   * @param {string} itemId - The ID of the item
   * @returns {Promise<Object>} - The audit logs for the item
   */
  async getLogsByItem(itemId) {
    try {

      const logs = await AuditLog.find({ itemId })
        .sort({ timestamp: -1 })
        .populate('userId', 'email username name')
        .lean();

      console.log(`Found ${logs.length} audit logs for item ${itemId}`);

      return {
        success: true,
        logs,
      };
    } catch (error) {
      console.error('Error fetching audit logs:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }
}

module.exports = new AuditService();