import * as React from 'react';
import { Outlet } from 'react-router-dom';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { getConnectionStatus } from '@/api/tradeMeAccount';
import { useToast } from '@/hooks/useToast';
import { Card, CardContent } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  AlertCircle,
  ShoppingBag,
  Archive,
  Clock,
  FileEdit,
  MessageSquare,
  Star,
  FileText,
  RefreshCw,
  ShieldAlert,
  Plus,
  ChevronDown
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle
} from '@/components/ui/navigation-menu';
import { cn } from '@/lib/utils';

const ListItem = React.forwardRef<
  React.ElementRef<"a">,
  React.ComponentPropsWithoutRef<"a"> & {
    title: string;
    icon?: React.ReactNode;
    isActive?: boolean;
    disabled?: boolean;
  }
>(({ className, title, children, icon, isActive, disabled, ...props }, ref) => {
  return (
    <li>
      <NavigationMenuLink asChild>
        <a
          ref={ref}
          className={cn(
            "block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-all duration-300 cursor-pointer",
            isActive
              ? "bg-accent text-accent-foreground"
              : "hover:bg-accent hover:text-accent-foreground",
            disabled && "pointer-events-none opacity-50",
            className
          )}
          {...props}
        >
          <div className="flex items-center gap-2 text-sm font-medium leading-none">
            {icon}
            <span>{title}</span>
          </div>
          <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
            {children}
          </p>
        </a>
      </NavigationMenuLink>
    </li>
  )
})
ListItem.displayName = "ListItem"

export function TradeMeLayout() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();
  const [accountStatus, setAccountStatus] = useState({
    connected: false,
    environment: 'sandbox'
  });
  const [loading, setLoading] = useState(true);
  const [scrolled, setScrolled] = useState(false);

  // Add scroll event listener
  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 10;
      if (isScrolled !== scrolled) {
        setScrolled(isScrolled);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [scrolled]);

  useEffect(() => {
    fetchConnectionStatus();
  }, []);

  const fetchConnectionStatus = async () => {
    try {
      setLoading(true);
      const response = await getConnectionStatus();
      if (response.success) {
        setAccountStatus(response.status);
      }
    } catch (error: any) {
      if (error?.response?.status !== 403) {
        console.error('Failed to fetch TradeMe connection status:', error);
        toast({ title: 'Error', description: error.message, variant: 'destructive' });
      }
    } finally {
      setLoading(false);
    }
  };

  const showConnectionWarning =
    !loading &&
    (user?.role === 'admin' || user?.role === 'manager') &&
    accountStatus.connected === false;

  const showSandboxNotice =
    !loading &&
    accountStatus.connected === true &&
    accountStatus.environment === 'sandbox';

  const getCurrentTab = () => {
    // Check if we're on a listing detail page
    if (location.pathname.includes('/trademe/listing/')) return '';
    if (location.pathname.includes('/trademe/sold')) return 'sold';
    if (location.pathname.includes('/trademe/unsold')) return 'unsold';
    if (location.pathname.includes('/trademe/archived')) return 'archived';
    if (location.pathname.includes('/trademe/draft')) return 'draft';
    if (location.pathname.includes('/trademe/questions')) return 'questions';
    if (location.pathname.includes('/trademe/sync')) return 'sync';
    if (location.pathname.includes('/trademe/feedback')) return 'feedback';
    if (location.pathname.includes('/trademe/templates')) return 'templates';
    // Only return 'selling' if we're on the selling page
    if (location.pathname === '/trademe' || location.pathname === '/trademe/selling') {
      return 'selling';
    }

    return '';
  };

  const handleTabChange = (value: string) => {
    navigate(`/trademe/${value}`);
  };

  // If no account is connected and user is not admin/manager, show a message
  if (!loading && !accountStatus.connected && user?.role !== 'admin' && user?.role !== 'manager') {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <AlertCircle className="h-12 w-12 text-destructive mb-4" />
              <h2 className="text-2xl font-bold mb-2">TradeMe Account Not Connected</h2>
              <p className="text-muted-foreground mb-6 max-w-md">
                A Trademe account needs to be connected before you can use this feature. Please contact your manager or administrator to set up the connection.
              </p>
              <Button variant="outline" onClick={() => navigate('/')}>
                Return to Dashboard
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {showConnectionWarning && (
        <Alert className="mb-4" variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>TradeMe Account Not Connected</AlertTitle>
          <AlertDescription>
            Please go to Settings &gt; TradeMe tab to connect your account.
          </AlertDescription>
        </Alert>
      )}
      {showSandboxNotice && (
        <Alert className="mb-4 bg-yellow-50 border-yellow-200 text-yellow-800 dark:bg-yellow-950 dark:border-yellow-800 dark:text-yellow-200">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>TradeMe Sandbox Mode</AlertTitle>
          <AlertDescription>
            You are connected to the TradeMe Sandbox environment. This is a test environment and no real transactions will occur.
          </AlertDescription>
        </Alert>
      )}
      <div className="sticky top-0 z-50 pt-6 bg-background/50 backdrop-blur-sm">
        <div className={cn(
          "bg-background border rounded-lg mb-6 transition-all duration-300 ease-in-out",
          scrolled
            ? "shadow-lg border-primary/20 bg-background/90 backdrop-blur-md border-b-2"
            : "shadow-md hover:shadow-lg",
          scrolled && "scale-[0.98]"
        )}>
        <div className={cn(
          "p-3 transition-all duration-300",
          scrolled && "p-2"
        )}>
          <NavigationMenu className="mx-auto max-w-none w-full justify-start">
            <NavigationMenuList className="flex flex-wrap gap-1 animate-in fade-in duration-500">
              {/* New Listing Button */}
              <NavigationMenuItem>
                <Button
                  variant="default"
                  size="sm"
                  className="mr-2 transition-all duration-300 cursor-pointer"
                  onClick={() => navigate('/trademe/listing/new')}
                >
                  <Plus className="mr-2 h-4 w-4" />
                  New Listing
                </Button>
              </NavigationMenuItem>

              {/* Direct Listing Links */}
              <NavigationMenuItem>
                <NavigationMenuLink
                  className={cn(
                    navigationMenuTriggerStyle(),
                    "bg-background hover:bg-accent cursor-pointer transition-all duration-300",
                    getCurrentTab() === 'selling' && "bg-accent text-accent-foreground"
                  )}
                  onClick={() => handleTabChange('selling')}
                >
                  <ShoppingBag className="mr-2 h-4 w-4" />
                  Selling
                </NavigationMenuLink>
              </NavigationMenuItem>

              <NavigationMenuItem>
                <NavigationMenuLink
                  className={cn(
                    navigationMenuTriggerStyle(),
                    "bg-background hover:bg-accent cursor-pointer transition-all duration-300",
                    getCurrentTab() === 'sold' && "bg-accent text-accent-foreground"
                  )}
                  onClick={() => handleTabChange('sold')}
                >
                  <Archive className="mr-2 h-4 w-4 text-green-500" />
                  Sold
                </NavigationMenuLink>
              </NavigationMenuItem>

              <NavigationMenuItem>
                <NavigationMenuLink
                  className={cn(
                    navigationMenuTriggerStyle(),
                    "bg-background hover:bg-accent cursor-pointer transition-all duration-300",
                    getCurrentTab() === 'draft' && "bg-accent text-accent-foreground"
                  )}
                  onClick={() => handleTabChange('draft')}
                >
                  <FileEdit className="mr-2 h-4 w-4 text-blue-500" />
                  Drafts
                </NavigationMenuLink>
              </NavigationMenuItem>

              <NavigationMenuItem>
                <NavigationMenuLink
                  className={cn(
                    navigationMenuTriggerStyle(),
                    "bg-background hover:bg-accent cursor-pointer transition-all duration-300",
                    getCurrentTab() === 'unsold' && "bg-accent text-accent-foreground"
                  )}
                  onClick={() => handleTabChange('unsold')}
                >
                  <Clock className="mr-2 h-4 w-4 text-red-500" />
                  Unsold
                </NavigationMenuLink>
              </NavigationMenuItem>

              <NavigationMenuItem>
                <NavigationMenuLink
                  className={cn(
                    navigationMenuTriggerStyle(),
                    "bg-background hover:bg-accent cursor-pointer transition-all duration-300",
                    getCurrentTab() === 'archived' && "bg-accent text-accent-foreground"
                  )}
                  onClick={() => handleTabChange('archived')}
                >
                  <Archive className="mr-2 h-4 w-4 text-amber-500" />
                  Archived
                </NavigationMenuLink>
              </NavigationMenuItem>

              {/* Other Direct Links */}
              <NavigationMenuItem>
                <NavigationMenuLink
                  className={cn(
                    navigationMenuTriggerStyle(),
                    "bg-background hover:bg-accent cursor-pointer",
                    getCurrentTab() === 'questions' && "bg-accent text-accent-foreground"
                  )}
                  onClick={() => handleTabChange('questions')}
                >
                  <MessageSquare className="mr-2 h-4 w-4" />
                  Questions
                </NavigationMenuLink>
              </NavigationMenuItem>

              <NavigationMenuItem>
                <NavigationMenuLink
                  className={cn(
                    navigationMenuTriggerStyle(),
                    "bg-background hover:bg-accent cursor-pointer",
                    getCurrentTab() === 'feedback' && "bg-accent text-accent-foreground"
                  )}
                  onClick={() => handleTabChange('feedback')}
                >
                  <Star className="mr-2 h-4 w-4" />
                  Feedback
                </NavigationMenuLink>
              </NavigationMenuItem>

              {/* Tools Group */}
              <NavigationMenuItem>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="bg-background hover:bg-accent cursor-pointer transition-all duration-300 h-10 px-4 py-2 flex items-center">
                      <FileText className="mr-2 h-4 w-4" />
                      Tools
                      <ChevronDown className="ml-1 h-3 w-3 transition-transform duration-200 group-data-[state=open]:rotate-180" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="center" className="p-2 min-w-[160px]">
                    <div className="flex flex-col gap-1">
                      <DropdownMenuItem asChild>
                        <Link to="/trademe/templates" className={cn(
                          "flex items-center gap-2 px-4 py-2 rounded-md cursor-pointer transition-all duration-300 hover:bg-accent",
                          getCurrentTab() === 'templates' && "bg-accent text-accent-foreground"
                        )}>
                          <FileText className="h-4 w-4 text-primary" />
                          <span className="font-medium">Manage Templates</span>
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link to="/trademe/sync" className={cn(
                          "flex items-center gap-2 px-4 py-2 rounded-md cursor-pointer transition-all duration-300 hover:bg-accent",
                          getCurrentTab() === 'sync' && "bg-accent text-accent-foreground"
                        )}>
                          <RefreshCw className="h-4 w-4 text-blue-500" />
                          <span className="font-medium">Sync Status</span>
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild disabled>
                        <Link to="#" className={cn(
                          "flex items-center gap-2 px-4 py-2 rounded-md transition-all duration-300 opacity-50 cursor-not-allowed"
                        )}>
                          <ShieldAlert className="h-4 w-4 text-red-500" />
                          <span className="font-medium">Blacklist</span>
                        </Link>
                      </DropdownMenuItem>
                    </div>
                  </DropdownMenuContent>
                </DropdownMenu>
              </NavigationMenuItem>
              </NavigationMenuList>
            </NavigationMenu>
          </div>
        </div>
      </div>
      <ScrollArea className="h-[calc(100vh-12rem)] w-full overflow-hidden">
        <Outlet />
      </ScrollArea>
    </div>
  );
}
