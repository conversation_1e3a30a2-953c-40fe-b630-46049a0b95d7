/**
 * TradeMe Service
 *
 * Handles TradeMe API interactions, account connection, and authentication.
 * This service is focused on the TradeMe API and account management,
 * while tradeMeItemsService.js and tradeMeItemsSyncService.js handle local database operations.
 */

const axios = require('axios');
const tradeMeUtils = require('./tradeMeUtils');
const { TRADEME_API, getOAuth, parseTradeMeDate, getRequestTokenUrl, getAuthorizationUrl, getAccessTokenUrl } = tradeMeUtils;
const TradeMeSettings = require('../models/TradeMeSettings');
const TradeMeItems = require('../models/TradeMeItems');
const TradeMeFeedback = require('../models/TradeMeFeedback');
const TradeMeCategory = require('../models/TradeMeCategory');
const Location = require('../models/Location');
const User = require('../models/User');
const mongoose = require('mongoose');
const { downloadAndSaveImage, isLocalImage } = require('./imageService');

// parseTradeMeDate is now imported from tradeMeUtils.js

// TRADEME_API and getOAuth are now imported from tradeMeUtils.js

/**
 * Get TradeMe account settings
 * @returns {Promise<Object>} TradeMe settings
 */
async function getSettings() {
  try {
    let settings = await TradeMeSettings.findOne();

    if (!settings) {
      settings = new TradeMeSettings();
      await settings.save();
    }

    return {
      success: true,
      accountStatus: {
        connected: settings.connected,
        username: settings.username,
        lastUpdated: settings.lastUpdated,
        environment: settings.environment
      }
    };
  } catch (error) {
    console.error('Error fetching TradeMe settings:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Get a request token for OAuth
 * @param {string} environment - 'production' or 'sandbox'
 * @param {string} callbackUrl - URL to redirect after authorization
 * @param {string[]} scopes - Array of scopes to request (MyTradeMeRead, MyTradeMeWrite, BiddingAndBuying)
 * @returns {Promise<Object>} Result of the request token request
 */
async function connectAccount(environment, callbackUrl, scopes = ['MyTradeMeRead', 'MyTradeMeWrite']) {
  try {
    // Create OAuth instance
    const oauth = getOAuth(environment);
    const apiEndpoints = TRADEME_API[environment];

    // Validate scopes
    const validScopes = ['MyTradeMeRead', 'MyTradeMeWrite', 'BiddingAndBuying'];
    const validatedScopes = scopes.filter(scope => validScopes.includes(scope));
    if (validatedScopes.length === 0) {
      validatedScopes.push('MyTradeMeRead'); // Default to read access if no valid scopes
    }

    // TradeMe API requires specific scopes to be requested during the OAuth flow
    const scopeParam = validatedScopes.join(',');
    const requestTokenUrl = `${getRequestTokenUrl(environment)}?scope=${scopeParam}`;

    // Prepare OAuth parameters
    const requestData = {
      url: requestTokenUrl,
      method: 'POST'
    };

    // Add callback URL if provided
    const oauthParams = {};
    if (callbackUrl) {
      // Ensure the callback URL uses HTTPS (TradeMe requirement)
      if (callbackUrl.startsWith('http:')) {
        callbackUrl = callbackUrl.replace('http:', 'https:');
      }
      oauthParams.oauth_callback = callbackUrl;
    } else {
      oauthParams.oauth_callback = 'oob'; // Out-of-band flow if no callback URL
    }

    // Make the request
    console.log(`Making request to ${requestTokenUrl}`);
    console.log('OAuth params:', oauthParams);

    // According to TradeMe API docs, for the request token request:
    // 1. The signature method should be PLAINTEXT
    // 2. The oauth_callback should be included in the Authorization header
    // 3. The signature is consumer_secret&

    // Create the authorization header with the correct signature
    const authParams = {
      oauth_consumer_key: oauth.consumer.key,
      oauth_callback: oauthParams.oauth_callback,
      oauth_signature_method: 'PLAINTEXT',
      oauth_signature: `${oauth.consumer.secret}&`,
      oauth_timestamp: Math.floor(Date.now() / 1000),
      oauth_nonce: oauth.getNonce(),
      oauth_version: '1.0'
    };

    // Convert the parameters to a header string
    const headers = {
      Authorization: 'OAuth ' + Object.keys(authParams)
        .map(key => `${key}="${encodeURIComponent(authParams[key])}"`)
        .join(', ')
    };

    console.log('Request headers:', headers);

    let requestTokenResponse;
    try {
      requestTokenResponse = await axios({
        method: 'post',
        url: requestTokenUrl,
        headers: headers
      });
      console.log('Request token response:', requestTokenResponse.data);
    } catch (error) {
      console.error('Error making request to TradeMe API:', error.message);
      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);
      }
      throw error;
    }

    // Parse response
    const requestTokenData = new URLSearchParams(requestTokenResponse.data);
    const requestToken = requestTokenData.get('oauth_token');
    const requestTokenSecret = requestTokenData.get('oauth_token_secret');

    if (!requestToken || !requestTokenSecret) {
      console.error('Invalid response from TradeMe API:', requestTokenResponse.data);
      throw new Error('Invalid response from TradeMe API: Missing token or token secret');
    }

    // Update settings
    let settings = await TradeMeSettings.findOne();
    if (!settings) {
      settings = new TradeMeSettings();
    }

    settings.environment = environment;
    settings.connected = false; // Not fully connected until user authorizes

    // Store the request token and secret
    // We use accessToken and accessSecret fields to store the request token and secret temporarily
    // These will be replaced with the actual access token and secret after authorization
    settings.accessToken = requestToken;
    settings.accessSecret = requestTokenSecret;
    settings.lastUpdated = new Date();

    console.log('Storing request token:', requestToken);
    console.log('Storing request token secret:', requestTokenSecret);

    await settings.save();

    // Generate authorization URL with scope parameter
    const authUrl = getAuthorizationUrl(environment, requestToken);

    return {
      success: true,
      message: 'TradeMe authorization initiated',
      authUrl: authUrl,
      requestToken: requestToken,
      scopes: validatedScopes
    };
  } catch (error) {
    console.error('Error connecting TradeMe account:', error);
    console.error('Error details:', error.response?.data || 'No response data');
    return {
      success: false,
      error: error.message || 'Failed to get request token'
    };
  }
}

/**
 * Complete TradeMe account connection with verification code or access token
 * @param {string} tokenOrAccessToken - OAuth token or access token from TradeMe
 * @param {string} verifierOrAccessTokenSecret - Verification code or access token secret
 * @param {string} environment - TradeMe environment ('production' or 'sandbox')
 * @param {Object} user - User object
 * @returns {Promise<Object>} Result of the connection completion
 */
async function completeConnection(tokenOrAccessToken, verifierOrAccessTokenSecret, environment, user) {
  try {
    console.log(`Completing TradeMe connection with token: ${tokenOrAccessToken}, verifier/secret: ${verifierOrAccessTokenSecret}, environment: ${environment}`);

    // Get current settings
    let settings = await TradeMeSettings.findOne();
    if (!settings) {
      settings = new TradeMeSettings();
    }

    // If environment is not provided, use the one from settings
    environment = environment || settings.environment || 'production';

    // Determine if this is an OAuth verification flow or a direct connection
    const isOAuthFlow = settings.accessSecret && !settings.connected;

    // If this is a direct connection with access token and secret
    if (!isOAuthFlow) {
      console.log('Direct connection with access token and secret');
      const accessToken = tokenOrAccessToken;
      const accessTokenSecret = verifierOrAccessTokenSecret;

      // Update settings with access token and user info
      settings.connected = true;
      settings.environment = environment;
      settings.accessToken = accessToken;
      settings.accessSecret = accessTokenSecret;
      settings.lastUpdated = new Date();

      if (user) {
        settings.lastUpdatedBy = user._id;
      } else {
        settings.lastUpdatedBy = 'Imported';
      }

      await settings.save();

      // Try to get member info
      try {
        const memberSummary = await getMemberSummary();
        if (memberSummary.success && memberSummary.memberSummary) {
          settings.username = memberSummary.memberSummary.MemberNickname || 'Unknown';
          settings.memberId = memberSummary.memberSummary.MemberId?.toString() || '';
          await settings.save();

          return {
            success: true,
            message: 'TradeMe account connected successfully',
            username: settings.username
          };
        }
      } catch (error) {
        console.error('Error getting member summary:', error);
      }

      return {
        success: true,
        message: 'TradeMe account connected successfully',
        username: settings.username || 'Unknown'
      };
    }

    // This is the OAuth verification flow
    console.log('OAuth verification flow - using provided access token and secret');
    const accessToken = tokenOrAccessToken;
    const accessTokenSecret = verifierOrAccessTokenSecret;

    // We already have the access token and secret from the previous step
    // No need to make another request to the AccessToken endpoint

    // Create OAuth instance
    const oauth = getOAuth(environment);

    if (!accessToken || !accessTokenSecret) {
      return {
        success: false,
        error: 'Failed to get valid access token from TradeMe'
      };
    }

    // Get the API base URL
    const apiBase = TRADEME_API[environment].apiBase;

    // Get user info to verify connection
    let userInfoResponse;
    try {
      userInfoResponse = await axios({
        method: 'get',
        url: `${apiBase}/MyTradeMe/Summary.json`,
        headers: oauth.toHeader(
          oauth.authorize(
            {
              url: `${apiBase}/MyTradeMe/Summary.json`,
              method: 'GET'
            },
            {
              key: accessToken,
              secret: accessTokenSecret
            }
          )
        )
      });
      console.log('User info response:', userInfoResponse.data);
    } catch (error) {
      console.error('Error getting user info:', error.message);
      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);
      }

      // Update settings with access token anyway, even if we can't get user info
      // This allows the user to still use the TradeMe API
      settings.connected = true;
      settings.environment = environment;
      settings.accessToken = accessToken;
      settings.accessSecret = accessTokenSecret;
      settings.lastUpdated = new Date();

      if (user) {
        settings.lastUpdatedBy = user._id;
      } else {
        settings.lastUpdatedBy = 'Imported';
      }

      await settings.save();

      return {
        success: true,
        message: 'TradeMe account connected successfully, but could not retrieve user info',
        username: 'Unknown'
      };
    }

    // Check token permissions
    let permissions = [];
    try {
      const permissionsResponse = await axios({
        method: 'get',
        url: `${apiBase}/Oauth/AccessToken/Permissions.json`,
        headers: oauth.toHeader(
          oauth.authorize(
            {
              url: `${apiBase}/Oauth/AccessToken/Permissions.json`,
              method: 'GET'
            },
            {
              key: accessToken,
              secret: accessTokenSecret
            }
          )
        )
      });

      // Store permissions if available
      if (permissionsResponse.data && Array.isArray(permissionsResponse.data)) {
        permissions = permissionsResponse.data;
        console.log('Permissions:', permissions);
      }
    } catch (error) {
      console.warn('Could not check token permissions:', error.message);
    }

    // Update settings with access token and user info
    settings.connected = true;
    settings.environment = environment;
    settings.username = userInfoResponse?.data?.MemberNickname || 'Unknown';
    settings.memberId = userInfoResponse?.data?.MemberId?.toString() || '';
    settings.accessToken = accessToken;
    settings.accessSecret = accessTokenSecret;
    settings.lastUpdated = new Date();

    if (user) {
      settings.lastUpdatedBy = user._id;
    } else {
      settings.lastUpdatedBy = 'Imported';
    }

    await settings.save();

    return {
      success: true,
      message: 'TradeMe account connected successfully',
      username: settings.username,
      permissions: permissions
    };
  } catch (error) {
    console.error('Error completing TradeMe connection:', error);
    console.error('Error details:', error.response?.data || 'No response data');
    return {
      success: false,
      error: error.message || 'Failed to complete TradeMe connection'
    };
  }
}

/**
 * Disconnect TradeMe account
 * @returns {Promise<Object>} Result of the disconnection attempt
 */
async function disconnectAccount() {
  try {
    // Disconnecting TradeMe account

    // Update settings
    let settings = await TradeMeSettings.findOne();
    if (!settings) {
      settings = new TradeMeSettings();
    }

    settings.connected = false;
    settings.username = '';
    settings.accessToken = '';
    settings.accessSecret = '';
    settings.memberId = '';
    settings.lastUpdated = new Date();
    settings.lastUpdatedBy = 'System';

    await settings.save();

    return {
      success: true,
      message: 'TradeMe account disconnected successfully'
    };
  } catch (error) {
    console.error('Error disconnecting TradeMe account:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Refresh TradeMe connection
 * @returns {Promise<Object>} Result of the refresh attempt
 */
async function refreshConnection() {
  try {
    // Refreshing TradeMe connection

    // Get current settings
    let settings = await TradeMeSettings.findOne();

    if (!settings || !settings.connected) {
      return {
        success: false,
        error: 'No active TradeMe connection to refresh'
      };
    }

    const environment = settings.environment;
    const accessToken = settings.accessToken;
    const accessTokenSecret = settings.accessSecret;

    // Create OAuth instance
    const oauth = getOAuth(environment);
    const apiEndpoints = TRADEME_API[environment];

    // Verify connection by fetching user info
    try {
      const userInfoResponse = await axios({
        method: 'get',
        url: `${apiEndpoints.apiBase}/MyTradeMe/Summary.json`,
        headers: oauth.toHeader(
          oauth.authorize(
            {
              url: `${apiEndpoints.apiBase}/MyTradeMe/Summary.json`,
              method: 'GET'
            },
            {
              key: accessToken,
              secret: accessTokenSecret
            }
          )
        )
      });

      // Update username in case it changed
      settings.username = userInfoResponse.data.MemberNickname || settings.username;
    } catch (error) {
      // If API call fails, connection is no longer valid
      settings.connected = false;
      await settings.save();

      return {
        success: false,
        error: 'TradeMe connection is no longer valid'
      };
    }

    settings.lastUpdated = new Date();
    settings.lastUpdatedBy = 'System';
    await settings.save();

    return {
      success: true,
      message: 'TradeMe connection refreshed successfully'
    };
  } catch (error) {
    console.error('Error refreshing TradeMe connection:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Create a new TradeMe listing
 * @param {Object} itemData - The item data to create a listing for
 * @param {Object} user - The user creating the listing
 * @returns {Promise<Object>} Result of the listing creation
 */
async function createListing(itemData, user) {
  try {
    // Creating TradeMe listing

    // Get current settings
    let settings = await TradeMeSettings.findOne();

    if (!settings || !settings.connected) {
      return {
        success: false,
        error: 'No active TradeMe connection'
      };
    }

    const environment = settings.environment;
    const accessToken = settings.accessToken;
    const accessTokenSecret = settings.accessSecret;

    // Create OAuth instance
    const oauth = getOAuth(environment);
    const apiEndpoints = TRADEME_API[environment];

    // Format data for TradeMe API
    const listingData = {
      Title: itemData.title,
      Description: itemData.description,
      Category: itemData.category,
      StartPrice: itemData.price,
      BuyNowPrice: itemData.buyNowPrice || 0,
      Duration: itemData.duration || 7,
      Pickup: itemData.pickup || 1, // 1 = pickup allowed
      IsNew: itemData.isNew || false,
      PhotoIds: itemData.photoIds || []
    };

    // Create listing on TradeMe
    const listingResponse = await axios({
      method: 'post',
      url: `${apiEndpoints.apiBase}/Selling.json`,
      headers: oauth.toHeader(
        oauth.authorize(
          {
            url: `${apiEndpoints.apiBase}/Selling.json`,
            method: 'POST'
          },
          {
            key: accessToken,
            secret: accessTokenSecret
          }
        )
      ),
      data: listingData
    });

    // We no longer create a local record here
    // This is now handled by tradeMeItemsService.listItemOnTradeMe

    return {
      success: true,
      message: 'TradeMe listing created successfully',
      listingId: listingResponse.data.ListingId
    };
  } catch (error) {
    console.error('Error creating TradeMe listing:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Update an existing TradeMe listing
 * @param {string} listingId - The TradeMe listing ID
 * @param {Object} itemData - The updated item data
 * @param {Object} user - The user updating the listing
 * @returns {Promise<Object>} Result of the update operation
 */
async function updateListing(listingId, itemData, user) {
  try {
    // Updating TradeMe listing

    // Get current settings
    let settings = await TradeMeSettings.findOne();

    if (!settings || !settings.connected) {
      return {
        success: false,
        error: 'No active TradeMe connection'
      };
    }

    const environment = settings.environment;
    const accessToken = settings.accessToken;
    const accessTokenSecret = settings.accessSecret;

    // Create OAuth instance
    const oauth = getOAuth(environment);
    const apiEndpoints = TRADEME_API[environment];

    // Format data for TradeMe API
    const listingData = {
      ListingId: listingId,
      Title: itemData.title,
      Description: itemData.description,
      Category: itemData.category,
      StartPrice: itemData.price,
      BuyNowPrice: itemData.buyNowPrice || 0,
      Pickup: itemData.pickup || 1, // 1 = pickup allowed
      IsNew: itemData.isNew || false,
      PhotoIds: itemData.photoIds || []
    };

    // Update listing on TradeMe
    try {
      await axios({
        method: 'post',
        url: `${apiEndpoints.apiBase}/Selling/Edit.json`,
        headers: oauth.toHeader(
          oauth.authorize(
            {
              url: `${apiEndpoints.apiBase}/Selling/Edit.json`,
              method: 'POST'
            },
            {
              key: accessToken,
              secret: accessTokenSecret
            }
          )
        ),
        data: listingData
      });

      return {
        success: true,
        message: 'TradeMe listing updated successfully',
        listingId: listingId
      };
    } catch (error) {
      console.error('Error updating TradeMe listing via API:', error.response?.data || error.message);

      // If we couldn't update via TradeMe API, we'll mark this as a "draft update"
      return {
        success: true,
        message: 'Listing updated locally only',
        draftUpdate: true
      };
    }
  } catch (error) {
    console.error('Error in updateListing service:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// syncListingsFromTradeMe function has been removed
// Use tradeMeItemsSyncService.syncListingsFromTradeMe instead

/**
 * Check if a listing is owned by our TradeMe account
 * @param {string} listingId - The TradeMe listing ID
 * @returns {Promise<boolean>} True if the listing is ours
 */
async function isOurListing(listingId) {
  try {
    // Get settings to check if we're connected
    const settings = await TradeMeSettings.findOne();
    if (!settings || !settings.connected) {
      console.log(`TradeMe Service - Not connected to TradeMe, listing ${listingId} is not ours`);
      return false;
    }

    // Check if the listing exists in our database
    const listing = await TradeMeItems.findOne({
      $or: [
        { currentListingId: listingId },
        { 'listingHistory.trademeListingId': listingId }
      ]
    });
    if (!listing) {
      return false;
    }

    // If we have the listing in our database with our username, it's ours
    return true;
  } catch (error) {
    console.error('Error checking if listing is ours:', error);
    return false;
  }
}

// Helper function to get default location ID
async function getDefaultLocationId() {
  const Location = mongoose.model('Location');
  try {
    // Try to find the first active location
    const location = await Location.findOne({ isActive: true });
    if (location) {
      return location._id;
    }

    // If no active location, create a placeholder
    const newLocation = new Location({
      name: 'Default Location',
      address: 'Default Address',
      phone: '0000000000',
      isActive: true
    });
    await newLocation.save();
    return newLocation._id;
  } catch (error) {
    console.error('Error getting default location:', error);
    // Return null if we can't get a location ID
    return null;
  }
}

// Helper function to get system user ID
async function getSystemUserId() {
  const User = mongoose.model('User');
  try {
    // Try to find an admin user
    const adminUser = await User.findOne({ role: 'admin' });
    if (adminUser) {
      return adminUser._id;
    }

    // If no admin user, return null
    return null;
  } catch (error) {
    console.error('Error getting system user ID:', error);
    return null;
  }
}

/**
 * Get default location ID for placeholder listings
 * @returns {Promise<string>} Default location ID
 */
async function getDefaultLocationId() {
  try {
    // Try to find a location in the database
    const location = await Location.findOne();
    if (location) {
      return location._id;
    }

    // If no location exists, create a default one
    const defaultLocation = new Location({
      name: 'Unknown',
      region: 'Unknown'
    });
    await defaultLocation.save();
    return defaultLocation._id;
  } catch (error) {
    console.error('Error getting default location ID:', error);
    return null;
  }
}

/**
 * Get system user ID for placeholder listings
 * @returns {Promise<string>} System user ID
 */
async function getSystemUserId() {
  try {
    // Try to find a system user
    const systemUser = await User.findOne({ username: 'system' });
    if (systemUser) {
      return systemUser._id;
    }

    // If no system user exists, create one
    const adminUser = await User.findOne({ role: 'admin' });
    if (adminUser) {
      return adminUser._id;
    }

    // If no admin user exists, use the first user we find
    const anyUser = await User.findOne();
    if (anyUser) {
      return anyUser._id;
    }

    // If no users exist at all, create a system user
    const newSystemUser = new User({
      username: 'system',
      email: '<EMAIL>',
      password: crypto.randomBytes(16).toString('hex'),
      role: 'admin'
    });
    await newSystemUser.save();
    return newSystemUser._id;
  } catch (error) {
    console.error('Error getting system user ID:', error);
    return null;
  }
}

/**
 * Get questions for a specific listing
 * @param {string} listingId - The TradeMe listing ID
 * @returns {Promise<Object>} Result with questions for the listing
 */
async function getQuestionsForListing(listingId) {
  try {
    // Getting questions for listing

    // First, check if the listing exists in our database
    const listing = await TradeMeItems.findOne({
      $or: [
        { currentListingId: listingId },
        { 'listingHistory.trademeListingId': listingId }
      ]
    });
    if (!listing) {
      return {
        success: false,
        error: 'Listing not found in database'
      };
    }

    // Get questions from the item
    const questions = listing.questions
      .filter(q => q.originalListingId === listingId.toString())
      .sort((a, b) => new Date(b.askDate) - new Date(a.askDate));

    // If we have a connected TradeMe account, try to sync questions for this listing
    const settings = await TradeMeSettings.findOne();
    if (settings && settings.connected) {
      try {
        // For a specific listing, we only need to fetch a minimal number of pages
        // This is now handled by tradeMeItemsSyncService
        // await syncListingsFromTradeMe({ maxPagesPerEndpoint: 1 });

        // Get the item again after sync to get the latest questions
        const updatedItem = await TradeMeItems.findById(listing._id);
        if (updatedItem) {
          const updatedQuestions = updatedItem.questions
            .filter(q => q.originalListingId === listingId.toString())
            .sort((a, b) => new Date(b.askDate) - new Date(a.askDate));

          return {
            success: true,
            questions: updatedQuestions
          };
        }
      } catch (syncError) {
        console.error(`TradeMe Service - Error syncing questions for listing ${listingId}:`, syncError);
        // Continue with the questions we already have
      }
    }

    return {
      success: true,
      questions
    };
  } catch (error) {
    console.error(`TradeMe Service - Error getting questions for listing ${listingId}:`, error);
    return {
      success: false,
      error: error.message
    };
  }
}



/**
 * Get the member summary from TradeMe
 * @returns {Promise<Object>} Result with member summary
 */
async function getMemberSummary() {
  try {
    // Getting TradeMe member summary

    // Get TradeMe settings
    const settings = await TradeMeSettings.findOne();
    if (!settings || !settings.connected) {
      return {
        success: false,
        error: 'TradeMe account not connected'
      };
    }

    const environment = settings.environment;
    const accessToken = settings.accessToken;
    const accessTokenSecret = settings.accessSecret;
    const apiEndpoints = TRADEME_API[environment];
    const oauth = getOAuth(environment);

    // Get member summary
    try {
      const summaryResponse = await axios({
        method: 'get',
        url: `${apiEndpoints.apiBase}/MyTradeMe/Summary.json?return_member_profile=true`,
        headers: oauth.toHeader(
          oauth.authorize(
            {
              url: `${apiEndpoints.apiBase}/MyTradeMe/Summary.json?return_member_profile=true`,
              method: 'GET'
            },
            {
              key: accessToken,
              secret: accessTokenSecret
            }
          )
        )
      });

      return {
        success: true,
        memberSummary: summaryResponse.data
      };
    } catch (error) {
      console.error('Error getting member summary:', error);
      return {
        success: false,
        error: error.message
      };
    }
  } catch (error) {
    console.error('Error in getMemberSummary:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Check the permissions of the current TradeMe token
 * @returns {Promise<Object>} Result with token permissions
 */
async function checkTokenPermissions() {
  try {
    // Checking TradeMe token permissions

    // Get TradeMe settings
    const settings = await TradeMeSettings.findOne();
    if (!settings || !settings.connected) {
      return {
        success: false,
        error: 'TradeMe account not connected'
      };
    }

    const environment = settings.environment;
    const accessToken = settings.accessToken;
    const accessTokenSecret = settings.accessSecret;
    const apiEndpoints = TRADEME_API[environment];
    const oauth = getOAuth(environment);

    // Check token permissions
    try {
      const permissionsResponse = await axios({
        method: 'get',
        url: `${apiEndpoints.apiBase}/Oauth/AccessToken/Permissions.json`,
        headers: oauth.toHeader(
          oauth.authorize(
            {
              url: `${apiEndpoints.apiBase}/Oauth/AccessToken/Permissions.json`,
              method: 'GET'
            },
            {
              key: accessToken,
              secret: accessTokenSecret
            }
          )
        )
      });

      return {
        success: true,
        permissions: permissionsResponse.data
      };
    } catch (error) {
      console.error('Error checking token permissions:', error);
      return {
        success: false,
        error: error.message
      };
    }
  } catch (error) {
    console.error('Error in checkTokenPermissions:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Fetch a single listing directly from TradeMe API
 * @param {string} listingId - The TradeMe listing ID
 * @returns {Promise<Object>} Result with the listing details
 */
async function fetchListingFromTradeMe(listingId) {
  try {
    // Fetching listing directly from TradeMe API

    // Get current settings
    let settings = await TradeMeSettings.findOne();

    if (!settings || !settings.connected) {
      return {
        success: false,
        error: 'No active TradeMe connection'
      };
    }

    const environment = settings.environment;
    const accessToken = settings.accessToken;
    const accessTokenSecret = settings.accessSecret;

    // Create OAuth instance
    const oauth = getOAuth(environment);
    const apiEndpoints = TRADEME_API[environment];

    // Fetch listing from TradeMe
    try {
      const response = await axios({
        method: 'get',
        url: `${apiEndpoints.apiBase}/Listings/${listingId}.json`,
        headers: oauth.toHeader(
          oauth.authorize(
            {
              url: `${apiEndpoints.apiBase}/Listings/${listingId}.json`,
              method: 'GET'
            },
            {
              key: accessToken,
              secret: accessTokenSecret
            }
          )
        )
      });

      // Check all possible fields where description might be stored
      const possibleDescriptionFields = ['Body', 'Description', 'body', 'description'];
      let description = 'No description provided';

      for (const field of possibleDescriptionFields) {
        if (response.data && response.data[field] && typeof response.data[field] === 'string' && response.data[field].trim() !== '') {
          description = response.data[field];
          break;
        }
      }

      // Update the listing in our database
      const listing = await TradeMeItems.findOne({
        $or: [
          { currentListingId: listingId },
          { 'listingHistory.trademeListingId': listingId }
        ]
      });
      if (listing) {
        listing.description = description;
        await listing.save();
      }

      return {
        success: true,
        listing: response.data
      };
    } catch (error) {
      console.error(`TradeMe Service - Error fetching listing ${listingId} from TradeMe API:`, error.response?.data || error.message);
      return {
        success: false,
        error: error.response?.data?.ErrorDescription || error.message
      };
    }
  } catch (error) {
    console.error(`TradeMe Service - Error in fetchListingFromTradeMe:`, error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Import the feedback and category services
const tradeMeFeedbackService = require('./tradeMeFeedbackService');
const tradeMeCategoryService = require('./tradeMeCategoryService');

// Export the sync functions from the feedback and category services
const syncFeedbackFromTradeMe = tradeMeFeedbackService.syncFeedbackFromTradeMe;
const syncCategoriesFromTradeMe = tradeMeCategoryService.syncCategoriesFromTradeMe;


/**
 * Get an access token using a request token and verifier
 * @param {string} environment - 'production' or 'sandbox'
 * @param {string} oauthToken - OAuth token from TradeMe
 * @param {string} oauthVerifier - Verification code from TradeMe
 * @returns {Promise<Object>} Result of the access token request
 */
async function getAccessToken(environment, oauthToken, oauthVerifier) {
  try {
    console.log(`Getting access token for environment: ${environment}, token: ${oauthToken}, verifier: ${oauthVerifier}`);

    // Get current settings
    let settings = await TradeMeSettings.findOne();
    if (!settings) {
      return {
        success: false,
        error: 'No pending TradeMe connection'
      };
    }

    // Get the request token secret from settings
    // In the OAuth flow, the request token secret is stored in the accessSecret field
    // when the request token is obtained
    const requestTokenSecret = settings.accessSecret;
    if (!requestTokenSecret) {
      console.error('No request token secret found in settings');
      return {
        success: false,
        error: 'No request token secret found. Please restart the OAuth flow.'
      };
    }

    console.log('Found request token secret:', requestTokenSecret);

    // Create OAuth instance
    const oauth = getOAuth(environment);

    // Get the access token URL
    const accessTokenUrl = getAccessTokenUrl(environment);

    // Prepare the request for access token
    const requestData = {
      url: accessTokenUrl,
      method: 'POST'
    };

    // According to TradeMe API docs, for the access token request:
    // 1. The signature method should be PLAINTEXT
    // 2. The oauth_verifier should be included in the Authorization header
    // 3. The signature is consumer_secret&request_token_secret

    // Create the authorization header with the correct signature
    const authParams = {
      oauth_consumer_key: oauth.consumer.key,
      oauth_token: oauthToken,
      oauth_verifier: oauthVerifier,
      oauth_signature_method: 'PLAINTEXT',
      oauth_signature: `${oauth.consumer.secret}&${requestTokenSecret}`,
      oauth_timestamp: Math.floor(Date.now() / 1000),
      oauth_nonce: oauth.getNonce(),
      oauth_version: '1.0'
    };

    // Convert the parameters to a header string
    const authHeader = {
      Authorization: 'OAuth ' + Object.keys(authParams)
        .map(key => `${key}="${encodeURIComponent(authParams[key])}"`)
        .join(', ')
    };

    console.log('Making request to get access token');
    console.log('Request URL:', accessTokenUrl);
    console.log('OAuth token:', oauthToken);
    console.log('OAuth verifier:', oauthVerifier);

    // Exchange request token for access token
    let accessTokenResponse;
    try {
      console.log('Authorization header:', authHeader);

      // According to TradeMe API docs, the oauth_verifier should be included in the Authorization header only
      // The request body should be empty
      accessTokenResponse = await axios({
        method: 'post',
        url: accessTokenUrl,
        headers: {
          ...authHeader,
          'Content-Type': 'application/x-www-form-urlencoded'
        }
        // No request body needed
      });
      console.log('Access token response:', accessTokenResponse.data);
    } catch (error) {
      console.error('Error making request to TradeMe API:', error.message);
      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);
      }
      throw error;
    }

    // Parse response
    const accessTokenData = new URLSearchParams(accessTokenResponse.data);
    const accessToken = accessTokenData.get('oauth_token');
    const accessTokenSecret = accessTokenData.get('oauth_token_secret');

    if (!accessToken || !accessTokenSecret) {
      console.error('Invalid response from TradeMe API:', accessTokenResponse.data);
      return {
        success: false,
        error: 'Invalid response from TradeMe API: Missing token or token secret'
      };
    }

    // Check token permissions
    let permissions = [];
    try {
      const permissionsResult = await checkTokenPermissions();
      if (permissionsResult.success && Array.isArray(permissionsResult.permissions)) {
        permissions = permissionsResult.permissions;
      }
    } catch (error) {
      console.warn('Could not check token permissions:', error.message);
    }

    return {
      success: true,
      accessToken,
      accessTokenSecret,
      permissions
    };
  } catch (error) {
    console.error('Error getting access token:', error);
    return {
      success: false,
      error: error.message || 'Failed to get access token'
    };
  }
}

module.exports = {
  getSettings,
  getRequestToken: connectAccount, // Alias connectAccount as getRequestToken
  getAccessToken, // Add getAccessToken function
  connectAccount,
  completeConnection,
  disconnectAccount,
  refreshConnection,
  createListing,
  syncFeedbackFromTradeMe,
  syncCategoriesFromTradeMe,
  isOurListing,
  getQuestionsForListing,
  checkTokenPermissions,
  getMemberSummary,
  fetchListingFromTradeMe,
  // Export constants and helper functions for use in other services
  getOAuth,
  TRADEME_API,
  parseTradeMeDate
};
