{"name": "node_server", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"start": "node server.js", "dev": "node server.js", "prod": "set NODE_ENV=production && node server.js", "install-service": "node install-service.js", "uninstall-service": "node uninstall-service.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "<PERSON>", "license": "ISC", "dependencies": {"@anthropic-ai/sdk": "^0.27.3", "axios": "^1.8.4", "bcrypt": "^5.1.1", "body-parser": "^1.20.2", "chart.js": "^4.4.1", "connect-flash": "^0.1.1", "connect-mongo": "^5.1.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "csurf": "^1.10.0", "csv-writer": "^1.6.0", "date-fns": "^4.1.0", "dotenv": "^16.4.1", "ejs": "^3.1.9", "express": "^4.21.2", "express-session": "^1.18.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.482.0", "moment": "^2.30.1", "mongoose": "^8.12.2", "multer": "^1.4.5-lts.2", "node-schedule": "^2.1.1", "oauth-1.0a": "^2.2.6", "openai": "^4.63.0", "pino": "^9.5.0", "puppeteer": "^24.6.1", "sharp": "^0.34.1", "uuid": "^11.1.0", "zod": "^3.24.2"}}