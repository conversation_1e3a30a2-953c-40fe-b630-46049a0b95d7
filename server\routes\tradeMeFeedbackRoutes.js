const express = require('express');
const router = express.Router();
const { requireUser } = require('./middleware/auth');
const tradeMeFeedbackService = require('../services/tradeMeFeedbackService');
const { TradeMeFeedback } = require('../models/TradeMeFeedback');

/**
 * @route GET /api/trademe/feedback
 * @description Get TradeMe feedback
 * @access Private
 */
router.get('/', requireUser, async (req, res) => {
  try {
    const { type = 'all', page = 1, limit = 10, rating } = req.query;

    // Parse limit and page as integers
    const parsedLimit = parseInt(limit);
    const parsedPage = parseInt(page);

    console.log(`TradeMe Feedback API - Received request with limit=${limit}, parsed as ${parsedLimit}`);

    // Build query options
    const options = {
      type,
      page: parsedPage,
      limit: parsedLimit
    };

    // Add rating filter if provided
    if (rating && ['Positive', 'Neutral', 'Negative'].includes(rating)) {
      options.rating = rating;
    }

    const result = await tradeMeFeedbackService.getFeedback(options);

    return res.status(200).json(result);
  } catch (error) {
    console.error('Error getting TradeMe feedback:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/trademe/feedback/stats
 * @description Get TradeMe feedback statistics
 * @access Private
 */
router.get('/stats', requireUser, async (req, res) => {
  try {
    const result = await tradeMeFeedbackService.getFeedbackStats();

    return res.status(200).json(result);
  } catch (error) {
    console.error('Error getting TradeMe feedback statistics:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route POST /api/trademe/feedback/:feedbackId/read
 * @description Mark TradeMe feedback as read
 * @access Private
 */
router.post('/:feedbackId/read', requireUser, async (req, res) => {
  try {
    const { feedbackId } = req.params;

    const result = await tradeMeFeedbackService.markFeedbackAsRead(feedbackId);

    return res.status(200).json(result);
  } catch (error) {
    console.error('Error marking TradeMe feedback as read:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route POST /api/trademe/feedback/read-all
 * @description Mark all TradeMe feedback as read
 * @access Private
 */
router.post('/read-all', requireUser, async (req, res) => {
  try {
    // Update all unread feedback
    const result = await TradeMeFeedback.updateMany(
      { isRead: false },
      { $set: { isRead: true } }
    );

    return res.status(200).json({
      success: true,
      message: `Marked ${result.modifiedCount} feedback items as seen`,
      count: result.modifiedCount
    });
  } catch (error) {
    console.error('Error marking all TradeMe feedback as read:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = router;
