import { useState, useEffect } from 'react';
import { useNavigate, useParams, Routes, Route } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/useToast';
import { getAuditStats, getStaffPerformance } from '@/api/buyPawnAudits';
import { AuditNavigation } from './AuditNavigation';
import { StaffComplianceTable } from './StaffComplianceTable';
import { Loader2, Download, ArrowLeft } from 'lucide-react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';

/**
 * Component for staff performance metrics and trends
 */
export function StaffPerformance() {
  return (
    <Routes>
      <Route index element={<StaffPerformanceList />} />
      <Route path=":employeeId" element={<StaffPerformanceDetail />} />
    </Routes>
  );
}

/**
 * Component to display a list of all staff and their performance metrics
 */
function StaffPerformanceList() {
  const [stats, setStats] = useState<any>(null);
  const [staffData, setStaffData] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isExporting, setIsExporting] = useState(false);
  const [timeRange, setTimeRange] = useState('30days');
  const { toast } = useToast();

  // Fetch staff performance statistics
  useEffect(() => {
    const fetchStats = async () => {
      try {
        setIsLoading(true);

        // Get audit stats
        const statsResult = await getAuditStats({ timeRange });

        if (statsResult.success) {
          setStats(statsResult.data);
        } else {
          toast({
            title: 'Error',
            description: statsResult.error || 'Failed to load audit statistics.',
            variant: 'destructive',
          });
        }

        // Get staff performance data
        const staffResult = await getStaffPerformance({ timeRange });

        if (staffResult.success) {
          setStaffData(staffResult.data);
        } else {
          toast({
            title: 'Error',
            description: staffResult.error || 'Failed to load staff performance data.',
            variant: 'destructive',
          });
        }
      } catch (error: any) {
        toast({
          title: 'Error',
          description: error.message || 'An unexpected error occurred.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchStats();
  }, [timeRange, toast]);

  // Handle export to PDF
  const handleExportPDF = async () => {
    try {
      setIsExporting(true);
      toast({
        title: 'Export Started',
        description: 'Generating PDF report...',
      });

      // Call the API to generate and download the PDF
      const response = await fetch('/api/buys/audits/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          format: 'pdf',
          type: 'staff_performance',
          timeRange,
          includeCharts: true,
          includeDetails: true,
        }),
      });

      setIsExporting(false);

      if (response.ok) {
        toast({
          title: 'Export Complete',
          description: 'PDF report has been downloaded.',
        });
      } else {
        const errorData = await response.json();
        toast({
          title: 'Export Failed',
          description: errorData.error || 'Failed to generate PDF report.',
          variant: 'destructive',
        });
      }
    } catch (error: any) {
      setIsExporting(false);
      toast({
        title: 'Export Failed',
        description: error.message || 'An unexpected error occurred.',
        variant: 'destructive',
      });
    }
  };

  if (isLoading && !stats) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col gap-4 mb-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold">Staff Performance</h1>
            <p className="text-muted-foreground">Monitor staff compliance and performance metrics</p>
          </div>
          <Button
            variant="outline"
            onClick={handleExportPDF}
            disabled={isExporting}
          >
            {isExporting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Exporting...
              </>
            ) : (
              <>
                <Download className="mr-2 h-4 w-4" />
                Export Report
              </>
            )}
          </Button>
        </div>

        <div className="flex justify-between items-center">
          <AuditNavigation />
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select time range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7days">Last 7 days</SelectItem>
              <SelectItem value="30days">Last 30 days</SelectItem>
              <SelectItem value="90days">Last 90 days</SelectItem>
              <SelectItem value="year">Last year</SelectItem>
              <SelectItem value="all">All time</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {stats && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Staff Compliance Performance</CardTitle>
              <CardDescription>
                Detailed performance metrics by staff member
              </CardDescription>
            </CardHeader>
            <CardContent>
              <StaffComplianceTable data={staffData} />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Overall Staff Performance Trends</CardTitle>
              <CardDescription>
                Compliance rates across all staff members over time
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={stats.staffPerformanceTrends}
                    margin={{
                      top: 5,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" opacity={0.1} />
                    <XAxis
                      dataKey="date"
                      tick={{ fill: 'hsl(var(--muted-foreground))' }}
                    />
                    <YAxis
                      tick={{ fill: 'hsl(var(--muted-foreground))' }}
                      domain={[0, 100]}
                      label={{
                        value: 'Compliance Rate (%)',
                        angle: -90,
                        position: 'insideLeft',
                        style: { fill: 'hsl(var(--muted-foreground))' }
                      }}
                    />
                    <Tooltip />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="averageComplianceRate"
                      name="Average Compliance"
                      stroke="hsl(var(--primary))"
                      strokeWidth={2}
                    />
                    <Line
                      type="monotone"
                      dataKey="highestComplianceRate"
                      name="Highest Compliance"
                      stroke="rgb(34, 197, 94)"
                      strokeWidth={2}
                    />
                    <Line
                      type="monotone"
                      dataKey="lowestComplianceRate"
                      name="Lowest Compliance"
                      stroke="rgb(239, 68, 68)"
                      strokeWidth={2}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}

/**
 * Component to display detailed performance metrics for a specific staff member
 */
function StaffPerformanceDetail() {
  const { employeeId } = useParams<{ employeeId: string }>();
  const [employee, setEmployee] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isExporting, setIsExporting] = useState(false);
  const [timeRange, setTimeRange] = useState('30days');
  const navigate = useNavigate();
  const { toast } = useToast();

  // Handle export employee report
  const handleExportEmployeeReport = async () => {
    if (!employee) return;

    try {
      setIsExporting(true);
      toast({
        title: 'Export Started',
        description: 'Generating employee report...',
      });

      // Call the API to generate and download the PDF
      const response = await fetch('/api/buys/audits/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          format: 'pdf',
          type: 'employee_performance',
          employeeId: employee.id,
          timeRange,
          includeCharts: true,
          includeDetails: true,
        }),
      });

      setIsExporting(false);

      if (response.ok) {
        toast({
          title: 'Export Complete',
          description: 'Employee report has been downloaded.',
        });
      } else {
        const errorData = await response.json();
        toast({
          title: 'Export Failed',
          description: errorData.error || 'Failed to generate employee report.',
          variant: 'destructive',
        });
      }
    } catch (error: any) {
      setIsExporting(false);
      toast({
        title: 'Export Failed',
        description: error.message || 'An unexpected error occurred.',
        variant: 'destructive',
      });
    }
  };

  // Fetch employee performance data
  useEffect(() => {
    const fetchEmployeeData = async () => {
      if (!employeeId) return;

      try {
        setIsLoading(true);

        // Call the API to get employee data
        const result = await getStaffPerformance({
          employeeId,
          timeRange
        });

        if (result.success) {
          setEmployee(result.data);
        } else {
          toast({
            title: 'Error',
            description: result.error || 'Failed to load employee data.',
            variant: 'destructive',
          });
        }
      } catch (error: any) {
        toast({
          title: 'Error',
          description: error.message || 'Failed to load employee data.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchEmployeeData();
  }, [employeeId, timeRange, toast]);

  if (isLoading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  if (!employee) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex justify-between items-center mb-6">
          <Button variant="outline" onClick={() => navigate('/buys/audits/staff')}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Staff List
          </Button>
        </div>
        <Card>
          <CardContent className="py-12">
            <div className="text-center">
              <h2 className="text-2xl font-bold mb-2">Employee Not Found</h2>
              <p className="text-muted-foreground mb-6">
                The employee you are looking for does not exist or has been deleted.
              </p>
              <Button onClick={() => navigate('/buys/audits/staff')}>
                Return to Staff List
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <Button variant="outline" onClick={() => navigate('/buys/audits/staff')}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Staff List
        </Button>
        <div className="flex items-center gap-4">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select time range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7days">Last 7 days</SelectItem>
              <SelectItem value="30days">Last 30 days</SelectItem>
              <SelectItem value="90days">Last 90 days</SelectItem>
              <SelectItem value="year">Last year</SelectItem>
              <SelectItem value="all">All time</SelectItem>
            </SelectContent>
          </Select>
          <Button
            variant="outline"
            onClick={handleExportEmployeeReport}
            disabled={isExporting}
          >
            {isExporting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Exporting...
              </>
            ) : (
              <>
                <Download className="mr-2 h-4 w-4" />
                Export Report
              </>
            )}
          </Button>
        </div>
      </div>

      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>{employee.name}</CardTitle>
            <CardDescription>
              Performance metrics and audit history
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">Total Audits</p>
                <p className="text-3xl font-bold">{employee.totalAudits}</p>
              </div>
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">Compliance Rate</p>
                <p className="text-3xl font-bold">{employee.complianceRate}%</p>
              </div>
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">Audit Breakdown</p>
                <div className="flex gap-4">
                  <div>
                    <span className="text-green-500 font-bold">{employee.auditBreakdown.compliant}</span>
                    <span className="text-xs text-muted-foreground ml-1">Compliant</span>
                  </div>
                  <div>
                    <span className="text-yellow-500 font-bold">{employee.auditBreakdown.minorNonCompliant}</span>
                    <span className="text-xs text-muted-foreground ml-1">Minor</span>
                  </div>
                  <div>
                    <span className="text-red-500 font-bold">{employee.auditBreakdown.majorNonCompliant}</span>
                    <span className="text-xs text-muted-foreground ml-1">Major</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="h-[300px] mb-6">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={employee.complianceHistory.map((item: any) => ({
                    ...item,
                    date: new Date(item.date).toLocaleDateString('en-GB', {
                      month: 'short',
                      year: 'numeric'
                    })
                  }))}
                  margin={{
                    top: 5,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" opacity={0.1} />
                  <XAxis
                    dataKey="date"
                    tick={{ fill: 'hsl(var(--muted-foreground))' }}
                  />
                  <YAxis
                    tick={{ fill: 'hsl(var(--muted-foreground))' }}
                    domain={[0, 100]}
                    label={{
                      value: 'Compliance Rate (%)',
                      angle: -90,
                      position: 'insideLeft',
                      style: { fill: 'hsl(var(--muted-foreground))' }
                    }}
                  />
                  <Tooltip />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="complianceRate"
                    name="Compliance Rate"
                    stroke="hsl(var(--primary))"
                    strokeWidth={2}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
