const mongoose = require('mongoose');

const metalPriceHistorySchema = new mongoose.Schema({
  metal: {
    type: String,
    required: true,
    enum: ['Gold', 'Silver', 'Platinum', 'Palladium'],
    index: true
  },
  date: {
    type: Date,
    required: true,
    index: true
  },
  price: {
    type: Number,
    required: true,
    min: 0
  }
}, {
  versionKey: false
});

// Create a compound index for efficient lookups by metal and date
metalPriceHistorySchema.index({ metal: 1, date: 1 }, { unique: true });

const MetalPriceHistory = mongoose.model('MetalPriceHistory', metalPriceHistorySchema);

module.exports = MetalPriceHistory;