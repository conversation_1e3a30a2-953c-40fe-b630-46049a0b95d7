import React, { useState, useEffect, useMemo } from 'react';
import { Ghost } from 'lucide-react';
import '../styles/login-animations.css';

interface GhostProps {
  size?: number;
  color?: string;
  className?: string;
  style?: React.CSSProperties;
  trailEffect?: boolean;
}

interface GhostConfig {
  size: number;
  color: string;
  className: string;
  style: React.CSSProperties;
}

const GhostIcon: React.FC<GhostProps> = ({
  size = 24,
  color = "currentColor",
  className = "",
  style = {}
}) => {
  return (
    <div className={`${className}`} style={style}>
      <Ghost
        size={size}
        color={color}
        style={{
          filter: 'drop-shadow(0 0 1px rgba(255, 255, 255, 0.3))',
          transition: 'all 0.5s ease-in-out'
        }}
      />
    </div>
  );
}

export const AnimatedGhosts: React.FC = () => {
  // Create a grid of positions to ensure ghosts are distributed across the entire screen
  // Use useMemo to ensure the ghost positions are only generated once
  const ghosts = useMemo(() => {
    const generateGhosts = (): GhostConfig[] => {
      const ghostsArray: GhostConfig[] = [];
      const sizes = [22, 25, 28, 30, 32, 35, 38, 40, 42, 45, 50];
      const opacities = [0.2, 0.25, 0.3];
      const animations = ['animate-float', 'animate-float-reverse'];

      // Create a 6x5 grid of positions (30 total positions)
      const rows = 6;
      const cols = 5;

      for (let row = 0; row < rows; row++) {
        for (let col = 0; col < cols; col++) {
          // Calculate base position with some randomness
          const baseTop = (row * 100) / rows;
          const baseLeft = (col * 100) / cols;

          // Add some randomness to the position (±10%)
          const top = baseTop + (Math.random() * 20 - 10);
          const left = baseLeft + (Math.random() * 20 - 10);

          // Randomly select properties
          const size = sizes[Math.floor(Math.random() * sizes.length)];
          const opacity = opacities[Math.floor(Math.random() * opacities.length)];
          const animation = animations[Math.floor(Math.random() * animations.length)];

          // Generate random animation delay
          const delay = (Math.random() * 4).toFixed(1) + 's';

          ghostsArray.push({
            size,
            color: `rgba(255, 255, 255, ${opacity})`,
            className: `${animation} absolute`,
            style: {
              animationDelay: delay,
              top: `${Math.max(0, Math.min(95, top))}%`,
              left: `${Math.max(0, Math.min(95, left))}%`,
              position: 'absolute'
            }
          });
        }
      }

      return ghostsArray;
    };

    return generateGhosts();
  }, []); // Empty dependency array ensures this runs only once

  return (
    <div className="fixed inset-0 w-full h-full pointer-events-none overflow-hidden" style={{ minHeight: '100vh', width: '100vw' }}>
      {/* Create a positioned container to ensure ghosts are distributed across the entire screen */}
      <div className="relative w-full h-full">
        {ghosts.map((ghost, index) => (
          <GhostIcon
            key={index}
            size={ghost.size}
            color={ghost.color}
            className={ghost.className}
            style={ghost.style}
          />
        ))}
      </div>
    </div>
  );
};
