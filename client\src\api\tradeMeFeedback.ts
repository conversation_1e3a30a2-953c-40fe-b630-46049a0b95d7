import api from './api';

export interface TradeMeFeedbackItem {
  _id: string;
  feedbackId: string;
  feedbackType: string; // 'Buyer' or 'Seller'
  rating: 'Positive' | 'Neutral' | 'Negative';
  comment: string;
  submittedBy: string;
  submittedDate: string;
  response?: string;
  responseDate?: string;
  listingId: string;
  listingTitle: string;
  isRead: boolean;
  isSeller: boolean;
  buyNowPrice?: string;
  isAnOffer?: boolean;
  maximumBidAmount?: string;
  offerPrice?: string;
  hasReturnedFeedback?: boolean;
  isEdited?: boolean;
  sellerId?: string;
  purchaseId?: string;
  feedbackFrom?: {
    memberId: number;
    nickname: string;
    dateAddressVerified?: string;
    dateJoined?: string;
    uniqueNegative?: number;
    uniquePositive?: number;
    feedbackCount?: number;
    isAddressVerified?: boolean;
    isDealer?: boolean;
    isAuthenticated?: boolean;
  };
  createdAt: string;
  updatedAt: string;
}

export interface FeedbackStats {
  totalCount: number;
  totalPositive: number;
  uniquePositive: number;
  totalNeutral: number;
  totalNegative: number;
  uniqueNegative: number;
  positiveFeedbackPercentage: number;
  nickname: string;
  lastLoggedIn: string;
  dateJoined: string;
  twoMonthListingCount: number;
  isAuthenticated: boolean;
  counts: {
    byRating: {
      positive: number;
      neutral: number;
      negative: number;
    };
    byType: {
      received: number;
      given: number;
    };
  };
}

/**
 * Get TradeMe feedback
 * @param options Options for fetching feedback
 * @returns Promise with feedback items and pagination info
 */
export const getFeedback = async (options: {
  type?: 'received' | 'given' | 'all';
  page?: number;
  limit?: number;
  rating?: 'Positive' | 'Neutral' | 'Negative';
  unreadOnly?: boolean;
  sort?: string;
} = {}) => {
  try {
    const params = new URLSearchParams();

    if (options.type) {
      params.append('type', options.type);
    }

    if (options.page) {
      params.append('page', options.page.toString());
    }

    if (options.limit) {
      params.append('limit', options.limit.toString());
    }

    if (options.rating) {
      params.append('rating', options.rating);
    }

    if (options.unreadOnly !== undefined) {
      params.append('unreadOnly', options.unreadOnly.toString());
    }

    if (options.sort) {
      params.append('sort', options.sort);
    }

    const response = await api.get(`/trademe/feedback?${params.toString()}`);
    return response.data;
  } catch (error: any) {
    console.error('Error getting TradeMe feedback:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

/**
 * Mark feedback as read
 * @param feedbackId ID of the feedback to mark as read
 * @returns Promise with result
 */
export const markFeedbackAsRead = async (feedbackId: string) => {
  try {
    const response = await api.post(`/trademe/feedback/${feedbackId}/read`);
    return response.data;
  } catch (error: any) {
    console.error('Error marking TradeMe feedback as read:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

/**
 * Mark all feedback as read
 * @returns Promise with result
 */
export const markAllFeedbackAsRead = async (): Promise<{ success: boolean; message?: string; count?: number; error?: string }> => {
  try {
    const response = await api.post('/trademe/feedback/read-all');
    return response.data;
  } catch (error: any) {
    console.error('Error marking all TradeMe feedback as read:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

/**
 * Get feedback statistics
 * @returns Promise with feedback statistics
 */
export const getFeedbackStats = async (): Promise<{ success: boolean; stats: FeedbackStats; error?: string }> => {
  try {
    const response = await api.get('/trademe/feedback/stats');
    return response.data;
  } catch (error: any) {
    console.error('Error getting TradeMe feedback stats:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};
