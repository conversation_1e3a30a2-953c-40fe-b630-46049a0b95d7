const mongoose = require('mongoose');
const config = require('../config/database');

// Connect to database
mongoose.connect(config.mongoURI)
  .then(() => console.log('MongoDB connected'))
  .catch(err => {
    console.error('MongoDB connection error:', err);
    process.exit(1);
  });

// Import both models
const Customer = require('../models/Customer');
const Contact = require('../models/Contact');

const migrateData = async () => {
  try {
    console.log('Starting migration from Customers to Contacts...');

    // Get all customers
    const customers = await Customer.find({});
    console.log(`Found ${customers.length} customers to migrate`);

    // Convert customers to contacts
    for (const customer of customers) {
      const contactData = {
        name: customer.name,
        type: 'customer', // Default type for existing customers
        email: customer.email,
        phone: customer.phone,
        address: customer.address,
        notes: customer.notes,
        dateAdded: customer.dateAdded,
        createdAt: customer.createdAt,
        updatedAt: customer.updatedAt
      };

      // Create new contact
      await Contact.create(contactData);
      console.log(`Migrated: ${customer.name}`);
    }

    console.log('Migration complete!');
    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
};

migrateData();