import React, { useState, useEffect, ReactNode } from 'react';
import { useSearchParams } from 'react-router-dom';
import { TradeMeItem as TradeItem, SaleStatus } from '@/api/tradeMeItems';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SelectGroup,
  SelectLabel
} from "@/components/ui/select";
import {
  Search,
  RefreshCw,
  Loader2,
  Filter,
  XCircle,
  Calendar,
  Mail,
  Truck,
  Package,
  CreditCard,
  Star,
  RotateCcw
} from 'lucide-react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";

export type TradeMeListingType = 'selling' | 'sold' | 'unsold';

interface TradeMeListingsLayoutProps {
  title: string;
  type: TradeMeListingType;
  listings: TradeItem[];
  totalItems: number;
  loading: boolean;
  syncing: boolean;
  onSync: () => Promise<void>;
  onSearch: (term: string) => void;
  onFilter: (filter: string) => void;
  onSort: (sort: string) => void;
  onTimeFilter?: (days: number) => void;
  onStatusFilter?: (status: SaleStatus | null) => void;
  children: ReactNode;
}

export function TradeMeListingsLayout({
  title,
  type,
  listings,
  totalItems,
  loading,
  syncing,
  onSync,
  onSearch,
  onFilter,
  onSort,
  onTimeFilter,
  onStatusFilter,
  children
}: TradeMeListingsLayoutProps) {
  const [searchParams, setSearchParams] = useSearchParams();
  const [searchTerm, setSearchTerm] = useState('');
  const [filter, setFilter] = useState(searchParams.get('filter') || 'all');
  const [sort, setSort] = useState(searchParams.get('sort') || 'default');
  const [timeFilter, setTimeFilter] = useState<number | null>(null);
  const [statusFilter, setStatusFilter] = useState<SaleStatus | null>(null);
  const [activeFilters, setActiveFilters] = useState<number>(0);
  const [showFilters, setShowFilters] = useState(false);

  // Count active filters
  useEffect(() => {
    let count = 0;
    if (filter !== 'all') count++;
    if (sort !== 'default') count++;
    if (timeFilter !== null) count++;
    if (statusFilter !== null) count++;
    if (searchTerm.trim() !== '') count++;
    setActiveFilters(count);
  }, [filter, sort, timeFilter, statusFilter, searchTerm]);

  // Handle search input
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Use a ref to track the previous search term
  const prevSearchTermRef = React.useRef<string>('');

  // Handle search submit with debounce
  useEffect(() => {
    // Skip if the search term is the same as before
    if (searchTerm === prevSearchTermRef.current) return;

    const timer = setTimeout(() => {
      console.log('Debounced search for:', searchTerm);
      prevSearchTermRef.current = searchTerm;
      onSearch(searchTerm);
    }, 500); // 500ms debounce

    return () => clearTimeout(timer);
  }, [searchTerm, onSearch]);

  // Handle filter change
  const handleFilterChange = (value: string) => {
    setFilter(value);
    setSearchParams(prev => {
      prev.set('filter', value);
      return prev;
    });
    onFilter(value);
  };

  // Handle sort change
  const handleSortChange = (value: string) => {
    setSort(value);
    setSearchParams(prev => {
      prev.set('sort', value);
      return prev;
    });
    onSort(value);
  };

  // Handle time filter change
  const handleTimeFilterChange = (days: number | null) => {
    setTimeFilter(days);
    if (onTimeFilter) {
      onTimeFilter(days || 0);
    }
  };

  // Handle status filter change
  const handleStatusFilterChange = (status: SaleStatus | null) => {
    setStatusFilter(status);
    if (onStatusFilter) {
      onStatusFilter(status);
    }
  };

  // Clear all filters
  const clearAllFilters = () => {
    setSearchTerm('');
    setFilter('all');
    setSort('default');
    setTimeFilter(null);
    setStatusFilter(null);
    onSearch('');
    onFilter('all');
    onSort('default');
    if (onTimeFilter) onTimeFilter(0);
    if (onStatusFilter) onStatusFilter(null);
    setSearchParams({});
  };

  // Render filter options based on listing type
  const renderFilterOptions = () => {
    switch (type) {
      case 'selling':
        return (
          <>
            <SelectItem value="all">All Current Listings</SelectItem>
            <SelectItem value="with_bids">Listings with Bids</SelectItem>
            <SelectItem value="reserve_met">Reserve Met</SelectItem>
            <SelectItem value="reserve_not_met">Reserve Not Met</SelectItem>
            <SelectItem value="with_questions">Listings with Questions</SelectItem>
            <SelectItem value="closing_soon">Closing Soon</SelectItem>
            <SelectItem value="new">Newly Listed</SelectItem>
          </>
        );
      case 'sold':
        return (
          <>
            <SelectItem value="all">All Sold Listings</SelectItem>
            <SelectItem value="payment_received">Payment Received</SelectItem>
            <SelectItem value="awaiting_payment">Awaiting Payment</SelectItem>
            <SelectItem value="feedback_needed">Feedback to Place</SelectItem>
            <SelectItem value="awaiting_pickup">Awaiting Pickup</SelectItem>
            <SelectItem value="awaiting_packaging">Awaiting Packaging</SelectItem>
          </>
        );
      case 'unsold':
        return (
          <>
            <SelectItem value="all">All Unsold Listings</SelectItem>
            <SelectItem value="with_bids">Had Bids</SelectItem>
            <SelectItem value="no_bids">No Bids</SelectItem>
            <SelectItem value="recent">Recently Ended</SelectItem>
          </>
        );
      default:
        return <SelectItem value="all">All Listings</SelectItem>;
    }
  };

  // Render sort options based on listing type
  const renderSortOptions = () => {
    const commonOptions = (
      <>
        <SelectItem value="default">Default</SelectItem>
        <SelectItem value="title_asc">Title (A-Z)</SelectItem>
        <SelectItem value="title_desc">Title (Z-A)</SelectItem>
        <SelectItem value="price_asc">Price (Low to High)</SelectItem>
        <SelectItem value="price_desc">Price (High to Low)</SelectItem>
      </>
    );

    switch (type) {
      case 'selling':
        return (
          <>
            {commonOptions}
            <SelectItem value="end_date_asc">Ending Soon</SelectItem>
            <SelectItem value="bids_desc">Most Bids</SelectItem>
            <SelectItem value="watchers_desc">Most Watchers</SelectItem>
            <SelectItem value="views_desc">Most Views</SelectItem>
            <SelectItem value="created_desc">Newest</SelectItem>
            <SelectItem value="created_asc">Oldest</SelectItem>
          </>
        );
      case 'sold':
        return (
          <>
            {commonOptions}
            <SelectItem value="end_date_desc">Recently Sold</SelectItem>
            <SelectItem value="end_date_asc">Oldest Sold</SelectItem>
            <SelectItem value="bids_desc">Most Bids</SelectItem>
          </>
        );
      case 'unsold':
        return (
          <>
            {commonOptions}
            <SelectItem value="end_date_desc">Recently Ended</SelectItem>
            <SelectItem value="end_date_asc">Oldest Ended</SelectItem>
            <SelectItem value="bids_desc">Most Bids</SelectItem>
          </>
        );
      default:
        return commonOptions;
    }
  };

  // Render advanced filters based on listing type
  const renderAdvancedFilters = () => {
    switch (type) {
      case 'selling':
        return (
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">Price Range</h4>
              <div className="flex items-center gap-2">
                <Input
                  type="number"
                  placeholder="Min"
                  className="w-24"
                  disabled
                />
                <span>to</span>
                <Input
                  type="number"
                  placeholder="Max"
                  className="w-24"
                  disabled
                />
                <Button variant="outline" size="sm" disabled>Apply</Button>
              </div>
              <p className="text-xs text-muted-foreground mt-1">Price filtering coming soon</p>
            </div>
            <div>
              <h4 className="font-medium mb-2">Listing Status</h4>
              <div className="grid grid-cols-1 gap-2">
                <Button
                  variant={filter === 'with_bids' ? "default" : "outline"}
                  size="sm"
                  className="justify-start"
                  onClick={() => handleFilterChange(filter === 'with_bids' ? 'all' : 'with_bids')}
                >
                  With Bids
                </Button>
                <Button
                  variant={filter === 'reserve_met' ? "default" : "outline"}
                  size="sm"
                  className="justify-start"
                  onClick={() => handleFilterChange(filter === 'reserve_met' ? 'all' : 'reserve_met')}
                >
                  Reserve Met
                </Button>
                <Button
                  variant={filter === 'with_questions' ? "default" : "outline"}
                  size="sm"
                  className="justify-start"
                  onClick={() => handleFilterChange(filter === 'with_questions' ? 'all' : 'with_questions')}
                >
                  With Questions
                </Button>
                <Button
                  variant={filter === 'closing_soon' ? "default" : "outline"}
                  size="sm"
                  className="justify-start"
                  onClick={() => handleFilterChange(filter === 'closing_soon' ? 'all' : 'closing_soon')}
                >
                  Closing Soon
                </Button>
              </div>
            </div>
          </div>
        );
      case 'sold':
        return (
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">Time Period</h4>
              <div className="grid grid-cols-1 gap-2">
                <Button
                  variant={timeFilter === 45 ? "default" : "outline"}
                  size="sm"
                  className="justify-start"
                  onClick={() => handleTimeFilterChange(timeFilter === 45 ? null : 45)}
                >
                  <Calendar className="mr-2 h-4 w-4" />
                  Last 45 days
                </Button>
                <Button
                  variant={timeFilter === 7 ? "default" : "outline"}
                  size="sm"
                  className="justify-start"
                  onClick={() => handleTimeFilterChange(timeFilter === 7 ? null : 7)}
                >
                  <Calendar className="mr-2 h-4 w-4" />
                  Last 7 days
                </Button>
                <Button
                  variant={timeFilter === 5 ? "default" : "outline"}
                  size="sm"
                  className="justify-start"
                  onClick={() => handleTimeFilterChange(timeFilter === 5 ? null : 5)}
                >
                  <Calendar className="mr-2 h-4 w-4" />
                  Last 5 days
                </Button>
                <Button
                  variant={timeFilter === 3 ? "default" : "outline"}
                  size="sm"
                  className="justify-start"
                  onClick={() => handleTimeFilterChange(timeFilter === 3 ? null : 3)}
                >
                  <Calendar className="mr-2 h-4 w-4" />
                  Last 3 days
                </Button>
                <Button
                  variant={timeFilter === 1 ? "default" : "outline"}
                  size="sm"
                  className="justify-start"
                  onClick={() => handleTimeFilterChange(timeFilter === 1 ? null : 1)}
                >
                  <Calendar className="mr-2 h-4 w-4" />
                  Last 24 hours
                </Button>
              </div>
            </div>
            <Separator />
            <div>
              <h4 className="font-medium mb-2">Sale Status</h4>
              <div className="grid grid-cols-1 gap-2">
                <Button
                  variant={statusFilter === 'awaiting_payment' ? "default" : "outline"}
                  size="sm"
                  className="justify-start"
                  onClick={() => handleStatusFilterChange(statusFilter === 'awaiting_payment' ? null : 'awaiting_payment')}
                >
                  <CreditCard className="mr-2 h-4 w-4" />
                  Payment Instructions Unsent
                  <Badge variant="outline" className="ml-auto">
                    {listings.filter(l => l.soldStatus === 'awaiting_payment').length}
                  </Badge>
                </Button>
                <Button
                  variant={statusFilter === 'no_status' ? "default" : "outline"}
                  size="sm"
                  className="justify-start"
                  onClick={() => handleStatusFilterChange(statusFilter === 'no_status' ? null : 'no_status')}
                >
                  <Star className="mr-2 h-4 w-4" />
                  Feedback to Place
                  <Badge variant="outline" className="ml-auto">
                    {listings.filter(l => l.soldStatus === 'no_status' || l.soldStatus === null).length}
                  </Badge>
                </Button>
                <Button
                  variant={statusFilter === 'awaiting_pickup_paid' ? "default" : "outline"}
                  size="sm"
                  className="justify-start"
                  onClick={() => handleStatusFilterChange(statusFilter === 'awaiting_pickup_paid' ? null : 'awaiting_pickup_paid')}
                >
                  <Truck className="mr-2 h-4 w-4" />
                  Awaiting Pickup (Paid)
                  <Badge variant="outline" className="ml-auto">
                    {listings.filter(l => l.soldStatus === 'awaiting_pickup_paid').length}
                  </Badge>
                </Button>
                <Button
                  variant={statusFilter === 'awaiting_packaging' ? "default" : "outline"}
                  size="sm"
                  className="justify-start"
                  onClick={() => handleStatusFilterChange(statusFilter === 'awaiting_packaging' ? null : 'awaiting_packaging')}
                >
                  <Package className="mr-2 h-4 w-4" />
                  Awaiting Packaging
                  <Badge variant="outline" className="ml-auto">
                    {listings.filter(l => l.soldStatus === 'awaiting_packaging').length}
                  </Badge>
                </Button>
                <Button
                  variant={statusFilter === 'refunded' ? "default" : "outline"}
                  size="sm"
                  className="justify-start"
                  onClick={() => handleStatusFilterChange(statusFilter === 'refunded' ? null : 'refunded')}
                >
                  <RotateCcw className="mr-2 h-4 w-4" />
                  Refunded
                  <Badge variant="outline" className="ml-auto">
                    {listings.filter(l => l.soldStatus === 'refunded').length}
                  </Badge>
                </Button>
                <Button
                  variant={statusFilter === 'cancelled' ? "default" : "outline"}
                  size="sm"
                  className="justify-start"
                  onClick={() => handleStatusFilterChange(statusFilter === 'cancelled' ? null : 'cancelled')}
                >
                  <XCircle className="mr-2 h-4 w-4" />
                  Cancelled
                  <Badge variant="outline" className="ml-auto">
                    {listings.filter(l => l.soldStatus === 'cancelled').length}
                  </Badge>
                </Button>
                <Button
                  variant={statusFilter === 'email_sent' ? "default" : "outline"}
                  size="sm"
                  className="justify-start"
                  onClick={() => handleStatusFilterChange(statusFilter === 'email_sent' ? null : 'email_sent')}
                >
                  <Mail className="mr-2 h-4 w-4" />
                  Email Sent
                  <Badge variant="outline" className="ml-auto">
                    {listings.filter(l => l.soldStatus === 'email_sent').length}
                  </Badge>
                </Button>
              </div>
            </div>
          </div>
        );
      case 'unsold':
        return (
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">Time Period</h4>
              <div className="grid grid-cols-1 gap-2">
                <Button
                  variant={timeFilter === 30 ? "default" : "outline"}
                  size="sm"
                  className="justify-start"
                  onClick={() => handleTimeFilterChange(timeFilter === 30 ? null : 30)}
                >
                  <Calendar className="mr-2 h-4 w-4" />
                  Last 30 days
                </Button>
                <Button
                  variant={timeFilter === 7 ? "default" : "outline"}
                  size="sm"
                  className="justify-start"
                  onClick={() => handleTimeFilterChange(timeFilter === 7 ? null : 7)}
                >
                  <Calendar className="mr-2 h-4 w-4" />
                  Last 7 days
                </Button>
                <Button
                  variant={timeFilter === 3 ? "default" : "outline"}
                  size="sm"
                  className="justify-start"
                  onClick={() => handleTimeFilterChange(timeFilter === 3 ? null : 3)}
                >
                  <Calendar className="mr-2 h-4 w-4" />
                  Last 3 days
                </Button>
              </div>
            </div>
            <Separator />
            <div>
              <h4 className="font-medium mb-2">Listing Status</h4>
              <div className="grid grid-cols-1 gap-2">
                <Button
                  variant={filter === 'with_bids' ? "default" : "outline"}
                  size="sm"
                  className="justify-start"
                  onClick={() => handleFilterChange(filter === 'with_bids' ? 'all' : 'with_bids')}
                >
                  Had Bids
                </Button>
                <Button
                  variant={filter === 'no_bids' ? "default" : "outline"}
                  size="sm"
                  className="justify-start"
                  onClick={() => handleFilterChange(filter === 'no_bids' ? 'all' : 'no_bids')}
                >
                  No Bids
                </Button>
                <Button
                  variant={filter === 'recent' ? "default" : "outline"}
                  size="sm"
                  className="justify-start"
                  onClick={() => handleFilterChange(filter === 'recent' ? 'all' : 'recent')}
                >
                  Recently Ended
                </Button>
              </div>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header with title */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold">{title}</h1>
          <div className="text-muted-foreground h-6 min-w-[150px]">
            {loading ? (
              <span>Loading...</span>
            ) : (
              <span className="transition-opacity duration-300">
                {totalItems} {type === 'selling' ? 'active' : type} {totalItems === 1 ? 'listing' : 'listings'}
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Filters and search bar */}
      <div className="bg-card rounded-lg border shadow-sm">
        <div className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            {/* Search bar */}
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="text"
                placeholder="Search listings..."
                className="pl-8"
                value={searchTerm}
                onChange={handleSearchChange}
              />
            </div>

            {/* Filter dropdown */}
            <div className="flex gap-2">
              <Select value={filter} onValueChange={handleFilterChange}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>Filter By</SelectLabel>
                    {renderFilterOptions()}
                  </SelectGroup>
                </SelectContent>
              </Select>

              {/* Sort dropdown */}
              <Select value={sort} onValueChange={handleSortChange}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Sort" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>Sort By</SelectLabel>
                    {renderSortOptions()}
                  </SelectGroup>
                </SelectContent>
              </Select>

              {/* Advanced filters button */}
              <Popover open={showFilters} onOpenChange={setShowFilters}>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="relative">
                    <Filter className="h-4 w-4 mr-2" />
                    Filters
                    {activeFilters > 0 && (
                      <Badge className="absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center">
                        {activeFilters}
                      </Badge>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80 p-0" align="end">
                  <div className="p-4 border-b">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">Filters</h4>
                      <Button variant="ghost" size="sm" onClick={clearAllFilters}>
                        Clear All
                      </Button>
                    </div>
                  </div>
                  <ScrollArea className="h-[400px] p-4">
                    {renderAdvancedFilters()}
                  </ScrollArea>
                </PopoverContent>
              </Popover>

              {/* Sync button */}
              <Button
                variant="default"
                onClick={onSync}
                disabled={syncing}
              >
                {syncing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Syncing...
                  </>
                ) : (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Sync
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>

        {/* Active filters display */}
        {activeFilters > 0 && (
          <div className="px-4 py-2 border-t bg-muted/40 flex flex-wrap gap-2 items-center">
            <span className="text-sm text-muted-foreground">Active filters:</span>
            {filter !== 'all' && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Filter: {filter.replace('_', ' ')}
                <XCircle
                  className="h-3 w-3 cursor-pointer"
                  onClick={() => handleFilterChange('all')}
                />
              </Badge>
            )}
            {sort !== 'default' && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Sort: {sort.replace('_', ' ')}
                <XCircle
                  className="h-3 w-3 cursor-pointer"
                  onClick={() => handleSortChange('default')}
                />
              </Badge>
            )}
            {timeFilter !== null && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Last {timeFilter} {timeFilter === 1 ? 'day' : 'days'}
                <XCircle
                  className="h-3 w-3 cursor-pointer"
                  onClick={() => handleTimeFilterChange(null)}
                />
              </Badge>
            )}
            {statusFilter !== null && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Status: {statusFilter.replace('_', ' ')}
                <XCircle
                  className="h-3 w-3 cursor-pointer"
                  onClick={() => handleStatusFilterChange(null)}
                />
              </Badge>
            )}
            {searchTerm.trim() !== '' && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Search: {searchTerm}
                <XCircle
                  className="h-3 w-3 cursor-pointer"
                  onClick={() => {
                    setSearchTerm('');
                    onSearch('');
                  }}
                />
              </Badge>
            )}
            <Button
              variant="ghost"
              size="sm"
              className="ml-auto text-xs"
              onClick={clearAllFilters}
            >
              Clear All
            </Button>
          </div>
        )}
      </div>

      {/* Main content */}
      <div className="space-y-6">
        {children}
      </div>
    </div>
  );
}
