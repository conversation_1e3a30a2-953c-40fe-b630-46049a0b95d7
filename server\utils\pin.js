const bcrypt = require('bcrypt');

/**
 * Hashes the PIN using bcrypt algorithm
 * @param {string} pin - The PIN to hash
 * @return {string} PIN hash
 */
const generatePinHash = async (pin) => {
  const salt = await bcrypt.genSalt();
  const hash = await bcrypt.hash(pin, salt);
  return hash;
};

/**
 * Validates the PIN against the hash
 * @param {string} pin - The PIN to verify
 * @param {string} hash - PIN hash to verify against
 * @return {boolean} True if the PIN matches the hash, false otherwise
 */
const validatePin = async (pin, hash) => {
  if (!hash) return false;
  const result = await bcrypt.compare(pin, hash);
  return result;
};

/**
 * Validates PIN complexity requirements
 * @param {string} pin - The PIN to validate
 * @return {Object} Validation result with success flag and message
 */
const validatePinComplexity = (pin) => {
  // PIN must be at least 6 digits
  if (!pin || pin.length < 6) {
    return {
      success: false,
      message: 'PIN must be at least 6 digits'
    };
  }

  // PIN must contain only numbers
  if (!/^\d+$/.test(pin)) {
    return {
      success: false,
      message: 'PIN must contain only numbers'
    };
  }

  return {
    success: true
  };
};

/**
 * Checks if PIN is expired
 * @param {Date} pinCreatedAt - Date when PIN was created
 * @return {boolean} True if PIN is expired, false otherwise
 */
const isPinExpired = (pinCreatedAt) => {
  if (!pinCreatedAt) return false;
  
  const expiryDays = 90; // 90 days expiry period
  const expiryDate = new Date(pinCreatedAt);
  expiryDate.setDate(expiryDate.getDate() + expiryDays);
  
  return new Date() > expiryDate;
};

/**
 * Checks if PIN can be reused based on previous PINs
 * @param {string} newPin - New PIN to check
 * @param {Array<string>} previousPins - Array of previous PIN hashes
 * @return {Promise<boolean>} True if PIN can be reused, false otherwise
 */
const canReusePin = async (newPin, previousPins) => {
  if (!previousPins || previousPins.length === 0) return true;
  
  // Check against last 4 PINs (or fewer if not enough history)
  const pinsToCheck = previousPins.slice(-4);
  
  for (const pinHash of pinsToCheck) {
    const matches = await validatePin(newPin, pinHash);
    if (matches) return false;
  }
  
  return true;
};

/**
 * Gets lockout duration based on failed attempts
 * @param {number} failedAttempts - Number of consecutive failed attempts
 * @return {number} Lockout duration in minutes
 */
const getLockoutDuration = (failedAttempts) => {
  if (failedAttempts < 5) return 0;
  
  const lockoutMap = {
    5: 1,    // 1 minute
    6: 3,    // 3 minutes
    7: 10,   // 10 minutes
    8: 30,   // 30 minutes
    9: 60,   // 60 minutes
  };
  
  return failedAttempts >= 10 ? 60 : (lockoutMap[failedAttempts] || 0);
};

/**
 * Calculates PIN expiry date
 * @param {Date} pinCreatedAt - Date when PIN was created
 * @return {Date} PIN expiry date
 */
const getPinExpiryDate = (pinCreatedAt) => {
  if (!pinCreatedAt) return null;
  
  const expiryDays = 90; // 90 days expiry period
  const expiryDate = new Date(pinCreatedAt);
  expiryDate.setDate(expiryDate.getDate() + expiryDays);
  
  return expiryDate;
};

module.exports = {
  generatePinHash,
  validatePin,
  validatePinComplexity,
  isPinExpired,
  canReusePin,
  getLockoutDuration,
  getPinExpiryDate
};
