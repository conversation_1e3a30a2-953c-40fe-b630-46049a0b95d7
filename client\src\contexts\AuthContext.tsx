import { createContext, useContext, useState, ReactNode, useEffect, useRef } from "react";
import { useNavigate } from 'react-router-dom';
import { login as apiLogin, register as apiRegister } from "@/api/auth";
import { getCurrentUser, User } from "@/api/user";
import { IdleTimer } from '../utils/idleTimer';
import { toast } from "sonner";
import axios from 'axios';
import api, { setGlobalAuthFailureHandler } from '@/api/api';
import { ReAuthModal } from "@/components/ReAuthModal";
import { fetchCsrfToken } from "@/utils/csrfUtils";

type AuthContextType = {
  isAuthenticated: boolean;
  user: User | null;
  login: (emailOrUsername: string, password: string, pin?: string) => Promise<void>;
  register: (email: string, password: string) => Promise<void>;
  logout: () => void;
  refreshUserData: () => Promise<User | null>;
};

const AuthContext = createContext<AuthContextType | null>(null);

export function AuthProvider({ children }: { children: ReactNode }) {
  const navigate = useNavigate();
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(() => {
    const token = localStorage.getItem("accessToken");
    return !!token;
  });
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [showReAuthModal, setShowReAuthModal] = useState(false);
  const [sessionExpired, setSessionExpired] = useState(false);
  const idleTimerRef = useRef<IdleTimer | null>(null);

  const refreshUserData = async () => {
    try {
      const userData = await getCurrentUser();
      setUser(userData);
      setIsAuthenticated(true);
      return userData;
    } catch (error: any) {
      console.error('Error refreshing user data:', error);
      if (error?.response?.status === 401) {
        const refreshToken = localStorage.getItem('refreshToken');
        if (!refreshToken) {
          throw error;
        }
        return null;
      }
      throw error;
    }
  };

  // Handle idle timeout
  const handleIdleTimeout = () => {
    console.log('Session expired due to inactivity');
    // Mark session as expired and show re-authentication modal
    setSessionExpired(true);
    setShowReAuthModal(true);
  };

  // Handle authentication failures from API calls
  const handleAuthFailure = () => {
    console.log('Authentication failure detected');
    // Only show modal if not already showing to prevent multiple modals
    if (!showReAuthModal && !sessionExpired) {
      console.log('Current user data:', user);
      toast.error('Your session has expired. Please re-authenticate to continue.');
      // Mark session as expired and show re-authentication modal
      // Note: We keep user data available for re-authentication
      setSessionExpired(true);
      setShowReAuthModal(true);
    }
  };

  // Initialize or reset the idle timer
  const setupIdleTimer = () => {
    // Clean up any existing timer
    if (idleTimerRef.current) {
      idleTimerRef.current.cleanup();
    }

    // Create a new idle timer with 10 minute timeout (600000 ms)
    idleTimerRef.current = new IdleTimer(600000, handleIdleTimeout);
  };

  // Initialize auth state from localStorage on mount
  useEffect(() => {
    // Set up global auth failure handler
    setGlobalAuthFailureHandler(handleAuthFailure);

    const initializeAuth = async () => {
      const token = localStorage.getItem('accessToken');
      if (token) {
        try {
          // Set token for both axios instances
          api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
          axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

          await refreshUserData();

          // Fetch CSRF token for authenticated user
          // We do this after authentication to ensure we have proper permissions
          fetchCsrfToken().then(token => {
            if (token) {
            } else {
              console.warn('Failed to fetch CSRF token during initialization');
            }
          });

          // Set up idle timer after successful authentication
          setupIdleTimer();
        } catch (error) {
          console.error('Auth initialization error:', error);
          localStorage.removeItem('accessToken');
          localStorage.removeItem('refreshToken');
          setIsAuthenticated(false);
          setUser(null);
        }
      } else {
        setIsAuthenticated(false);
      }
      setLoading(false);
    };

    initializeAuth();

    // Clean up idle timer on unmount
    return () => {
      if (idleTimerRef.current) {
        idleTimerRef.current.cleanup();
      }
    };
  }, []);

  const login = async (emailOrUsername: string, password: string, pin?: string) => {
    try {
      // Determine if we're doing PIN login or password login
      const loginData = pin
        ? { pin } // PIN-only login, no username/email required
        : { emailOrUsername, password };

      const response = await apiLogin(loginData);

      if (!response?.success) {
        throw new Error(response?.message || 'Login failed');
      }

      localStorage.setItem("refreshToken", response.refreshToken);
      localStorage.setItem("accessToken", response.accessToken);

      api.defaults.headers.common['Authorization'] = `Bearer ${response.accessToken}`;
      axios.defaults.headers.common['Authorization'] = `Bearer ${response.accessToken}`;

      setIsAuthenticated(true);
      setUser(response.user);

      // Fetch CSRF token after successful login
      fetchCsrfToken().then(token => {
        if (token) {
          console.log('CSRF token fetched after login');
        } else {
          console.warn('Failed to fetch CSRF token after login');
        }
      });

      // Reset idle timer on successful login
      setupIdleTimer();

      // Hide re-auth modal if it was showing
      setShowReAuthModal(false);
    } catch (error) {
      localStorage.removeItem("refreshToken");
      localStorage.removeItem("accessToken");
      delete api.defaults.headers.common['Authorization'];
      delete axios.defaults.headers.common['Authorization'];
      setIsAuthenticated(false);
      setUser(null);
      throw error;
    }
  };

  const logout = () => {
    // Clean up idle timer
    if (idleTimerRef.current) {
      idleTimerRef.current.cleanup();
      idleTimerRef.current = null;
    }

    localStorage.removeItem("refreshToken");
    localStorage.removeItem("accessToken");
    delete api.defaults.headers.common['Authorization'];
    delete axios.defaults.headers.common['Authorization'];
    setIsAuthenticated(false);
    setUser(null);
    setShowReAuthModal(false);
    setSessionExpired(false);
    navigate('/login', { replace: true });
  };

  const value = {
    isAuthenticated: isAuthenticated && !sessionExpired, // Ensure user is considered unauthenticated when session expired
    user, // Keep user data available for re-authentication
    login,
    register: apiRegister,
    logout,
    refreshUserData
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  // Handle successful re-authentication
  const handleReAuthSuccess = () => {
    setShowReAuthModal(false);
    setSessionExpired(false);
    // Reset the idle timer
    setupIdleTimer();
  };

  return (
    <AuthContext.Provider value={value}>
      {/* Session expired overlay to prevent interaction */}
      {sessionExpired && (
        <div
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40"
          style={{ pointerEvents: 'all' }}
        />
      )}
      {children}
      <ReAuthModal
        isOpen={showReAuthModal}
        onClose={() => {}} // Non-dismissible modal
        onSuccess={handleReAuthSuccess}
        onLogout={logout}
      />
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
