import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { HIDDeviceInfo as HIDDeviceInfoType } from '@/utils/hidUtils';
import { Usb, Info } from 'lucide-react';

interface HIDDeviceInfoProps {
  hidDevice: HIDDeviceInfoType | null;
}

const HIDDeviceInfoCard = ({ hidDevice }: HIDDeviceInfoProps) => {
  const [expandedCollection, setExpandedCollection] = useState<number | null>(null);

  if (!hidDevice) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>HID Device Information</CardTitle>
          <CardDescription>
            Connect a controller via HID to view detailed information
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center text-muted-foreground">
          <Usb className="mx-auto h-12 w-12 mb-2 opacity-50" />
          <p>No HID device connected</p>
        </CardContent>
      </Card>
    );
  }

  const toggleCollection = (index: number) => {
    if (expandedCollection === index) {
      setExpandedCollection(null);
    } else {
      setExpandedCollection(index);
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>HID Device Information</CardTitle>
          <Badge variant="outline">
            {hidDevice.connected ? 'Connected' : 'Disconnected'}
          </Badge>
        </div>
        <CardDescription>
          Detailed information about the connected HID device
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <h3 className="text-sm font-medium mb-1">Product Name</h3>
            <p className="text-sm">{hidDevice.productName}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium mb-1">Vendor/Product ID</h3>
            <p className="text-sm">
              {hidDevice.vendorId.toString(16).padStart(4, '0').toUpperCase()}:
              {hidDevice.productId.toString(16).padStart(4, '0').toUpperCase()}
            </p>
          </div>
        </div>

        <div>
          <h3 className="text-sm font-medium mb-2">Collections ({hidDevice.collections.length})</h3>
          <div className="space-y-2 max-h-60 overflow-y-auto pr-2">
            {hidDevice.collections.map((collection, index) => (
              <div
                key={index}
                className="border rounded-md p-3 cursor-pointer hover:bg-muted/50 transition-colors"
                onClick={() => toggleCollection(index)}
              >
                <div className="flex justify-between items-center">
                  <div>
                    <span className="font-medium">Usage Page: </span>
                    <code className="text-xs bg-muted px-1 py-0.5 rounded">
                      0x{collection.usagePage.toString(16).padStart(4, '0').toUpperCase()}
                    </code>
                  </div>
                  <div>
                    <span className="font-medium">Usage: </span>
                    <code className="text-xs bg-muted px-1 py-0.5 rounded">
                      0x{collection.usage.toString(16).padStart(4, '0').toUpperCase()}
                    </code>
                  </div>
                </div>

                {expandedCollection === index && (
                  <div className="mt-3 space-y-3 border-t pt-3">
                    {collection.inputReports.length > 0 && (
                      <div>
                        <h4 className="text-xs font-medium mb-1">Input Reports ({collection.inputReports.length})</h4>
                        <div className="space-y-1">
                          {collection.inputReports.map((report, reportIndex) => (
                            <div key={reportIndex} className="text-xs pl-2 border-l-2 border-primary/30">
                              <div className="flex justify-between">
                                <span>Report ID: {report.reportId}</span>
                                <span>Items: {report.items.length}</span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {collection.outputReports.length > 0 && (
                      <div>
                        <h4 className="text-xs font-medium mb-1">Output Reports ({collection.outputReports.length})</h4>
                        <div className="space-y-1">
                          {collection.outputReports.map((report, reportIndex) => (
                            <div key={reportIndex} className="text-xs pl-2 border-l-2 border-primary/30">
                              <div className="flex justify-between">
                                <span>Report ID: {report.reportId}</span>
                                <span>Items: {report.items.length}</span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {collection.featureReports.length > 0 && (
                      <div>
                        <h4 className="text-xs font-medium mb-1">Feature Reports ({collection.featureReports.length})</h4>
                        <div className="space-y-1">
                          {collection.featureReports.map((report, reportIndex) => (
                            <div key={reportIndex} className="text-xs pl-2 border-l-2 border-primary/30">
                              <div className="flex justify-between">
                                <span>Report ID: {report.reportId}</span>
                                <span>Items: {report.items.length}</span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {collection.children.length > 0 && (
                      <div>
                        <h4 className="text-xs font-medium mb-1">Child Collections ({collection.children.length})</h4>
                        <div className="space-y-1">
                          {collection.children.map((child, childIndex) => (
                            <div key={childIndex} className="text-xs pl-2 border-l-2 border-primary/30">
                              <div className="flex justify-between">
                                <span>Usage Page: 0x{child.usagePage.toString(16).padStart(4, '0').toUpperCase()}</span>
                                <span>Usage: 0x{child.usage.toString(16).padStart(4, '0').toUpperCase()}</span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        <div className="flex items-center text-xs text-muted-foreground">
          <Info className="h-3 w-3 mr-1" />
          <span>Click on a collection to view details</span>
        </div>
      </CardContent>
    </Card>
  );
};

export default HIDDeviceInfoCard;
