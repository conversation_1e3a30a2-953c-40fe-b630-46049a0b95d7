/**
 * TradeMe Category Service
 *
 * Handles syncing and managing TradeMe categories
 */

const axios = require('axios');
const TradeMeSettings = require('../models/TradeMeSettings');
const TradeMeCategory = require('../models/TradeMeCategory');
const { getOAuth, TRADEME_API } = require('./tradeMeUtils');

/**
 * Sync categories from TradeMe API
 * @param {Object} options - Options for the sync operation
 * @param {number} options.maxPagesPerEndpoint - Maximum number of pages to fetch
 * @param {Object} options.syncLog - Sync log object for tracking
 * @returns {Promise<Object>} Result of the sync operation
 */
async function syncCategoriesFromTradeMe(options = {}) {
  try {
    // Default options
    const { syncLog = null } = options;

    // Track API calls and stats
    let apiCallCount = 0;

    // Track sync statistics
    const stats = {
      itemsProcessed: 0,
      newItems: 0,
      updatedItems: 0,
      apiCallsMade: 0
    };

    // Get current settings
    let settings = await TradeMeSettings.findOne();

    if (!settings || !settings.connected) {
      return {
        success: false,
        error: 'No active TradeMe connection'
      };
    }

    const environment = settings.environment;
    const accessToken = settings.accessToken;
    const accessTokenSecret = settings.accessSecret;

    // Create OAuth instance
    const oauth = getOAuth(environment);
    const apiEndpoints = TRADEME_API[environment];

    // Helper function to make authenticated API calls with ETag support
    const makeAuthenticatedRequest = async (endpoint, existingETag = null) => {
      let headers = {};

      // If we have an ETag, add the If-None-Match header
      if (existingETag) {
        // Make sure the ETag is properly formatted with quotes
        let etag = existingETag;

        // If the ETag doesn't already have quotes, add them
        if (!etag.startsWith('"') && !etag.endsWith('"')) {
          etag = `"${etag}"`;
        }

        headers['If-None-Match'] = etag;
        console.log(`Using ETag for ${endpoint}: ${etag}`);
      }

      // Add OAuth headers
      const oauthHeaders = oauth.toHeader(
        oauth.authorize(
          {
            url: `${apiEndpoints.apiBase}${endpoint}`,
            method: 'GET'
          },
          {
            key: accessToken,
            secret: accessTokenSecret
          }
        )
      );

      // Merge headers
      headers = { ...headers, ...oauthHeaders };

      apiCallCount++;
      stats.apiCallsMade++;
      console.log(`TradeMe API Call #${apiCallCount}: ${endpoint}${existingETag ? ' (with ETag)' : ''}`);

      const url = `${apiEndpoints.apiBase}${endpoint}`;

      // Update the syncLog if provided
      if (syncLog) {
        syncLog.apiCallsMade = stats.apiCallsMade;
        await syncLog.save();
      }

      try {
        const response = await axios({
          method: 'get',
          url: url,
          headers: headers,
          validateStatus: function (status) {
            // Accept 304 Not Modified as a valid status
            return (status >= 200 && status < 300) || status === 304;
          }
        });

        // Log the response status and headers for debugging
        console.log(`Response status for ${endpoint}: ${response.status}`);
        console.log(`Response headers for ${endpoint}:`, JSON.stringify(response.headers));

        // If we got a new ETag, log it
        if (response.headers.etag) {
          // Store the ETag without quotes for consistency
          let etag = response.headers.etag;
          if (etag.startsWith('"') && etag.endsWith('"')) {
            etag = etag.substring(1, etag.length - 1);
          }

          console.log(`Received new ETag for ${endpoint}: ${etag}`);

          // Note: We no longer save ETags in a separate collection
          // They will be saved directly in the category documents by the calling code
        } else {
          console.log(`No ETag received for ${endpoint}`);
        }

        // If we got a 304 Not Modified, return the cached data
        if (response.status === 304) {
          console.log(`Using cached data for ${endpoint} (304 Not Modified)`);
          // For categories, we'll just continue with what's in the database
          // Return a mock response with a flag indicating it's from cache
          return {
            data: null,
            status: 304,
            fromCache: true,
            headers: response.headers
          };
        }

        return response;
      } catch (error) {
        console.error(`Error making authenticated request to ${endpoint}:`, error.message);
        throw error;
      }
    };

    // Fetch root categories
    console.log('Starting to fetch TradeMe categories...');

    // Check if we have a special category document for storing the root categories ETag
    let rootCategoriesETag = await TradeMeCategory.findOne({ categoryId: 'root_categories_etag' });

    // If we don't have one, create it
    if (!rootCategoriesETag) {
      rootCategoriesETag = new TradeMeCategory({
        categoryId: 'root_categories_etag',
        name: 'Root Categories ETag',
        path: 'Root Categories ETag',
        isLeaf: false,
        etag: null,
        lastSynced: new Date()
      });
      await rootCategoriesETag.save();
      console.log('Created special category document for root categories ETag');
    }

    const rootCategoriesResponse = await makeAuthenticatedRequest('/Categories.json',
      rootCategoriesETag.etag
    );

    // If we got a 304 Not Modified, we can skip processing
    if (rootCategoriesResponse.status === 304) {
      console.log('Categories have not changed since last sync (304 Not Modified)');
      console.log('Skipping full category sync process');

      // Update the last synced timestamp for all categories
      const updateResult = await TradeMeCategory.updateMany({}, { lastSynced: new Date() });
      console.log(`Updated lastSynced timestamp for ${updateResult.modifiedCount} categories`);

      return {
        success: true,
        message: 'Categories have not changed since last sync',
        stats: {
          itemsProcessed: 0,
          newItems: 0,
          updatedItems: 0,
          apiCallsMade: stats.apiCallsMade,
          cacheHit: true
        }
      };
    } else if (rootCategoriesResponse.headers && rootCategoriesResponse.headers.etag) {
      // Save the new ETag for root categories
      let etag = rootCategoriesResponse.headers.etag;
      if (etag.startsWith('"') && etag.endsWith('"')) {
        etag = etag.substring(1, etag.length - 1);
      }

      rootCategoriesETag.etag = etag;
      rootCategoriesETag.lastSynced = new Date();
      await rootCategoriesETag.save();
      console.log(`Updated root categories ETag: ${etag}`);
    }

    if (!rootCategoriesResponse.data || !rootCategoriesResponse.data.Subcategories) {
      return {
        success: false,
        error: 'Failed to fetch root categories'
      };
    }

    // Helper function to fetch detailed category information with ETag support
    const fetchCategoryDetails = async (categoryId, existingCategory = null) => {
      try {
        // Check if we have an existing category
        const endpoint = `/Categories/${categoryId}/Details.json`;

        // We'll keep track of ETags without logging every single one

        // Make the API request with ETag support
        // Pass the etag directly if available
        const detailsResponse = await makeAuthenticatedRequest(
          endpoint,
          existingCategory && existingCategory.etag ? existingCategory.etag : null
        );

        // If we got a 304 Not Modified and have an existing category, use the existing data
        if (detailsResponse.status === 304 && existingCategory) {
          console.log(`Using cached details for category ${categoryId} (304 Not Modified)`);

          // Update the lastSynced timestamp
          existingCategory.lastSynced = new Date();
          await existingCategory.save();

          // Return null data but include the response for ETag info
          return { data: null, response: detailsResponse };
        }

        // If we have a response with data, return it
        if (detailsResponse.data) {
          // We'll process fees without logging every category's fee structure

          // Check if the ETag has actually changed
          if (existingCategory && detailsResponse.headers && detailsResponse.headers.etag) {
            // Store the ETag without quotes for consistency
            let etag = detailsResponse.headers.etag;
            if (etag.startsWith('"') && etag.endsWith('"')) {
              etag = etag.substring(1, etag.length - 1);
            }

            // Only update if the ETag has changed
            if (existingCategory.etag !== etag) {
              // Only log when ETags change to reduce noise
              existingCategory.etag = etag;
              existingCategory.lastSynced = new Date();
              await existingCategory.save();
            } else {
              // Just update the lastSynced timestamp
              existingCategory.lastSynced = new Date();
              await existingCategory.save();
            }
          }

          // Return both the data and the full response
          return { data: detailsResponse.data, response: detailsResponse };
        }

        return { data: null, response: detailsResponse };
      } catch (error) {
        console.error(`Error fetching details for category ${categoryId}:`, error.message);
        return { data: null, response: null };
      }
    };

    // Helper function to process category fees
    const processCategoryFees = (fees) => {
      if (!fees) return {};

      // Process success fees without logging every occurrence

      return {
        bold: fees.Bold,
        bundle: fees.Bundle,
        endDate: fees.EndDate,
        feature: fees.Feature,
        gallery: fees.Gallery,
        galleryPlus: fees.GalleryPlus,
        highlight: fees.Highlight,
        homepage: fees.Homepage,
        listing: fees.Listing,
        multiPhoto: fees.MultiPhoto,
        reserve: fees.Reserve,
        subtitle: fees.Subtitle,
        tenDays: fees.TenDays,
        withdrawal: fees.Withdrawal,
        superFeature: fees.SuperFeature,
        superFeatureBundle: fees.SuperFeatureBundle,
        highVolume: fees.HighVolume,
        listingFeeTiers: fees.ListingFeeTiers ? fees.ListingFeeTiers.map(tier => ({
          minimumTierPrice: tier.MinimumTierPrice,
          fixedFee: tier.FixedFee,
          percentageFee: tier.PercentageFee
        })) : [],
        minimumSuccessFee: fees.MinimumSuccessFee,
        maximumSuccessFee: fees.MaximumSuccessFee,
        successFeeTiers: fees.SuccessFeeTiers ? fees.SuccessFeeTiers.map(tier => ({
          minimumTierPrice: tier.MinimumTierPrice,
          fixedFee: tier.FixedFee,
          percentageFee: tier.PercentageFee
        })) : [],
        branding: fees.Branding,
        secondCategory: fees.SecondCategory
      };
    };

    // Helper function to process payment methods
    const processPaymentMethods = (paymentMethods) => {
      if (!paymentMethods || !Array.isArray(paymentMethods)) return [];

      // Process payment methods silently

      return paymentMethods.map(method => ({
        id: method.Id,
        name: method.Name,
        defaultsToOn: method.DefaultsToOn || false,
        logoUrl: method.LogoUrl,
        sellerFeePercentage: method.SellerFeePercentage
      }));
    };

    // Helper function to process category attributes
    const processCategoryAttributes = (attributes) => {
      if (!attributes || !Array.isArray(attributes)) return [];

      return attributes.map(attr => ({
        name: attr.Name,
        displayName: attr.DisplayName,
        type: attr.Type,
        range: attr.Range ? {
          lower: attr.Range.Lower,
          upper: attr.Range.Upper
        } : undefined,
        maxStringLength: attr.MaxStringLength,
        options: attr.Options ? attr.Options.map(opt => ({
          value: opt.Value,
          display: opt.Display
        })) : [],
        units: attr.Units ? attr.Units.map(unit => ({
          display: unit.Display,
          multiplier: unit.Multiplier
        })) : [],
        isRequiredForSell: attr.IsRequiredForSell || false,
        groupName: attr.GroupName
      }));
    };

    // Process categories recursively
    const processCategories = async (categories, parentId = null, parentPath = '') => {
      for (const category of categories) {
        try {
          // Check if category already exists
          // Convert category number to string to handle IDs with dashes
          const categoryId = category.Number.toString();
          let existingCategory = await TradeMeCategory.findOne({
            categoryId: categoryId
          });

          const categoryPath = parentPath ? `${parentPath} > ${category.Name}` : category.Name;

          // Fetch detailed category information for leaf categories or if we're updating an existing category
          let categoryDetails = null;
          let detailsResponse = null;
          if (category.IsLeaf || existingCategory) {
            const result = await fetchCategoryDetails(categoryId, existingCategory);
            categoryDetails = result.data;
            detailsResponse = result.response;
          }

          if (existingCategory) {
            // Update existing category with basic info
            existingCategory.name = category.Name;
            existingCategory.path = categoryPath;
            existingCategory.parentId = parentId;
            existingCategory.isLeaf = category.IsLeaf || false;
            existingCategory.hasLegalNotice = category.HasLegalNotice || false;
            existingCategory.legalNotice = category.LegalNotice || '';
            existingCategory.count = category.Count || 0;

            // Update with detailed information if available
            if (categoryDetails) {
              existingCategory.isRestricted = categoryDetails.IsRestricted || false;
              existingCategory.isWine = categoryDetails.IsWine || false;
              existingCategory.canListAuctions = categoryDetails.CanListAuctions || true;
              existingCategory.canListClassifieds = categoryDetails.CanListClassifieds || true;
              existingCategory.canRelist = categoryDetails.CanRelist || true;
              existingCategory.authenticatedBidsOnly = categoryDetails.AuthenticatedBidsOnly || false;
              existingCategory.defaultDuration = categoryDetails.DefaultDuration || 7;
              existingCategory.allowedDurations = categoryDetails.AllowedDurations || [2, 3, 4, 5, 6, 7];
              existingCategory.fees = processCategoryFees(categoryDetails.Fees);
              existingCategory.freePhotoCount = categoryDetails.FreePhotoCount || 10;
              existingCategory.maximumPhotoCount = categoryDetails.MaximumPhotoCount || 20;
              existingCategory.isFreeToRelist = categoryDetails.IsFreeToRelist || false;
              existingCategory.attributes = processCategoryAttributes(categoryDetails.Attributes);
              existingCategory.hasAutomaticallyCreatedTitle = categoryDetails.HasAutomaticallyCreatedTitle || false;
              existingCategory.canUseTradeMeShipping = categoryDetails.CanUseTradeMeShipping || false;
              existingCategory.maximumTitleLength = categoryDetails.MaximumTitleLength || 80;
              existingCategory.areaOfBusiness = categoryDetails.AreaOfBusiness || 0;
              existingCategory.extensionPeriod = categoryDetails.ExtensionPeriod || 0;
              existingCategory.defaultRelistDuration = categoryDetails.DefaultRelistDuration || 7;
              existingCategory.canHaveSecondCategory = categoryDetails.CanHaveSecondCategory || false;
              existingCategory.canBeSecondCategory = categoryDetails.CanBeSecondCategory || false;
              existingCategory.isFirearms = categoryDetails.IsFirearms || false;
              existingCategory.paymentMethods = processPaymentMethods(categoryDetails.PaymentMethods);
            }

            await existingCategory.save();

            // Update statistics
            stats.itemsProcessed++;
            stats.updatedItems++;
          } else {
            // Create new category with basic info
            const newCategoryData = {
              categoryId: categoryId,
              name: category.Name,
              path: categoryPath,
              parentId: parentId,
              isLeaf: category.IsLeaf || false,
              hasLegalNotice: category.HasLegalNotice || false,
              legalNotice: category.LegalNotice || '',
              count: category.Count || 0,
              lastSynced: new Date()
            };

            // Add detailed information if available
            if (categoryDetails) {
              // Store the ETag if available in the response
              if (detailsResponse && detailsResponse.headers && detailsResponse.headers.etag) {
                // Store the ETag without quotes for consistency
                let etag = detailsResponse.headers.etag;
                if (etag.startsWith('"') && etag.endsWith('"')) {
                  etag = etag.substring(1, etag.length - 1);
                }

                console.log(`Adding etag for new category ${categoryId}: ${etag}`);
                newCategoryData.etag = etag;
              }

              newCategoryData.isRestricted = categoryDetails.IsRestricted || false;
              newCategoryData.isWine = categoryDetails.IsWine || false;
              newCategoryData.canListAuctions = categoryDetails.CanListAuctions || true;
              newCategoryData.canListClassifieds = categoryDetails.CanListClassifieds || true;
              newCategoryData.canRelist = categoryDetails.CanRelist || true;
              newCategoryData.authenticatedBidsOnly = categoryDetails.AuthenticatedBidsOnly || false;
              newCategoryData.defaultDuration = categoryDetails.DefaultDuration || 7;
              newCategoryData.allowedDurations = categoryDetails.AllowedDurations || [2, 3, 4, 5, 6, 7];
              newCategoryData.fees = processCategoryFees(categoryDetails.Fees);
              newCategoryData.freePhotoCount = categoryDetails.FreePhotoCount || 10;
              newCategoryData.maximumPhotoCount = categoryDetails.MaximumPhotoCount || 20;
              newCategoryData.isFreeToRelist = categoryDetails.IsFreeToRelist || false;
              newCategoryData.attributes = processCategoryAttributes(categoryDetails.Attributes);
              newCategoryData.hasAutomaticallyCreatedTitle = categoryDetails.HasAutomaticallyCreatedTitle || false;
              newCategoryData.canUseTradeMeShipping = categoryDetails.CanUseTradeMeShipping || false;
              newCategoryData.maximumTitleLength = categoryDetails.MaximumTitleLength || 80;
              newCategoryData.areaOfBusiness = categoryDetails.AreaOfBusiness || 0;
              newCategoryData.extensionPeriod = categoryDetails.ExtensionPeriod || 0;
              newCategoryData.defaultRelistDuration = categoryDetails.DefaultRelistDuration || 7;
              newCategoryData.canHaveSecondCategory = categoryDetails.CanHaveSecondCategory || false;
              newCategoryData.canBeSecondCategory = categoryDetails.CanBeSecondCategory || false;
              newCategoryData.isFirearms = categoryDetails.IsFirearms || false;
              newCategoryData.paymentMethods = processPaymentMethods(categoryDetails.PaymentMethods);
            }

            const newCategory = new TradeMeCategory(newCategoryData);
            await newCategory.save();

            // Update statistics
            stats.itemsProcessed++;
            stats.newItems++;
          }

          // Process subcategories if any
          if (category.Subcategories && category.Subcategories.length > 0) {
            console.log(`Processing ${category.Subcategories.length} subcategories for ${category.Name} (${categoryId})`);

            // If we have subcategories directly in the response and an existing category,
            // save the ETag from the parent response
            if (existingCategory && detailsResponse && detailsResponse.headers && detailsResponse.headers.etag) {
              // Store the ETag without quotes for consistency
              let etag = detailsResponse.headers.etag;
              if (etag.startsWith('"') && etag.endsWith('"')) {
                etag = etag.substring(1, etag.length - 1);
              }

              console.log(`Saving ETag for parent category with subcategories ${category.Name} (${categoryId}): ${etag}`);
              existingCategory.etag = etag;
              existingCategory.lastSynced = new Date();
              await existingCategory.save();
            }

            await processCategories(category.Subcategories, categoryId, categoryPath);
          } else if (!category.Subcategories && !category.IsLeaf) {
            // Fetch subcategories if not included in the response
            try {
              console.log(`Fetching subcategories for ${category.Name} (${category.Number})`);
              const subcategoriesEndpoint = `/Categories/${category.Number}.json`;

              // Check for ETags silently

              // Pass the etag directly if available
              const subcategoriesResponse = await makeAuthenticatedRequest(
                subcategoriesEndpoint,
                existingCategory && existingCategory.etag ? existingCategory.etag : null
              );

              // If we got a 304 Not Modified, we can skip processing subcategories
              if (subcategoriesResponse.status === 304) {
                console.log(`Using cached subcategories for category ${category.Name} (${category.Number}) - 304 Not Modified`);

                // Update the lastSynced timestamp
                if (existingCategory) {
                  existingCategory.lastSynced = new Date();
                  await existingCategory.save();
                }
              } else if (subcategoriesResponse.data && subcategoriesResponse.data.Subcategories) {
                console.log(`Processing ${subcategoriesResponse.data.Subcategories.length} subcategories for ${category.Name} (${category.Number})`);

                // Save the ETag for this category
                if (existingCategory && subcategoriesResponse.headers && subcategoriesResponse.headers.etag) {
                  // Store the ETag without quotes for consistency
                  let etag = subcategoriesResponse.headers.etag;
                  if (etag.startsWith('"') && etag.endsWith('"')) {
                    etag = etag.substring(1, etag.length - 1);
                  }

                  // Only update if the ETag has changed
                  if (existingCategory.etag !== etag) {
                    existingCategory.etag = etag;
                    existingCategory.lastSynced = new Date();
                    await existingCategory.save();
                  } else {
                    // Just update the lastSynced timestamp
                    existingCategory.lastSynced = new Date();
                    await existingCategory.save();
                  }
                }

                await processCategories(subcategoriesResponse.data.Subcategories, categoryId, categoryPath);
              }

              // Add a small delay to avoid hitting rate limits
              await new Promise(resolve => setTimeout(resolve, 100));
            } catch (error) {
              console.error(`Error fetching subcategories for category ${category.Number}:`, error.message);
            }
          }
        } catch (error) {
          console.error(`Error processing category ${category.Number}:`, error);
        }
      }
    };

    // Save ETags for top-level categories
    if (rootCategoriesResponse.headers && rootCategoriesResponse.headers.etag) {
      // Store the ETag without quotes for consistency
      let etag = rootCategoriesResponse.headers.etag;
      if (etag.startsWith('"') && etag.endsWith('"')) {
        etag = etag.substring(1, etag.length - 1);
      }

      // Update the root categories ETag document
      if (rootCategoriesETag.etag !== etag) {
        console.log(`Updating root categories ETag: ${etag}`);
        rootCategoriesETag.etag = etag;
        rootCategoriesETag.lastSynced = new Date();
        await rootCategoriesETag.save();
      } else {
        // Just update the lastSynced timestamp
        rootCategoriesETag.lastSynced = new Date();
        await rootCategoriesETag.save();
      }

      // For each top-level category, save the ETag only if it's different
      for (const category of rootCategoriesResponse.data.Subcategories) {
        const categoryId = category.Number.toString();
        let existingCategory = await TradeMeCategory.findOne({ categoryId });

        if (existingCategory) {
          if (existingCategory.etag !== etag) {
            // Update ETag silently for top-level categories
            existingCategory.etag = etag;
            existingCategory.lastSynced = new Date();
            await existingCategory.save();
          } else {
            // Just update the lastSynced timestamp
            existingCategory.lastSynced = new Date();
            await existingCategory.save();
          }
        }
      }
    }

    // Start processing from root categories
    await processCategories(rootCategoriesResponse.data.Subcategories);

    // Build category hierarchy
    await buildCategoryHierarchy();

    // Log the statistics
    console.log('TradeMe categories sync completed with the following statistics:');
    console.log(`- Items processed: ${stats.itemsProcessed}`);
    console.log(`- New items: ${stats.newItems}`);
    console.log(`- Updated items: ${stats.updatedItems}`);
    console.log(`- API calls made: ${stats.apiCallsMade}`);

    return {
      success: true,
      message: 'TradeMe categories sync completed',
      stats: stats
    };
  } catch (error) {
    console.error('Error syncing TradeMe categories:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Build the category hierarchy by setting subcategories
 */
async function buildCategoryHierarchy() {
  try {
    // Get all categories
    const categories = await TradeMeCategory.find();

    // Group categories by parent ID
    const categoryMap = {};
    categories.forEach(category => {
      categoryMap[category.categoryId] = category;
    });

    // Set subcategories for each parent
    for (const category of categories) {
      if (category.parentId) {
        const parent = categoryMap[category.parentId];
        if (parent) {
          if (!parent.subcategories.includes(category._id)) {
            parent.subcategories.push(category._id);
            await parent.save();
          }
        }
      }
    }

    console.log('Category hierarchy built successfully');
  } catch (error) {
    console.error('Error building category hierarchy:', error);
  }
}

/**
 * Get categories from the database
 * @param {Object} options - Query options
 * @param {number} options.parentId - Parent category ID (null for root categories)
 * @returns {Promise<Object>} Categories
 */
async function getCategories(options = {}) {
  try {
    const { parentId = null } = options;

    let query = {};
    if (parentId === null) {
      query.parentId = null;
    } else {
      query.parentId = parentId;
    }

    const categories = await TradeMeCategory.find(query)
      .sort({ name: 1 })
      .populate('subcategories');

    return {
      success: true,
      categories
    };
  } catch (error) {
    console.error('Error getting TradeMe categories:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Get a category by ID
 * @param {string} categoryId - Category ID
 * @returns {Promise<Object>} Category
 */
async function getCategoryById(categoryId) {
  try {
    const category = await TradeMeCategory.findOne({ categoryId })
      .populate('subcategories');

    if (!category) {
      return {
        success: false,
        error: 'Category not found'
      };
    }

    // Check if we need to fetch detailed information from TradeMe API
    // This is useful if we're missing some of the detailed fields
    if (category.isLeaf && (!category.fees || !category.attributes)) {
      try {
        // Get current settings
        let settings = await TradeMeSettings.findOne();

        if (settings && settings.connected) {

          // Make the API call using makeAuthenticatedRequest
          const response = await makeAuthenticatedRequest(
            `/Categories/${categoryId}/Details.json`,
            category.etag
          );

          // If we got a 304 Not Modified, we can use the existing data
          if (response.status === 304) {
            console.log(`Using cached details for category ${categoryId} (304 Not Modified)`);
            // Update the last synced timestamp
            category.lastSynced = new Date();
            await category.save();
          }
          // Process the response and update the category if we got new data
          else if (response.data) {
            // Save the ETag if available
            if (response.headers && response.headers.etag) {
              // Store the ETag without quotes for consistency
              let etag = response.headers.etag;
              if (etag.startsWith('"') && etag.endsWith('"')) {
                etag = etag.substring(1, etag.length - 1);
              }

              // Only update if the ETag has changed
              if (category.etag !== etag) {
                category.etag = etag;
                category.lastSynced = new Date();
              } else {
                category.lastSynced = new Date();
              }
            }

            const details = response.data;

            // Process fees
            if (details.Fees) {
              category.fees = {
                bold: details.Fees.Bold,
                bundle: details.Fees.Bundle,
                endDate: details.Fees.EndDate,
                feature: details.Fees.Feature,
                gallery: details.Fees.Gallery,
                galleryPlus: details.Fees.GalleryPlus,
                highlight: details.Fees.Highlight,
                homepage: details.Fees.Homepage,
                listing: details.Fees.Listing,
                multiPhoto: details.Fees.MultiPhoto,
                reserve: details.Fees.Reserve,
                subtitle: details.Fees.Subtitle,
                tenDays: details.Fees.TenDays,
                withdrawal: details.Fees.Withdrawal,
                superFeature: details.Fees.SuperFeature,
                superFeatureBundle: details.Fees.SuperFeatureBundle,
                highVolume: details.Fees.HighVolume,
                minimumSuccessFee: details.Fees.MinimumSuccessFee,
                maximumSuccessFee: details.Fees.MaximumSuccessFee
              };

              // Process listing fee tiers
              if (details.Fees.ListingFeeTiers && Array.isArray(details.Fees.ListingFeeTiers)) {
                category.fees.listingFeeTiers = details.Fees.ListingFeeTiers.map(tier => ({
                  minimumTierPrice: tier.MinimumTierPrice,
                  fixedFee: tier.FixedFee,
                  percentageFee: tier.PercentageFee
                }));
              }

              // Process success fee tiers
              if (details.Fees.SuccessFeeTiers && Array.isArray(details.Fees.SuccessFeeTiers)) {
                category.fees.successFeeTiers = details.Fees.SuccessFeeTiers.map(tier => ({
                  minimumTierPrice: tier.MinimumTierPrice,
                  fixedFee: tier.FixedFee,
                  percentageFee: tier.PercentageFee
                }));
              }
            }

            // Process attributes
            if (details.Attributes && Array.isArray(details.Attributes)) {
              category.attributes = details.Attributes.map(attr => {
                const attribute = {
                  name: attr.Name,
                  displayName: attr.DisplayName,
                  type: attr.Type,
                  isRequiredForSell: attr.IsRequiredForSell || false,
                  groupName: attr.GroupName
                };

                // Add range if available
                if (attr.Range) {
                  attribute.range = {
                    lower: attr.Range.Lower,
                    upper: attr.Range.Upper
                  };
                }

                // Add max string length if available
                if (attr.MaxStringLength) {
                  attribute.maxStringLength = attr.MaxStringLength;
                }

                // Add options if available
                if (attr.Options && Array.isArray(attr.Options)) {
                  attribute.options = attr.Options.map(opt => ({
                    value: opt.Value,
                    display: opt.Display
                  }));
                }

                // Add units if available
                if (attr.Units && Array.isArray(attr.Units)) {
                  attribute.units = attr.Units.map(unit => ({
                    display: unit.Display,
                    multiplier: unit.Multiplier
                  }));
                }

                return attribute;
              });
            }

            // Update other fields
            category.isRestricted = details.IsRestricted || false;
            category.isWine = details.IsWine || false;
            category.canListAuctions = details.CanListAuctions || true;
            category.canListClassifieds = details.CanListClassifieds || true;
            category.canRelist = details.CanRelist || true;
            category.authenticatedBidsOnly = details.AuthenticatedBidsOnly || false;
            category.defaultDuration = details.DefaultDuration || 7;
            category.allowedDurations = details.AllowedDurations || [2, 3, 4, 5, 6, 7];
            category.freePhotoCount = details.FreePhotoCount || 10;
            category.maximumPhotoCount = details.MaximumPhotoCount || 20;
            category.isFreeToRelist = details.IsFreeToRelist || false;
            category.hasAutomaticallyCreatedTitle = details.HasAutomaticallyCreatedTitle || false;
            category.canUseTradeMeShipping = details.CanUseTradeMeShipping || false;
            category.maximumTitleLength = details.MaximumTitleLength || 80;
            category.areaOfBusiness = details.AreaOfBusiness || 0;
            category.extensionPeriod = details.ExtensionPeriod || 0;
            category.defaultRelistDuration = details.DefaultRelistDuration || 7;
            category.canHaveSecondCategory = details.CanHaveSecondCategory || false;
            category.canBeSecondCategory = details.CanBeSecondCategory || false;
            category.isFirearms = details.IsFirearms || false;

            // Process payment methods
            if (details.PaymentMethods && Array.isArray(details.PaymentMethods)) {
              category.paymentMethods = details.PaymentMethods.map(method => ({
                id: method.Id,
                name: method.Name,
                defaultsToOn: method.DefaultsToOn || false,
                logoUrl: method.LogoUrl,
                sellerFeePercentage: method.SellerFeePercentage
              }));
            }

            // Save the updated category
            await category.save();
          }
        }
      } catch (apiError) {
        console.error('Error fetching detailed category information:', apiError);
        // Continue with the existing category data
      }
    }

    return {
      success: true,
      category
    };
  } catch (error) {
    console.error('Error getting TradeMe category:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Search categories by name
 * @param {string} query - Search query
 * @returns {Promise<Object>} Matching categories
 */
async function searchCategories(query) {
  try {
    if (!query || query.length < 2) {
      return {
        success: false,
        error: 'Search query must be at least 2 characters'
      };
    }

    const categories = await TradeMeCategory.find({
      $or: [
        { name: { $regex: query, $options: 'i' } },
        { path: { $regex: query, $options: 'i' } }
      ]
    }).sort({ path: 1 }).limit(20);

    return {
      success: true,
      categories
    };
  } catch (error) {
    console.error('Error searching TradeMe categories:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Get category suggestions from TradeMe API based on search term
 * @param {string} searchString - Search term to get suggestions for
 * @returns {Promise<Object>} Category suggestions
 */
async function getCategorySuggestions(searchString) {
  try {
    // Get current settings
    let settings = await TradeMeSettings.findOne();

    if (!settings || !settings.connected) {
      return {
        success: false,
        error: 'No active TradeMe connection'
      };
    }

    const environment = settings.environment;
    const accessToken = settings.accessToken;
    const accessTokenSecret = settings.accessSecret;

    // Create OAuth instance
    const oauth = getOAuth(environment);
    const apiEndpoints = TRADEME_API[environment];

    // Build the URL with query parameters
    const endpoint = '/Search/Suggestions.json';
    const queryParams = new URLSearchParams({
      search_string: searchString,
      category_id: 0, // 0 for all categories
      max_category_suggestions: 20, // Increase to get more direct category matches
      max_auto_suggestions: 5 // Reduce auto suggestions since we're focusing on categories
    }).toString();

    const url = `${apiEndpoints.apiBase}${endpoint}?${queryParams}`;

    // Make the API call
    const response = await axios({
      method: 'get',
      url: url,
      headers: oauth.toHeader(
        oauth.authorize(
          {
            url: url,
            method: 'GET'
          },
          {
            key: accessToken,
            secret: accessTokenSecret
          }
        )
      )
    });

    // Process the response
    const suggestions = {
      categorySuggestions: [],
      autoSuggestions: []
    };

    // Process category suggestions
    if (response.data.CategorySuggestions && response.data.CategorySuggestions.length > 0) {
      suggestions.categorySuggestions = response.data.CategorySuggestions.map(cat => {
        // Create a full path with ancestors and current category
        const fullPath = cat.AncestorsNames && cat.AncestorsNames.length > 0
          ? [...cat.AncestorsNames, cat.Name].join(' > ')
          : cat.Name;

        return {
          id: cat.Id.toString(),
          name: cat.Name,
          path: fullPath,
          // Store the full ancestry for better display
          ancestors: cat.AncestorsNames || []
        };
      });
    }

    // Process auto suggestions with their category suggestions
    if (response.data.AutoSuggestions && response.data.AutoSuggestions.length > 0) {
      suggestions.autoSuggestions = response.data.AutoSuggestions.map(suggestion => {
        const result = {
          searchTerm: suggestion.SearchTerm,
          categories: []
        };

        if (suggestion.SearchTermCategorySuggestions && suggestion.SearchTermCategorySuggestions.length > 0) {
          // For each category suggestion in the auto suggestion
          result.categories = suggestion.SearchTermCategorySuggestions.map(cat => {
            // Try to extract a path from the description
            // The description often contains the full path with " in " prefix
            let path = cat.Description;
            let name = path;

            // If the description contains " in ", it's likely a path
            if (path.includes(' in ')) {
              const parts = path.split(' in ');
              name = parts[0].trim();
              // The rest is the path
              const pathPart = parts.slice(1).join(' in ').trim();
              path = `${pathPart} > ${name}`;
            }

            return {
              id: cat.Id.toString(),
              name: name,
              description: cat.Description,
              path: path
            };
          });
        }

        return result;
      });
    }

    return {
      success: true,
      suggestions
    };
  } catch (error) {
    console.error('Error getting TradeMe category suggestions:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Fix ETags in the database
 * @returns {Promise<Object>} Result of the fix
 */
async function fixETags() {
  try {
    // Fix ETags in the TradeMeCategory collection
    const categories = await TradeMeCategory.find({
      etag: { $exists: true, $ne: null }
    });

    let fixedETags = 0;

    for (const category of categories) {
      let needsUpdate = false;

      // Fix etag
      if (category.etag) {
        let etag = category.etag;
        if (etag.startsWith('"') && etag.endsWith('"')) {
          category.etag = etag.substring(1, etag.length - 1);
          needsUpdate = true;
          fixedETags++;
        }
      }

      // Migrate detailsEtag to etag if it exists
      if (category.detailsEtag && !category.etag) {
        let etag = category.detailsEtag;
        if (etag.startsWith('"') && etag.endsWith('"')) {
          etag = etag.substring(1, etag.length - 1);
        }
        category.etag = etag;
        delete category.detailsEtag;
        needsUpdate = true;
        fixedETags++;
      }

      if (needsUpdate) {
        await category.save();
      }
    }

    return {
      success: true,
      message: 'ETags fixed successfully',
      stats: {
        fixedETags,
        totalFixed: fixedETags
      }
    };
  } catch (error) {
    console.error('Error fixing ETags:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Check if ETags are working properly
 * @returns {Promise<Object>} Result of the check
 */
async function checkETags() {
  try {
    // Get current settings
    let settings = await TradeMeSettings.findOne();

    if (!settings || !settings.connected) {
      return {
        success: false,
        error: 'No active TradeMe connection'
      };
    }

    // Get root categories ETag
    const rootCategoriesETag = await TradeMeCategory.findOne({ categoryId: 'root_categories_etag' });

    // Get category ETags
    const categories = await TradeMeCategory.find({
      etag: { $exists: true, $ne: null }
    }).limit(100);

    return {
      success: true,
      message: 'ETag check completed',
      stats: {
        rootCategoriesETag: rootCategoriesETag ? {
          etag: rootCategoriesETag.etag,
          lastSynced: rootCategoriesETag.lastSynced
        } : null,
        categoryETagCount: categories.length,
        categoryETags: categories.map(cat => ({
          categoryId: cat.categoryId,
          name: cat.name,
          etag: cat.etag,
          lastSynced: cat.lastSynced
        }))
      }
    };
  } catch (error) {
    console.error('Error checking ETags:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Check for categories with payment methods
 * @returns {Promise<Object>} Result of the check
 */
async function checkPaymentMethods() {
  try {
    // Find categories with payment methods
    const categoriesWithPaymentMethods = await TradeMeCategory.find({
      'paymentMethods.0': { $exists: true }
    });

    console.log(`Found ${categoriesWithPaymentMethods.length} categories with payment methods`);

    // Get details for each category
    const categoryDetails = categoriesWithPaymentMethods.map(cat => ({
      categoryId: cat.categoryId,
      name: cat.name,
      path: cat.path,
      paymentMethodsCount: cat.paymentMethods ? cat.paymentMethods.length : 0,
      paymentMethods: cat.paymentMethods
    }));

    return {
      success: true,
      message: 'Payment methods check completed',
      stats: {
        categoriesWithPaymentMethods: categoriesWithPaymentMethods.length
      },
      categories: categoryDetails
    };
  } catch (error) {
    console.error('Error checking payment methods:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Check for categories with success fees
 * @returns {Promise<Object>} Result of the check
 */
async function checkSuccessFees() {
  try {
    // Find categories with success fees
    const categoriesWithSuccessFees = await TradeMeCategory.find({
      $or: [
        { 'fees.minimumSuccessFee': { $exists: true, $ne: null } },
        { 'fees.maximumSuccessFee': { $exists: true, $ne: null } },
        { 'fees.successFeeTiers.0': { $exists: true } }
      ]
    });

    console.log(`Found ${categoriesWithSuccessFees.length} categories with success fees`);

    // Get details for each category
    const categoryDetails = categoriesWithSuccessFees.map(cat => ({
      categoryId: cat.categoryId,
      name: cat.name,
      path: cat.path,
      minimumSuccessFee: cat.fees.minimumSuccessFee,
      maximumSuccessFee: cat.fees.maximumSuccessFee,
      successFeeTiersCount: cat.fees.successFeeTiers ? cat.fees.successFeeTiers.length : 0,
      successFeeTiers: cat.fees.successFeeTiers
    }));

    return {
      success: true,
      message: 'Success fees check completed',
      stats: {
        categoriesWithSuccessFees: categoriesWithSuccessFees.length
      },
      categories: categoryDetails
    };
  } catch (error) {
    console.error('Error checking success fees:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Update payment methods for all categories
 * @returns {Promise<Object>} Result of the operation
 */
async function updatePaymentMethods() {
  try {
    // Get current settings
    let settings = await TradeMeSettings.findOne();
    if (!settings || !settings.connected) {
      return {
        success: false,
        error: 'TradeMe is not connected'
      };
    }

    // Find all leaf categories
    const leafCategories = await TradeMeCategory.find({ isLeaf: true });
    console.log(`Found ${leafCategories.length} leaf categories`);

    let updatedCount = 0;
    let alreadyHadPaymentMethodsCount = 0;
    let errorCount = 0;

    // Process each leaf category
    for (const category of leafCategories) {
      try {
        // Skip if the category already has payment methods
        if (category.paymentMethods && category.paymentMethods.length > 0) {
          alreadyHadPaymentMethodsCount++;
          continue;
        }

        // Fetch detailed information from TradeMe API
        const response = await makeAuthenticatedRequest(
          `/Categories/${category.categoryId}/Details.json`,
          category.etag
        );

        // If we got a 304 Not Modified, skip this category
        if (response.status === 304) {
          console.log(`Using cached details for category ${category.categoryId} (304 Not Modified)`);
          continue;
        }

        // Process the response and update the category if we got new data
        if (response.data && response.data.PaymentMethods) {
          // Process payment methods
          category.paymentMethods = response.data.PaymentMethods.map(method => ({
            id: method.Id,
            name: method.Name,
            defaultsToOn: method.DefaultsToOn || false,
            logoUrl: method.LogoUrl,
            sellerFeePercentage: method.SellerFeePercentage
          }));

          // Save the ETag if available
          if (response.headers && response.headers.etag) {
            // Store the ETag without quotes for consistency
            let etag = response.headers.etag;
            if (etag.startsWith('"') && etag.endsWith('"')) {
              etag = etag.substring(1, etag.length - 1);
            }

            // Only update if the ETag has changed
            if (category.etag !== etag) {
              category.etag = etag;
            }
          }

          category.lastSynced = new Date();
          await category.save();
          updatedCount++;

          console.log(`Updated payment methods for category ${category.name} (${category.categoryId})`);
        }

        // Add a small delay to avoid hitting rate limits
        await new Promise(resolve => setTimeout(resolve, 100));
      } catch (error) {
        console.error(`Error updating payment methods for category ${category.categoryId}:`, error.message);
        errorCount++;
      }
    }

    return {
      success: true,
      message: 'Payment methods update completed',
      stats: {
        totalLeafCategories: leafCategories.length,
        alreadyHadPaymentMethods: alreadyHadPaymentMethodsCount,
        updatedCategories: updatedCount,
        errorCount: errorCount
      }
    };
  } catch (error) {
    console.error('Error updating payment methods:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Migrate detailsEtag to etag for all categories
 * @returns {Promise<Object>} Result of the operation
 */
async function migrateDetailsTags() {
  try {
    // Find all categories with detailsEtag but no etag
    const categories = await TradeMeCategory.find({
      detailsEtag: { $exists: true, $ne: null },
      $or: [
        { etag: { $exists: false } },
        { etag: null }
      ]
    });

    console.log(`Found ${categories.length} categories with detailsEtag but no etag`);

    let migratedCount = 0;

    // Process each category
    for (const category of categories) {
      // Migrate detailsEtag to etag
      let etag = category.detailsEtag;
      if (etag.startsWith('"') && etag.endsWith('"')) {
        etag = etag.substring(1, etag.length - 1);
      }

      category.etag = etag;
      delete category.detailsEtag;
      category.lastSynced = new Date();
      await category.save();
      migratedCount++;

      console.log(`Migrated detailsEtag to etag for category ${category.name} (${category.categoryId})`);
    }

    return {
      success: true,
      message: 'detailsEtag migration completed',
      stats: {
        totalCategories: categories.length,
        migratedCategories: migratedCount
      }
    };
  } catch (error) {
    console.error('Error migrating detailsEtag:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Optimized sync that checks root categories ETag first
 * @returns {Promise<Object>} Result of the sync
 */
async function optimizedSync() {
  try {
    // Get current settings
    let settings = await TradeMeSettings.findOne();
    if (!settings || !settings.connected) {
      return {
        success: false,
        error: 'TradeMe is not connected'
      };
    }

    console.log('Starting optimized TradeMe category sync');

    // Find the root categories ETag document
    let rootCategoriesETag = await TradeMeCategory.findOne({ categoryId: 'root_categories_etag' });

    // If we don't have a root categories ETag document, create one
    if (!rootCategoriesETag) {
      rootCategoriesETag = new TradeMeCategory({
        categoryId: 'root_categories_etag',
        name: 'Root Categories ETag',
        path: 'Root Categories ETag',
        isLeaf: false
      });
      await rootCategoriesETag.save();
      console.log('Created root categories ETag document');
    }

    // Create a helper function for making authenticated requests
    const makeAuthenticatedRequest = async (endpoint, existingETag = null) => {
      const environment = settings.environment;
      const accessToken = settings.accessToken;
      const accessTokenSecret = settings.accessSecret;
      const oauth = getOAuth(environment);
      const apiEndpoints = TRADEME_API[environment];

      let headers = {};

      // If we have an ETag, add the If-None-Match header
      if (existingETag) {
        // Make sure the ETag is properly formatted with quotes
        let etag = existingETag;

        // If the ETag doesn't already have quotes, add them
        if (!etag.startsWith('"') && !etag.endsWith('"')) {
          etag = `"${etag}"`;
        }

        headers['If-None-Match'] = etag;
      }

      // Add OAuth headers
      const oauthHeaders = oauth.toHeader(
        oauth.authorize(
          {
            url: `${apiEndpoints.apiBase}${endpoint}`,
            method: 'GET'
          },
          {
            key: accessToken,
            secret: accessTokenSecret
          }
        )
      );

      // Merge headers
      headers = { ...headers, ...oauthHeaders };

      // Make the API call
      const url = `${apiEndpoints.apiBase}${endpoint}`;

      try {
        const response = await axios({
          method: 'get',
          url: url,
          headers: headers,
          validateStatus: function (status) {
            // Accept 304 Not Modified as a valid status
            return (status >= 200 && status < 300) || status === 304;
          }
        });

        return response;
      } catch (error) {
        console.error(`Error making authenticated request to ${endpoint}:`, error.message);
        throw error;
      }
    };

    const rootCategoriesResponse = await makeAuthenticatedRequest(
      '/Categories.json',
      rootCategoriesETag.etag
    );

    // If we got a 304 Not Modified, we can skip the sync
    if (rootCategoriesResponse.status === 304) {
      console.log('Root categories have not changed (304 Not Modified) - skipping sync');

      // Update the lastSynced timestamp
      rootCategoriesETag.lastSynced = new Date();
      await rootCategoriesETag.save();

      return {
        success: true,
        message: 'Categories are up to date (no changes detected)',
        stats: {
          categoriesChecked: 1,
          categoriesUpdated: 0,
          categoriesAdded: 0
        }
      };
    }

    // If we got a 200 OK but the ETag is the same, we can still skip the sync
    if (rootCategoriesResponse.status === 200 &&
        rootCategoriesResponse.headers &&
        rootCategoriesResponse.headers.etag) {

      // Store the ETag without quotes for consistency
      let etag = rootCategoriesResponse.headers.etag;
      if (etag.startsWith('"') && etag.endsWith('"')) {
        etag = etag.substring(1, etag.length - 1);
      }

      if (rootCategoriesETag.etag === etag) {
        console.log('Root categories ETag unchanged - skipping sync');

        // Update the lastSynced timestamp
        rootCategoriesETag.lastSynced = new Date();
        await rootCategoriesETag.save();

        return {
          success: true,
          message: 'Categories are up to date (ETag unchanged)',
          stats: {
            categoriesChecked: 1,
            categoriesUpdated: 0,
            categoriesAdded: 0
          }
        };
      }

      // If we got here, the ETag has changed, so we need to do a full sync
      console.log('Root categories ETag has changed - performing full sync');
      rootCategoriesETag.etag = etag;
      rootCategoriesETag.lastSynced = new Date();
      await rootCategoriesETag.save();
    }

    // Perform a full sync
    return await syncCategoriesFromTradeMe();
  } catch (error) {
    console.error('Error in optimized sync:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

module.exports = {
  syncCategoriesFromTradeMe,
  getCategories,
  getCategoryById,
  searchCategories,
  getCategorySuggestions,
  checkETags,
  fixETags,
  migrateDetailsTags,
  checkSuccessFees,
  checkPaymentMethods,
  updatePaymentMethods,
  optimizedSync
};
