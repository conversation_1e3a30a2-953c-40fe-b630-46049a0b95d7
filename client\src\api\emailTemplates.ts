import api from './api';

export interface EmailTemplate {
  _id: string;
  title: string;
  subject: string;
  body: string;
  category: 'Trademe' | 'Personal Finance' | 'Buys/WIW' | 'Other';
  createdBy: {
    _id: string;
    username: string;
    email: string;
    fullName: string;
  };
  createdAt: string;
  updatedAt: string;
  updatedBy?: {
    _id: string;
    username: string;
    email: string;
    fullName: string;
  };
}

// Description: Get all email templates
// Endpoint: GET /api/email-templates
// Request: { category?: string }
// Response: { success: boolean, templates: EmailTemplate[] }
export const getEmailTemplates = async (category?: string) => {
  try {
    const response = await api.get('/email-templates', {
      params: { category }
    });
    return response.data;
  } catch (error: any) {
    console.error('Error fetching email templates:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Description: Create a new email template
// Endpoint: POST /api/email-templates
// Request: { title: string, subject: string, body: string, category?: string }
// Response: { success: boolean, template: EmailTemplate, message: string }
export const createEmailTemplate = async (data: { 
  title: string, 
  subject: string, 
  body: string, 
  category?: string 
}) => {
  try {
    const response = await api.post('/email-templates', data);
    return response.data;
  } catch (error: any) {
    console.error('Error creating email template:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Description: Update an email template
// Endpoint: PUT /api/email-templates/:id
// Request: { title?: string, subject?: string, body?: string, category?: string }
// Response: { success: boolean, template: EmailTemplate, message: string }
export const updateEmailTemplate = async (
  id: string, 
  data: { 
    title?: string, 
    subject?: string, 
    body?: string, 
    category?: string 
  }
) => {
  try {
    const response = await api.put(`/email-templates/${id}`, data);
    return response.data;
  } catch (error: any) {
    console.error('Error updating email template:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Description: Delete an email template
// Endpoint: DELETE /api/email-templates/:id
// Request: {}
// Response: { success: boolean, message: string }
export const deleteEmailTemplate = async (id: string) => {
  try {
    const response = await api.delete(`/email-templates/${id}`);
    return response.data;
  } catch (error: any) {
    console.error('Error deleting email template:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};
