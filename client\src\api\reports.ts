import api from './api';

export interface ReportData {
  _id: string;
  title: string;
  type: 'sales' | 'inventory' | 'customers' | 'performance';
  period: 'daily' | 'weekly' | 'monthly' | 'yearly';
  data: any;
  lastUpdated: string;
}

// Description: Get reports data
// Endpoint: GET /api/reports
// Request: {}
// Response: ReportData[]
export const getReports = async () => {
  return new Promise<{ reports: ReportData[] }>((resolve) => {
    setTimeout(() => {
      resolve({
        reports: [
          {
            _id: '1',
            title: 'Sales Overview',
            type: 'sales',
            period: 'monthly',
            data: {
              labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
              values: [4500, 5200, 4800, 6100, 5400, 6800]
            },
            lastUpdated: '2024-01-22'
          },
          {
            _id: '2',
            title: 'Inventory Turnover',
            type: 'inventory',
            period: 'monthly',
            data: {
              categories: ['Electronics', 'Jewelry', 'Watches'],
              turnover: [12, 8, 15]
            },
            lastUpdated: '2024-01-22'
          }
        ]
      });
    }, 500);
  });
};