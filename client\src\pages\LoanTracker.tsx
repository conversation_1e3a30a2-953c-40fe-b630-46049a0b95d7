import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Loader2, Search, Plus, RefreshCw, Edit, Trash2 } from 'lucide-react';
import { useToast } from '@/hooks/useToast';
import { useAuth } from '@/contexts/AuthContext';
import { format, parseISO, isValid, startOfDay, endOfDay, subDays } from 'date-fns';
import {
  getLoanApplications,
  getLoanStatistics,
  createLoanApplication,
  updateLoanApplication,
  deleteLoanApplication,
  getLoanSettings,
  LoanApplication,
  LoanApplicationFormData,
  LoanSubmissionByDay,
  LoanSubmissionByUser,
  PaginationData,
  LoanSettings
} from '@/api/loanApplications';
import { LoanApplicationForm } from '@/components/LoanApplicationForm';
import { PaginationControls } from '@/components/ui/pagination-controls';
import { DateRangePicker } from '@/components/DateRangePicker';
import { DateRange } from 'react-day-picker';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend, AreaChart, Area, ReferenceLine } from 'recharts';
import { LoanStatusForm } from '@/components/LoanStatusForm';

export function LoanTracker() {
  // State for applications list
  const [applications, setApplications] = useState<LoanApplication[]>([]);
  const [pagination, setPagination] = useState<PaginationData>({ total: 0, page: 1, limit: 10, pages: 0 });
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  // State for statistics
  const [statsLoading, setStatsLoading] = useState(true);
  const [statsPeriod, setStatsPeriod] = useState<'7days' | '14days' | '1month' | '1year' | 'all' | 'custom'>('1month');
  const [dateRange, setDateRange] = useState<DateRange | undefined>();
  const [submissionsByDay, setSubmissionsByDay] = useState<LoanSubmissionByDay[]>([]);
  const [submissionsByUser, setSubmissionsByUser] = useState<LoanSubmissionByUser[]>([]);

  // State for goals
  const [settings, setSettings] = useState<LoanSettings | null>(null);
  const [weeklyStats, setWeeklyStats] = useState<{
    weeklySubmissions: number;
    weeklyFunded: number;
    weekStart: Date;
    weekEnd: Date;
  }>({
    weeklySubmissions: 0,
    weeklyFunded: 0,
    weekStart: new Date(),
    weekEnd: new Date()
  });

  // State for modals
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedApplication, setSelectedApplication] = useState<LoanApplication | null>(null);

  const { toast } = useToast();
  const { user } = useAuth();
  const isAdminOrManager = user?.role === 'admin' || user?.role === 'manager';

  // Load applications on initial render and when filters change
  useEffect(() => {
    // Only fetch when page or status filter changes
    // We handle limit changes separately in the onLimitChange handler
    fetchApplications();
  }, [pagination.page, statusFilter]);

  // Load statistics on initial render and when period or date range changes
  useEffect(() => {
    fetchStatistics();
  }, [statsPeriod, dateRange]);

  // Load settings and calculate weekly stats on initial render
  useEffect(() => {
    // Only fetch settings for admin/manager roles
    if (isAdminOrManager) {
      fetchSettings();
    }
    calculateWeeklyStats();
  }, [isAdminOrManager]);

  // Fetch loan applications with current filters
  const fetchApplications = async (customLimit?: number) => {
    setLoading(true);
    try {
      // Use customLimit if provided, otherwise use the state value
      const limitToUse = customLimit || pagination.limit;
      console.log(`Client: Fetching loan applications with limit=${limitToUse}`);

      const response = await getLoanApplications(
        pagination.page,
        limitToUse,
        searchTerm,
        statusFilter
      );

      if (response.success) {
        setApplications(response.data);
        setPagination(response.pagination);
      } else {
        toast({
          title: 'Error',
          description: 'Failed to load loan applications',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error fetching loan applications:', error);
      toast({
        title: 'Error',
        description: 'Failed to load loan applications',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch loan statistics
  const fetchStatistics = async () => {
    setStatsLoading(true);
    try {
      let startDate: string | undefined;
      let endDate: string | undefined;

      if (statsPeriod === 'custom' && dateRange?.from) {
        startDate = startOfDay(dateRange.from).toISOString();
        endDate = dateRange.to ? endOfDay(dateRange.to).toISOString() : endOfDay(dateRange.from).toISOString();
      }

      const response = await getLoanStatistics(statsPeriod, startDate, endDate);

      if (response.success) {
        setSubmissionsByDay(response.data.submissionsByDay);
        setSubmissionsByUser(response.data.submissionsByUser);
      } else {
        toast({
          title: 'Error',
          description: 'Failed to load loan statistics',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error fetching loan statistics:', error);
      toast({
        title: 'Error',
        description: 'Failed to load loan statistics',
        variant: 'destructive',
      });
    } finally {
      setStatsLoading(false);
    }
  };

  // Fetch loan settings
  const fetchSettings = async () => {
    try {
      const response = await getLoanSettings();

      if (response.success) {
        setSettings(response.data);
      } else {
        toast({
          title: 'Error',
          description: 'Failed to load loan settings',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error fetching loan settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to load loan settings',
        variant: 'destructive',
      });
    }
  };

  // Calculate weekly statistics (Monday to Sunday)
  const calculateWeeklyStats = async () => {
    try {
      // Calculate current week's start (Monday) and end (Sunday)
      const today = new Date();
      const dayOfWeek = today.getDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday

      // Calculate days to subtract to get to Monday (if today is Sunday, subtract 6 days)
      const daysToSubtract = dayOfWeek === 0 ? 6 : dayOfWeek - 1;

      const weekStart = new Date(today);
      weekStart.setDate(today.getDate() - daysToSubtract);
      weekStart.setHours(0, 0, 0, 0);

      const weekEnd = new Date(weekStart);
      weekEnd.setDate(weekStart.getDate() + 6);
      weekEnd.setHours(23, 59, 59, 999);

      // Fetch statistics for the current week
      const response = await getLoanStatistics(
        'custom',
        weekStart.toISOString(),
        weekEnd.toISOString()
      );

      if (response.success) {
        // Calculate weekly totals
        const weeklySubmissions = response.data.submissionsByDay.reduce(
          (sum, day) => sum + (day.total || 0), 0
        );

        const weeklyFunded = response.data.submissionsByDay.reduce(
          (sum, day) => sum + ((day.approved || 0) + (day.approvedPaid || 0)), 0
        );

        setWeeklyStats({
          weeklySubmissions,
          weeklyFunded,
          weekStart,
          weekEnd
        });
      }
    } catch (error) {
      console.error('Error calculating weekly statistics:', error);
    }
  };

  // Effect to handle search as user types
  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setPagination(prev => ({ ...prev, page: 1 })); // Reset to first page
      fetchApplications();
    }, 300); // 300ms delay after typing stops

    return () => clearTimeout(delayDebounceFn);
  }, [searchTerm]);

  // Handle search button click
  const handleSearch = () => {
    setPagination(prev => ({ ...prev, page: 1 })); // Reset to first page
    fetchApplications();
  };

  // Handle add application
  const handleAddApplication = async (data: LoanApplicationFormData) => {
    try {
      const response = await createLoanApplication(data);

      if (response.success) {
        toast({
          title: 'Success',
          description: 'Loan application added successfully',
        });
        setIsAddModalOpen(false);
        fetchApplications();
        fetchStatistics();
        calculateWeeklyStats();
      } else {
        toast({
          title: 'Error',
          description: 'Failed to add loan application',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error adding loan application:', error);
      toast({
        title: 'Error',
        description: 'Failed to add loan application',
        variant: 'destructive',
      });
    }
  };

  // Handle edit application
  const handleEditApplication = async (data: LoanApplicationFormData) => {
    if (!selectedApplication) return;

    try {
      const response = await updateLoanApplication(selectedApplication._id, data);

      if (response.success) {
        toast({
          title: 'Success',
          description: 'Loan application updated successfully',
        });
        setIsEditModalOpen(false);
        fetchApplications();
        fetchStatistics();
        calculateWeeklyStats();
      } else {
        toast({
          title: 'Error',
          description: 'Failed to update loan application',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error updating loan application:', error);
      toast({
        title: 'Error',
        description: 'Failed to update loan application',
        variant: 'destructive',
      });
    }
  };

  // Handle delete application
  const handleDeleteApplication = async () => {
    if (!selectedApplication) return;

    try {
      const response = await deleteLoanApplication(selectedApplication._id);

      if (response.success) {
        toast({
          title: 'Success',
          description: 'Loan application deleted successfully',
        });
        setIsDeleteModalOpen(false);
        fetchApplications();
        fetchStatistics();
        calculateWeeklyStats();
      } else {
        toast({
          title: 'Error',
          description: 'Failed to delete loan application',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error deleting loan application:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete loan application',
        variant: 'destructive',
      });
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = parseISO(dateString);
    return isValid(date) ? format(date, 'PPP') : 'Invalid date';
  };

  // Get status badge color
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'awaitingDecision':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">Awaiting Decision</Badge>;
      case 'approved':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Approved</Badge>;
      case 'approvedPaid':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Approved & Paid</Badge>;
      case 'declined':
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Declined</Badge>;
      case 'cancelled':
        return <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">Cancelled</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };


  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Personal Loan Tracker</h1>
        <Button onClick={() => setIsAddModalOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Add Application
        </Button>
      </div>

      {/* Statistics Section */}
      <div className="space-y-4">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <h2 className="text-xl font-semibold">Statistics</h2>
          <div className="flex flex-col sm:flex-row gap-4">
              <Select value={statsPeriod} onValueChange={(value: any) => {
                setStatsPeriod(value);
                // Initialize date range if not set
                if (!dateRange) {
                  const to = new Date();
                  const from = subDays(to, 30);
                  setDateRange({ from, to });
                }
              }}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select period" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7days">Last 7 Days</SelectItem>
                  <SelectItem value="14days">Last 14 Days</SelectItem>
                  <SelectItem value="1month">Last Month</SelectItem>
                  <SelectItem value="1year">Last Year</SelectItem>
                  <SelectItem value="all">All Time</SelectItem>
                  <SelectItem value="custom">Custom Range</SelectItem>
                </SelectContent>
              </Select>

              <DateRangePicker
                dateRange={dateRange}
                onDateRangeChange={setDateRange}
              />
          </div>
        </div>

        {statsLoading ? (
          <div className="flex justify-center items-center h-64">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : (
          <div className="space-y-6">
            {/* Weekly Goals Card */}
            {settings && (
              <Card className="mb-6">
                <CardHeader>
                  <CardTitle>Weekly Goals</CardTitle>
                  <CardDescription>
                    Progress for current week ({format(weeklyStats.weekStart, 'MMM dd')} - {format(weeklyStats.weekEnd, 'MMM dd')})
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Submissions Goal */}
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <h3 className="text-sm font-medium">Submissions Goal</h3>
                        <span className="text-sm text-muted-foreground">
                          {weeklyStats.weeklySubmissions} / {settings.weeklySubmissionGoal}
                        </span>
                      </div>
                      <div className="h-2 bg-gray-800 rounded-full overflow-hidden">
                        <div
                          className="h-full bg-indigo-500 rounded-full"
                          style={{
                            width: `${Math.min(100, (weeklyStats.weeklySubmissions / settings.weeklySubmissionGoal) * 100)}%`,
                          }}
                        ></div>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {Math.round((weeklyStats.weeklySubmissions / settings.weeklySubmissionGoal) * 100)}% of weekly goal
                      </p>
                    </div>

                    {/* Funded Goal */}
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <h3 className="text-sm font-medium">Funded Goal</h3>
                        <span className="text-sm text-muted-foreground">
                          {weeklyStats.weeklyFunded} / {settings.weeklyFundedGoal}
                        </span>
                      </div>
                      <div className="h-2 bg-gray-800 rounded-full overflow-hidden">
                        <div
                          className="h-full bg-green-500 rounded-full"
                          style={{
                            width: `${Math.min(100, (weeklyStats.weeklyFunded / settings.weeklyFundedGoal) * 100)}%`,
                          }}
                        ></div>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {Math.round((weeklyStats.weeklyFunded / settings.weeklyFundedGoal) * 100)}% of weekly goal
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Statistics Cards */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
              {/* Calculate statistics from the data */}
              {(() => {
                // Calculate totals
                const totalSubmitted = submissionsByDay.reduce((sum, day) => sum + (day.total || 0), 0);
                const totalApproved = submissionsByDay.reduce((sum, day) => sum + ((day.approved || 0) + (day.approvedPaid || 0)), 0);
                const totalDeclined = submissionsByDay.reduce((sum, day) => sum + (day.declined || 0), 0);
                const totalAwaitingDecision = submissionsByDay.reduce((sum, day) => sum + (day.awaitingDecision || 0), 0);

                // Calculate approval rate
                const approvalRate = totalSubmitted > 0
                  ? ((totalApproved / totalSubmitted) * 100).toFixed(1)
                  : '0.0';

                // Stat items with their colors
                const stats = [
                  { label: 'Total Submitted', value: totalSubmitted, color: '#8884d8', icon: '📝' },
                  { label: 'Total Approved', value: totalApproved, color: '#82ca9d', icon: '✅' },
                  { label: 'Total Declined', value: totalDeclined, color: '#ff6b6b', icon: '❌' },
                  { label: 'Awaiting Decision', value: totalAwaitingDecision, color: '#ffc107', icon: '⏳' },
                  { label: 'Approval Rate', value: `${approvalRate}%`, color: '#4caf50', icon: '📊' },
                ];

                return stats.map((stat, index) => (
                  <div key={index} className="flex items-center p-4 rounded-lg" style={{ backgroundColor: 'rgba(30, 30, 30, 0.6)', border: '1px solid #333' }}>
                    <div className="flex items-center justify-center h-12 w-12 rounded-full mr-4" style={{ backgroundColor: stat.color + '20', color: stat.color }}>
                      <span className="text-xl">{stat.icon}</span>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-400">{stat.label}</p>
                      <p className="text-2xl font-bold" style={{ color: stat.color }}>{stat.value}</p>
                    </div>
                  </div>
                ));
              })()}
            </div>

            {/* Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Submissions by Day Chart */}
              <Card>
                <CardHeader>
                  <CardTitle>Loan Submissions Over Time</CardTitle>
                  <CardDescription>Tracking submitted and approved loans</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[400px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart
                        data={submissionsByDay.map(item => ({
                          ...item,
                          combinedApproved: (item.approved || 0) + (item.approvedPaid || 0),
                          submissionGoal: settings?.weeklySubmissionGoal ? settings.weeklySubmissionGoal / 7 : 0, // Daily goal based on weekly
                          fundedGoal: settings?.weeklyFundedGoal ? settings.weeklyFundedGoal / 7 : 0 // Daily goal based on weekly
                        }))}
                        margin={{ top: 10, right: 30, left: 20, bottom: 30 }}
                      >
                        <defs>
                          <linearGradient id="colorTotal" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="0%" stopColor="#6366f1" stopOpacity={0.5}/>
                            <stop offset="100%" stopColor="#6366f1" stopOpacity={0.1}/>
                          </linearGradient>
                          <linearGradient id="colorApproved" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="0%" stopColor="#22c55e" stopOpacity={0.5}/>
                            <stop offset="100%" stopColor="#22c55e" stopOpacity={0.1}/>
                          </linearGradient>
                        </defs>
                        <XAxis
                          dataKey="date"
                          axisLine={false}
                          tickLine={false}
                          tick={{ fontSize: 12, fill: '#888' }}
                          tickFormatter={(date) => format(new Date(date), 'MMM dd')}
                          tickMargin={10}
                          height={40}
                          stroke="#444"
                          minTickGap={5}
                        />
                        <YAxis
                          axisLine={false}
                          tickLine={false}
                          tick={{ fontSize: 12, fill: '#888' }}
                          tickMargin={10}
                          stroke="#444"
                          width={40}
                        />
                        <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="#333" opacity={0.4} />
                        <Tooltip
                          content={({ active, payload, label }) => {
                            if (active && payload && payload.length) {
                              return (
                                <div style={{
                                  backgroundColor: '#1e1e1e',
                                  border: '1px solid #333',
                                  borderRadius: '4px',
                                  padding: '8px 12px',
                                  boxShadow: '0 2px 5px rgba(0,0,0,0.5)'
                                }}>
                                  <p style={{ color: '#e0e0e0', margin: '0 0 8px 0', fontWeight: 'bold' }}>
                                    {format(new Date(label), 'MMM dd, yyyy')}
                                  </p>
                                  {payload.map((entry) => (
                                    <div key={entry.name} style={{ display: 'flex', alignItems: 'center', marginBottom: '4px' }}>
                                      <div style={{
                                        width: '10px',
                                        height: '10px',
                                        backgroundColor: entry.dataKey === 'total' ? '#6366f1' : '#22c55e',
                                        marginRight: '8px',
                                        borderRadius: '50%'
                                      }} />
                                      <p style={{ color: '#e0e0e0', margin: 0 }}>
                                        {entry.name}: <span style={{ fontWeight: 'bold' }}>{entry.value}</span>
                                      </p>
                                    </div>
                                  ))}
                                </div>
                              );
                            }
                            return null;
                          }}
                          cursor={{ stroke: '#666', strokeWidth: 1 }}
                        />
                        <Legend
                          verticalAlign="bottom"
                          height={36}
                          iconSize={16}
                          wrapperStyle={{ fontSize: 14, paddingBottom: 5 }}
                          iconType="circle"
                          formatter={(value) => <span style={{ color: '#e0e0e0' }}>{value}</span>}
                        />
                        <Area
                          type="monotone"
                          dataKey="total"
                          name="Total Submitted"
                          stroke="#6366f1"
                          strokeWidth={2}
                          fill="url(#colorTotal)"
                          activeDot={{ r: 6, strokeWidth: 0 }}
                        />
                        <Area
                          type="monotone"
                          dataKey="combinedApproved"
                          name="Total Approved"
                          stroke="#22c55e"
                          strokeWidth={2}
                          fill="url(#colorApproved)"
                          activeDot={{ r: 6, strokeWidth: 0 }}
                        />
                        {settings && settings.weeklySubmissionGoal && settings.weeklyFundedGoal && (
                          <>
                            {/* Reference line for submission goal */}
                            <ReferenceLine
                              y={settings.weeklySubmissionGoal / 7}
                              stroke="#6366f1"
                              strokeDasharray="3 3"
                              label={{
                                value: 'Submission Goal',
                                position: 'insideTopRight',
                                fill: '#6366f1',
                                fontSize: 12
                              }}
                            />
                            {/* Reference line for funded goal */}
                            <ReferenceLine
                              y={settings.weeklyFundedGoal / 7}
                              stroke="#22c55e"
                              strokeDasharray="3 3"
                              label={{
                                value: 'Funded Goal',
                                position: 'insideBottomRight',
                                fill: '#22c55e',
                                fontSize: 12
                              }}
                            />
                          </>
                        )}
                      </AreaChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>

              {/* Submissions by User Chart */}
              <Card>
                <CardHeader>
                  <CardTitle>Submissions by User</CardTitle>
                  <CardDescription>Number of loans submitted and approved by each user</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[400px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        data={submissionsByUser.map(item => ({
                          ...item,
                          combinedApproved: (item.approved || 0) + (item.approvedPaid || 0),
                          // Extract first name for display
                          firstName: item.fullName.split(' ')[0]
                        }))}
                        margin={{ top: 10, right: 30, left: 20, bottom: 30 }}
                        barGap={2}
                        barCategoryGap={16}
                      >
                        <defs>
                          <linearGradient id="colorSubmitted" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="0%" stopColor="#6366f1" stopOpacity={0.9}/>
                            <stop offset="100%" stopColor="#6366f1" stopOpacity={0.7}/>
                          </linearGradient>
                          <linearGradient id="colorApprovedBar" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="0%" stopColor="#22c55e" stopOpacity={0.9}/>
                            <stop offset="100%" stopColor="#22c55e" stopOpacity={0.7}/>
                          </linearGradient>
                        </defs>
                        <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="#333" opacity={0.4} />
                        <XAxis
                          dataKey="firstName"
                          axisLine={false}
                          tickLine={false}
                          tick={{ fontSize: 12, fill: '#888' }}
                          height={30}
                          interval={0}
                          stroke="#444"
                          padding={{ left: 10, right: 10 }}
                        />
                        <YAxis
                          axisLine={false}
                          tickLine={false}
                          tick={{ fontSize: 12, fill: '#888' }}
                          tickMargin={10}
                          stroke="#444"
                          width={40}
                        />
                        <Tooltip
                          content={({ active, payload, label }) => {
                            if (active && payload && payload.length) {
                              return (
                                <div style={{
                                  backgroundColor: '#1e1e1e',
                                  border: '1px solid #333',
                                  borderRadius: '4px',
                                  padding: '8px 12px',
                                  boxShadow: '0 2px 5px rgba(0,0,0,0.5)'
                                }}>
                                  <p style={{ color: '#e0e0e0', margin: '0 0 8px 0', fontWeight: 'bold' }}>
                                    User: {label}
                                  </p>
                                  {payload.map((entry) => (
                                    <div key={entry.name} style={{ display: 'flex', alignItems: 'center', marginBottom: '4px' }}>
                                      <div style={{
                                        width: '10px',
                                        height: '10px',
                                        backgroundColor: entry.dataKey === 'count' ? '#6366f1' : '#22c55e',
                                        marginRight: '8px',
                                        borderRadius: '50%'
                                      }} />
                                      <p style={{ color: '#e0e0e0', margin: 0 }}>
                                        {entry.name}: <span style={{ fontWeight: 'bold' }}>{entry.value}</span>
                                      </p>
                                    </div>
                                  ))}
                                </div>
                              );
                            }
                            return null;
                          }}
                        />
                        <Legend
                          verticalAlign="bottom"
                          height={36}
                          iconSize={16}
                          wrapperStyle={{ fontSize: 14, paddingBottom: 5 }}
                          iconType="circle"
                          formatter={(value) => <span style={{ color: '#e0e0e0' }}>{value}</span>}
                        />
                        <Bar
                          dataKey="count"
                          name="Total Submitted"
                          fill="url(#colorSubmitted)"
                          barSize={24}
                          radius={[4, 4, 0, 0]}
                          stroke="#6366f1"
                          strokeWidth={1}
                        />
                        <Bar
                          dataKey="combinedApproved"
                          name="Total Approved"
                          fill="url(#colorApprovedBar)"
                          barSize={24}
                          radius={[4, 4, 0, 0]}
                          stroke="#22c55e"
                          strokeWidth={1}
                        />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </div>

      {/* Applications Table Section */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Applications</CardTitle>
          <CardDescription>View and manage loan applications</CardDescription>
        </CardHeader>
        <CardContent>
          {/* Search and Filter */}
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex w-full md:w-1/2">
              <Input
                placeholder="Search by customer name or loan ID..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="rounded-r-none"
              />
              <Button
                variant="default"
                className="rounded-l-none"
                onClick={handleSearch}
              >
                <Search className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex gap-2 w-full md:w-1/2 md:justify-end">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="awaitingDecision">Awaiting Decision</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="approvedPaid">Approved & Paid</SelectItem>
                  <SelectItem value="declined">Declined</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" onClick={() => fetchApplications()}>
                <RefreshCw className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Applications Table */}
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : applications.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No loan applications found.
            </div>
          ) : (
            <>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Loan ID</TableHead>
                      <TableHead>Customer</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Submitted Date</TableHead>
                      <TableHead>Submitted By</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {applications.map((application) => (
                      <TableRow key={application._id}>
                        <TableCell className="font-medium">{application.loanId}</TableCell>
                        <TableCell>{application.customerName}</TableCell>
                        <TableCell>${application.loanAmount.toLocaleString()}</TableCell>
                        <TableCell>{formatDate(application.submittedDate)}</TableCell>
                        <TableCell>{application.submittedBy.fullName}</TableCell>
                        <TableCell>{getStatusBadge(application.status)}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => {
                                setSelectedApplication(application);
                                setIsEditModalOpen(true);
                              }}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            {isAdminOrManager && (
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => {
                                  setSelectedApplication(application);
                                  setIsDeleteModalOpen(true);
                                }}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination */}
              <div className="mt-4 flex justify-end">
                <PaginationControls
                  currentPage={pagination.page}
                  totalPages={pagination.pages}
                  totalItems={pagination.total}
                  limit={pagination.limit}
                  onPageChange={(page) => setPagination(prev => ({ ...prev, page }))}
                  onLimitChange={(limit) => {
                    // Ensure limit is a number
                    const numericLimit = Number(limit);
                    console.log(`LoanTracker: Changing limit to ${limit} (${typeof limit}), numeric: ${numericLimit}`);

                    // Update pagination state
                    setPagination(prev => ({ ...prev, limit: numericLimit, page: 1 }));

                    // Use setTimeout to ensure state is updated before fetching
                    setTimeout(() => {
                      // Pass the numeric limit directly to fetchApplications
                      fetchApplications(numericLimit);
                    }, 0);
                  }}
                />
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Add Application Modal */}
      <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Add New Loan Application</DialogTitle>
            <DialogDescription>
              Enter the details of the new loan application.
            </DialogDescription>
          </DialogHeader>
          <LoanApplicationForm onSubmit={handleAddApplication} />
        </DialogContent>
      </Dialog>

      {/* Edit Application Modal */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit Loan Application</DialogTitle>
            <DialogDescription>
              Update the details of the loan application.
            </DialogDescription>
          </DialogHeader>
          {selectedApplication && (
            <>
              {/* Show full form for admins/managers or the employee who submitted the loan */}
              {(isAdminOrManager || selectedApplication.submittedBy._id === user?._id) ? (
                <LoanApplicationForm
                  onSubmit={handleEditApplication}
                  initialData={{
                    loanId: selectedApplication.loanId,
                    loanAmount: selectedApplication.loanAmount,
                    customerId: selectedApplication.customerId,
                    customerName: selectedApplication.customerName,
                    status: selectedApplication.status,
                    submittedDate: selectedApplication.submittedDate,
                    submittedBy: selectedApplication.submittedBy._id,
                    notes: selectedApplication.notes,
                  }}
                />
              ) : (
                /* Show status-only form for employees editing other users' loans */
                <LoanStatusForm
                  onSubmit={handleEditApplication}
                  initialData={{
                    loanId: selectedApplication.loanId,
                    loanAmount: selectedApplication.loanAmount,
                    customerId: selectedApplication.customerId,
                    customerName: selectedApplication.customerName,
                    status: selectedApplication.status,
                    submittedDate: selectedApplication.submittedDate,
                    submittedBy: selectedApplication.submittedBy._id,
                    notes: selectedApplication.notes,
                  }}
                />
              )}
            </>
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Modal */}
      <Dialog open={isDeleteModalOpen} onOpenChange={setIsDeleteModalOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this loan application? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end gap-2 mt-4">
            <Button variant="outline" onClick={() => setIsDeleteModalOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteApplication}>
              Delete
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
