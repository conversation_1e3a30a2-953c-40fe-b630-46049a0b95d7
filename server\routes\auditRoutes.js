const express = require('express');
const router = express.Router();
const auditService = require('../services/auditService');
const { requireUser } = require('./middleware/auth');

// Get audit logs for an item
router.get('/item/:itemId', requireUser, async (req, res) => {
  try {
    
    // Removed permission check to allow all authenticated users to access audit logs
    const itemId = req.params.itemId;
    const result = await auditService.getLogsByItem(itemId);

    if (result.success) {
      return res.status(200).json({
        success: true,
        data: result.logs,
      });
    } else {
      console.error(`Failed to retrieve audit logs: ${result.error}`);
      return res.status(400).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error('Error in get audit logs route:', error);
    return res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

module.exports = router;