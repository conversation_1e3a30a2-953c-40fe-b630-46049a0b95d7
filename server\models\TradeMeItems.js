const mongoose = require('mongoose');

/**
 * EMBEDDED SCHEMAS
 * These schemas are embedded within the main TradeMeItems schema
 */

/**
 * Question Schema - Embedded in TradeMeItems
 * Represents questions asked on TradeMe listings
 */
const questionSchema = new mongoose.Schema({
  trademeQuestionId: {
    type: String,
    required: true
  },
  question: {
    type: String,
    required: true
  },
  answer: {
    type: String
  },
  askerName: {
    type: String,
    required: true
  },
  askDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  answerDate: {
    type: Date
  },
  isAnswered: {
    type: Boolean,
    default: false
  },
  answeredBy: {
    type: mongoose.Schema.Types.Mixed, // Can be ObjectId (for User) or String (for 'Imported')
    ref: 'User',
    get: function(v) {
      return v === 'Imported' ? { username: 'Imported', fullName: 'Imported' } : v;
    }
  },
  originalListingId: {
    type: String,
    required: true,
    comment: 'The TradeMe listing ID where this question was originally asked'
  },
  comment: {
    type: String,
    comment: 'Internal comment about this question (not visible to TradeMe users)'
  }
});

/**
 * Listing History Schema - Embedded in TradeMeItems
 * Tracks the history of an item's listings on TradeMe
 */
const listingHistorySchema = new mongoose.Schema({
  trademeListingId: {
    type: String,
    required: true
  },
  startDate: {
    type: Date,
    required: true
  },
  endDate: {
    type: Date,
    required: true
  },
  startPrice: {
    type: Number,
    required: true
  },
  buyNowPrice: {
    type: Number
  },
  reservePrice: {
    type: Number
  },
  views: {
    type: Number,
    default: 0
  },
  watchers: {
    type: Number,
    default: 0
  },
  bids: {
    type: Number,
    default: 0
  },
  status: {
    type: String,
    enum: ['active', 'sold', 'unsold', 'withdrawn'],
    required: true
  },
  soldPrice: {
    type: Number
  },
  soldDate: {
    type: Date
  },
  buyer: {
    type: String
  },
  createdBy: {
    type: mongoose.Schema.Types.Mixed, // Can be ObjectId (for User) or String (for 'Imported')
    ref: 'User',
    required: true,
    get: function(v) {
      return v === 'Imported' ? { username: 'Imported', fullName: 'Imported' } : v;
    }
  }
});

/**
 * Fault Category Schema - Embedded in TradeMeItems
 * Represents categories of faults for items
 */
const faultCategorySchema = new mongoose.Schema({
  name: {
    type: String,
    required: true
  },
  description: {
    type: String
  }
});

/**
 * Note Schema - Embedded in TradeMeItems
 * Represents internal notes about the item
 */
const noteSchema = new mongoose.Schema({
  text: {
    type: String,
    required: true
  },
  createdBy: {
    type: mongoose.Schema.Types.Mixed, // Can be ObjectId (for User) or String (for 'Imported')
    ref: 'User',
    required: true,
    get: function(v) {
      return v === 'Imported' ? { username: 'Imported', fullName: 'Imported' } : v;
    }
  },
  createdAt: {
    type: Date,
    default: Date.now,
    immutable: true
  }
});

/**
 * MAIN SCHEMA
 * TradeMeItems Schema - Represents a single physical item that can be listed on TradeMe
 */
const tradeMeItemsSchema = new mongoose.Schema({
  /**
   * BASIC ITEM INFORMATION
   * Core details about the item
   */
  title: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true
  },
  stockCode: {
    type: String,
    trim: true,
    index: true
  },
  cost: {
    type: Number,
    min: 0,
    comment: 'Cost of the item (internal, not shown on TradeMe)'
  },
  subtitle: {
    type: String,
    maxlength: 50
  },

  /**
   * LOCATION INFORMATION
   * Where the item is physically located
   */
  location: {
    type: String,
    comment: 'Physical location of the item (e.g., shelf, retail floor)'
  },
  locationId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Location',
    required: true
  },
  geographicLocation: {
    latitude: Number,
    longitude: Number,
    accuracy: {
      type: String,
      enum: ['None', 'Address', 'Street', 'Suburb']
    }
  },

  /**
   * STATUS INFORMATION
   * Current status of the item
   */
  status: {
    type: String,
    enum: ['draft', 'queued', 'active', 'sold', 'archived', 'removed'],
    default: 'draft',
    required: true,
    index: true
  },
  
  // Removal information
  removalReason: {
    type: String,
    enum: ['sold_in_store', 'listing_error', 'other'],
    default: null
  },
  removalNote: {
    type: String
  },
  removedBy: {
    type: mongoose.Schema.Types.Mixed, // Can be ObjectId (for User) or String (for 'Imported')
    ref: 'User',
    get: function(v) {
      return v === 'Imported' ? { username: 'Imported', fullName: 'Imported' } : v;
    }
  },
  removedAt: {
    type: Date
  },

  // Secondary status for sold items
  soldStatus: {
    type: String,
    enum: [
      'instant_payment_received',
      'bank_payment_received',
      'email_sent',
      'awaiting_pickup_paid',
      'awaiting_pickup_unpaid',
      'awaiting_packaging',
      'awaiting_packaging_paid',
      'awaiting_return_package',
      'awaiting_payment',
      'refunded',
      'partially_refunded',
      'cancelled',
      null
    ],
    default: null
  },
  soldStatusUpdatedBy: {
    type: mongoose.Schema.Types.Mixed, // Can be ObjectId (for User) or String (for 'Imported')
    ref: 'User',
    get: function(v) {
      return v === 'Imported' ? { username: 'Imported', fullName: 'Imported' } : v;
    }
  },
  soldStatusUpdatedAt: {
    type: Date
  },

  /**
   * CURRENT LISTING INFORMATION
   * Details about the current or most recent TradeMe listing
   */
  currentListingId: {
    type: String,
    index: true
  },
  currentPrice: {
    type: Number,
    required: true,
    min: 0
  },
  buyNowPrice: {
    type: Number,
    min: 0
  },
  reservePrice: {
    type: Number,
    min: 0
  },
  category: {
    type: String,
    required: true
  },
  categoryPath: {
    type: String,
    comment: 'Full category path for reference'
  },
  tradeMeCategoryId: {
    type: String,
    comment: 'Reference to the TradeMe category ID'
  },
  /**
   * TRADEME LISTING PROPERTIES
   * Specific properties for TradeMe listings
   */
  isNew: {
    type: Boolean,
    default: false
  },
  isOrNearOffer: {
    type: Boolean,
    default: false,
    comment: 'Specifies if "ONO" (or near offer) should be displayed with price'
  },
  duration: {
    type: Number,
    default: 7,
    comment: 'Duration of listing in days'
  },
  
  // Promotional flags
  isBold: {
    type: Boolean,
    default: false
  },
  isFeatured: {
    type: Boolean,
    default: false
  },
  hasGallery: {
    type: Boolean,
    default: false
  },
  isHighlighted: {
    type: Boolean,
    default: false
  },
  
  // Quantity and pricing
  quantity: {
    type: Number,
    min: 1,
    default: 1
  },
  wasPrice: {
    type: Number,
    min: 0
  },
  
  /**
   * SHIPPING AND PAYMENT OPTIONS
   * Details about shipping and payment methods
   */
  // Shipping options from TradeMe API
  shippingOptions: [{
    type: {
      type: String,
      enum: ['None', 'Undecided', 'Pickup', 'Free', 'Custom', 'TradeMe'],
      default: 'Custom'
    },
    price: {
      type: mongoose.Schema.Types.Decimal128,
      get: function(v) {
        if (v === null || v === undefined) return 0;
        return parseFloat(v.toString());
      },
      default: 0
    },
    method: {
      type: String
    },
    name: {
      type: String,
      comment: 'Display name for the shipping option'
    }
  }],
  
  isFlatShippingCharge: {
    type: Boolean,
    default: true,
    comment: 'If true, shipping is one rate for multiple quantities'
  },
  
  // Payment methods
  paymentMethods: [{
    type: String,
    enum: ['BankDeposit', 'CreditCard', 'Cash', 'Other', 'Ping', 'Afterpay']
  }],
  otherPaymentMethod: {
    type: String
  },
  sendPaymentInstructions: {
    type: Boolean,
    default: false
  },
  
  // Pickup options
  pickup: {
    type: String,
    enum: ['None', 'Allow', 'Demand', 'Forbid'],
    default: 'Allow'
  },
  
  /**
   * SOLD ITEM INFORMATION
   * Details about sold items
   */
  // Sold information
  soldPrice: {
    type: mongoose.Schema.Types.Decimal128,
    get: function(v) {
      if (v === null || v === undefined) return 0;
      return parseFloat(v.toString());
    },
    comment: 'Price the item sold for on TradeMe'
  },
  soldDate: {
    type: Date,
    comment: 'Date the item was sold on TradeMe'
  },
  purchaseId: {
    type: String,
    comment: 'TradeMe purchase ID for the sale'
  },
  referenceNumber: {
    type: String,
    comment: 'TradeMe reference number for the sale'
  },
  
  // Buyer information
  buyer: {
    type: mongoose.Schema.Types.Mixed, // Can be ObjectId (for User) or String (for 'Imported')
    ref: 'User',
    comment: 'Employee who purchased the item from customer',
    get: function(v) {
      return v === 'Imported' ? { username: 'Imported', fullName: 'Imported' } : v;
    }
  },
  
  // Buyer delivery information
  buyerDeliveryAddress: {
    name: String,
    address1: String,
    address2: String,
    suburb: String,
    city: String,
    postcode: String,
    country: String,
    phoneNumber: String
  },
  
  // Buyer payment information
  buyerPaymentInfo: {
    // TradeMe buyer information
    tradeMeBuyer: {
      type: String,
      comment: 'TradeMe username of the buyer'
    },
    
    // Purchase and reference numbers
    purchaseId: {
      type: String,
      comment: 'TradeMe purchase ID for the sale'
    },
    referenceNumber: {
      type: String,
      comment: 'TradeMe reference number for the sale'
    },
    
    // Selected shipping information
    selectedShipping: {
      type: String,
      comment: 'Selected shipping method for the sale'
    },
    
    // Message from buyer
    messageFromBuyer: {
      type: String,
      comment: 'Message from the buyer during checkout'
    },
    
    // Feedback flags
    hasSellerPlacedFeedback: {
      type: Boolean,
      default: false,
      comment: 'Whether the seller has placed feedback for this sale'
    },
    hasBuyerPlacedFeedback: {
      type: Boolean,
      default: false,
      comment: 'Whether the buyer has placed feedback for this sale'
    },
    
    // Clearance flag
    isClearance: {
      type: Boolean,
      default: false,
      comment: 'Whether this was a clearance sale'
    },
    
    // Credit card information
    hasPaidByCreditCard: Boolean,
    creditCardType: String,
    creditCardLastFourDigits: String,
    creditCardPaymentDate: Date,
    isPaymentPending: Boolean,
    
    // Payment type and amounts
    paymentType: {
      type: String,
      enum: ['None', 'BankDeposit', 'CreditCard', 'Cash', 'SafeTrader', 'Other', 'Ping', 'Afterpay'],
      default: 'None',
      comment: 'Payment method used by the buyer (mapped from TradeMe PaymentType/PaymentMethod)'
    },
    paymentAmount: {
      type: mongoose.Schema.Types.Decimal128,
      get: function(v) {
        if (v === null || v === undefined) return 0;
        return parseFloat(v.toString());
      },
      default: 0
    },
    paymentMethodFee: {
      type: mongoose.Schema.Types.Decimal128,
      get: function(v) {
        if (v === null || v === undefined) return 0;
        return parseFloat(v.toString());
      },
      default: 0
    },
    gstCollected: {
      type: mongoose.Schema.Types.Decimal128,
      get: function(v) {
        if (v === null || v === undefined) return 0;
        return parseFloat(v.toString());
      },
      default: 0
    },
    pingEscrowStatus: {
      type: String,
      enum: ['None', 'ActiveEscrow', 'ReleasedFromEscrow', 'ReversedFromEscrow'],
      default: 'None'
    },
    isPayNowPurchase: Boolean,
    payNowRefundValue: {
      type: mongoose.Schema.Types.Decimal128,
      get: function(v) {
        if (v === null || v === undefined) return 0;
        return parseFloat(v.toString());
      },
      default: 0
    },
    refunds: [{
      amount: {
        type: mongoose.Schema.Types.Decimal128,
        get: function(v) {
          if (v === null || v === undefined) return 0;
          return parseFloat(v.toString());
        },
        default: 0
      },
      destination: String,
      date: Date
    }]
  },
  
  /**
   * FAULT TRACKING
   * Information about item faults
   */
  isFaulty: {
    type: Boolean,
    default: false
  },
  faultCategories: [faultCategorySchema],
  faultDescription: {
    type: String
  },
  
  /**
   * RELIST SETTINGS
   * Configuration for automatic relisting
   */
  relistSettings: {
    initialListings: {
      type: Number,
      default: 1,
      comment: 'Number of times to list at full price before reducing'
    },
    priceReductionPercent: {
      type: Number,
      default: 2,
      comment: 'Percentage to reduce price on each relist after initialListings'
    },
    autoRelist: {
      type: Boolean,
      default: true,
      comment: 'Whether to automatically relist the item when it ends unsold'
    },
    minPrice: {
      type: Number,
      comment: 'Minimum price to list the item for (will not reduce below this)'
    },
    maxRelistCount: {
      type: Number,
      default: 100,
      comment: 'Maximum number of times to relist the item'
    }
  },
  
  /**
   * COLLECTIONS AND REFERENCES
   * Collections of related data and references to other entities
   */
  // Images
  images: [{
    type: String,
    comment: 'Path to locally stored image'
  }],
  
  // Questions
  questions: [questionSchema],
  
  // Notes
  notes: [noteSchema],
  
  // Listing history
  listingHistory: [listingHistorySchema],
  
  // Embedded content
  embeddedContent: {
    youTubeVideoKey: String,
    vimeoVideoKey: String
  },
  
  // External reference ID
  externalReferenceId: {
    type: String,
    maxlength: 50
  },
  
  // Phone numbers
  homePhoneNumber: {
    type: String
  },
  mobilePhoneNumber: {
    type: String
  },
  
  /**
   * ENVIRONMENT AND METADATA
   * System information and metadata
   */
  // Environment (production or sandbox)
  environment: {
    type: String,
    enum: ['production', 'sandbox'],
    default: 'production'
  },
  
  // Metadata
  createdBy: {
    type: mongoose.Schema.Types.Mixed, // Can be ObjectId (for User) or String (for 'Imported')
    ref: 'User',
    required: true,
    get: function(v) {
      return v === 'Imported' ? { username: 'Imported', fullName: 'Imported' } : v;
    }
  },
  createdAt: {
    type: Date,
    default: Date.now,
    immutable: true
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  
  // Import and sync information
  importedAt: {
    type: Date,
    comment: 'When this item was imported from TradeMe'
  },
  importedBy: {
    type: mongoose.Schema.Types.Mixed, // Can be ObjectId (for User) or String (for 'Imported')
    ref: 'User',
    comment: 'User who imported this item',
    get: function(v) {
      return v === 'Imported' ? { username: 'Imported', fullName: 'Imported' } : v;
    }
  },
  lastSyncedAt: {
    type: Date,
    comment: 'Last time this item was successfully synced with TradeMe API'
  },
  lastSyncAttempt: {
    type: Date,
    comment: 'Last time a sync was attempted (even if it failed)'
  },
  syncErrors: [{
    timestamp: {
      type: Date,
      default: Date.now
    },
    message: {
      type: String,
      required: true
    },
    details: {
      type: mongoose.Schema.Types.Mixed
    }
  }]
}, {
  versionKey: false,
  timestamps: true,
  toJSON: { getters: true },
  toObject: { getters: true }
});

// Indexes for efficient querying
tradeMeItemsSchema.index({ status: 1, createdAt: -1 });
tradeMeItemsSchema.index({ status: 1, soldStatus: 1 });
tradeMeItemsSchema.index({ buyer: 1 });
tradeMeItemsSchema.index({ isFaulty: 1 });
tradeMeItemsSchema.index({ currentListingId: 1 }, { sparse: true });

// Pre-save middleware to update timestamps
tradeMeItemsSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Virtual for calculating total listings
tradeMeItemsSchema.virtual('totalListings').get(function() {
  return this.listingHistory.length;
});

// Virtual for calculating current listing number
tradeMeItemsSchema.virtual('currentListingNumber').get(function() {
  return this.listingHistory.length;
});

// Method to calculate next listing price based on relist settings
tradeMeItemsSchema.methods.calculateNextListingPrice = function() {
  const { initialListings, priceReductionPercent, minPrice } = this.relistSettings;
  const totalListings = this.listingHistory.length;

  // If we haven't reached the initial listings count, use the current price
  if (totalListings < initialListings) {
    return {
      currentPrice: this.currentPrice,
      buyNowPrice: this.buyNowPrice,
      reservePrice: this.reservePrice
    };
  }

  // Calculate reduced prices
  const reductionFactor = 1 - (priceReductionPercent / 100);
  let newCurrentPrice = this.currentPrice * reductionFactor;
  let newBuyNowPrice = this.buyNowPrice ? this.buyNowPrice * reductionFactor : null;
  let newReservePrice = this.reservePrice ? this.reservePrice * reductionFactor : null;

  // Ensure prices don't go below minimum
  if (minPrice) {
    newCurrentPrice = Math.max(newCurrentPrice, minPrice);
    if (newBuyNowPrice) newBuyNowPrice = Math.max(newBuyNowPrice, minPrice);
    if (newReservePrice) newReservePrice = Math.max(newReservePrice, minPrice);
  }

  return {
    currentPrice: Math.round(newCurrentPrice * 100) / 100,
    buyNowPrice: newBuyNowPrice ? Math.round(newBuyNowPrice * 100) / 100 : null,
    reservePrice: newReservePrice ? Math.round(newReservePrice * 100) / 100 : null
  };
};

// Method to add a new listing to history
tradeMeItemsSchema.methods.addListingToHistory = function(listingData) {
  this.listingHistory.push(listingData);

  // Update current listing ID if this is an active listing
  if (listingData.status === 'active') {
    this.currentListingId = listingData.trademeListingId;
    this.status = 'active';
  }

  return this;
};

// Method to add a question
tradeMeItemsSchema.methods.addQuestion = function(questionData) {
  // Check if question already exists
  const existingQuestion = this.questions.find(
    q => q.trademeQuestionId === questionData.trademeQuestionId
  );

  if (existingQuestion) {
    // Update existing question
    Object.assign(existingQuestion, questionData);
  } else {
    // Add new question
    this.questions.push(questionData);
  }

  return this;
};

// Method to answer a question
tradeMeItemsSchema.methods.answerQuestion = function(trademeQuestionId, answer, user) {
  const question = this.questions.find(q => q.trademeQuestionId === trademeQuestionId);

  if (question) {
    question.answer = answer;
    question.isAnswered = true;
    question.answerDate = new Date();
    question.answeredBy = user._id;
  }

  return this;
};

module.exports = mongoose.model('TradeMeItems', tradeMeItemsSchema);
