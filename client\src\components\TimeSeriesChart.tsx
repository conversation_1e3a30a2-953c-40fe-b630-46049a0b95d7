import React from 'react';
import {
  ResponsiveContainer,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  BarChart,
  Bar,
  LineChart,
  Line
} from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export interface TimeSeriesData {
  label: string;
  value: number;
}

interface TimeSeriesChartProps {
  data: TimeSeriesData[];
  title: string;
  description?: string;
  type?: 'area' | 'bar' | 'line';
  height?: number;
  className?: string;
  valueFormatter?: (value: number) => string;
  colors?: {
    stroke: string;
    fill: string;
  };
}

export function TimeSeriesChart({
  data,
  title,
  description,
  type = 'area',
  height = 300,
  className = '',
  valueFormatter = (value: number) => value.toString(),
  colors = {
    stroke: '#3b82f6', // blue-500
    fill: 'rgba(59, 130, 246, 0.2)', // blue-500 with opacity
  }
}: TimeSeriesChartProps) {
  // Custom tooltip formatter
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border rounded-md shadow-md p-2 text-sm">
          <p className="font-medium">{label}</p>
          <p className="text-primary">
            Requests: {valueFormatter(payload[0].value)}
          </p>
        </div>
      );
    }
    return null;
  };

  // Render the appropriate chart type
  const renderChart = () => {
    switch (type) {
      case 'bar':
        return (
          <BarChart data={data}>
            <CartesianGrid strokeDasharray="3 3" vertical={false} />
            <XAxis 
              dataKey="label" 
              tick={{ fontSize: 12 }}
              tickLine={false}
              axisLine={{ stroke: '#e5e7eb' }}
            />
            <YAxis 
              tick={{ fontSize: 12 }}
              tickLine={false}
              axisLine={{ stroke: '#e5e7eb' }}
              tickFormatter={valueFormatter}
            />
            <Tooltip content={<CustomTooltip />} />
            <Bar 
              dataKey="value" 
              name="Requests" 
              fill={colors.fill} 
              stroke={colors.stroke}
              strokeWidth={2}
              radius={[4, 4, 0, 0]}
            />
          </BarChart>
        );
      
      case 'line':
        return (
          <LineChart data={data}>
            <CartesianGrid strokeDasharray="3 3" vertical={false} />
            <XAxis 
              dataKey="label" 
              tick={{ fontSize: 12 }}
              tickLine={false}
              axisLine={{ stroke: '#e5e7eb' }}
            />
            <YAxis 
              tick={{ fontSize: 12 }}
              tickLine={false}
              axisLine={{ stroke: '#e5e7eb' }}
              tickFormatter={valueFormatter}
            />
            <Tooltip content={<CustomTooltip />} />
            <Line 
              type="monotone" 
              dataKey="value" 
              name="Requests" 
              stroke={colors.stroke} 
              strokeWidth={2}
              dot={{ r: 4, fill: colors.stroke, strokeWidth: 2 }}
              activeDot={{ r: 6, fill: colors.stroke, strokeWidth: 2 }}
            />
          </LineChart>
        );
      
      case 'area':
      default:
        return (
          <AreaChart data={data}>
            <defs>
              <linearGradient id="colorValue" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor={colors.stroke} stopOpacity={0.8}/>
                <stop offset="95%" stopColor={colors.stroke} stopOpacity={0.1}/>
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" vertical={false} />
            <XAxis 
              dataKey="label" 
              tick={{ fontSize: 12 }}
              tickLine={false}
              axisLine={{ stroke: '#e5e7eb' }}
            />
            <YAxis 
              tick={{ fontSize: 12 }}
              tickLine={false}
              axisLine={{ stroke: '#e5e7eb' }}
              tickFormatter={valueFormatter}
            />
            <Tooltip content={<CustomTooltip />} />
            <Area 
              type="monotone" 
              dataKey="value" 
              name="Requests" 
              stroke={colors.stroke} 
              strokeWidth={2}
              fill="url(#colorValue)" 
              dot={{ r: 4, fill: colors.stroke, strokeWidth: 2 }}
              activeDot={{ r: 6, fill: colors.stroke, strokeWidth: 2 }}
            />
          </AreaChart>
        );
    }
  };

  return (
    <Card className={className}>
      <CardHeader className="pb-2">
        <CardTitle className="text-base font-medium">{title}</CardTitle>
        {description && <p className="text-xs text-muted-foreground">{description}</p>}
      </CardHeader>
      <CardContent>
        <div style={{ width: '100%', height }}>
          <ResponsiveContainer width="100%" height="100%">
            {renderChart()}
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}
