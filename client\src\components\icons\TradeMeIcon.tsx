import React from 'react';

interface TradeMeIconProps {
  className?: string;
  size?: number | string;
  color?: string;
}

// This component mimics the Lucide icon interface
export const TradeMeIcon: React.FC<TradeMeIconProps> = ({ className }) => {
  return (
    <svg
      className={className}
      width="16"
      height="16"
      viewBox="0 0 48 48"
      xmlns="http://www.w3.org/2000/svg"
      preserveAspectRatio="xMidYMid meet"
    >
      <style>
        {`.j{fill:none;stroke:currentColor;stroke-linecap:round;stroke-linejoin:round;stroke-width:3;}`}
      </style>
      <g>
        <circle className="j" cx="24.9698" cy="17.64" r="1.8627"/>
        <g>
          <path className="j" d="m4.5,21.7792s4.5428-3.2473,6.7346-4.5182c3.0903-1.7919,8.5173-4.5291,11.2977-4.9066c2.0843-.283,3.2596.0306,4.6946.6966c2.0612.9567,4.187,3.8864,4.0282,6.5421-.1818,3.0417-.847,4.1226-2.3624,5.6638-1.0604,1.0784-2.6949,2.623-3.6451,3.7997-4.1393,5.1263-5.1265,13.634-5.1265,13.634"/>
          <path className="j" d="m27.9835,13.5354c3.559-2.5068,9.8863-6.6014,14.0236-8.0831.5083-.1478,1.0751-.0817,1.3645.4522.235.4336.1777,1.1639-.4256,1.5129-11.6901,6.7616-12.8118,8.3897-12.8118,8.3897"/>
          <path className="j" d="m18.0195,10.6283c3.3922-3.9828,7.6326-2.0748,8.5563-1.2418s1.3175,2.6653,1.3175,2.6653m0,0s.8783-1.2569.9389-1.8475c.0606-.5906.6209-2.423-1.045-3.8465-1.6658-1.4236-3.3013-.9843-3.3013-.9843"/>
        </g>
        <circle className="j" cx="23.7005" cy="19.6655" r="4.2405"/>
      </g>
    </svg>
  );
};
