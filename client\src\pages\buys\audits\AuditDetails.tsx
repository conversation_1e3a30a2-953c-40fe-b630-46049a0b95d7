import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useToast } from '@/hooks/useToast';
import { getAuditById } from '@/api/buyPawnAudits';
import { AuditNavigation } from '@/components/buys/audits/AuditNavigation';
import { AuditDetailsView } from '@/components/buys/audits/AuditDetailsView';
import { AuditEditModal } from '@/components/buys/audits/AuditEditModal';
import { AuditFollowupModal } from '@/components/buys/audits/AuditFollowupModal';
import { Button } from '@/components/ui/button';
import { Loader2, ArrowLeft } from 'lucide-react';

/**
 * Component for viewing a single audit's details with comprehensive functionality
 */
export function AuditDetails() {
  const { toast } = useToast();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [isLoading, setIsLoading] = useState(true);
  const [audit, setAudit] = useState<any>(null);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [followupModalOpen, setFollowupModalOpen] = useState(false);
  const [followupMode, setFollowupMode] = useState<'flag' | 'complete' | 'unflag'>('flag');
  
  // Load audit details
  useEffect(() => {
    const loadAudit = async () => {
      if (!id) return;
      
      setIsLoading(true);
      try {
        const result = await getAuditById(id);
        
        if (result.success) {
          setAudit(result.data);
        } else {
          toast({
            title: 'Error',
            description: result.error || 'Failed to load audit details.',
            variant: 'destructive',
          });
        }
      } catch (error: any) {
        toast({
          title: 'Error',
          description: error.message || 'An unexpected error occurred.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    loadAudit();
  }, [id, toast]);

  // Handle audit update
  const handleAuditUpdate = (updatedAudit: any) => {
    setAudit(updatedAudit);
  };

  // Handle edit modal
  const handleEdit = () => {
    setEditModalOpen(true);
  };

  // Handle follow-up actions
  const handleFlag = () => {
    setFollowupMode('flag');
    setFollowupModalOpen(true);
  };

  const handleUnflag = () => {
    setFollowupMode('unflag');
    setFollowupModalOpen(true);
  };

  const handleCompleteFollowup = () => {
    setFollowupMode('complete');
    setFollowupModalOpen(true);
  };

  const handleAddComment = () => {
    // This will be handled by the comments component
    console.log('Add comment clicked');
  };

  
  if (isLoading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  if (!audit) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center gap-4 mb-6">
          <Button variant="outline" onClick={() => navigate('/buys/audits')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Audits
          </Button>
        </div>
        <div className="text-center py-8 text-muted-foreground">
          Audit not found or failed to load.
        </div>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto py-6">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Button variant="outline" onClick={() => navigate('/buys/audits')}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Audits
        </Button>
      </div>

      {/* Navigation */}
      <AuditNavigation />

      {/* Main Content */}
      <AuditDetailsView
        audit={audit}
        onEdit={handleEdit}
        onFlag={handleFlag}
        onUnflag={handleUnflag}
        onCompleteFollowup={handleCompleteFollowup}
        onAddComment={handleAddComment}
      />

      {/* Edit Modal */}
      <AuditEditModal
        audit={audit}
        isOpen={editModalOpen}
        onClose={() => setEditModalOpen(false)}
        onSave={handleAuditUpdate}
      />

      {/* Follow-up Modal */}
      <AuditFollowupModal
        audit={audit}
        isOpen={followupModalOpen}
        onClose={() => setFollowupModalOpen(false)}
        onUpdate={handleAuditUpdate}
        mode={followupMode}
      />
    </div>
  );
}
