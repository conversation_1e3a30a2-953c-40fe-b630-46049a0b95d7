import { LogsStatistics } from '@/api/logs';
import { LogsStatCard } from '@/components/LogsStatCard';
import { TimeSeriesChart } from '@/components/TimeSeriesChart';
import { Activity, Users, Clock, AlertTriangle } from 'lucide-react';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface LogsDashboardProps {
  stats: LogsStatistics;
  isLoading?: boolean;
}

export function LogsDashboard({ stats, isLoading = false }: LogsDashboardProps) {
  // Format number with commas
  const formatNumber = (num: number) => {
    return num.toLocaleString();
  };

  // Render loading state
  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {Array(4).fill(0).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="pb-2">
                <div className="h-5 w-24 bg-muted rounded"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 w-16 bg-muted rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
        <Card className="animate-pulse">
          <CardHeader className="pb-2">
            <div className="h-5 w-40 bg-muted rounded"></div>
          </CardHeader>
          <CardContent>
            <div className="h-[300px] w-full bg-muted rounded"></div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <LogsStatCard
          title="Total Requests"
          value={formatNumber(stats.totalRequests)}
          icon={<Activity className="h-4 w-4" />}
        />
        <LogsStatCard
          title="Unique Users"
          value={formatNumber(stats.uniqueUsers)}
          icon={<Users className="h-4 w-4" />}
        />
        <LogsStatCard
          title="Avg Response Time"
          value={`${stats.avgResponseTime} ms`}
          icon={<Clock className="h-4 w-4" />}
        />
        <LogsStatCard
          title="Error Rate"
          value={`${stats.errorRate}%`}
          icon={<AlertTriangle className="h-4 w-4" />}
          className={stats.errorRate > 5 ? "border-red-500" : ""}
        />
      </div>
      
      <Tabs defaultValue="time" className="w-full">
        <TabsList>
          <TabsTrigger value="time">Requests Over Time</TabsTrigger>
          <TabsTrigger value="types">Request Types</TabsTrigger>
          <TabsTrigger value="status">Status Codes</TabsTrigger>
        </TabsList>
        
        <TabsContent value="time" className="mt-2">
          <TimeSeriesChart
            data={stats.requestsOverTime}
            title="Requests Over Time (Last 7 Days)"
            description="Number of requests per day over the last week"
            type="area"
            height={300}
            valueFormatter={formatNumber}
            colors={{
              stroke: '#3b82f6', // blue-500
              fill: 'rgba(59, 130, 246, 0.2)', // blue-500 with opacity
            }}
          />
        </TabsContent>
        
        <TabsContent value="types" className="mt-2">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base font-medium">Requests by Type</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {stats.requestsByType.map((item, index) => (
                  <div key={index} className="space-y-1">
                    <div className="flex items-center justify-between text-sm">
                      <span>{item.label}</span>
                      <span className="font-medium">{formatNumber(item.value)}</span>
                    </div>
                    <div className="h-2 w-full rounded-full bg-secondary">
                      <div
                        className={`h-full rounded-full ${item.color}`}
                        style={{
                          width: `${Math.min(100, (item.value / Math.max(...stats.requestsByType.map(i => i.value), 1)) * 100)}%`,
                        }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="status" className="mt-2">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base font-medium">Requests by Status Code</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {stats.requestsByStatus.map((item, index) => (
                  <div key={index} className="space-y-1">
                    <div className="flex items-center justify-between text-sm">
                      <span>{item.label}</span>
                      <span className="font-medium">{formatNumber(item.value)}</span>
                    </div>
                    <div className="h-2 w-full rounded-full bg-secondary">
                      <div
                        className={`h-full rounded-full ${item.color}`}
                        style={{
                          width: `${Math.min(100, (item.value / Math.max(...stats.requestsByStatus.map(i => i.value), 1)) * 100)}%`,
                        }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
