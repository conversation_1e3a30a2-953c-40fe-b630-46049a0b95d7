import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";

interface ReAuthModalProps {
  isOpen: boolean;
  onClose: () => void; // Keep for compatibility but won't be used
  onSuccess: () => void;
  onLogout: () => void;
}

export function ReAuthModal({ isOpen, onSuccess, onLogout }: ReAuthModalProps) {
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const { user, login } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!password.trim()) {
      toast.error("Please enter your password");
      return;
    }

    if (!user?.email) {
      console.error("No user email available for re-authentication", { user });
      toast.error("Unable to re-authenticate. Please logout and login again.");
      return;
    }

    setIsLoading(true);
    try {
      await login(user.email, password);
      toast.success("Session extended successfully");
      onSuccess();
      setPassword("");
    } catch (error) {
      console.error("Re-authentication error:", error);
      toast.error("Invalid password. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={() => {}}>
      <DialogContent className="sm:max-w-[425px]" onPointerDownOutside={(e) => e.preventDefault()} onEscapeKeyDown={(e) => e.preventDefault()}>
        <DialogHeader>
          <DialogTitle>Session Timeout</DialogTitle>
          <DialogDescription>
            Your session has expired due to inactivity. Please enter your password for <strong>{user?.email || user?.username || 'your account'}</strong> to continue using the application.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter your password"
                autoFocus
              />
            </div>
          </div>
          <DialogFooter className="flex justify-between">
            <Button variant="outline" type="button" onClick={onLogout}>
              Logout
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Verifying..." : "Continue Session"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
