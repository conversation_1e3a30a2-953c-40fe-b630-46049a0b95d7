import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Loader2, AlertCircle, ImageIcon, CheckCircle, Truck } from 'lucide-react';
import {
  Alert,
  AlertDescription,
  AlertTitle
} from '@/components/ui/alert';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { checkDuplicateStockCode } from '@/api/tradeMeImport';
import { getAllUsers } from '@/api/user';
import { toast } from 'sonner';
import { User } from '@/api/user';

interface ImportItemFormProps {
  item: any;
  locations?: any[];
  onSave: (updatedItem: any) => void;
  onSkip: () => void;
}

const ImportItemForm: React.FC<ImportItemFormProps> = ({
  item,
  onSave,
  onSkip
}) => {
  const [formData, setFormData] = useState({
    stockCode: item.stockCode || '',
    cost: item.cost || 0,
    locationId: '64e5a7e1c2c24a1e5c5f7e1a',
    location: item.location || 'TradeMe Area',
    buyer: item.buyer || 'none',
    isFaulty: item.isFaulty || false,
    faultDescription: item.faultDescription || '',
    faultCategories: item.faultCategories
      ? item.faultCategories.map((cat: any) => cat.name || cat)
      : [],
    relistSettings: {
      autoRelist: item.relistSettings?.autoRelist ?? true,
      priceReductionPercent: item.relistSettings?.priceReductionPercent ?? 2,
      minPrice:
        item.relistSettings?.minPrice ||
        item.cost ||
        item.currentPrice * 0.5,
      maxRelistCount: item.relistSettings?.maxRelistCount ?? 100
    }
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [users, setUsers] = useState<User[]>([]);

  const primaryImage =
    item.images && item.images.length > 0 ? item.images[0] : null;
  const [imageLoaded, setImageLoaded] = useState(false);

  useEffect(() => {
    // Determine if this is a sold item
    const isSoldItem = item.status === 'sold';

    // Always include relistSettings but make them empty for sold items
    const newFormData = {
      stockCode: item.stockCode || '',
      cost: item.cost || 0,
      locationId: '64e5a7e1c2c24a1e5c5f7e1a',
      location: item.location || 'Retail Floor', // Default to Retail Floor
      buyer: item.buyer || 'none',
      isFaulty: isSoldItem ? false : (item.isFaulty || false), // No faulty for sold items
      faultDescription: item.faultDescription || '',
      faultCategories: item.faultCategories
        ? item.faultCategories.map((cat: any) => cat.name || cat)
        : [],
      relistSettings: isSoldItem
        ? {
            autoRelist: false,
            priceReductionPercent: 0,
            minPrice: 0,
            maxRelistCount: 0
          }
        : {
            autoRelist: item.relistSettings?.autoRelist ?? true,
            priceReductionPercent: item.relistSettings?.priceReductionPercent ?? 2,
            minPrice:
              item.relistSettings?.minPrice ||
              item.cost ||
              item.currentPrice * 0.5,
            maxRelistCount: item.relistSettings?.maxRelistCount ?? 100
          }
    };

    setFormData(newFormData);
    setImageLoaded(false);
    setError(null);
  }, [item]);

  useEffect(() => {
    if (primaryImage) {
      const imageUrl = getImageUrl(primaryImage, item.currentListingId);
      checkImageExists(imageUrl).then(() => {});
    }
  }, [primaryImage, item]);

  const getImageUrl = (
    imagePath: string | null,
    listingId: string | number
  ): string => {
    if (!imagePath) return '';
    if (imagePath.startsWith('http')) return imagePath;

    // Use stockCode if available, otherwise fall back to listing ID
    const folderName = item.stockCode && item.stockCode.trim() !== ''
      ? item.stockCode
      : `listing-${listingId}`;

    const filename = imagePath.split('/').pop() || imagePath;
    return `/uploads/trademe/${folderName}/${filename}`;
  };

  const checkImageExists = async (url: string): Promise<boolean> => {
    try {
      const response = await fetch(url, { method: 'HEAD' });
      return response.ok;
    } catch {
      return false;
    }
  };

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const usersList = await getAllUsers();
        const sortedUsers = usersList.sort((a, b) =>
          a.fullName.localeCompare(b.fullName)
        );
        setUsers(sortedUsers);
      } catch {
        toast.error('Failed to load users');
      }
    };
    fetchUsers();
  }, []);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    if (name === 'cost') {
      const costValue = parseFloat(value) || 0;
      setFormData(prev => ({
        ...prev,
        cost: costValue,
        relistSettings: {
          ...prev.relistSettings,
          minPrice: costValue
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleCheckboxChange = (name: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  const isFaultCategorySelected = (category: string): boolean => {
    return formData.faultCategories.some((cat: any) => {
      if (typeof cat === 'string') return cat === category;
      return cat.name === category;
    });
  };

  const handleRelistSettingChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const { name, value } = e.target;
    let parsedValue: boolean | number =
      name === 'autoRelist' ? value === 'true' : Number(value);
    if (name === 'minPrice') {
      const minPrice = parseFloat(value) || 0;
      parsedValue = minPrice;
    }
    setFormData(prev => ({
      ...prev,
      relistSettings: {
        ...prev.relistSettings,
        [name]: parsedValue
      }
    }));
  };

  const handleRelistCheckboxChange = (checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      relistSettings: {
        ...prev.relistSettings,
        autoRelist: checked
      }
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    try {
      if (!formData.stockCode) throw new Error('Stock code is required');
      if (formData.cost <= 0) throw new Error('Cost must be greater than 0');
      if (!formData.location || formData.location.trim() === '')
        throw new Error('Physical location is required');
      if (formData.isFaulty && !formData.faultDescription)
        throw new Error('Fault description is required for faulty items');

      try {
        const checkResult = await checkDuplicateStockCode(
          formData.stockCode,
          item._id
        );
        if (checkResult.isDuplicate) {
          throw new Error(
            `Stock code "${formData.stockCode}" is already in use by another item`
          );
        }
      } catch (checkError: any) {
        if (checkError.message.includes('already in use')) throw checkError;
      }

      const dataToSubmit = {
        ...formData,
        buyer: formData.buyer === 'none' ? null : formData.buyer,
        faultCategories: formData.faultCategories.map((category: string) => ({
          name: category
        }))
      };

      // Instead of calling updateImportedItem, we'll pass the data to the parent component
      // which will handle saving the item to the database
      onSave(dataToSubmit);
    } catch (error: any) {
      setError(error.message || 'Failed to update item');
      toast.error(`Update failed: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <CardTitle>Update Imported Item</CardTitle>
        <CardDescription>
          Please provide additional information for this imported item
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Item Title and Image */}
          <div className="border rounded-lg p-4 bg-muted/10">
            <div className="flex flex-col md:flex-row gap-4">
              {/* Image on the left */}
              {primaryImage && (
                <div className="md:w-1/3">
                  <div className="relative h-48 w-full bg-muted/20 rounded-md overflow-hidden">
                    {/* Placeholder for when image is loading or fails */}
                    <div className="absolute inset-0 flex flex-col items-center justify-center text-gray-400">
                      <ImageIcon className="w-12 h-12 mb-2 opacity-30" />
                      <span className="text-sm">Image not available</span>
                    </div>

                    <img
                      src={getImageUrl(primaryImage, item.currentListingId)}
                      alt={item.title || 'Item image'}
                      className={`w-full h-full object-contain relative z-10 ${imageLoaded ? 'opacity-100' : 'opacity-0'}`}
                      onLoad={() => setImageLoaded(true)}
                      onError={e => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                      }}
                    />
                  </div>

                </div>
              )}

              {/* Title and details on the right */}
              <div className="md:w-2/3 flex flex-col justify-center">
                <h3 className="text-lg font-semibold">{item.title}</h3>
                {item.currentListingId && (
                  <p className="text-xs text-muted-foreground mt-1">
                    TradeMe Listing ID: {item.currentListingId}
                  </p>
                )}
              </div>
            </div>
          </div>
          {/* Item Details */}
          <section>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <Label htmlFor="stockCode" className="text-xs font-medium">
                  Stock Code <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="stockCode"
                  name="stockCode"
                  value={formData.stockCode}
                  onChange={handleChange}
                  required
                  placeholder="Enter stock code"
                  className="mt-1 h-8 text-sm"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Unique identifier
                </p>
              </div>
              <div>
                <Label htmlFor="cost" className="text-xs font-medium">
                  Cost Price <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="cost"
                  name="cost"
                  type="number"
                  min="0.01"
                  step="0.01"
                  value={formData.cost}
                  onChange={handleChange}
                  placeholder="Enter cost"
                  required
                  className="mt-1 h-8 text-sm"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Purchase price of item
                </p>
              </div>
              <div>
                <Label htmlFor="location" className="text-xs font-medium">
                  Physical Location <span className="text-red-500">*</span>
                </Label>
                {formData.location !== 'Retail Floor' && formData.location !== 'TradeMe Area' ? (
                  <div className="space-y-2 mt-1">
                    <Select
                      value="custom"
                      onValueChange={value => {
                        if (value !== 'custom') {
                          setFormData(prev => ({ ...prev, location: value }));
                        }
                      }}
                    >
                      <SelectTrigger className="h-8 text-sm">
                        <SelectValue placeholder="Custom Location" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Retail Floor">Retail Floor</SelectItem>
                        <SelectItem value="TradeMe Area">TradeMe Area</SelectItem>
                        <SelectItem value="custom">Custom Location</SelectItem>
                      </SelectContent>
                    </Select>
                    <Input
                      id="customLocation"
                      value={formData.location}
                      onChange={e => setFormData(prev => ({ ...prev, location: e.target.value }))}
                      className="h-8 text-sm"
                      placeholder="Enter custom location"
                    />
                  </div>
                ) : (
                  <div className="space-y-2 mt-1">
                    <Select
                      value={formData.location}
                      onValueChange={value => {
                        if (value === 'custom') {
                          setFormData(prev => ({ ...prev, location: '' }));
                        } else {
                          setFormData(prev => ({ ...prev, location: value }));
                        }
                      }}
                    >
                      <SelectTrigger className="h-8 text-sm">
                        <SelectValue placeholder="Select location" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Retail Floor">Retail Floor</SelectItem>
                        <SelectItem value="TradeMe Area">TradeMe Area</SelectItem>
                        <SelectItem value="custom">Custom Location</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}
                <p className="text-xs text-muted-foreground mt-1">
                  Where this item is stored
                </p>
              </div>
              <div>
                <Label htmlFor="buyer" className="text-xs font-medium">Buyer</Label>
                <Select
                  value={formData.buyer}
                  onValueChange={value =>
                    setFormData(prev => ({ ...prev, buyer: value }))
                  }
                >
                  <SelectTrigger className="h-8 text-sm mt-1">
                    <SelectValue placeholder="Select buyer" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">None</SelectItem>
                    {users.map(user => (
                      <SelectItem key={user._id} value={user._id}>
                        {user.fullName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground mt-1">
                  Employee who purchased the item
                </p>
              </div>
            </div>
          </section>

          {/* Image Preview section removed - now at the top */}
          {false && (
            <section>
              <Label>Primary Image</Label>
              <div className="mt-2 relative w-full max-w-[300px] h-[200px] bg-gray-100 dark:bg-gray-800 rounded-md overflow-hidden">
                <div className="absolute inset-0 flex flex-col items-center justify-center text-gray-400">
                  <ImageIcon className="w-12 h-12 mb-2 opacity-30" />
                  <span className="text-sm">Image not available</span>
                </div>
                <img
                  src={getImageUrl(primaryImage, item.currentListingId)}
                  alt={item.title || 'Item image'}
                  className={`w-full h-full object-contain relative z-10 ${
                    imageLoaded ? 'opacity-100' : 'opacity-0'
                  }`}
                  onLoad={() => setImageLoaded(true)}
                  onError={e => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                  }}
                />
              </div>
              <div className="flex flex-col space-y-1 mt-1">
                <p className="text-xs text-muted-foreground">
                  {item.images?.length > 1
                    ? `+${item.images.length - 1} more images`
                    : ''}
                </p>
                <div className="flex flex-wrap gap-2">
                  <button
                    type="button"
                    className="text-xs text-blue-500 hover:underline"
                    onClick={() => {
                      if (primaryImage) {
                        window.open(
                          getImageUrl(primaryImage, item.currentListingId),
                          '_blank'
                        );
                      }
                    }}
                  >
                    View image in new tab
                  </button>
                  <button
                    type="button"
                    className="text-xs text-green-500 hover:underline"
                    onClick={() => {
                      if (primaryImage) {
                        const filename =
                          primaryImage.split('/').pop() || primaryImage;
                        const folderName = item.stockCode && item.stockCode.trim() !== ''
                          ? item.stockCode
                          : `listing-${item.currentListingId}`;
                        const directUrl = `/uploads/trademe/${folderName}/${filename}`;
                        window.open(directUrl, '_blank');
                      }
                    }}
                  >
                    Try direct path
                  </button>
                  <button
                    type="button"
                    className="text-xs text-orange-500 hover:underline"
                    onClick={() => {
                      if (primaryImage) {
                        const filename =
                          primaryImage.split('/').pop() || primaryImage;
                        const folderName = item.stockCode && item.stockCode.trim() !== ''
                          ? item.stockCode
                          : `listing-${item.currentListingId}`;
                        const backendUrl = `http://localhost:3000/uploads/trademe/${folderName}/${filename}`;
                        window.open(backendUrl, '_blank');
                      }
                    }}
                  >
                    Try backend directly
                  </button>
                  <button
                    type="button"
                    className="text-xs text-purple-500 hover:underline"
                    onClick={() => {
                      if (primaryImage) {
                        const filename =
                          primaryImage.split('/').pop() || primaryImage;
                        const folderName = item.stockCode && item.stockCode.trim() !== ''
                          ? item.stockCode
                          : `listing-${item.currentListingId}`;
                        const computedUrl = getImageUrl(
                          primaryImage,
                          item.currentListingId
                        );
                        const directUrl = `/uploads/trademe/${folderName}/${filename}`;
                        const backendUrl = `http://localhost:3000/uploads/trademe/${folderName}/${filename}`;
                        const paths = {
                          original: primaryImage,
                          filename: filename,
                          computed: computedUrl,
                          direct: directUrl,
                          backend: backendUrl
                        };
                        console.table(paths);
                        checkImageExists(computedUrl).then(exists => {
                          alert(
                            `Image paths:\n\nOriginal: ${paths.original}\nFilename: ${paths.filename}\nComputed: ${paths.computed}\nDirect: ${paths.direct}\nBackend: ${paths.backend}\n\nImage exists: ${exists}`
                          );
                        });
                      }
                    }}
                  >
                    Show path info
                  </button>
                </div>
              </div>
            </section>
          )}

          {/* Buyer section moved to be on the same line as Physical Location */}

          {/* Faulty and Relisting Sections Side by Side - Only show for active items */}
          {item.status !== 'sold' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Faulty Section */}
              <section className="bg-muted/5 p-4 rounded-lg border border-muted/10">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isFaulty"
                    checked={formData.isFaulty}
                    onCheckedChange={checked =>
                      handleCheckboxChange('isFaulty', checked as boolean)
                    }
                  />
                  <Label htmlFor="isFaulty" className="text-sm font-medium">Item is faulty</Label>
                </div>
                {formData.isFaulty && (
                  <div className="pt-2 space-y-4">
                    <div>
                      <Label htmlFor="faultDescription" className="text-xs font-medium">
                        Fault Description <span className="text-red-500">*</span>
                      </Label>
                      <Textarea
                        id="faultDescription"
                        name="faultDescription"
                        value={formData.faultDescription}
                        onChange={handleChange}
                        placeholder="Describe the fault"
                        rows={3}
                        required
                        className="text-sm min-h-[80px] mt-1"
                      />
                    </div>
                    <div>
                      <Label className="text-xs font-medium">Fault Categories</Label>
                      <div className="grid grid-cols-2 gap-2 mt-1">
                        {[
                          'screen',
                          'battery',
                          'charging',
                          'buttons',
                          'software',
                          'other'
                        ].map(category => (
                          <div
                            key={category}
                            className="flex items-center space-x-2"
                          >
                            <Checkbox
                              id={`faultCategory-${category}`}
                              checked={isFaultCategorySelected(category)}
                              onCheckedChange={checked => {
                                const newCategories = checked
                                  ? [...formData.faultCategories, category]
                                  : formData.faultCategories.filter((c: any) => {
                                      if (typeof c === 'string')
                                        return c !== category;
                                      return c.name !== category;
                                    });
                                setFormData(prev => ({
                                  ...prev,
                                  faultCategories: newCategories
                                }));
                              }}
                            />
                            <Label htmlFor={`faultCategory-${category}`}>
                              {category.charAt(0).toUpperCase() +
                                category.slice(1)}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </section>

              {/* Relisting Settings - Only show for non-sold items */}
              {item.status !== 'sold' && (
                <section className="bg-muted/5 p-4 rounded-lg border border-muted/10">
                <h3 className="text-sm font-semibold mb-1">Relisting Settings</h3>
                <div className="flex items-center space-x-2 mb-3">
                  <Checkbox
                    id="autoRelist"
                    checked={formData.relistSettings.autoRelist}
                    onCheckedChange={checked =>
                      handleRelistCheckboxChange(checked as boolean)
                    }
                  />
                  <Label htmlFor="autoRelist" className="text-sm font-medium">
                    Automatically relist if unsold
                  </Label>
                </div>
                {formData.relistSettings.autoRelist && (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor="maxRelistCount" className="text-xs font-medium">Max Relists</Label>
                      <Input
                        id="maxRelistCount"
                        name="maxRelistCount"
                        type="number"
                        min="1"
                        value={formData.relistSettings.maxRelistCount}
                        onChange={handleRelistSettingChange}
                        className="h-8 text-sm mt-1"
                      />
                    </div>
                    <div>
                      <Label htmlFor="priceReductionPercent" className="text-xs font-medium">
                        Price Reduction %
                      </Label>
                      <Input
                        id="priceReductionPercent"
                        name="priceReductionPercent"
                        type="number"
                        min="0"
                        max="100"
                        value={formData.relistSettings.priceReductionPercent}
                        onChange={handleRelistSettingChange}
                        className="h-8 text-sm mt-1"
                      />
                    </div>
                    <div>
                      <Label htmlFor="minPrice" className="text-xs font-medium">Minimum Price</Label>
                      <Input
                        id="minPrice"
                        name="minPrice"
                        type="number"
                        min="0"
                        step="0.01"
                        value={formData.relistSettings.minPrice}
                        onChange={handleRelistSettingChange}
                        className="h-8 text-sm mt-1"
                      />
                    </div>
                  </div>
                )}
              </section>
              )}
            </div>
          )}

          {/* Sold Item Information - Only show for sold items */}
          {item.status === 'sold' && (
            <div className="border rounded-lg p-4 bg-blue-50 dark:bg-blue-950/20">
              <div className="flex items-center mb-3">
                <CheckCircle className="h-5 w-5 text-blue-500 mr-2" />
                <h3 className="text-lg font-semibold">Sold Item Information</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label className="text-xs font-medium">Sold Date</Label>
                  <div className="text-sm mt-1">
                    {item.soldDate ? new Date(item.soldDate).toLocaleDateString() : 'Unknown'}
                  </div>
                </div>

                <div>
                  <Label className="text-xs font-medium">Sold Price</Label>
                  <div className="text-sm font-medium mt-1">
                    ${item.soldPrice?.toFixed(2) || item.currentPrice?.toFixed(2) || '0.00'}
                  </div>
                </div>

                <div>
                  <Label className="text-xs font-medium">TradeMe Buyer</Label>
                  <div className="text-sm mt-1">
                    {item.tradeMeBuyer || 'Unknown'}
                  </div>
                </div>
              </div>

              {/* Buyer Delivery Address - if available */}
              {item.buyerDeliveryAddress && (
                <div className="mt-4 pt-4 border-t border-blue-200 dark:border-blue-800">
                  <h4 className="text-sm font-semibold mb-2">Buyer Delivery Address</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {item.buyerDeliveryAddress.name && (
                      <div>
                        <Label className="text-xs font-medium">Name</Label>
                        <div className="text-sm mt-1">{item.buyerDeliveryAddress.name}</div>
                      </div>
                    )}

                    {(item.buyerDeliveryAddress.address1 || item.buyerDeliveryAddress.address2) && (
                      <div>
                        <Label className="text-xs font-medium">Address</Label>
                        <div className="text-sm mt-1">
                          {item.buyerDeliveryAddress.address1}
                          {item.buyerDeliveryAddress.address2 && (
                            <span><br />{item.buyerDeliveryAddress.address2}</span>
                          )}
                        </div>
                      </div>
                    )}

                    {(item.buyerDeliveryAddress.suburb || item.buyerDeliveryAddress.city) && (
                      <div>
                        <Label className="text-xs font-medium">City</Label>
                        <div className="text-sm mt-1">
                          {item.buyerDeliveryAddress.suburb && `${item.buyerDeliveryAddress.suburb}, `}
                          {item.buyerDeliveryAddress.city}
                        </div>
                      </div>
                    )}

                    {item.buyerDeliveryAddress.postcode && (
                      <div>
                        <Label className="text-xs font-medium">Postcode</Label>
                        <div className="text-sm mt-1">{item.buyerDeliveryAddress.postcode}</div>
                      </div>
                    )}

                    {item.buyerDeliveryAddress.phoneNumber && (
                      <div>
                        <Label className="text-xs font-medium">Phone</Label>
                        <div className="text-sm mt-1">{item.buyerDeliveryAddress.phoneNumber}</div>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Buyer Payment Info - if available */}
              {item.buyerPaymentInfo && (
                <div className="mt-4 pt-4 border-t border-blue-200 dark:border-blue-800">
                  <h4 className="text-sm font-semibold mb-2">Payment Information</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    <div>
                      <Label className="text-xs font-medium">Payment Method</Label>
                      <div className="text-sm mt-1">
                        {item.buyerPaymentInfo.paymentType === 'CreditCard' || item.buyerPaymentInfo.hasPaidByCreditCard
                          ? `Credit Card${item.buyerPaymentInfo.creditCardType ? ` (${item.buyerPaymentInfo.creditCardType})` : ''}`
                          : item.buyerPaymentInfo.paymentType || 'Unknown'}
                      </div>
                    </div>

                    {item.buyerPaymentInfo.paymentAmount > 0 && (
                      <div>
                        <Label className="text-xs font-medium">Payment Amount</Label>
                        <div className="text-sm mt-1">${item.buyerPaymentInfo.paymentAmount.toFixed(2)}</div>
                      </div>
                    )}

                    {item.buyerPaymentInfo.creditCardLastFourDigits && (
                      <div>
                        <Label className="text-xs font-medium">Card Number</Label>
                        <div className="text-sm mt-1">xxxx-xxxx-xxxx-{item.buyerPaymentInfo.creditCardLastFourDigits}</div>
                      </div>
                    )}

                    {item.buyerPaymentInfo.creditCardPaymentDate && (
                      <div>
                        <Label className="text-xs font-medium">Payment Date</Label>
                        <div className="text-sm mt-1">
                          {new Date(item.buyerPaymentInfo.creditCardPaymentDate).toLocaleDateString()}
                        </div>
                      </div>
                    )}

                    <div>
                      <Label className="text-xs font-medium">Payment Status</Label>
                      <div className="text-sm mt-1">
                        {item.buyerPaymentInfo.isPaymentPending ? 'Pending' : 'Completed'}
                      </div>
                    </div>

                    {item.buyerPaymentInfo.paymentMethodFee > 0 && (
                      <div>
                        <Label className="text-xs font-medium">Payment Fee</Label>
                        <div className="text-sm mt-1">${item.buyerPaymentInfo.paymentMethodFee.toFixed(2)}</div>
                      </div>
                    )}

                    {item.buyerPaymentInfo.gstCollected > 0 && (
                      <div>
                        <Label className="text-xs font-medium">GST Collected</Label>
                        <div className="text-sm mt-1">${item.buyerPaymentInfo.gstCollected.toFixed(2)}</div>
                      </div>
                    )}

                    {item.buyerPaymentInfo.isPayNowPurchase && (
                      <div>
                        <Label className="text-xs font-medium">Pay Now Purchase</Label>
                        <div className="text-sm mt-1">Yes</div>
                      </div>
                    )}

                    {item.buyerPaymentInfo.pingEscrowStatus && item.buyerPaymentInfo.pingEscrowStatus !== 'None' && (
                      <div>
                        <Label className="text-xs font-medium">Ping Escrow Status</Label>
                        <div className="text-sm mt-1">{item.buyerPaymentInfo.pingEscrowStatus}</div>
                      </div>
                    )}
                  </div>

                  {item.buyerPaymentInfo.messageFromBuyer && (
                    <div className="mt-2">
                      <Label className="text-xs font-medium">Message from Buyer</Label>
                      <div className="text-sm mt-1 p-2 bg-white dark:bg-blue-900/30 rounded-md">
                        {item.buyerPaymentInfo.messageFromBuyer}
                      </div>
                    </div>
                  )}

                  {item.buyerPaymentInfo.refunds && item.buyerPaymentInfo.refunds.length > 0 && (
                    <div className="mt-4">
                      <Label className="text-xs font-medium">Refunds</Label>
                      <div className="mt-1 space-y-2">
                        {item.buyerPaymentInfo.refunds.map((refund: any, index: number) => (
                          <div key={index} className="p-2 bg-white dark:bg-blue-900/30 rounded-md">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                              <div>
                                <span className="text-xs text-muted-foreground">Amount:</span>
                                <span className="text-sm ml-1">${refund.amount.toFixed(2)}</span>
                              </div>
                              {refund.destination && (
                                <div>
                                  <span className="text-xs text-muted-foreground">To:</span>
                                  <span className="text-sm ml-1">{refund.destination}</span>
                                </div>
                              )}
                              {refund.date && (
                                <div>
                                  <span className="text-xs text-muted-foreground">Date:</span>
                                  <span className="text-sm ml-1">{new Date(refund.date).toLocaleDateString()}</span>
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {/* Shipping Options - Show for all items */}
          {item.shippingOptions && item.shippingOptions.length > 0 && (
            <div className="border rounded-lg p-4 bg-gray-50 dark:bg-gray-900/20">
              <div className="flex items-center mb-3">
                <Truck className="h-5 w-5 text-gray-500 mr-2" />
                <h3 className="text-lg font-semibold">Shipping Options</h3>
              </div>

              <div className="space-y-2">
                {item.shippingOptions.map((option: { name?: string; method?: string; price?: number }, index: number) => (
                  <div key={index} className="flex justify-between items-center p-2 bg-white dark:bg-gray-800 rounded-md">
                    <div>
                      <span className="font-medium">{option.name || option.method || 'Shipping Option'}</span>
                      {option.method && option.method !== option.name && (
                        <span className="text-sm text-muted-foreground ml-2">({option.method})</span>
                      )}
                    </div>
                    <div className="font-medium">
                      ${option.price?.toFixed(2) || '0.00'}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="flex justify-end space-x-3 pt-6">
            <Button
              type="button"
              variant="outline"
              onClick={onSkip}
              disabled={isSubmitting}
              className="h-9"
            >
              Skip for Now
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="h-9 px-6"
            >
              {isSubmitting && (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              )}
              {isSubmitting ? 'Saving...' : 'Save Item'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default ImportItemForm;
