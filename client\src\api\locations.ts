import api from './api';

export interface Location {
  _id: string;
  name: string;
  address: string;
  phone: string;
  isActive: boolean;
  createdAt: string;
}

// Description: Get all locations
// Endpoint: GET /api/locations
// Request: {}
// Response: { locations: Location[] }
export const getLocations = async (): Promise<{ success: boolean; locations: Location[] }> => {
  try {
    const response = await api.get('/locations');
    return response.data;
  } catch (error: any) {
    console.error('Error fetching locations:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Description: Create a new location
// Endpoint: POST /api/locations
// Request: { name: string, address: string, phone: string }
// Response: Location
export const createLocation = async (data: { name: string; address: string; phone: string }): Promise<Location> => {
  try {
    const response = await api.post('/locations', data);
    return response.data;
  } catch (error: any) {
    console.error('Error creating location:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Description: Delete a location
// Endpoint: DELETE /api/locations/:id
// Request: {}
// Response: { success: boolean, message: string }
export const deleteLocation = async (id: string): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await api.delete(`/locations/${id}`);
    return response.data;
  } catch (error: any) {
    console.error('Error deleting location:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Description: Seed initial locations
// Endpoint: POST /api/locations/seed
// Request: {}
// Response: { success: boolean, message: string, locations?: Location[] }
export const seedLocations = async (): Promise<{ success: boolean; message: string; locations?: Location[] }> => {
  try {
    const response = await api.post('/locations/seed');
    return response.data;
  } catch (error: any) {
    console.error('Error seeding locations:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Description: Toggle location active status
// Endpoint: PATCH /api/locations/:id/toggle-status
// Request: { isActive: boolean }
// Response: Location
export const toggleLocationStatus = async (id: string, isActive: boolean): Promise<Location> => {
  try {
    const response = await api.patch(`/locations/${id}/toggle-status`, { isActive });
    return response.data;
  } catch (error: any) {
    console.error('Error toggling location status:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};