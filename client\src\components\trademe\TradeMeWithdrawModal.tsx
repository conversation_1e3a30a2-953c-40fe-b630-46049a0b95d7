import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectTrigger, SelectValue } from '@/components/ui/select';
import { RefreshCw } from 'lucide-react';
import { TradeMeTemplate, getTemplates } from '@/api/tradeMeTemplates';
import { useToast } from '@/hooks/useToast';

interface TradeMeWithdrawModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onWithdraw: (reason: string) => Promise<void>;
  isSubmitting?: boolean;
}

export function TradeMeWithdrawModal({
  open,
  onOpenChange,
  onWithdraw,
  isSubmitting = false
}: TradeMeWithdrawModalProps) {
  const { toast } = useToast();
  const [withdrawReason, setWithdrawReason] = useState('');
  const [templates, setTemplates] = useState<TradeMeTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState("");
  const [loading, setLoading] = useState(false);
  const [defaultTemplate, setDefaultTemplate] = useState<TradeMeTemplate | null>(null);

  // Fetch templates when modal opens
  useEffect(() => {
    if (open) {
      fetchTemplates();
    }
  }, [open]);

  // Fetch withdraw templates
  const fetchTemplates = async () => {
    try {
      setLoading(true);
      const result = await getTemplates('withdraw');

      if (result.success) {
        setTemplates(result.templates || []);

        // Check for default template
        const defaultTmpl = result.templates.find((t: TradeMeTemplate) => t.isDefault);
        if (defaultTmpl) {
          setDefaultTemplate(defaultTmpl);
          setWithdrawReason(defaultTmpl.content);
          setSelectedTemplate(defaultTmpl._id);
        }
      }
    } catch (error) {
      console.error('Failed to fetch templates:', error);
      toast({
        title: 'Error',
        description: 'Failed to load withdraw templates',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle template selection
  const handleTemplateChange = (value: string) => {
    setSelectedTemplate(value);

    // If "custom" is selected, reset the withdraw reason
    if (value === 'custom') {
      setWithdrawReason('');
      return;
    }

    // Find the template and set the withdraw reason
    const template = templates.find(t => t._id === value);
    if (template) {
      setWithdrawReason(template.content || '');
    }
  };

  // Handle withdraw submission
  const handleWithdrawSubmit = async () => {
    if (!withdrawReason.trim()) {
      toast({
        title: 'Error',
        description: 'Please provide a reason for withdrawing the listing',
        variant: 'destructive',
      });
      return;
    }

    try {
      await onWithdraw(withdrawReason);

      // Reset form
      setWithdrawReason('');
      setSelectedTemplate('');
    } catch (error: any) {
      console.error('Error withdrawing listing:', error);
      toast({
        title: 'Error',
        description: error.message || 'An error occurred',
        variant: 'destructive',
      });
    }
  };

  // Reset form when modal closes
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      // Reset form only if not submitting
      if (!isSubmitting) {
        setWithdrawReason('');
        setSelectedTemplate('');
      }
    } else if (defaultTemplate) {
      // Set default template when opening
      setWithdrawReason(defaultTemplate.content);
      setSelectedTemplate(defaultTemplate._id);
    }

    onOpenChange(open);
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Withdraw Listing</DialogTitle>
          <DialogDescription>
            Are you sure you want to withdraw this listing from TradeMe?
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <Label htmlFor="template-select">Use Template</Label>
              {loading && <RefreshCw className="h-4 w-4 animate-spin" />}
            </div>
            <Select value={selectedTemplate} onValueChange={handleTemplateChange}>
              <SelectTrigger id="template-select" className="w-full">
                <SelectValue placeholder="Select a template or write custom reason" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="custom">Custom Reason</SelectItem>
                <SelectGroup>
                  <SelectLabel>Templates</SelectLabel>
                  {templates.map((template) => (
                    <SelectItem key={template._id} value={template._id}>
                      {template.title}
                    </SelectItem>
                  ))}
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="withdraw-reason">Reason for withdrawal</Label>
            <Textarea
              id="withdraw-reason"
              placeholder="Please provide a reason for withdrawing this listing"
              value={withdrawReason}
              onChange={(e) => setWithdrawReason(e.target.value)}
              className="min-h-[100px]"
            />
          </div>

        </div>
        <DialogFooter className="sm:justify-between">
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            type="button"
            variant="destructive"
            onClick={handleWithdrawSubmit}
            disabled={isSubmitting || !withdrawReason.trim()}
          >
            {isSubmitting ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Withdrawing...
              </>
            ) : (
              'Withdraw Listing'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default TradeMeWithdrawModal;
