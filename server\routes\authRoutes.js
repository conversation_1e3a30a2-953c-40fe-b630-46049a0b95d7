const express = require("express");
const UserService = require("../services/userService.js");
const User = require("../models/User.js");
const { requireUser } = require("./middleware/auth.js");
const {
  generateAccessToken,
  generateRefreshToken,
} = require("../utils/auth.js");
const { validatePassword } = require("../utils/password.js");
const {
  generatePinHash,
  validatePin,
  validatePinComplexity,
  isPinExpired,
  canReusePin,
  getLockoutDuration,
  getPinExpiryDate
} = require("../utils/pin.js");
const jwt = require("jsonwebtoken");
const seedService = require("../services/seedService");
const router = express.Router();

router.post("/seed-admin", async (req, res) => {
  try {
    console.log("Attempting to seed admin user...");
    const result = await seedService.seedAdminUser();

    if (!result.success) {
      console.error("Failed to seed admin user:", result.message);
      return res.status(500).json({
        success: false,
        message: result.message,
      });
    }

    console.log("Admin user seeding successful:", result.message);

    // Only include loginDetails if a new admin user was created (not found)
    const response = {
      success: true,
      message: result.message
    };

    // Check if admin was newly created, not just found
    if (result.message === "Admin user created successfully") {
      response.loginDetails = {
        email: "<EMAIL>",
        username: "admin",
        password: "admin123"
      };
    }

    return res.status(200).json(response);
  } catch (error) {
    console.error("Error in seed-admin endpoint:", error);
    return res.status(500).json({
      success: false,
      message: error.message,
    });
  }
});

router.get("/seed-admin", async (req, res) => {
  try {
    console.log("Attempting to seed admin user via GET request...");
    const result = await seedService.seedAdminUser();

    if (!result.success) {
      console.error("Failed to seed admin user:", result.message);
      return res.status(500).json({
        success: false,
        message: result.message,
      });
    }

    console.log("Admin user seeding successful:", result.message);

    // Only include loginDetails if a new admin user was created (not found)
    const response = {
      success: true,
      message: result.message
    };

    // Check if admin was newly created, not just found
    if (result.message === "Admin user created successfully") {
      response.loginDetails = {
        email: "<EMAIL>",
        username: "admin",
        password: "admin123"
      };
    }

    return res.status(200).json(response);
  } catch (error) {
    console.error("Error in seed-admin endpoint:", error);
    return res.status(500).json({
      success: false,
      message: error.message,
    });
  }
});

router.post("/login", async (req, res) => {
  try {
    const { emailOrUsername, password, pin } = req.body;

    // Check if we're doing PIN login or regular login
    if (pin) {
      // PIN login - no username/email required
      // Find user by PIN (we'll need to search all users)
      const allUsers = await User.find({ pin: { $ne: null } });

      // Check each user's PIN
      let user = null;
      for (const potentialUser of allUsers) {
        const isPinValid = await validatePin(pin, potentialUser.pin);
        if (isPinValid) {
          user = potentialUser;
          break;
        }
      }

      if (!user) {
        // Log failed login attempt
        const AccessLog = require('../models/AccessLog');
        const failedLoginLog = new AccessLog({
          timestamp: new Date(),
          username: 'PIN login attempt',
          fullName: '',
          userRole: 'none',
          ipAddress: req.ip || req.connection.remoteAddress,
          method: req.method,
          path: req.originalUrl,
          fullUrl: req.protocol + '://' + req.get('host') + req.originalUrl,
          queryParams: {},
          userAgent: req.headers['user-agent'] || 'unknown',
          requestType: 'auth',
          referrer: req.headers['referer'] || req.headers['referrer'] || '',
          pageName: 'Login',
          statusCode: 401,
          responseTime: 0
        });

        failedLoginLog.save().catch(err => {
          console.error('Error saving failed login log:', err);
        });

        console.log(`\x1b[35m[AUTH]\x1b[0m ${new Date().toISOString()} | Failed PIN login attempt | Invalid PIN`);

        return res.status(401).json({
          success: false,
          message: 'Invalid PIN'
        });
      }

      if (!user.isActive) {
        return res.status(403).json({
          success: false,
          message: 'Account is deactivated. Please contact your administrator.'
        });
      }

      // Check if PIN is set
      if (!user.pin) {
        return res.status(401).json({
          success: false,
          message: 'PIN login not enabled for this account'
        });
      }

      // Check if PIN is expired
      if (isPinExpired(user.pinCreatedAt)) {
        return res.status(401).json({
          success: false,
          message: 'Your PIN has expired. Please set a new PIN in your profile.'
        });
      }

      // Check if account is locked due to too many failed attempts
      if (user.pinLockedUntil && new Date() < new Date(user.pinLockedUntil)) {
        const lockoutMinutes = Math.ceil((new Date(user.pinLockedUntil) - new Date()) / (1000 * 60));
        return res.status(401).json({
          success: false,
          message: `Account temporarily locked. Try again in ${lockoutMinutes} minute${lockoutMinutes !== 1 ? 's' : ''}.`
        });
      }

      // Validate PIN
      const isPinValid = await validatePin(pin, user.pin);
      if (!isPinValid) {
        // Increment failed attempts
        user.pinFailedAttempts = (user.pinFailedAttempts || 0) + 1;

        // Check if we need to lock the account
        const lockoutDuration = getLockoutDuration(user.pinFailedAttempts);
        if (lockoutDuration > 0) {
          const lockoutUntil = new Date();
          lockoutUntil.setMinutes(lockoutUntil.getMinutes() + lockoutDuration);
          user.pinLockedUntil = lockoutUntil;
        }

        await user.save();

        // Log failed login attempt
        const AccessLog = require('../models/AccessLog');
        const failedLoginLog = new AccessLog({
          timestamp: new Date(),
          username: user.username,
          fullName: user.fullName || '',
          userRole: user.role || 'none',
          ipAddress: req.ip || req.connection.remoteAddress,
          method: req.method,
          path: req.originalUrl,
          fullUrl: req.protocol + '://' + req.get('host') + req.originalUrl,
          queryParams: {},
          userAgent: req.headers['user-agent'] || 'unknown',
          requestType: 'auth',
          referrer: req.headers['referer'] || req.headers['referrer'] || '',
          pageName: 'Login',
          statusCode: 401,
          responseTime: 0
        });

        failedLoginLog.save().catch(err => {
          console.error('Error saving failed login log:', err);
        });

        console.log(`\x1b[35m[AUTH]\x1b[0m ${new Date().toISOString()} | Failed PIN login attempt for user: ${user.username} | Invalid PIN (Attempt ${user.pinFailedAttempts})`);

        if (lockoutDuration > 0) {
          return res.status(401).json({
            success: false,
            message: `Too many failed attempts. Account locked for ${lockoutDuration} minute${lockoutDuration !== 1 ? 's' : ''}.`
          });
        } else {
          return res.status(401).json({
            success: false,
            message: 'Invalid PIN'
          });
        }
      }

      // Reset failed attempts on successful login
      user.pinFailedAttempts = 0;
      user.pinLockedUntil = null;

      // Update last login
      user.lastLoginAt = new Date();
      await user.save();

      // Generate tokens
      const accessToken = generateAccessToken(user);
      const refreshToken = generateRefreshToken(user);

      // Save refresh token to user
      user.refreshToken = refreshToken;
      await user.save();

      // Log successful login
      const AccessLog = require('../models/AccessLog');
      const successLoginLog = new AccessLog({
        timestamp: new Date(),
        username: user.username,
        fullName: user.fullName || '',
        userRole: user.role || 'none',
        ipAddress: req.ip || req.connection.remoteAddress,
        method: req.method,
        path: req.originalUrl,
        fullUrl: req.protocol + '://' + req.get('host') + req.originalUrl,
        queryParams: {},
        userAgent: req.headers['user-agent'] || 'unknown',
        requestType: 'auth',
        referrer: req.headers['referer'] || req.headers['referrer'] || '',
        pageName: 'Login',
        statusCode: 200,
        responseTime: 0
      });

      successLoginLog.save().catch(err => {
        console.error('Error saving successful login log:', err);
      });

      console.log(`\x1b[35m[AUTH]\x1b[0m ${new Date().toISOString()} | Successful PIN login for user: ${user.username}`);

      return res.json({
        success: true,
        user: {
          _id: user._id,
          email: user.email,
          username: user.username,
          fullName: user.fullName,
          role: user.role,
          isActive: user.isActive,
        },
        accessToken,
        refreshToken,
      });
    } else {
      // Regular password login
      const user = await User.findOne({
        $or: [
          { email: emailOrUsername.toLowerCase() },
          { username: emailOrUsername }
        ]
      });

      if (!user) {
        // Log failed login attempt
        const AccessLog = require('../models/AccessLog');
        const failedLoginLog = new AccessLog({
          timestamp: new Date(),
          username: emailOrUsername,
          fullName: '',
          userRole: 'none',
          ipAddress: req.ip || req.connection.remoteAddress,
          method: req.method,
          path: req.originalUrl,
          fullUrl: req.protocol + '://' + req.get('host') + req.originalUrl,
          queryParams: {},
          userAgent: req.headers['user-agent'] || 'unknown',
          requestType: 'auth',
          referrer: req.headers['referer'] || req.headers['referrer'] || '',
          pageName: 'Login',
          statusCode: 401,
          responseTime: 0
        });

        failedLoginLog.save().catch(err => {
          console.error('Error saving failed login log:', err);
        });

        console.log(`\x1b[35m[AUTH]\x1b[0m ${new Date().toISOString()} | Failed login attempt for user: ${emailOrUsername} | User not found`);

        return res.status(401).json({
          success: false,
          message: 'Invalid credentials'
        });
      }

      if (!user.isActive) {
        return res.status(403).json({
          success: false,
          message: 'Account is deactivated. Please contact your administrator.'
        });
      }

      const isValid = await validatePassword(password, user.password);
      if (!isValid) {
        // Log failed login attempt (invalid password)
        const AccessLog = require('../models/AccessLog');
        const failedLoginLog = new AccessLog({
          timestamp: new Date(),
          username: user.username,
          fullName: user.fullName || '',
          userRole: user.role || 'none',
          ipAddress: req.ip || req.connection.remoteAddress,
          method: req.method,
          path: req.originalUrl,
          fullUrl: req.protocol + '://' + req.get('host') + req.originalUrl,
          queryParams: {},
          userAgent: req.headers['user-agent'] || 'unknown',
          requestType: 'auth',
          referrer: req.headers['referer'] || req.headers['referrer'] || '',
          pageName: 'Login',
          statusCode: 401,
          responseTime: 0
        });

        failedLoginLog.save().catch(err => {
          console.error('Error saving failed login log:', err);
        });

        console.log(`\x1b[35m[AUTH]\x1b[0m ${new Date().toISOString()} | Failed login attempt for user: ${user.username} | Invalid password`);

        return res.status(401).json({
          success: false,
          message: 'Invalid credentials'
        });
      }

      // Update last login
      user.lastLoginAt = new Date();
      await user.save();

      // Generate tokens
      const accessToken = generateAccessToken(user);
      const refreshToken = generateRefreshToken(user);

      // Save refresh token to user
      user.refreshToken = refreshToken;
      await user.save();

      // Log successful login
      const AccessLog = require('../models/AccessLog');
      const successLoginLog = new AccessLog({
        timestamp: new Date(),
        username: user.username,
        fullName: user.fullName || '',
        userRole: user.role || 'none',
        ipAddress: req.ip || req.connection.remoteAddress,
        method: req.method,
        path: req.originalUrl,
        fullUrl: req.protocol + '://' + req.get('host') + req.originalUrl,
        queryParams: {},
        userAgent: req.headers['user-agent'] || 'unknown',
        requestType: 'auth',
        referrer: req.headers['referer'] || req.headers['referrer'] || '',
        pageName: 'Login',
        statusCode: 200,
        responseTime: 0
      });

      successLoginLog.save().catch(err => {
        console.error('Error saving successful login log:', err);
      });

      console.log(`\x1b[35m[AUTH]\x1b[0m ${new Date().toISOString()} | Successful login for user: ${user.username}`);

      return res.json({
        success: true,
        user: {
          _id: user._id,
          email: user.email,
          username: user.username,
          fullName: user.fullName,
          role: user.role,
          isActive: user.isActive,
        },
        accessToken,
        refreshToken,
      });
    }
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Login failed',
      error: error.message
    });
  }
});

router.post("/register", requireUser, async (req, res, next) => {
  // Only allow admins to create new users
  if (!req.user || req.user.role !== 'admin') {
    return res.status(403).json({ 
      success: false,
      message: "Only administrators can create new user accounts" 
    });
  }
  
  try {
    const user = await UserService.create(req.body);

    // Log user creation event
    const AccessLog = require('../models/AccessLog');
    const userCreationLog = new AccessLog({
      timestamp: new Date(),
      username: user.username || user.email,
      fullName: user.fullName || '',
      userRole: user.role || 'employee',
      ipAddress: req.ip || req.connection.remoteAddress,
      method: req.method,
      path: req.originalUrl,
      fullUrl: req.protocol + '://' + req.get('host') + req.originalUrl,
      queryParams: {},
      userAgent: req.headers['user-agent'] || 'unknown',
      requestType: 'user',
      referrer: req.headers['referer'] || req.headers['referrer'] || '',
      pageName: 'Register',
      statusCode: 200,
      responseTime: 0
    });

    userCreationLog.save().catch(err => {
      console.error('Error saving user creation log:', err);
    });

    console.log(`\x1b[33m[USER]\x1b[0m ${new Date().toISOString()} | New user created by admin: ${user.username || user.email}`);

    return res.status(200).json(user);
  } catch (error) {
    console.error(`Error while registering user: ${error}`);
    return res.status(400).json({ message: error.message });
  }
});

router.post("/logout", async (req, res) => {
  const { email } = req.body;

  const user = await User.findOne({ email });
  if (user) {
    user.refreshToken = null;
    await user.save();
  }

  res.status(200).json({ message: "User logged out successfully." });
});

router.post("/refresh", async (req, res) => {
  const { refreshToken } = req.body;

  if (!refreshToken) {
    return res.status(401).json({
      success: false,
      message: "Refresh token is required",
    });
  }

  try {
    // Verify the refresh token
    const decoded = jwt.verify(refreshToken, process.env.REFRESH_TOKEN_SECRET);

    // Find the user
    const user = await UserService.get(decoded.sub);

    if (!user) {
      return res.status(403).json({
        success: false,
        message: "User not found",
      });
    }

    if (user.refreshToken !== refreshToken) {
      return res.status(403).json({
        success: false,
        message: "Invalid refresh token",
      });
    }

    // Generate new tokens
    const newAccessToken = generateAccessToken(user);
    const newRefreshToken = generateRefreshToken(user);

    // Update user's refresh token in database
    user.refreshToken = newRefreshToken;
    await user.save();

    // Return new tokens
    return res.status(200).json({
      success: true,
      data: {
        accessToken: newAccessToken,
        refreshToken: newRefreshToken,
      },
    });
  } catch (error) {
    console.error(`Token refresh error: ${error.message}`);

    if (error.name === "TokenExpiredError") {
      return res.status(403).json({
        success: false,
        message: "Refresh token has expired",
      });
    }

    return res.status(403).json({
      success: false,
      message: "Invalid refresh token",
    });
  }
});

router.get("/me", requireUser, async (req, res) => {
  try {
    // Populate the location field
    const user = await User.findById(req.user._id).populate("location").exec();

    if (!user) {
      console.error("User not found for ID:", req.user._id);
      return res.status(404).json({ message: "User not found" });
    }

    return res.status(200).json(user);
  } catch (error) {
    console.error(`Error while fetching user: ${error.stack}`);
    return res.status(500).json({ message: "Server error" });
  }
});

router.get("/check-username/:username", async (req, res) => {
  try {
    const { username } = req.params;
    const excludeUserId = req.query.userId || null;

    const isUnique = await UserService.isUsernameUnique(
      username,
      excludeUserId,
    );

    console.log(
      `Username availability check for "${username}": ${isUnique ? "available" : "taken"}`,
    );
    return res.status(200).json({ available: isUnique });
  } catch (error) {
    console.error(`Error checking username availability: ${error.stack}`);
    return res.status(500).json({ message: error.message });
  }
});

router.patch("/me", requireUser, async (req, res) => {
  try {
    const { role, location, username, email, fullName } = req.body;

    // Validate inputs
    if (role && !["admin", "manager", "employee"].includes(role)) {
      return res
        .status(400)
        .json({ message: "Invalid role. Must be admin, manager, or employee" });
    }

    // Check if user is trying to change role
    if (role && role !== req.user.role) {
      // Only admin users can change roles
      if (req.user.role !== "admin") {
        return res
          .status(403)
          .json({ message: "Only admin users can change roles" });
      }
    }

    // Check username uniqueness if provided
    if (username && username !== req.user.username) {
      const isUnique = await UserService.isUsernameUnique(
        username,
        req.user._id,
      );
      if (!isUnique) {
        return res.status(400).json({ message: "Username already taken" });
      }
    }

    // Check email uniqueness if provided
    if (email && email !== req.user.email) {
      const existingUser = await User.findOne({
        email: email.toLowerCase(),
        _id: { $ne: req.user._id }
      });
      if (existingUser) {
        return res.status(400).json({ message: "Email already in use" });
      }
    }

    // Prepare update data
    const updateData = {};
    if (role) updateData.role = role;
    if (location !== undefined) updateData.location = location;
    if (username !== undefined) updateData.username = username;
    if (email) updateData.email = email.toLowerCase();
    if (fullName) updateData.fullName = fullName;

    // Update user
    const updatedUser = await UserService.update(req.user._id, updateData);

    // Populate location
    const populatedUser = await User.findById(updatedUser._id)
      .populate("location")
      .exec();

    console.log(
      `User updated successfully: ${JSON.stringify(populatedUser, null, 2)}`,
    );
    return res.status(200).json(populatedUser);
  } catch (error) {
    console.error(`Error updating user: ${error.stack}`);
    return res.status(500).json({ message: error.message });
  }
});

router.patch("/me/password", requireUser, async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;

    // Validate inputs
    if (!currentPassword || !newPassword) {
      return res
        .status(400)
        .json({ message: "Current password and new password are required" });
    }

    // Get the current user
    const user = await User.findById(req.user._id);
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    // Verify current password
    const passwordValid = await validatePassword(
      currentPassword,
      user.password,
    );
    if (!passwordValid) {
      return res.status(400).json({ message: "Current password is incorrect" });
    }

    // Update password
    await UserService.setPassword(user, newPassword);

    console.log(`Password updated successfully for user: ${user._id}`);
    return res.status(200).json({ message: "Password updated successfully" });
  } catch (error) {
    console.error(`Error updating password: ${error.stack}`);
    return res.status(500).json({ message: error.message });
  }
});

// PIN Management Endpoints
router.get("/pin-status", requireUser, async (req, res) => {
  try {
    const user = await User.findById(req.user._id);
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    const hasPin = !!user.pin;
    let pinExpiresAt = null;

    if (hasPin && user.pinCreatedAt) {
      pinExpiresAt = getPinExpiryDate(user.pinCreatedAt);
    }

    return res.status(200).json({
      hasPin,
      pinExpiresAt,
      isLocked: user.pinLockedUntil && new Date() < new Date(user.pinLockedUntil),
      lockedUntil: user.pinLockedUntil
    });
  } catch (error) {
    console.error(`Error getting PIN status: ${error.stack}`);
    return res.status(500).json({ message: error.message });
  }
});

router.post("/setup-pin", requireUser, async (req, res) => {
  try {
    const { pin } = req.body;

    // Validate PIN
    const validation = validatePinComplexity(pin);
    if (!validation.success) {
      return res.status(400).json({
        success: false,
        message: validation.message
      });
    }

    // Get the current user
    const user = await User.findById(req.user._id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found"
      });
    }

    // Check if PIN can be reused
    if (user.pin) {
      const canReuse = await canReusePin(pin, user.previousPins);
      if (!canReuse) {
        return res.status(400).json({
          success: false,
          message: "Cannot reuse one of your previous PINs"
        });
      }
    }

    // Store previous PIN if exists
    if (user.pin) {
      // Keep only the last 4 PINs
      if (user.previousPins.length >= 4) {
        user.previousPins.shift(); // Remove oldest PIN
      }
      user.previousPins.push(user.pin);
    }

    // Hash and save the new PIN
    const pinHash = await generatePinHash(pin);
    user.pin = pinHash;
    user.pinCreatedAt = new Date();
    user.pinFailedAttempts = 0;
    user.pinLockedUntil = null;

    await user.save();

    console.log(`PIN setup successfully for user: ${user._id}`);
    return res.status(200).json({
      success: true,
      message: "PIN setup successfully",
      expiresAt: getPinExpiryDate(user.pinCreatedAt)
    });
  } catch (error) {
    console.error(`Error setting up PIN: ${error.stack}`);
    return res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

router.post("/remove-pin", requireUser, async (req, res) => {
  try {
    // Get the current user
    const user = await User.findById(req.user._id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found"
      });
    }

    // Store the current PIN in previous PINs if it exists
    if (user.pin) {
      // Keep only the last 4 PINs
      if (user.previousPins.length >= 4) {
        user.previousPins.shift(); // Remove oldest PIN
      }
      user.previousPins.push(user.pin);
    }

    // Remove PIN
    user.pin = null;
    user.pinCreatedAt = null;
    user.pinFailedAttempts = 0;
    user.pinLockedUntil = null;

    await user.save();

    console.log(`PIN removed successfully for user: ${user._id}`);
    return res.status(200).json({
      success: true,
      message: "PIN removed successfully"
    });
  } catch (error) {
    console.error(`Error removing PIN: ${error.stack}`);
    return res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

router.get("/users", requireUser, async (req, res) => {
  try {
    // Only allow admin and managers to fetch all users
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({ message: "Unauthorized access" });
    }

    const users = await User.find({})
      .populate("location")
      .select("-password -refreshToken")
      .sort({ createdAt: -1 })
      .exec();

    return res.status(200).json(users);
  } catch (error) {
    console.error(`Error while fetching users: ${error.stack}`);
    return res.status(500).json({ message: "Server error" });
  }
});

// Endpoint for getting users for dropdown lists - accessible to all authenticated users
router.get("/users-list", async (req, res) => {
  // Temporarily remove authentication requirement for debugging
  // requireUser middleware would normally be here
  try {
    console.log('GET /auth/users-list endpoint called');

    // Get the isActive filter from query params, default to showing all users
    const { isActive } = req.query;
    console.log('Query params:', { isActive });

    // Build the query
    const query = {};
    if (isActive !== undefined) {
      query.isActive = isActive === 'true';
    }
    console.log('MongoDB query:', query);

    // Get users with minimal fields needed for dropdowns
    const users = await User.find(query)
      .select("_id username fullName isActive")
      .sort({ fullName: 1 }) // Sort alphabetically by fullName
      .exec();

    console.log(`Found ${users.length} users`);

    const response = {
      success: true,
      data: users
    };

    console.log('Sending response:', {
      success: response.success,
      userCount: response.data.length
    });

    return res.status(200).json(response);
  } catch (error) {
    console.error(`Error while fetching users list: ${error.stack}`);
    return res.status(500).json({
      success: false,
      error: "Failed to fetch users list"
    });
  }
});

router.patch("/users/:id", requireUser, async (req, res) => {
  try {
    // Allow admin and manager users to update other users
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({ message: "Only admin and manager users can update other users" });
    }

    const { id } = req.params;
    const { username, email, role } = req.body;

    // Check if the user being updated is an admin
    const userToUpdate = await User.findById(id);
    if (!userToUpdate) {
      return res.status(404).json({ message: "User not found" });
    }

    // Prevent managers from updating admin users
    if (req.user.role === 'manager' && userToUpdate.role === 'admin') {
      return res.status(403).json({ message: "Managers cannot update admin users" });
    }

    // Validate role if provided
    if (role && !['admin', 'manager', 'employee'].includes(role)) {
      return res.status(400).json({ message: "Invalid role" });
    }

    // Check if email is unique if it's being changed
    if (email) {
      const existingUser = await User.findOne({
        email: email.toLowerCase(),
        _id: { $ne: id }
      });
      if (existingUser) {
        return res.status(400).json({ message: "Email already in use" });
      }
    }

    // Check if username is unique if it's being changed
    if (username) {
      const isUnique = await UserService.isUsernameUnique(username, id);
      if (!isUnique) {
        return res.status(400).json({ message: "Username already taken" });
      }
    }

    const updateData = {
      ...(username && { username }),
      ...(email && { email: email.toLowerCase() }),
      ...(role && { role })
    };

    const updatedUser = await User.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    ).populate('location');

    if (!updatedUser) {
      return res.status(404).json({ message: "User not found" });
    }

    // Remove sensitive data before sending response
    const userResponse = updatedUser.toObject();
    delete userResponse.password;
    delete userResponse.refreshToken;

    return res.status(200).json(userResponse);
  } catch (error) {
    console.error(`Error updating user: ${error.stack}`);
    return res.status(500).json({ message: error.message || "Server error" });
  }
});

router.post('/users/:id/reset-password', requireUser, async (req, res) => {
  try {
    // Only allow admin and manager users to reset passwords
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({ message: 'Only admins and managers can reset user passwords' });
    }

    const { id } = req.params;
    const { newPassword } = req.body;

    // Validate new password
    if (!newPassword || newPassword.length < 6) {
      return res.status(400).json({ message: 'New password must be at least 6 characters long' });
    }

    const user = await User.findById(id);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Prevent managers from resetting admin user passwords
    if (req.user.role === 'manager' && user.role === 'admin') {
      return res.status(403).json({ message: 'Managers cannot reset passwords for admin users' });
    }

    // Reset the user's password
    await UserService.setPassword(user, newPassword);

    console.log(`Password reset successfully for user: ${user._id}`);
    return res.status(200).json({ message: 'Password reset successfully' });
  } catch (error) {
    console.error(`Error resetting password: ${error.stack}`);
    return res.status(500).json({ message: error.message });
  }
});

router.patch('/users/:id/toggle-status', requireUser, async (req, res) => {
  try {
    // Allow both admins and managers to toggle user status
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({ message: 'Only admins and managers can modify user status' });
    }

    const { id } = req.params;
    const user = await User.findById(id);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Prevent managers from toggling admin user status
    if (req.user.role === 'manager' && user.role === 'admin') {
      return res.status(403).json({ message: 'Managers cannot modify admin users' });
    }

    // Don't allow deactivating the last admin
    if (user.role === 'admin' && user.isActive) {
      const adminCount = await User.countDocuments({ role: 'admin', isActive: true });
      if (adminCount <= 1) {
        return res.status(400).json({ message: 'Cannot deactivate the last admin user' });
      }
    }

    user.isActive = !user.isActive;
    user.deactivatedAt = user.isActive ? null : new Date();
    await user.save();

    res.json({
      _id: user._id,
      email: user.email,
      username: user.username,
      fullName: user.fullName,
      role: user.role,
      isActive: user.isActive,
      deactivatedAt: user.deactivatedAt,
    });
  } catch (error) {
    res.status(500).json({ message: 'Failed to toggle user status' });
  }
});

module.exports = router;
