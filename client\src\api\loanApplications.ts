import api from './api';

export interface LoanApplication {
  _id: string;
  loanId: string;
  loanAmount: number;
  customerId: string;
  customerName: string;
  status: 'awaitingDecision' | 'approved' | 'approvedPaid' | 'declined' | 'cancelled';
  submittedDate: string;
  createdAt: string;
  createdBy: {
    _id: string;
    fullName: string;
    username: string;
  };
  submittedBy: {
    _id: string;
    fullName: string;
    username: string;
  };
  notes: string;
}

export interface LoanApplicationFormData {
  loanId: string;
  loanAmount: number;
  customerId: string;
  customerName: string;
  status: 'awaitingDecision' | 'approved' | 'approvedPaid' | 'declined' | 'cancelled';
  submittedDate: string;
  submittedBy?: string;
  notes?: string;
}

export interface PaginationData {
  total: number;
  page: number;
  limit: number;
  pages: number;
}

export interface LoanApplicationsResponse {
  success: boolean;
  data: LoanApplication[];
  pagination: PaginationData;
}

export interface LoanApplicationResponse {
  success: boolean;
  data: LoanApplication;
}

export interface LoanStatusCount {
  count: number;
  totalAmount: number;
}

export interface LoanStatusCounts {
  awaitingDecision?: LoanStatusCount;
  approved?: LoanStatusCount;
  approvedPaid?: LoanStatusCount;
  declined?: LoanStatusCount;
  cancelled?: LoanStatusCount;
}

export interface LoanSubmissionByDay {
  date: string;
  total: number;
  awaitingDecision: number;
  approved: number;
  approvedPaid: number;
  declined: number;
  cancelled: number;
  totalAmount: number;
}

export interface LoanSubmissionByUser {
  userId: string;
  fullName: string;
  username: string;
  count: number;
  awaitingDecision: number;
  approved: number;
  approvedPaid: number;
  declined: number;
  cancelled: number;
  totalAmount: number;
}

export interface LoanStatisticsResponse {
  success: boolean;
  data: {
    statusCounts: LoanStatusCounts;
    submissionsByDay: LoanSubmissionByDay[];
    submissionsByUser: LoanSubmissionByUser[];
    dateRange: {
      startDate: string;
      endDate: string;
      period: string;
    };
  };
}

export interface LoanSettings {
  _id: string;
  submissionBonus: number;
  fundedBonus: number;
  weeklySubmissionGoal: number;
  weeklyFundedGoal: number;
  updatedAt: string;
  updatedBy: {
    _id: string;
    fullName: string;
    username: string;
  };
}

export interface LoanSettingsResponse {
  success: boolean;
  data: LoanSettings;
}

export interface LoanSettingsFormData {
  submissionBonus: number;
  fundedBonus: number;
  weeklySubmissionGoal: number;
  weeklyFundedGoal: number;
}

// Get all loan applications with pagination and filtering
export const getLoanApplications = async (
  page = 1,
  limit = 10,
  search = '',
  status = 'all',
  startDate?: string,
  endDate?: string,
  submittedBy?: string
): Promise<LoanApplicationsResponse> => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
  });

  if (search) params.append('search', search);
  if (status !== 'all') params.append('status', status);
  if (startDate) params.append('startDate', startDate);
  if (endDate) params.append('endDate', endDate);
  if (submittedBy) params.append('submittedBy', submittedBy);

  const response = await api.get(`/loan-applications?${params.toString()}`);
  return response.data;
};

// Get loan application by ID
export const getLoanApplication = async (id: string): Promise<LoanApplicationResponse> => {
  const response = await api.get(`/loan-applications/${id}`);
  return response.data;
};

// Create new loan application
export const createLoanApplication = async (
  data: LoanApplicationFormData
): Promise<LoanApplicationResponse> => {
  const response = await api.post('/loan-applications', data);
  return response.data;
};

// Update loan application
export const updateLoanApplication = async (
  id: string,
  data: Partial<LoanApplicationFormData>
): Promise<LoanApplicationResponse> => {
  const response = await api.patch(`/loan-applications/${id}`, data);
  return response.data;
};

// Delete loan application
export const deleteLoanApplication = async (id: string): Promise<{ success: boolean; message: string }> => {
  const response = await api.delete(`/loan-applications/${id}`);
  return response.data;
};

// Get loan application statistics
export const getLoanStatistics = async (
  period: '7days' | '14days' | '1month' | '1year' | 'all' | 'custom' = '7days',
  startDate?: string,
  endDate?: string,
  userId?: string
): Promise<LoanStatisticsResponse> => {
  const params = new URLSearchParams({
    period,
  });

  if (startDate) params.append('startDate', startDate);
  if (endDate) params.append('endDate', endDate);
  if (userId) params.append('userId', userId);

  const response = await api.get(`/loan-applications/stats/summary?${params.toString()}`);
  return response.data;
};

// Get loan settings
export const getLoanSettings = async (): Promise<LoanSettingsResponse> => {
  const response = await api.get('/loan-applications/settings/bonus');
  return response.data;
};

// Update loan settings
export const updateLoanSettings = async (
  data: LoanSettingsFormData
): Promise<LoanSettingsResponse> => {
  const response = await api.put('/loan-applications/settings/bonus', data);
  return response.data;
};

// Get active users for loan applications
export const getActiveUsersForLoanApplications = async (): Promise<User[]> => {
  try {
    const response = await api.get('/loan-applications/active-users');
    return response.data;
  } catch (error) {
    console.error('Error fetching active users for loan applications:', error);
    throw new Error(error?.response?.data?.message || error.message);
  }
};
