import { useState, useEffect } from 'react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Loader2,
  Edit,
  Save,
  X,
  Trash2
} from 'lucide-react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose
} from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';


import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { useToast } from '@/hooks/useToast';
import { useAuth } from '@/contexts/AuthContext';
import { getInventoryItem, updateInventoryItem, deleteInventoryItem } from '@/api/inventory';
import { getItemNotes, addItemNote, deleteItemNote, ItemNote as ItemNoteType } from '@/api/notes';
import { getItemAuditLogs, AuditLog } from '@/api/audit';
import { ItemNote } from '@/components/ItemNote';


// Define the form schema
const inventoryItemSchema = z.object({
  name: z.string().min(1, "Name is required"),
  category: z.string().min(1, "Category is required"),
  brand: z.string().optional(),
  model: z.string().optional(),
  modelNumber: z.string().optional(),
  releaseYear: z.coerce.number().nullable(),
  lastRRP: z.coerce.number().min(0, "Last RRP must be at least 0"),
  currentPrice: z.coerce.number().min(0, "Current price must be at least 0"),
});

type InventoryItemFormValues = z.infer<typeof inventoryItemSchema>;

interface InventoryItemModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  itemId: string | null;
  onItemDeleted?: () => void;
}

export function InventoryItemModal({ open, onOpenChange, itemId, onItemDeleted }: InventoryItemModalProps) {
  const { toast } = useToast();
  const { user } = useAuth();

  const [isLoading, setIsLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);


  // Notes state
  const [notes, setNotes] = useState<ItemNoteType[]>([]);
  const [noteContent, setNoteContent] = useState('');
  const [isAddingNote, setIsAddingNote] = useState(false);
  const [isLoadingNotes, setIsLoadingNotes] = useState(false);

  // Audit logs state
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
  const [isLoadingAuditLogs, setIsLoadingAuditLogs] = useState(false);

  // Form definition
  const form = useForm<InventoryItemFormValues>({
    resolver: zodResolver(inventoryItemSchema),
    defaultValues: {
      name: '',
      category: '',
      brand: '',
      model: '',
      modelNumber: '',
      releaseYear: null,
      lastRRP: 0,
      currentPrice: 0,
    },
  });

  // Fetch item details when the modal opens and itemId changes
  useEffect(() => {
    if (open && itemId) {
      fetchItemDetails();
      fetchNotes();
      fetchAuditLogs();
    } else {
      // Reset state when modal closes
      setIsEditing(false);
      setNoteContent('');
    }
  }, [open, itemId]);

  // Fetch item details
  const fetchItemDetails = async () => {
    if (!itemId) return;

    try {
      setIsLoading(true);

      // Fetch inventory item
      const itemResponse = await getInventoryItem(itemId);
      if (itemResponse.success && itemResponse.data) {
        // Set form values
        const item = itemResponse.data;
        form.reset({
          name: item.name || '',
          category: item.category || '',
          brand: item.brand || '',
          model: item.model || '',
          modelNumber: item.modelNumber || '',
          releaseYear: item.releaseYear || null,
          lastRRP: item.lastRRP || 0,
          currentPrice: item.currentPrice || 0,
        });
      }
    } catch (error: any) {
      console.error("Error fetching item details:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to fetch item details"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch notes
  const fetchNotes = async () => {
    if (!itemId) return;

    try {
      setIsLoadingNotes(true);
      const response = await getItemNotes(itemId);
      if (response.success) {
        setNotes(response.data);
      }
    } catch (error: any) {
      console.error("Error fetching notes:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to fetch notes"
      });
    } finally {
      setIsLoadingNotes(false);
    }
  };

  // Fetch audit logs
  const fetchAuditLogs = async () => {
    if (!itemId) return;

    try {
      setIsLoadingAuditLogs(true);
      const response = await getItemAuditLogs(itemId);
      if (response.success) {
        setAuditLogs(response.data);
      }
    } catch (error: any) {
      console.error("Error fetching audit logs:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to fetch audit logs"
      });
    } finally {
      setIsLoadingAuditLogs(false);
    }
  };

  // Add a note
  const handleAddNote = async () => {
    if (!itemId || !noteContent.trim()) return;

    try {
      setIsAddingNote(true);
      const response = await addItemNote(itemId, noteContent);

      if (response.success) {
        toast({
          title: "Success",
          description: "Note added successfully"
        });
        setNoteContent('');
        fetchNotes(); // Refresh notes
      }
    } catch (error: any) {
      console.error("Error adding note:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to add note"
      });
    } finally {
      setIsAddingNote(false);
    }
  };

  // Delete a note
  const handleDeleteNote = async (noteId: string) => {
    try {
      const response = await deleteItemNote(noteId);

      if (response.success) {
        toast({
          title: "Success",
          description: "Note deleted successfully"
        });
        // Update the notes list
        setNotes(prevNotes => prevNotes.filter(note => note._id !== noteId));
      }
    } catch (error: any) {
      console.error("Error deleting note:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to delete note"
      });
      throw error; // Re-throw for the component to handle
    }
  };

  // Handle form submission
  const onSubmit = async (data: InventoryItemFormValues) => {
    if (!itemId) return;

    try {
      setIsSubmitting(true);
      const formData = {...data};

      const response = await updateInventoryItem(itemId, formData);

      if (response.success) {
        toast({
          title: "Success",
          description: "Item updated successfully"
        });
        setIsEditing(false);

        // Refresh audit logs since we've made changes
        fetchAuditLogs();
      }
    } catch (error: any) {
      console.error("Error updating item:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to update item"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Delete an item
  const handleDeleteItem = async () => {
    if (!itemId) return;

    try {
      setIsDeleting(true);
      const response = await deleteInventoryItem(itemId);

      if (response.success) {
        toast({
          title: "Success",
          description: "Item deleted successfully"
        });
        onOpenChange(false);
        if (onItemDeleted) {
          onItemDeleted();
        }
      }
    } catch (error: any) {
      console.error("Error deleting item:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to delete item"
      });
    } finally {
      setIsDeleting(false);
      setDeleteDialogOpen(false);
    }
  };

  // Check if user can edit
  const canEdit = user?.role === 'admin' || user?.role === 'manager';

  // External search functions
  const handleGoogleSearch = () => {
    const searchQuery = encodeURIComponent(form.getValues('model') || form.getValues('name'));
    window.open(`https://www.google.com/search?q=${searchQuery}`, '_blank');
  };

  const handleTradeMeSearch = () => {
    const searchQuery = encodeURIComponent(form.getValues('model') || form.getValues('name'));
    window.open(`https://www.bidbud.co.nz/search?search_string=${searchQuery}&condition=Used`, '_blank');
  };

  const handlePriceSpySearch = () => {
    const searchQuery = encodeURIComponent(form.getValues('model') || form.getValues('name'));
    window.open(`https://pricespy.co.nz/search?search=${searchQuery}`, '_blank');
  };

  if (isLoading && open) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[1100px] h-[85vh] max-h-[900px] overflow-y-auto p-2 pt-2 pb-1">
          <div className="flex items-center justify-center h-64">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[1100px] h-[85vh] max-h-[900px] p-3 pt-3 pb-1 flex flex-col">
          <DialogHeader className="flex flex-row items-center justify-between p-0 pb-1 pt-1 pr-1">
            <div>
              <DialogTitle className="text-xl">{form.watch("brand")} {form.watch("model")}</DialogTitle>
            </div>
            <div className="flex items-center space-x-2">
              {/* Search buttons */}
              <div className="flex items-center space-x-1 mr-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleGoogleSearch}
                  className="flex items-center gap-1 h-7 py-0"
                >
                  <img
                    src="/img/icons/google-logo.ico"
                    alt="Google"
                    className="h-4 w-4"
                  />
                  <span>Search on Google</span>
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleTradeMeSearch}
                  className="flex items-center gap-1 h-7 py-0"
                >
                  <img
                    src="/img/icons/trademe-icon.png"
                    alt="TradeMe"
                    className="h-4 w-4"
                  />
                  <span>Search on Trademe</span>
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={handlePriceSpySearch}
                  className="flex items-center gap-1 h-7 py-0"
                >
                  <img
                    src="/img/icons/pricespy-logo.ico"
                    alt="PriceSpy"
                    className="h-4 w-4"
                  />
                  <span>Search on PriceSpy</span>
                </Button>
              </div>
            </div>
          </DialogHeader>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-1 pt-0 flex-grow overflow-hidden">
            <div className="border rounded-md p-2">
              <h3 className="text-lg font-medium mb-2">Item Details</h3>
              <ScrollArea>
                <Form {...form}>
                  <div className="grid gap-2">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                    <FormField
                      control={form.control}
                      name="brand"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Brand</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              disabled={!isEditing}
                              placeholder="Brand"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="category"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Category</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              disabled={!isEditing}
                              placeholder="Category"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                    <FormField
                      control={form.control}
                      name="model"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Model</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              disabled={!isEditing}
                              placeholder="Model"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="modelNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Model Number</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              disabled={!isEditing}
                              placeholder="Model Number"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
                    <FormField
                      control={form.control}
                      name="releaseYear"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Release Year</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              placeholder="YYYY"
                              disabled={!isEditing}
                              value={field.value !== null ? field.value : ''}
                              onChange={(e) => {
                                const value = e.target.value !== '' ? Number(e.target.value) : null;
                                field.onChange(value);
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="lastRRP"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Last RRP ($)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              step="0.01"
                              placeholder="0.00"
                              disabled={!isEditing}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="currentPrice"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Current Price ($)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              step="0.01"
                              placeholder="0.00"
                              disabled={!isEditing}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {/* Item action buttons */}
                <div className="mt-4 flex justify-start">
                  {canEdit && !isEditing && (
                    <>
                      <Button
                        onClick={() => setIsEditing(true)}
                        size="sm"
                        className="mr-1"
                      >
                        <Edit className="h-4 w-4 mr-2" />
                        Edit Item
                      </Button>
                      <Button
                        variant="destructive"
                        onClick={() => setDeleteDialogOpen(true)}
                        size="sm"
                        className=""
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete Item
                      </Button>
                    </>
                  )}

                  {isEditing && (
                    <>
                      <Button
                        variant="ghost"
                        onClick={() => setIsEditing(false)}
                        size="sm"
                        className="mr-1"
                      >
                        <X className="h-4 w-4 mr-2" />
                        Cancel
                      </Button>
                      <Button
                        onClick={form.handleSubmit(onSubmit)}
                        disabled={isSubmitting}
                        size="sm"
                        className=""
                      >
                        {isSubmitting ? (
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        ) : (
                          <Save className="h-4 w-4 mr-2" />
                        )}
                        Save
                      </Button>
                    </>
                  )}
                </div>
              </Form>
              </ScrollArea>
            </div>

            <div className="md:row-span-2 border rounded-md p-2 flex flex-col">
              <h3 className="text-lg font-medium mb-2">Item Notes</h3>
              <div className="mb-2">
                <Textarea
                  placeholder="Add a note about this item..."
                  value={noteContent}
                  onChange={(e) => setNoteContent(e.target.value)}
                  className="resize-none mb-2"
                  rows={2}
                />
                <Button
                  onClick={handleAddNote}
                  disabled={isAddingNote || !noteContent.trim()}
                  className="w-full"
                >
                  {isAddingNote ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : null}
                  Add Note
                </Button>
              </div>

              <Separator className="mb-2" />

              <ScrollArea className="flex-grow" style={{ height: "calc(100vh - 350px)" }}>
                <div className="flex-grow flex flex-col">
                  {isLoadingNotes ? (
                    <div className="flex justify-center py-4 flex-grow">
                      <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                    </div>
                  ) : notes.length === 0 ? (
                    <div className="text-center py-4 text-muted-foreground flex-grow">
                      No notes yet. Add the first note above.
                    </div>
                  ) : (
                    <div className="space-y-1 flex-grow">
                      {notes.map((note) => (
                        <ItemNote
                          key={note._id}
                          note={note}
                          onDelete={handleDeleteNote}
                        />
                      ))}
                    </div>
                  )}
                </div>
              </ScrollArea>

            </div>

            <div className="border rounded-md p-2 flex flex-col">
              <h3 className="text-lg font-medium mb-2">Item History</h3>
              <ScrollArea>
                {isLoadingAuditLogs ? (
                  <div className="flex justify-center py-4 flex-grow">
                    <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                  </div>
                ) : auditLogs.length === 0 ? (
                  <div className="text-center py-4 text-muted-foreground flex-grow">
                    No history available for this item.
                  </div>
                ) : (
                  <div className="flex-grow">
                    <div className="space-y-1">
                      {auditLogs.map((log) => (
                        <div key={log._id} className="text-sm border-b border-border/40 pb-1 last:border-0 last:pb-0">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-1">
                              <span className={`px-1.5 py-0.5 rounded-sm text-xs font-medium ${log.action === 'create' ? 'bg-green-500/20 text-green-500' : log.action === 'update' ? 'bg-blue-500/20 text-blue-500' : 'bg-red-500/20 text-red-500'}`}>
                                {log.action.toUpperCase()}
                              </span>
                              <span className="text-muted-foreground text-xs">
                                by {log.userId.name || log.userId.username || log.userId.email}
                              </span>
                            </div>
                            <span className="text-xs text-muted-foreground">
                              {new Date(log.timestamp).toLocaleString()}
                            </span>
                          </div>
                          {log.action === 'update' && Object.keys(log.changes).length > 0 && (
                            <div className="mt-0.5 pl-2 border-l-2 border-blue-500/30">
                              {Object.keys(log.changes).map(field => {
                                const fieldName = field
                                  .replace(/([A-Z])/g, ' $1')
                                  .replace(/^./, str => str.toUpperCase());

                                const oldValue = log.previousValues[field];
                                const newValue = log.newValues[field];

                                // Format values for display
                                const formatValue = (value: any) => {
                                  if (value === null || value === undefined) return 'None';
                                  if (typeof value === 'boolean') return value ? 'Yes' : 'No';
                                  if (typeof value === 'number') {
                                    if (field === 'lastRRP' || field === 'currentPrice') {
                                      return `$${value.toFixed(2)}`;
                                    }
                                    return value.toString();
                                  }
                                  return String(value);
                                };

                                return (
                                  <div key={field} className="text-xs grid grid-cols-3 gap-1 py-0.5">
                                    <span className="font-medium">{fieldName}</span>
                                    <span className="text-muted-foreground line-through">{formatValue(oldValue)}</span>
                                    <span className="font-medium">{formatValue(newValue)}</span>
                                  </div>
                                );
                              })}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </ScrollArea>
            </div>
          </div>

          <DialogFooter className="mt-auto flex justify-end p-0 pt-2 pb-2 px-2">
            <DialogClose asChild>
              <Button variant="outline" size="sm">Close</Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Item</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this item? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault();
                handleDeleteItem();
              }}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : null}
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
