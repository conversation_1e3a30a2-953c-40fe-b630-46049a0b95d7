const Location = require('../models/Location');

/**
 * Service for managing locations
 */
class LocationService {
  /**
   * Get all locations
   * @returns {Promise<Array>} List of all locations
   */
  async getAllLocations() {
    try {
      const locations = await Location.find({});
      return locations;
    } catch (error) {
      console.error('Error getting locations:', error);
      throw error;
    }
  }

  /**
   * Create a new location
   * @param {Object} locationData - Location data
   * @returns {Promise<Object>} Created location
   */
  async createLocation(locationData) {
    try {
      const location = new Location(locationData);
      await location.save();
      console.log('Location created successfully:', location._id);
      return location;
    } catch (error) {
      console.error('Error creating location:', error);
      throw error;
    }
  }

  /**
   * Delete a location by ID
   * @param {string} id - Location ID
   * @returns {Promise<Object>} Deletion result
   */
  async deleteLocation(id) {
    try {
      const result = await Location.findByIdAndDelete(id);
      if (!result) {
        console.error(`Location with ID ${id} not found`);
        throw new Error('Location not found');
      }
      console.log(`Location ${id} deleted successfully`);
      return { success: true, message: 'Location deleted successfully' };
    } catch (error) {
      console.error('Error deleting location:', error);
      throw error;
    }
  }

  /**
   * Toggle location active status
   * @param {string} id - Location ID
   * @param {boolean} isActive - New active status
   * @returns {Promise<Object>} Updated location
   */
  async toggleLocationStatus(id, isActive) {
    try {
      const location = await Location.findByIdAndUpdate(
        id,
        { isActive },
        { new: true, runValidators: true }
      );

      if (!location) {
        console.error(`Location with ID ${id} not found`);
        throw new Error('Location not found');
      }

      console.log(`Location ${id} status updated successfully to ${isActive}`);
      return location;
    } catch (error) {
      console.error('Error updating location status:', error);
      throw error;
    }
  }

  /**
   * Seeds initial locations data
   * @returns {Promise<Object>} Result of the operation
   */
  async seedLocations() {
    try {
      // Check if locations already exist
      const existingLocations = await Location.countDocuments();
      console.log(`Found ${existingLocations} existing locations`);

      if (existingLocations > 0) {
        console.log('Locations already exist, skipping seeding');
        return {
          success: true,
          message: "Locations already exist, skipping seeding",
          count: existingLocations
        };
      }

      // Initial locations data
      const initialLocations = [
        {
          name: "Main Store",
          address: "123 Main St, Auckland",
          phone: "09-555-1234",
          isActive: true
        },
        {
          name: "Downtown Branch",
          address: "456 Queen St, Auckland CBD",
          phone: "09-555-5678",
          isActive: true
        },
        {
          name: "Westside Shop",
          address: "789 West Coast Rd, Glen Eden",
          phone: "09-555-9012",
          isActive: true
        },
        {
          name: "North Shore Location",
          address: "321 Takapuna Beach Rd, North Shore",
          phone: "09-555-3456",
          isActive: true
        },
        {
          name: "South Auckland Branch",
          address: "654 Great South Rd, Manukau",
          phone: "09-555-6789",
          isActive: false
        }
      ];

      // Insert all locations
      const result = await Location.insertMany(initialLocations);


      return {
        success: true,
        message: `${result.length} locations seeded successfully`,
        locations: result
      };
    } catch (error) {
      console.error("Error seeding locations:", error);
      return {
        success: false,
        message: error.message,
        error
      };
    }
  }
}

module.exports = new LocationService();