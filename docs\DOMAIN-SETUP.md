# Domain and SSL Setup Guide for GH0ST Application

This guide will help you set up your domain (rotorua.ghst.nz) with Cloudflare and configure SSL for your locally hosted GH0ST application.

## Prerequisites

- A registered domain (ghst.nz) with <PERSON>flare as the nameserver
- Administrator access to your Cloudflare account
- Administrator access to your Windows server
- Administrator access to your network router (for port forwarding)

## 1. Cloudflare Configuration

### Create a Cloudflare API Token

1. Log in to your Cloudflare account
2. Go to "My Profile" > "API Tokens"
3. Click "Create Token"
4. Select "Create Custom Token"
5. Name it "Caddy DNS Verification"
6. Add the following permissions:
   - Zone - DNS - Edit
   - Zone - Zone - Read
7. Set Zone Resources to "Include" - "Specific zone" - "ghst.nz"
8. Create the token and copy it (you'll need it later)

### Configure DNS Settings

1. Go to the DNS section for your domain
2. Add an A record:
   - Type: A
   - Name: rotorua (for rotorua.ghst.nz)
   - Content: Your public IP address
   - Proxy status: Proxied (orange cloud)
   - TTL: Auto

### Configure SSL/TLS Settings

1. Go to the SSL/TLS section
2. Set SSL/TLS encryption mode to "Full (strict)"
3. Go to Edge Certificates tab
4. Enable "Always Use HTTPS"
5. Enable "Automatic HTTPS Rewrites"

## 2. Install and Configure Caddy

Caddy is a modern web server that will handle SSL certificates automatically.

### Install Caddy

1. Run the `install-caddy-dns.bat` script as administrator:
   ```
   Right-click install-caddy-dns.bat > Run as administrator
   ```

2. Edit the Cloudflare API token:
   - When prompted, run the NSSM editor:
     ```
     "C:\Program Files\Caddy\nssm.exe" edit "Caddy Web Server"
     ```
   - Go to the Environment tab
   - Replace `your_cloudflare_api_token_here` with the API token you created earlier
   - Click Update, then OK

3. Restart the Caddy service:
   ```
   net stop "Caddy Web Server"
   net start "Caddy Web Server"
   ```

4. Alternatively, you can test Caddy without installing it as a service:
   ```
   run-caddy.bat
   ```
   This will run Caddy in the foreground, showing all logs directly in the console.

### Verify Caddy Configuration

1. Check if Caddy is running:
   ```
   sc query "Caddy Web Server"
   ```

2. Check the logs for any errors:
   ```
   type "C:\Program Files\Caddy\logs\access.log"
   ```

## 3. Configure Port Forwarding

You need to forward ports 80 and 443 from your router to your server:

1. Log in to your router's admin interface (typically http://*********** or http://***********)
2. Find the Port Forwarding section (might be under Advanced Settings)
3. Add two port forwarding rules:
   - Forward external port 80 to internal port 80 on your server's IP address
   - Forward external port 443 to internal port 443 on your server's IP address

## 4. Test Your Domain

1. Open a web browser on a different device (not connected to your local network)
2. Navigate to https://rotorua.ghst.nz
3. You should see your GH0ST application with a valid SSL certificate

## 5. Troubleshooting

### SSL Certificate Issues

If Caddy can't obtain an SSL certificate:

1. Check the Caddy logs:
   ```
   type "C:\Program Files\Caddy\logs\access.log"
   ```

2. Verify your Cloudflare API token has the correct permissions

3. Make sure ports 80 and 443 are properly forwarded

### Application Not Loading

If your application doesn't load:

1. Check if your GH0ST application is running:
   ```
   sc query "GH0ST Application"
   ```

2. Verify Caddy is running:
   ```
   sc query "Caddy Web Server"
   ```

3. Check if you can access the application locally:
   ```
   http://localhost:3000
   ```

### DNS Issues

If your domain doesn't resolve:

1. Check your Cloudflare DNS settings
2. Verify your A record points to your correct public IP address
3. Use a DNS lookup tool to check if your domain is resolving correctly:
   ```
   nslookup rotorua.ghst.nz
   ```

## 6. Maintenance

### Updating Your IP Address

If your public IP address changes:

1. Update the A record in Cloudflare DNS settings
2. No need to restart Caddy or your application

### Renewing SSL Certificates

Caddy automatically renews SSL certificates, so you don't need to worry about this.

### Monitoring

Consider setting up monitoring for your services:

1. Check Caddy service status regularly
2. Monitor your application's logs
3. Set up alerts for service failures

## Additional Resources

- [Caddy Documentation](https://caddyserver.com/docs/)
- [Cloudflare Documentation](https://developers.cloudflare.com/fundamentals/)
- [Let's Encrypt Documentation](https://letsencrypt.org/docs/)
