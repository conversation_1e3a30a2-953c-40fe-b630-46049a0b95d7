require('dotenv').config();
const mongoose = require('mongoose');
const { connectDB } = require('../config/database');

async function dropEmailIndex() {
  try {
    await connectDB();
    console.log('Connected to MongoDB');

    // Drop the problematic index
    const result = await mongoose.connection.db.collection('customers').dropIndex('email_1');
    console.log('Index dropped successfully:', result);

    console.log('Now creating new index with correct constraints...');

    // Optional: Directly create the new index
    await mongoose.connection.db.collection('customers').createIndex(
      { email: 1 },
      {
        unique: true,
        sparse: true,
        partialFilterExpression: { email: { $exists: true, $ne: null, $ne: "" } }
      }
    );

    console.log('New index created successfully');
  } catch (error) {
    console.error('Error:', error.message);
    console.error(error.stack);
    if (error.codeName === 'IndexNotFound') {
      console.log('Index was already removed or doesn\'t exist');
    }
  } finally {
    await mongoose.connection.close();
    console.log('MongoDB connection closed');
  }
}

dropEmailIndex();