# GH0ST Business Management Application - Deployment Guide

## Windows Deployment Guide

This guide will help you deploy the GH0ST application on a Windows server.

### Prerequisites

- Windows 10/11 or Windows Server 2016/2019/2022
- Node.js 16+ installed
- MongoDB installed and running
- Administrator privileges

### Quick Deployment

For a quick automated deployment, run the `deploy.bat` script as administrator:

1. Right-click on `deploy.bat`
2. Select "Run as administrator"
3. Follow the on-screen instructions

The script will:
- Install dependencies
- Build the client application
- Set up the production environment
- Create necessary directories
- Install the application as a Windows service

### Manual Deployment Steps

If you prefer to deploy manually, follow these steps:

#### 1. Install Dependencies

```bash
# Install client dependencies
cd client
npm install --production

# Install server dependencies
cd ../server
npm install --production
npm install node-windows --save
```

#### 2. Build the Client Application

```bash
# From the project root
cd client
npm run build

# Copy build files to server/public
mkdir -p ../server/public
xcopy /E /I /Y dist\* ..\server\public\
```

#### 3. Configure Environment

```bash
# Copy production environment file
cd ../server
copy .env.production .env
```

Edit the `.env` file to configure:
- Database connection
- JWT secrets
- API keys
- Upload directory paths

#### 4. Create Required Directories

```bash
mkdir uploads
mkdir logs
```

#### 5. Run as a Service

```bash
# Install as a Windows service
node install-service.js
```

#### 6. Manual Start (Alternative to Service)

```bash
# Start in production mode
npm run prod
```

### Accessing the Application

Once deployed, the application will be available at:
- http://localhost:3000 (default)

### Troubleshooting

#### Service Won't Start

Check the Windows Event Viewer for errors:
1. Open Event Viewer
2. Navigate to Windows Logs > Application
3. Look for errors related to "GH0ST Application"

#### Database Connection Issues

Verify MongoDB is running:
```bash
mongod --version
```

Check the connection string in `.env` file.

#### File Permission Issues

Ensure the Windows user running the service has write permissions to:
- `server/uploads` directory
- `server/logs` directory

#### CSRF Token Errors

If you encounter CSRF token errors:
1. Check that cookies are being properly set
2. Verify that the client can access the CSRF token endpoint
3. Check browser console for any CORS errors

#### Security Headers

The application uses Helmet for security headers. If you're running behind a reverse proxy, ensure it's not stripping these headers.

### Uninstalling

To uninstall the Windows service:
```bash
cd server
node uninstall-service.js
```

## Production Maintenance

### Logs

Logs are stored in the `server/logs` directory. Check these for troubleshooting.

### Database Backups

It's recommended to set up regular MongoDB backups:

```bash
# Create a backup script (backup.bat)
@echo off
set BACKUP_PATH=C:\backups\mongodb
set TIMESTAMP=%date:~-4,4%%date:~-7,2%%date:~-10,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set TIMESTAMP=%TIMESTAMP: =0%
mkdir %BACKUP_PATH%\%TIMESTAMP%
mongodump --db pythagora --out %BACKUP_PATH%\%TIMESTAMP%
```

### Updating the Application

To update the application:

1. Stop the service:
   ```bash
   net stop "GH0ST Application"
   ```

2. Pull the latest code:
   ```bash
   git pull
   ```

3. Run the deployment script:
   ```bash
   deploy.bat
   ```

## Security Considerations

- The application uses JWT authentication with refresh tokens
- CSRF protection is enabled for all non-GET requests
- Security headers are set using Helmet
- Passwords are hashed using bcrypt

## License

This software is proprietary and confidential.
Copyright © 2024 GH0ST. All rights reserved.
