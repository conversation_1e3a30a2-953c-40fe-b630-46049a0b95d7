[data-sonner-toaster][data-theme='dark'] {
  --normal-bg: hsl(var(--background));
  --normal-border: hsl(var(--border));
  --normal-text: hsl(var(--foreground));

  --success-bg: hsl(147.8, 80.4%, 3.1%);
  --success-border: hsl(147.8, 80.4%, 15.1%);
  --success-text: hsl(147.8, 80.4%, 89.8%);

  --error-bg: hsl(347.2, 84.4%, 3.1%);
  --error-border: hsl(347.2, 84.4%, 15.1%);
  --error-text: hsl(347.2, 84.4%, 89.8%);

  --warning-bg: hsl(47.8, 84.4%, 3.1%);
  --warning-border: hsl(47.8, 84.4%, 15.1%);
  --warning-text: hsl(47.8, 84.4%, 89.8%);

  --info-bg: hsl(217.2, 84.4%, 3.1%);
  --info-border: hsl(217.2, 84.4%, 15.1%);
  --info-text: hsl(217.2, 84.4%, 89.8%);
}

/* Base toast styles */
[data-sonner-toast] {
  border-width: 1px;
  border-style: solid;
}

/* Normal toast */
[data-sonner-toast][data-type='default'] {
  border-color: var(--normal-border);
}

/* Success toast */
[data-sonner-toast][data-type='success'] {
  border-color: var(--success-text) !important;
  box-shadow: 0 0 0 1px var(--success-text);
}

/* Error toast */
[data-sonner-toast][data-type='error'] {
  border-color: var(--error-text) !important;
  box-shadow: 0 0 0 1px var(--error-text);
}

/* Warning toast */
[data-sonner-toast][data-type='warning'] {
  border-color: var(--warning-text) !important;
  box-shadow: 0 0 0 1px var(--warning-text);
}

/* Info toast */
[data-sonner-toast][data-type='info'] {
  border-color: var(--info-text) !important;
  box-shadow: 0 0 0 1px var(--info-text);
}

/* Close button styling */
[data-sonner-toaster][data-theme='dark'] [data-sonner-toast] [data-close-button] {
  background: var(--normal-bg);
  border-color: var(--normal-border);
  color: var(--normal-text);
}

[data-sonner-toaster][data-theme='dark'] [data-sonner-toast] [data-close-button]:hover {
  background: hsl(var(--background));
  border-color: hsl(var(--border));
}

/* Specific close button colors for each type */
[data-sonner-toast][data-type='success'] [data-close-button] {
  color: var(--success-text) !important;
}

[data-sonner-toast][data-type='error'] [data-close-button] {
  color: var(--error-text) !important;
}

[data-sonner-toast][data-type='warning'] [data-close-button] {
  color: var(--warning-text) !important;
}

[data-sonner-toast][data-type='info'] [data-close-button] {
  color: var(--info-text) !important;
}
