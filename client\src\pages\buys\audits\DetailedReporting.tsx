import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/hooks/useToast';
import { useAuth } from '@/contexts/AuthContext';
import { getAudits, getAuditStats, Audit } from '@/api/buyPawnAudits';
import { AuditNavigation } from '@/components/buys/audits/AuditNavigation';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, Download } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { DatePicker } from '@/components/ui/date-picker';

// Charts
import {
  PieChart,
  Pie,
  Cell,
  ResponsiveContainer,
  Legend,
  Tooltip,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  LineChart,
  Line,
} from 'recharts';

/**
 * Component for detailed audit reporting and statistics
 */
export function DetailedReporting() {
  const { toast } = useToast();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState<any>({
    totalAudits: 0,
    byType: {
      buy: 0,
      pawn: 0,
      price: 0,
    },
    byCompliance: {
      compliant: 0,
      minorNonCompliant: 0,
      majorNonCompliant: 0,
    },
    flagged: {
      total: 0,
      followedUp: 0,
      pending: 0,
    },
    complianceRate: 0,
  });

  const [timeRange, setTimeRange] = useState('all');
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);

  // Load audit statistics
  useEffect(() => {
    const loadStats = async () => {
      setIsLoading(true);
      try {
        const result = await getAuditStats();

        if (result.success) {
          setStats(result.data);
        } else {
          toast({
            title: 'Error',
            description: result.error || 'Failed to load audit statistics.',
            variant: 'destructive',
          });
        }
      } catch (error: any) {
        toast({
          title: 'Error',
          description: error.message || 'An unexpected error occurred.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadStats();
  }, [toast]);

  // Handle time range change
  const handleTimeRangeChange = (value: string) => {
    setTimeRange(value);

    // Set date range based on selection
    const now = new Date();
    let start: Date | undefined = undefined;
    let end: Date | undefined = undefined;

    switch (value) {
      case 'week':
        start = new Date(now);
        start.setDate(now.getDate() - 7);
        end = now;
        break;
      case 'month':
        start = new Date(now);
        start.setMonth(now.getMonth() - 1);
        end = now;
        break;
      case 'quarter':
        start = new Date(now);
        start.setMonth(now.getMonth() - 3);
        end = now;
        break;
      case 'year':
        start = new Date(now);
        start.setFullYear(now.getFullYear() - 1);
        end = now;
        break;
      case 'custom':
        // Keep current custom dates if set
        break;
      default:
        // 'all' - no date filtering
        break;
    }

    setStartDate(start);
    setEndDate(end);
  };

  // Handle export to CSV
  const handleExportCSV = async () => {
    try {
      // Get all audits for the selected time range
      const filters: any = {
        limit: 1000, // Get a large number of audits
      };

      if (startDate && endDate) {
        filters.startDate = startDate.toISOString();
        filters.endDate = endDate.toISOString();
      }

      const result = await getAudits(filters);

      if (result.success && result.data.length > 0) {
        // Convert audits to CSV
        const headers = [
          'Transaction ID',
          'Audit Type',
          'Employee',
          'Amount',
          'Compliance',
          'Score',
          'Flagged',
          'Followed Up',
          'Audit Date',
        ];

        const rows = result.data.map((audit: any) => [
          audit.transactionId,
          audit.auditType,
          audit.employeeName,
          audit.amount,
          audit.overallCompliance,
          audit.overallScore,
          audit.flaggedForFollowup ? 'Yes' : 'No',
          audit.followedUp ? 'Yes' : 'No',
          new Date(audit.auditDate).toLocaleDateString(),
        ]);

        const csvContent = [
          headers.join(','),
          ...rows.map((row: any[]) => row.join(',')),
        ].join('\n');

        // Create and download CSV file
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.setAttribute('href', url);
        link.setAttribute('download', `audit-report-${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        toast({
          title: 'Export Successful',
          description: `Exported ${result.data.length} audits to CSV.`,
        });
      } else {
        toast({
          title: 'Export Failed',
          description: 'No audits found for the selected time range.',
          variant: 'destructive',
        });
      }
    } catch (error: any) {
      toast({
        title: 'Export Failed',
        description: error.message || 'An unexpected error occurred.',
        variant: 'destructive',
      });
    }
  };

  // Prepare chart data
  const complianceData = [
    { name: 'Compliant', value: stats.byCompliance.compliant, color: '#4ade80' },
    { name: 'Minor Non-Compliant', value: stats.byCompliance.minorNonCompliant, color: '#facc15' },
    { name: 'Major Non-Compliant', value: stats.byCompliance.majorNonCompliant, color: '#f87171' },
  ];

  const auditTypeData = [
    { name: 'Buy', value: stats.byType.buy, color: '#60a5fa' },
    { name: 'Pawn', value: stats.byType.pawn, color: '#c084fc' },
    { name: 'Price', value: stats.byType.price, color: '#34d399' },
  ];

  const followupData = [
    { name: 'Completed', value: stats.flagged.followedUp, color: '#4ade80' },
    { name: 'Pending', value: stats.flagged.pending, color: '#f87171' },
  ];

  // Check if user has permission to access this page
  if (user?.role !== 'admin' && user?.role !== 'manager') {
    return (
      <div className="container mx-auto py-6">
        <Card>
          <CardHeader>
            <CardTitle>Access Denied</CardTitle>
            <CardDescription>
              You do not have permission to access the detailed reporting page.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => navigate('/buys/audits')}>Back to Dashboard</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-3xl font-bold mb-6">Detailed Reporting</h1>
      <AuditNavigation />

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Filters */}
        <div className="lg:col-span-1 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Report Filters</CardTitle>
              <CardDescription>
                Select filters to generate a custom report
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="dateRange">Date Range</Label>
                <div className="grid grid-cols-2 gap-2">
                  <Input
                    id="startDate"
                    type="date"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                    placeholder="Start Date"
                  />
                  <Input
                    id="endDate"
                    type="date"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                    placeholder="End Date"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="transactionType">Transaction Type</Label>
                <Select value={transactionType} onValueChange={setTransactionType}>
                  <SelectTrigger id="transactionType">
                    <SelectValue placeholder="All Types" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="buy">Buy</SelectItem>
                    <SelectItem value="pawn">Pawn</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="employee">Employee</Label>
                <Select value={employee} onValueChange={setEmployee}>
                  <SelectTrigger id="employee">
                    <SelectValue placeholder="All Employees" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Employees</SelectItem>
                    {users.map((user) => (
                      <SelectItem key={user._id} value={user._id}>
                        {user.fullName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>



              <Separator />

              <div className="space-y-2">
                <Label htmlFor="complianceStatus">Compliance Status</Label>
                <Select value={complianceStatus} onValueChange={setComplianceStatus}>
                  <SelectTrigger id="complianceStatus">
                    <SelectValue placeholder="All Statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="compliant">Compliant</SelectItem>
                    <SelectItem value="minor_non_compliant">Minor Non-Compliant</SelectItem>
                    <SelectItem value="major_non_compliant">Major Non-Compliant</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="riskLevel">Risk Level</Label>
                <Select value={riskLevel} onValueChange={setRiskLevel}>
                  <SelectTrigger id="riskLevel">
                    <SelectValue placeholder="All Risk Levels" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Risk Levels</SelectItem>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="flagStatus">Flag Status</Label>
                <Select value={flagStatus} onValueChange={setFlagStatus}>
                  <SelectTrigger id="flagStatus">
                    <SelectValue placeholder="All Flag Statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Flag Statuses</SelectItem>
                    <SelectItem value="flagged">Flagged</SelectItem>
                    <SelectItem value="not_flagged">Not Flagged</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Button
                className="w-full"
                onClick={generateReport}
                disabled={isGenerating}
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <Filter className="mr-2 h-4 w-4" />
                    Generate Report
                  </>
                )}
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Report Columns</CardTitle>
              <CardDescription>
                Select columns to include in the report
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between">
                <Button variant="outline" size="sm" onClick={selectAllColumns}>
                  Select All
                </Button>
                <Button variant="outline" size="sm" onClick={deselectAllColumns}>
                  Deselect All
                </Button>
              </div>

              <Separator />

              <div className="space-y-2">
                {columns.map((column) => (
                  <div key={column.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`column-${column.id}`}
                      checked={column.checked}
                      onCheckedChange={() => toggleColumn(column.id)}
                    />
                    <Label
                      htmlFor={`column-${column.id}`}
                      className="text-sm font-normal cursor-pointer"
                    >
                      {column.label}
                    </Label>
                  </div>
                ))}
              </div>

              <Button
                className="w-full"
                onClick={exportToCsv}
                disabled={audits.length === 0}
              >
                <Download className="mr-2 h-4 w-4" />
                Export to CSV
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Right Column - Report Results */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Report Results</CardTitle>
                  <CardDescription>
                    {audits.length > 0
                      ? `Showing ${audits.length} audit records`
                      : 'No results to display. Use the filters to generate a report.'}
                  </CardDescription>
                </div>
                {audits.length > 0 && (
                  <Button variant="outline" size="sm" onClick={() => setAudits([])}>
                    Clear Results
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {isGenerating ? (
                <div className="flex justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              ) : audits.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  Use the filters on the left to generate a report.
                </div>
              ) : (
                <div className="rounded-md border overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        {columns.filter(col => col.checked).map((column) => (
                          <TableHead key={column.id}>{column.label}</TableHead>
                        ))}
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {audits.map((audit) => (
                        <TableRow
                          key={audit._id}
                          className="cursor-pointer hover:bg-muted/50"
                          onClick={() => navigate(`/buys/audits/${audit._id}`)}
                        >
                          {columns.filter(col => col.checked).map((column) => (
                            <TableCell key={`${audit._id}-${column.id}`}>
                              {column.id === 'transactionId' && audit.transactionId}
                              {column.id === 'transactionType' && (
                                <span className="capitalize">{audit.transactionType}</span>
                              )}
                              {column.id === 'date' && formatDate(audit.auditDate)}
                              {column.id === 'employee' && (
                                typeof audit.dealId !== 'string' && audit.dealId?.employee?.fullName
                                  ? audit.dealId.employee.fullName
                                  : 'N/A'
                              )}
                              {column.id === 'location' && (
                                typeof audit.dealId !== 'string' && audit.dealId?.location
                                  ? audit.dealId.location
                                  : 'N/A'
                              )}
                              {column.id === 'amount' && (
                                typeof audit.dealId !== 'string' && audit.dealId?.amount
                                  ? `$${audit.dealId.amount.toFixed(2)}`
                                  : 'N/A'
                              )}
                              {column.id === 'items' && (
                                typeof audit.dealId !== 'string' && audit.dealId?.items
                                  ? audit.dealId.items.map(item => item.name).join(', ')
                                  : 'N/A'
                              )}
                              {column.id === 'overallCompliance' && getStatusBadge(audit.overallCompliance)}
                              {column.id === 'riskScore' && audit.riskScore}
                              {column.id === 'riskLevel' && getRiskBadge(audit.riskLevel)}
                              {column.id === 'dataEntryQuality' && getStatusBadge(audit.dataEntryQuality.status)}
                              {column.id === 'complianceAssessment' && getStatusBadge(audit.complianceAssessment.status)}
                              {column.id === 'pricingAndLimitCompliance' && getStatusBadge(audit.pricingAndLimitCompliance.status)}
                              {column.id === 'essentialItemOutcome' && getStatusBadge(audit.essentialItemCheck.compliance)}
                              {column.id === 'flagStatus' && (
                                <Badge variant={audit.flaggedForFollowup ? 'destructive' : 'outline'}>
                                  {audit.flaggedForFollowup ? 'Flagged' : 'Not Flagged'}
                                </Badge>
                              )}
                              {column.id === 'auditNotes' && (
                                <div className="max-w-[200px] truncate">
                                  {audit.auditNotes || 'No notes'}
                                </div>
                              )}
                            </TableCell>
                          ))}
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
