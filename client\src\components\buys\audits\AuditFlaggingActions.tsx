import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/useToast';
import { flagAudit, unflagAudit, completeFollowup } from '@/api/buyPawnAudits';
import { Flag, FlagOff, CheckCircle, AlertTriangle } from 'lucide-react';
import { Audit } from '@/api/buyPawnAudits';

interface AuditFlaggingActionsProps {
  audit: Audit;
  onUpdate: () => void;
  size?: 'sm' | 'default';
  variant?: 'default' | 'ghost' | 'outline';
}

export function AuditFlaggingActions({ audit, onUpdate, size = 'sm', variant = 'ghost' }: AuditFlaggingActionsProps) {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [flagDialogOpen, setFlagDialogOpen] = useState(false);
  const [unflagDialogOpen, setUnflagDialogOpen] = useState(false);
  const [completeDialogOpen, setCompleteDialogOpen] = useState(false);
  const [flagReason, setFlagReason] = useState('');
  const [followupNotes, setFollowupNotes] = useState('');

  // Get the current flag status
  const getFlagStatus = () => {
    if (!audit.flaggedForFollowup) return 'no';
    if (audit.followedUp) return 'done';
    return 'yes';
  };

  // Get flag status badge
  const getFlagStatusBadge = () => {
    const status = getFlagStatus();
    switch (status) {
      case 'no':
        return (
          <Badge variant="outline" className="text-gray-500">
            <FlagOff className="mr-1 h-3 w-3" />
            Not Flagged
          </Badge>
        );
      case 'yes':
        return (
          <Badge variant="destructive" className="bg-yellow-500/10 text-yellow-600 border-yellow-500/20">
            <Flag className="mr-1 h-3 w-3" />
            Flagged
          </Badge>
        );
      case 'done':
        return (
          <Badge variant="outline" className="bg-green-500/10 text-green-600 border-green-500/20">
            <CheckCircle className="mr-1 h-3 w-3" />
            Completed
          </Badge>
        );
    }
  };

  // Handle flagging an audit
  const handleFlag = async () => {
    if (!flagReason.trim()) {
      toast({
        title: 'Error',
        description: 'Please provide a reason for flagging this audit.',
        variant: 'destructive',
      });
      return;
    }

    setIsLoading(true);
    try {
      await flagAudit(audit._id, flagReason);
      toast({
        title: 'Success',
        description: 'Audit has been flagged for follow-up.',
      });
      setFlagDialogOpen(false);
      setFlagReason('');
      onUpdate();
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to flag audit.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle unflagging an audit
  const handleUnflag = async () => {
    setIsLoading(true);
    try {
      await unflagAudit(audit._id);
      toast({
        title: 'Success',
        description: 'Flag has been removed from audit.',
      });
      setUnflagDialogOpen(false);
      onUpdate();
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to unflag audit.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle completing follow-up
  const handleCompleteFollowup = async () => {
    if (!followupNotes.trim()) {
      toast({
        title: 'Error',
        description: 'Please provide notes about the follow-up action taken.',
        variant: 'destructive',
      });
      return;
    }

    setIsLoading(true);
    try {
      await completeFollowup(audit._id, followupNotes);
      toast({
        title: 'Success',
        description: 'Follow-up has been marked as completed.',
      });
      setCompleteDialogOpen(false);
      setFollowupNotes('');
      onUpdate();
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to complete follow-up.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const flagStatus = getFlagStatus();

  return (
    <div className="flex items-center gap-2">
      {/* Status Badge */}
      {getFlagStatusBadge()}

      {/* Action Buttons */}
      {flagStatus === 'no' && (
        <Dialog open={flagDialogOpen} onOpenChange={setFlagDialogOpen}>
          <DialogTrigger asChild>
            <Button variant={variant} size={size}>
              <Flag className="h-4 w-4 mr-1" />
              Flag
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Flag Audit for Follow-up</DialogTitle>
              <DialogDescription>
                Please provide a reason for flagging this audit. This will mark it for follow-up action.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="flagReason">Reason for flagging</Label>
                <Textarea
                  id="flagReason"
                  placeholder="Describe why this audit needs follow-up..."
                  value={flagReason}
                  onChange={(e) => setFlagReason(e.target.value)}
                  rows={3}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setFlagDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleFlag} disabled={isLoading}>
                {isLoading ? 'Flagging...' : 'Flag Audit'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {flagStatus === 'yes' && (
        <>
          <Dialog open={completeDialogOpen} onOpenChange={setCompleteDialogOpen}>
            <DialogTrigger asChild>
              <Button variant={variant} size={size}>
                <CheckCircle className="h-4 w-4 mr-1" />
                Complete
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Complete Follow-up</DialogTitle>
                <DialogDescription>
                  Please provide details about the follow-up action that was taken to resolve this flagged audit.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="followupNotes">Follow-up notes</Label>
                  <Textarea
                    id="followupNotes"
                    placeholder="Describe what action was taken to resolve this issue..."
                    value={followupNotes}
                    onChange={(e) => setFollowupNotes(e.target.value)}
                    rows={3}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setCompleteDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCompleteFollowup} disabled={isLoading}>
                  {isLoading ? 'Completing...' : 'Mark Complete'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Dialog open={unflagDialogOpen} onOpenChange={setUnflagDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" size={size}>
                <FlagOff className="h-4 w-4 mr-1" />
                Unflag
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Remove Flag</DialogTitle>
                <DialogDescription>
                  Are you sure you want to remove the flag from this audit? This will mark it as no longer requiring follow-up.
                </DialogDescription>
              </DialogHeader>
              <DialogFooter>
                <Button variant="outline" onClick={() => setUnflagDialogOpen(false)}>
                  Cancel
                </Button>
                <Button variant="destructive" onClick={handleUnflag} disabled={isLoading}>
                  {isLoading ? 'Removing...' : 'Remove Flag'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </>
      )}

      {flagStatus === 'done' && audit.flagReason && (
        <div className="text-xs text-muted-foreground">
          <AlertTriangle className="h-3 w-3 inline mr-1" />
          {audit.flagReason}
        </div>
      )}
    </div>
  );
}
