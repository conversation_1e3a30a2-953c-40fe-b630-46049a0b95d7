import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';

interface TransactionDetailsFormProps {
  control: any;
  users: any[];
  isLoadingUsers: boolean;
  disabled?: boolean;
}

/**
 * Component for entering transaction details in the audit form
 */
export function TransactionDetailsForm({ 
  control, 
  users, 
  isLoadingUsers, 
  disabled = false 
}: TransactionDetailsFormProps) {
  return (
    <Card>
      <CardContent className="pt-6 space-y-4">
        {/* Transaction ID */}
        <FormField
          control={control}
          name="transactionId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Transaction ID</FormLabel>
              <FormControl>
                <Input placeholder="Enter transaction ID" {...field} disabled={disabled} />
              </FormControl>
              <FormDescription>
                Enter the unique identifier for this transaction
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Employee */}
        <FormField
          control={control}
          name="employeeId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Employee</FormLabel>
              <FormControl>
                <select
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  value={field.value}
                  onChange={field.onChange}
                  disabled={isLoadingUsers || disabled}
                >
                  <option value="">Select an employee</option>
                  {users.map((user) => (
                    <option key={user._id} value={user._id}>
                      {user.fullName}
                    </option>
                  ))}
                </select>
              </FormControl>
              <FormDescription>
                Select the employee who processed this transaction
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <Separator />

        {/* Amount */}
        <FormField
          control={control}
          name="amount"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Amount</FormLabel>
              <FormControl>
                <Input type="text" placeholder="Enter amount" {...field} disabled={disabled} />
              </FormControl>
              <FormDescription>
                Enter the transaction amount
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Item Description */}
        <FormField
          control={control}
          name="itemDescription"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Item Description</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Enter item description"
                  className="min-h-[100px]"
                  {...field}
                  disabled={disabled}
                />
              </FormControl>
              <FormDescription>
                Provide a detailed description of the item(s) involved in this transaction
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
      </CardContent>
    </Card>
  );
}
