import {
  <PERSON><PERSON>ons<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Line,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
} from 'recharts';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';

interface DataItem {
  [key: string]: any;
}

interface LineConfig {
  key: string;
  name: string;
  color: string;
  strokeDasharray?: string;
}

interface MultiLineChartProps {
  data: DataItem[];
  title: string;
  description?: string;
  xAxisKey: string;
  lines: LineConfig[];
  height?: number;
  className?: string;
  valueFormatter?: (value: number) => string;
  dateFormatter?: (date: string) => string;
  dataTransformer?: (data: DataItem[]) => DataItem[];
}

export function MultiLineChart({
  data,
  title,
  description,
  xAxisKey,
  lines,
  height = 300,
  className = '',
  valueFormatter = (value: number) => value.toString(),
  dateFormatter = (date: string) => date,
  dataTransformer,
}: MultiLineChartProps) {
  // Transform data if a transformer is provided
  const chartData = dataTransformer ? dataTransformer(data) : data;
  // Custom tooltip formatter
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border rounded-md shadow-md p-2 text-sm">
          <p className="font-medium">{dateFormatter(label)}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }}>
              {entry.name}: {valueFormatter(entry.value)}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <Card className={className}>
      <CardHeader className="pb-2">
        <CardTitle className="text-base font-medium">{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        <div style={{ width: '100%', height }}>
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={chartData}
              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" vertical={false} />
              <XAxis
                dataKey={xAxisKey}
                tick={{ fontSize: 12 }}
                tickLine={false}
                axisLine={{ stroke: '#e5e7eb' }}
                tickFormatter={dateFormatter}
              />
              <YAxis
                tick={{ fontSize: 12 }}
                tickLine={false}
                axisLine={{ stroke: '#e5e7eb' }}
                tickFormatter={valueFormatter}
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              {lines.map((line, index) => (
                <Line
                  key={index}
                  type="monotone"
                  dataKey={line.key}
                  name={line.name}
                  stroke={line.color}
                  strokeWidth={2}
                  strokeDasharray={line.strokeDasharray}
                  dot={{ r: 4, fill: line.color, strokeWidth: 2 }}
                  activeDot={{ r: 6, fill: line.color, strokeWidth: 2 }}
                />
              ))}
            </LineChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}
