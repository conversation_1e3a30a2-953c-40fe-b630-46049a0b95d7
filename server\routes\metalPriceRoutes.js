const express = require('express');
const router = express.Router();
const metalPriceService = require('../services/metalPriceService');
const { requireUser } = require('./middleware/auth');
const mongoose = require('mongoose');

// Get all metal prices
router.get('/', async (req, res) => {
  try {
    const result = await metalPriceService.getMetalPrices(req.query);
    if (result.success) {
      return res.status(200).json({ success: true, data: result });
    } else {
      return res.status(400).json({ success: false, message: result.error });
    }
  } catch (error) {
    console.error('Error getting metal prices:', error);
    return res.status(500).json({ success: false, message: 'An error occurred while retrieving metal prices' });
  }
});

// Create a metal price - DEVELOPMENT VERSION (no auth required for testing)
router.post('/', async (req, res) => {
  try {
    const { metal, purity, spotPrice, minBuyPrice, maxBuyPrice, meltPrice, nextUpdateAt } = req.body;

    // Validate required fields
    if (!metal || !purity || spotPrice === undefined || minBuyPrice === undefined ||
        maxBuyPrice === undefined || meltPrice === undefined || !nextUpdateAt) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: metal, purity, spotPrice, minBuyPrice, maxBuyPrice, meltPrice, and nextUpdateAt are required'
      });
    }

    // Validate numeric values
    if (spotPrice < 0 || minBuyPrice < 0 || maxBuyPrice < 0 || meltPrice < 0) {
      return res.status(400).json({ success: false, message: 'All price values must be positive numbers' });
    }

    // Create the metal price using the service
    const MetalPrice = require('../models/MetalPrice');
    const newMetalPrice = new MetalPrice({
      metal,
      purity,
      spotPrice,
      minBuyPrice,
      maxBuyPrice,
      meltPrice,
      nextUpdateAt: new Date(nextUpdateAt),
      lastUpdated: new Date()
    });

    const savedMetalPrice = await newMetalPrice.save();

    return res.status(201).json({ success: true, data: savedMetalPrice });
  } catch (error) {
    console.error('Error creating metal price:', error);
    if (error.name === 'ValidationError') {
      return res.status(400).json({ success: false, message: error.message });
    }
    return res.status(500).json({ success: false, message: 'An error occurred while creating metal price' });
  }
});

// Get metal price settings
router.get('/settings', requireUser, async (req, res) => {
  try {
    const result = await metalPriceService.getSettings();
    if (result.success) {
      return res.status(200).json({ success: true, data: result.settings });
    } else {
      return res.status(400).json({ success: false, message: result.error });
    }
  } catch (error) {
    console.error('Error getting metal price settings:', error);
    return res.status(500).json({ success: false, message: 'An error occurred while retrieving settings' });
  }
});

// Update metal price settings
router.put('/settings', requireUser, async (req, res) => {
  try {
    const result = await metalPriceService.updateSettings(req.body);
    if (result.success) {
      return res.status(200).json({ success: true, data: result.settings });
    } else {
      return res.status(400).json({ success: false, message: result.error });
    }
  } catch (error) {
    console.error('Error updating metal price settings:', error);
    return res.status(500).json({ success: false, message: 'An error occurred while updating settings' });
  }
});

// Update prices from API
router.post('/update', requireUser, async (req, res) => {
  try {
    const result = await metalPriceService.updatePricesFromApi();
    if (result.success) {
      return res.status(200).json({ success: true, data: result });
    } else {
      return res.status(400).json({ success: false, message: result.error });
    }
  } catch (error) {
    console.error('Error updating metal prices from API:', error);
    return res.status(500).json({ success: false, message: 'An error occurred while updating prices' });
  }
});

// Get historical prices with optional metal filter and comparing
router.get('/history/:metal?', async (req, res) => {
  try {
    const metal = req.params.metal || 'all';
    const days = parseInt(req.query.days || 365); // Default to 365 days max
    const compare = req.query.compare === 'true'; // If true, return all metals for comparison

    // Limit days to 365 to match API limitations
    const validDays = Math.min(days, 365);

    let actualMetal = metal;
    if (compare && metal !== 'all') {
      // If compare is true and a specific metal is requested,
      // we'll still get all metals but highlight the requested one
      actualMetal = 'all';
    }

    const result = await metalPriceService.getHistoricalPrices(actualMetal, validDays);
    if (result.success) {
      // If a specific metal was requested, mark it as the primary
      if (metal !== 'all' && compare) {
        const processedHistory = {
          primary: metal,
          data: result.history,
          warning: result.warning
        };
        return res.status(200).json({ success: true, data: processedHistory });
      }

      // Include any warnings in the response
      const responseData = {
        success: true,
        data: result.history
      };

      if (result.warning) {
        responseData.warning = result.warning;
      }

      return res.status(200).json(responseData);
    } else {
      return res.status(400).json({ success: false, message: result.error });
    }
  } catch (error) {
    console.error('Error getting historical metal prices:', error);
    return res.status(500).json({
      success: false,
      message: 'An error occurred while retrieving historical prices'
    });
  }
});

// Get API usage
router.get('/api-usage', requireUser, async (req, res) => {
  try {
    const result = await metalPriceService.getApiUsage();
    if (result.success) {
      return res.status(200).json({ success: true, data: result.usage });
    } else {
      return res.status(400).json({ success: false, message: result.error });
    }
  } catch (error) {
    console.error('Error getting API usage:', error);
    return res.status(500).json({ success: false, message: 'An error occurred while retrieving API usage' });
  }
});

// Seed initial metal prices
router.post('/seed', requireUser, async (req, res) => {
  try {
    const result = await metalPriceService.seedMetalPrices();
    if (result.success) {
      return res.status(200).json({ success: true, data: result });
    } else {
      return res.status(400).json({ success: false, message: result.error });
    }
  } catch (error) {
    console.error('Error seeding metal prices:', error);
    return res.status(500).json({ success: false, message: 'An error occurred while seeding metal prices' });
  }
});

// Update a specific metal price by ID
router.put('/:id', requireUser, async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`Received request to update metal price with ID: ${id}`, req.body);

    // Validate ID format before sending to service
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({ success: false, message: 'Invalid metal price ID format' });
    }

    const result = await metalPriceService.updateMetalPrice(id, req.body);

    if (result.success) {
      console.log(`Successfully updated metal price with ID: ${id}`);
      return res.status(200).json({ success: true, data: result.metalPrice });
    } else {
      console.error(`Failed to update metal price: ${result.error}`);
      const statusCode = result.error === 'Metal price not found' ? 404 : 400;
      return res.status(statusCode).json({ success: false, message: result.error });
    }
  } catch (error) {
    console.error('Error updating metal price:', error);
    return res.status(500).json({ success: false, message: 'An error occurred while updating metal price' });
  }
});

module.exports = router;