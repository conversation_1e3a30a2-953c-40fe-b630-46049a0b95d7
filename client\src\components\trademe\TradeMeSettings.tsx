import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Tabs,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  getRequestToken,
  getAccessToken,
  connectAccount,
  disconnectAccount,
  getSettings,
  refreshConnection,
  getMemberSummary,
} from "@/api/tradeMeAccount";
import TradeMeTemplateManager from "@/components/trademe/TradeMeTemplateManager";
import {
  Loader2,
  RefreshCw,
  Link,
  Unlink,
  User,
  Download,
  Upload,
  Archive,
} from "lucide-react";
import { useNavigate } from "react-router-dom";

interface TradeMeAccountStatus {
  connected: boolean;
  username?: string;
  lastUpdated?: string;
  environment: "production" | "sandbox";
}

export function TradeMeSettings() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [accountStatus, setAccountStatus] = useState<TradeMeAccountStatus>({
    connected: false,
    environment: "sandbox",
  });
  const [memberSummary, setMemberSummary] = useState<any>(null);
  const [loadingMemberSummary, setLoadingMemberSummary] = useState(false);
  const [useSandbox, setUseSandbox] = useState(true);

  // OAuth flow states
  const [authUrl, setAuthUrl] = useState<string | null>(null);
  const [showVerifierDialog, setShowVerifierDialog] = useState(false);
  const [oauthToken, setOauthToken] = useState("");
  const [verifier, setVerifier] = useState("");
  const [verifying, setVerifying] = useState(false);

  // Check if user has permission to view this page
  const hasPermission = user?.role === "admin" || user?.role === "manager";

  // API Handlers
  const fetchTradeMeSettings = async () => {
    if (!hasPermission) return;

    try {
      setLoading(true);
      const response = await getSettings();
      console.log('TradeMe settings response:', response);

      if (response && response.accountStatus) {
        console.log('Setting account status:', response.accountStatus);
        setAccountStatus(response.accountStatus);
        setUseSandbox(response.accountStatus.environment === "sandbox");
      } else {
        // Set default values if no settings are found
        console.log('No account status found, setting defaults');
        setAccountStatus({
          connected: false,
          environment: "sandbox",
        });
        setUseSandbox(true);
      }
    } catch (error) {
      console.error("Failed to fetch TradeMe settings:", error);
      toast.error(`Failed to load TradeMe settings: ${(error as Error).message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleConnectAccount = async () => {
    try {
      console.log(`Connecting to TradeMe in ${useSandbox ? 'sandbox' : 'production'} environment`);

      // Define the scopes we need
      const scopes = ['MyTradeMeRead', 'MyTradeMeWrite'];

      // Generate a callback URL based on the current window location
      // TradeMe requires HTTPS for the callback URL
      const baseUrl = window.location.origin.replace('http:', 'https:');
      const callbackUrl = `${baseUrl}/trademe/callback`;
      console.log(`Using callback URL: ${callbackUrl}`);

      const result = await getRequestToken(useSandbox ? "sandbox" : "production", callbackUrl, scopes);

      if (result.success && result.authUrl) {
        setAuthUrl(result.authUrl);
        // Open the authorization URL in a new window
        window.open(result.authUrl, "_blank", "width=800,height=600");
        setShowVerifierDialog(true);
        toast.info("Please authorize the application in the new window and enter the verification code");

        // Show the requested scopes to the user
        if (result.scopes) {
          console.log(`Requesting access with scopes: ${result.scopes.join(', ')}`);
        }
      } else {
        toast.error(result.error ? `Failed to initiate TradeMe connection: ${result.error}` : "Failed to initiate TradeMe connection");
      }
    } catch (error) {
      console.error("Failed to connect TradeMe account:", error);
      toast.error(`Failed to connect TradeMe account: ${(error as Error).message}`);
    }
  };

  const handleVerifierSubmit = async () => {
    if (!oauthToken.trim() || !verifier.trim()) {
      toast.error("Please enter both the OAuth token and verification code");
      return;
    }

    try {
      setVerifying(true);
      console.log(`Submitting OAuth token: ${oauthToken} and verifier: ${verifier}`);

      // Get access token
      const accessTokenResult = await getAccessToken(useSandbox ? "sandbox" : "production", oauthToken, verifier);
      console.log('Access token result:', accessTokenResult);

      if (!accessTokenResult.success) {
        throw new Error(accessTokenResult.error || 'Failed to get access token');
      }

      // Log permissions if available
      if (accessTokenResult.permissions && accessTokenResult.permissions.length > 0) {
        console.log('TradeMe permissions granted:', accessTokenResult.permissions);
      }

      // Connect account with the access token
      console.log(`Connecting account with access token: ${accessTokenResult.accessToken}`);
      const result = await connectAccount(
        useSandbox ? "sandbox" : "production",
        accessTokenResult.accessToken,
        accessTokenResult.accessTokenSecret
      );

      console.log('Connect account result:', result);

      if (result.success) {
        toast.success(`TradeMe account connected successfully as ${result.username}!`);
        setShowVerifierDialog(false);
        setOauthToken("");
        setVerifier("");
        // Refresh the settings to show the connected account
        fetchTradeMeSettings();
      } else {
        toast.error(`Failed to complete TradeMe connection: ${result.error}`);
      }
    } catch (error) {
      console.error("Failed to complete TradeMe connection:", error);
      toast.error(`Failed to complete TradeMe connection: ${(error as Error).message}`);
    } finally {
      setVerifying(false);
    }
  };

  const handleDisconnectAccount = async () => {
    try {
      await disconnectAccount();
      toast.success("TradeMe account disconnected successfully!");
      fetchTradeMeSettings();
    } catch (error) {
      console.error("Failed to disconnect TradeMe account:", error);
      toast.error(`Failed to disconnect TradeMe account: ${(error as Error).message}`);
    }
  };

  const handleRefreshConnection = async () => {
    try {
      setRefreshing(true);
      await refreshConnection();
      toast.success("TradeMe connection refreshed successfully!");
      fetchTradeMeSettings();
    } catch (error) {
      console.error("Failed to refresh TradeMe connection:", error);
      toast.error(`Failed to refresh TradeMe connection: ${(error as Error).message}`);
    } finally {
      setRefreshing(false);
    }
  };

  const fetchMemberSummary = async () => {
    try {
      setLoadingMemberSummary(true);
      const result = await getMemberSummary();
      if (result.success) {
        setMemberSummary(result.memberSummary);
        console.log('TradeMe member summary:', result.memberSummary);
      } else {
        toast.error(result.error || "Failed to get TradeMe member summary");
      }
    } catch (error) {
      console.error("Failed to get TradeMe member summary:", error);
      toast.error(`Failed to get TradeMe member summary: ${(error as Error).message}`);
    } finally {
      setLoadingMemberSummary(false);
    }
  };

  // Event Handlers
  const toggleEnvironment = (checked: boolean) => {
    setUseSandbox(checked);
  };

  // Parse TradeMe date format: "/Date(1514764800)/"
  const parseTradeMeDate = (dateString: string) => {
    if (!dateString) return null;

    // Check if it's in the "/Date(timestamp)/" format
    const match = /\/Date\((\d+)(?:[-+]\d+)?\)\//.exec(dateString);
    if (match) {
      const timestamp = parseInt(match[1], 10);
      return new Date(timestamp);
    }

    // If not in the special format, try regular date parsing
    const date = new Date(dateString);
    return isNaN(date.getTime()) ? null : date;
  };

  // Format date with fallback
  const formatDate = (dateString: string) => {
    if (!dateString) return 'Not available';

    const date = parseTradeMeDate(dateString);
    return date ? date.toLocaleDateString() : 'Invalid date';
  };

  // Get PayNow status text
  const getPayNowStatusText = (status: number) => {
    const statusMap: Record<number, string> = {
      0: 'Unknown',
      1: 'Not Registered',
      2: 'Pending First Activation',
      3: 'Active Pending Replacement Activation',
      4: 'Active',
      5: 'Suspended',
      6: 'Bounced',
      7: 'Bounced Pending Replacement Activation'
    };

    return statusMap[status] || 'Unknown';
  };

  // Effects
  useEffect(() => {
    fetchTradeMeSettings();
  }, []);

  // Fetch member summary when connected
  useEffect(() => {
    if (accountStatus?.connected) {
      fetchMemberSummary();
    }
  }, [accountStatus?.connected]);

  // UI Components
  const MemberSummarySection = () => {
    if (!memberSummary) return null;

    return (
      <div className="mt-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium">Account Information</h3>
          <Button
            variant="ghost"
            size="sm"
            onClick={fetchMemberSummary}
            disabled={loadingMemberSummary}
            className="flex items-center gap-1 text-xs"
          >
            {loadingMemberSummary ? (
              <>
                <Loader2 className="h-3 w-3 animate-spin" />
                Refreshing...
              </>
            ) : (
              <>
                <RefreshCw className="h-3 w-3" />
                Refresh
              </>
            )}
          </Button>
        </div>

        {loadingMemberSummary && !memberSummary ? (
          <div className="flex items-center justify-center py-8 bg-muted/20 rounded-lg">
            <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
            <span className="ml-2 text-muted-foreground">Loading account information...</span>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Profile Overview Card */}
            <Card className="overflow-hidden border-muted shadow-md bg-card">
              <div className="bg-gradient-to-r from-primary to-primary/80 p-6">
                <div className="flex items-center gap-4">
                  <div className="bg-background rounded-full shadow-md overflow-hidden w-14 h-14 flex items-center justify-center relative">
                    {memberSummary.MemberProfile && memberSummary.MemberProfile.Photo ? (
                      <img
                        src={memberSummary.MemberProfile.Photo}
                        alt="Profile"
                        className="w-full h-full object-cover absolute inset-0"
                        onError={(e) => {
                          // If image fails to load, show the User icon instead
                          e.currentTarget.style.display = 'none';
                          e.currentTarget.nextElementSibling?.removeAttribute('style');
                        }}
                      />
                    ) : null}
                    <User
                      className="h-8 w-8 text-primary"
                      style={memberSummary.MemberProfile?.Photo ? {display: 'none'} : undefined}
                    />
                  </div>
                  <div className="text-primary-foreground">
                    <h3 className="text-xl font-semibold">{memberSummary.Nickname}</h3>
                    <p className="text-primary-foreground/80 text-sm">{memberSummary.FirstName} {memberSummary.LastName}</p>
                    <div className="flex items-center mt-1 text-xs">
                      <span className="bg-background/20 text-primary-foreground px-2 py-0.5 rounded-full font-medium">
                        Member since {parseTradeMeDate(memberSummary.DateJoined)?.getFullYear() || 'Unknown'}
                      </span>
                      {memberSummary.IsAddressVerified && (
                        <span className="bg-green-500/20 text-primary-foreground px-2 py-0.5 rounded-full font-medium ml-2">
                          Verified
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              <CardContent className="p-6">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center p-3 bg-primary/5 dark:bg-primary/10 rounded-lg">
                    <p className="text-2xl font-bold text-primary">{memberSummary.TotalFeedback}</p>
                    <p className="text-xs text-muted-foreground">Feedback</p>
                  </div>
                  <div className="text-center p-3 bg-green-500/5 dark:bg-green-500/10 rounded-lg">
                    <p className="text-2xl font-bold text-green-600 dark:text-green-500">{memberSummary.SellingCount}</p>
                    <p className="text-xs text-muted-foreground">Selling</p>
                  </div>
                  <div className="text-center p-3 bg-amber-500/5 dark:bg-amber-500/10 rounded-lg">
                    <p className="text-2xl font-bold text-amber-600 dark:text-amber-500">{memberSummary.SoldCount}</p>
                    <p className="text-xs text-muted-foreground">Sold</p>
                  </div>
                  <div className="text-center p-3 bg-purple-500/5 dark:bg-purple-500/10 rounded-lg">
                    <p className="text-2xl font-bold text-purple-600 dark:text-purple-500">{memberSummary.WatchListCount}</p>
                    <p className="text-xs text-muted-foreground">Watchlist</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Detailed Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="shadow-sm border-muted bg-card">
                <CardHeader className="pb-2">
                  <CardTitle className="text-base font-medium">Account Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between border-b border-border pb-2">
                      <span className="text-sm text-muted-foreground">Member ID</span>
                      <span className="text-sm font-medium">{memberSummary.MemberId}</span>
                    </div>
                    <div className="flex justify-between border-b border-border pb-2">
                      <span className="text-sm text-muted-foreground">Email</span>
                      <span className="text-sm font-medium">{memberSummary.Email}</span>
                    </div>
                    <div className="flex justify-between border-b border-border pb-2">
                      <span className="text-sm text-muted-foreground">Date Joined</span>
                      <span className="text-sm font-medium">{formatDate(memberSummary.DateJoined)}</span>
                    </div>
                    {memberSummary.DateAddressVerified && (
                      <div className="flex justify-between border-b border-border pb-2">
                        <span className="text-sm text-muted-foreground">Address Verified</span>
                        <span className="text-sm font-medium">{formatDate(memberSummary.DateAddressVerified)}</span>
                      </div>
                    )}
                    {memberSummary.Balance !== undefined && (
                      <div className="flex justify-between border-b border-border pb-2">
                        <span className="text-sm text-muted-foreground">Account Balance</span>
                        <span className="text-sm font-medium">${memberSummary.Balance.toFixed(2)}</span>
                      </div>
                    )}
                    {memberSummary.PayNowBalance !== undefined && (
                      <div className="flex justify-between border-b border-border pb-2">
                        <span className="text-sm text-muted-foreground">Pay Now Balance</span>
                        <span className="text-sm font-medium">${memberSummary.PayNowBalance.toFixed(2)}</span>
                      </div>
                    )}
                    {memberSummary.PayNowStatus !== undefined && (
                      <div className="flex justify-between border-b border-border pb-2">
                        <span className="text-sm text-muted-foreground">Pay Now Status</span>
                        <span className="text-sm font-medium">{getPayNowStatusText(memberSummary.PayNowStatus)}</span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-sm border-muted bg-card">
                <CardHeader className="pb-2">
                  <CardTitle className="text-base font-medium">Account Features</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between border-b border-border pb-2">
                      <span className="text-sm text-muted-foreground">Business Account</span>
                      <span className={`text-xs px-2 py-1 rounded-full ${memberSummary.IsBusiness ? 'bg-green-500/20 text-green-600 dark:text-green-400' : 'bg-muted text-muted-foreground'}`}>
                        {memberSummary.IsBusiness ? 'Yes' : 'No'}
                      </span>
                    </div>
                    {memberSummary.IsBusiness && memberSummary.BusinessName && (
                      <div className="flex justify-between border-b border-border pb-2">
                        <span className="text-sm text-muted-foreground">Business Name</span>
                        <span className="text-sm font-medium">{memberSummary.BusinessName}</span>
                      </div>
                    )}
                    <div className="flex items-center justify-between border-b border-border pb-2">
                      <span className="text-sm text-muted-foreground">Pay Now Accepted</span>
                      <span className={`text-xs px-2 py-1 rounded-full ${memberSummary.IsPayNowAccepted ? 'bg-green-500/20 text-green-600 dark:text-green-400' : 'bg-muted text-muted-foreground'}`}>
                        {memberSummary.IsPayNowAccepted ? 'Yes' : 'No'}
                      </span>
                    </div>
                    <div className="flex items-center justify-between border-b border-border pb-2">
                      <span className="text-sm text-muted-foreground">Top Seller</span>
                      <span className={`text-xs px-2 py-1 rounded-full ${memberSummary.IsTopSeller ? 'bg-green-500/20 text-green-600 dark:text-green-400' : 'bg-muted text-muted-foreground'}`}>
                        {memberSummary.IsTopSeller ? 'Yes' : 'No'}
                      </span>
                    </div>
                    <div className="flex items-center justify-between border-b border-border pb-2">
                      <span className="text-sm text-muted-foreground">Auto Billing</span>
                      <span className={`text-xs px-2 py-1 rounded-full ${memberSummary.AutoBillingEnabled ? 'bg-green-500/20 text-green-600 dark:text-green-400' : 'bg-muted text-muted-foreground'}`}>
                        {memberSummary.AutoBillingEnabled ? 'Enabled' : 'Disabled'}
                      </span>
                    </div>
                    <div className="flex items-center justify-between border-b border-border pb-2">
                      <span className="text-sm text-muted-foreground">Professional Trader</span>
                      <span className={`text-xs px-2 py-1 rounded-full ${memberSummary.IsInTrade ? 'bg-green-500/20 text-green-600 dark:text-green-400' : 'bg-muted text-muted-foreground'}`}>
                        {memberSummary.IsInTrade ? 'Yes' : 'No'}
                      </span>
                    </div>
                    {memberSummary.HighVolumeListingCount !== undefined && memberSummary.HighVolumeThreshold !== undefined && (
                      <div className="flex justify-between border-b border-border pb-2">
                        <span className="text-sm text-muted-foreground">High Volume Listings</span>
                        <span className="text-sm font-medium">{memberSummary.HighVolumeListingCount} / {memberSummary.HighVolumeThreshold}</span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Address and Profile Information - Side by Side */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Address Information */}
              {memberSummary.MembershipAddress ? (
                <Card className="shadow-sm border-muted bg-card h-full">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base font-medium">Account Primary Address</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="p-4 bg-muted/30 dark:bg-muted/10 rounded-lg">
                      <p className="font-medium">{memberSummary.MembershipAddress.Name}</p>
                      <p>{memberSummary.MembershipAddress.Address1}</p>
                      {memberSummary.MembershipAddress.Address2 && <p>{memberSummary.MembershipAddress.Address2}</p>}
                      <p>
                        {memberSummary.MembershipAddress.Suburb && <>{memberSummary.MembershipAddress.Suburb}, </>}
                        {memberSummary.MembershipAddress.City}
                      </p>
                      {memberSummary.MembershipAddress.Postcode && <p>{memberSummary.MembershipAddress.Postcode}</p>}
                      <p>{memberSummary.MembershipAddress.Country}</p>
                    </div>
                  </CardContent>
                </Card>
              ) : memberSummary.MemberProfile && (
                <div className="hidden md:block"></div> /* Empty div for grid spacing when only profile exists */
              )}

              {/* Member Profile */}
              {memberSummary.MemberProfile ? (
                <Card className="shadow-sm border-muted bg-card h-full">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base font-medium">Profile Information</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {memberSummary.MemberProfile.Occupation && (
                        <div className="flex justify-between border-b border-border pb-2">
                          <span className="text-sm text-muted-foreground">Occupation</span>
                          <span className="text-sm font-medium">{memberSummary.MemberProfile.Occupation}</span>
                        </div>
                      )}
                      {memberSummary.MemberProfile.Biography && (
                        <div className="border-b border-border pb-2">
                          <span className="text-sm text-muted-foreground block mb-1">Biography</span>
                          <p className="text-sm">{memberSummary.MemberProfile.Biography}</p>
                        </div>
                      )}
                      {memberSummary.MemberProfile.Quote && (
                        <div className="border-b border-border pb-2">
                          <span className="text-sm text-muted-foreground block mb-1">Quote</span>
                          <p className="text-sm italic">"{memberSummary.MemberProfile.Quote}"</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ) : memberSummary.MembershipAddress && (
                <div className="hidden md:block"></div> /* Empty div for grid spacing when only address exists */
              )}
            </div>

            {/* Tax Information */}
            {memberSummary.TaxLiabilities && memberSummary.TaxLiabilities.length > 0 && (
              <Card className="shadow-sm border-muted bg-card">
                <CardHeader className="pb-2">
                  <CardTitle className="text-base font-medium">Tax Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {memberSummary.TaxLiabilities.map((tax: any, index: number) => (
                      <div key={index} className="p-4 bg-muted/30 dark:bg-muted/10 rounded-lg">
                        <div className="flex justify-between mb-2">
                          <span className="font-medium">{tax.Name || 'Tax'}</span>
                          <span className="text-sm bg-primary/10 px-2 py-0.5 rounded-full">
                            {tax.Country || 'Unknown'}
                          </span>
                        </div>
                        <div className="text-sm space-y-1">
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Rate:</span>
                            <span>{(tax.FlatRate * 100).toFixed(2)}%</span>
                          </div>
                          {tax.Description && (
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">Description:</span>
                              <span>{tax.Description}</span>
                            </div>
                          )}
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Effective:</span>
                            <span>{tax.IsTaxEffective ? 'Yes' : 'No'}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Statistics */}
            <Card className="shadow-sm border-muted bg-card">
              <CardHeader className="pb-2">
                <CardTitle className="text-base font-medium">Detailed Statistics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  <div className="p-3 bg-muted/30 dark:bg-muted/10 rounded-lg text-center">
                    <p className="text-sm text-muted-foreground">Positive Feedback</p>
                    <p className="text-lg font-semibold text-green-600 dark:text-green-500">{memberSummary.PositiveFeedback}</p>
                  </div>
                  <div className="p-3 bg-muted/30 dark:bg-muted/10 rounded-lg text-center">
                    <p className="text-sm text-muted-foreground">Negative Feedback</p>
                    <p className="text-lg font-semibold text-red-600 dark:text-red-500">{memberSummary.NegativeFeedback}</p>
                  </div>
                  <div className="p-3 bg-muted/30 dark:bg-muted/10 rounded-lg text-center">
                    <p className="text-sm text-muted-foreground">Unsold Items</p>
                    <p className="text-lg font-semibold">{memberSummary.UnsoldCount}</p>
                  </div>
                  <div className="p-3 bg-muted/30 dark:bg-muted/10 rounded-lg text-center">
                    <p className="text-sm text-muted-foreground">Won Items</p>
                    <p className="text-lg font-semibold text-blue-600 dark:text-blue-500">{memberSummary.WonCount}</p>
                  </div>
                  <div className="p-3 bg-muted/30 dark:bg-muted/10 rounded-lg text-center">
                    <p className="text-sm text-muted-foreground">Lost Items</p>
                    <p className="text-lg font-semibold">{memberSummary.LostCount}</p>
                  </div>
                  <div className="p-3 bg-muted/30 dark:bg-muted/10 rounded-lg text-center">
                    <p className="text-sm text-muted-foreground">Auto Billing</p>
                    <p className="text-lg font-semibold">{memberSummary.AutoBillingEnabled ? 'Enabled' : 'Disabled'}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    );
  };

  const AccountStatusSection = () => (
    <div>
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">API Connection</h3>
        {accountStatus?.connected && (
          <div className="flex space-x-2">
            <Button
              onClick={handleRefreshConnection}
              disabled={refreshing}
              variant="outline"
              size="sm"
              className="flex items-center text-xs"
            >
              {refreshing ? (
                <Loader2 className="mr-1 h-3 w-3 animate-spin" />
              ) : (
                <RefreshCw className="mr-1 h-3 w-3" />
              )}
              Refresh
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={handleDisconnectAccount}
              className="flex items-center text-xs"
            >
              <Unlink className="mr-1 h-3 w-3" />
              Disconnect
            </Button>
          </div>
        )}
      </div>

      <div className="mt-4">
        <Card className="overflow-hidden border-muted bg-card">
          <CardContent className="p-0">
            {accountStatus?.connected ? (
              <div className="grid grid-cols-1 md:grid-cols-3 divide-y md:divide-y-0 md:divide-x divide-border bg-muted/30 dark:bg-muted/10">
                <div className="p-4 flex flex-col justify-center">
                  <div className="flex items-center">
                    <div className={`w-3 h-3 rounded-full mr-3 bg-green-500`}></div>
                    <p className="font-medium">Connected</p>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1 ml-6">
                    as {accountStatus?.username || memberSummary?.Nickname || 'Unknown'}
                  </p>
                </div>

                <div className="p-4 flex flex-col justify-center">
                  <p className="text-sm font-medium">Environment</p>
                  <div className="flex items-center mt-1">
                    <Badge
                      variant={accountStatus?.environment === "sandbox" ? "outline" : "default"}
                      className="mr-2"
                    >
                      {accountStatus?.environment === "sandbox" ? "Sandbox" : "Production"}
                    </Badge>
                    <span className="text-xs text-muted-foreground">
                      {accountStatus?.environment === "sandbox"
                        ? "Test Environment"
                        : "Live Environment"}
                    </span>
                  </div>
                </div>

                <div className="p-4 flex flex-col justify-center">
                  <p className="text-sm font-medium">Last Updated</p>
                  <div className="flex items-center mt-1">
                    <span className="text-xs text-muted-foreground mt-1">
                      {accountStatus?.lastUpdated
                        ? formatDate(accountStatus?.lastUpdated)
                        : 'Never updated'}
                    </span>
                  </div>
                </div>
              </div>
            ) : (
              <div>
                <div className="flex items-center p-4 bg-muted/30 dark:bg-muted/10">
                  <div className="w-3 h-3 rounded-full mr-3 bg-red-500"></div>
                  <div>
                    <p className="font-medium">Not Connected</p>
                  </div>
                </div>

                <div className="p-6 border-t border-border">
                  <div className="flex flex-col items-center justify-center space-y-4">
                    <div className="bg-primary/10 p-4 rounded-full">
                      <Link className="h-8 w-8 text-primary" />
                    </div>
                    <h4 className="text-lg font-medium">Connect to TradeMe</h4>
                    <p className="text-sm text-center text-muted-foreground max-w-md">
                      Connect your TradeMe account to manage your listings, answer questions, and more.
                    </p>

                    <div className="bg-muted/30 dark:bg-muted/10 p-4 rounded-lg w-full mt-2">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="flex items-center space-x-2">
                            <Label htmlFor="sandbox-mode" className="text-sm font-medium">Use Sandbox Environment</Label>
                          </div>
                          <p className="text-xs text-muted-foreground mt-1">
                            Sandbox mode uses the TradeMe test environment.
                          </p>
                        </div>
                        <Switch
                          id="sandbox-mode"
                          checked={useSandbox}
                          onCheckedChange={toggleEnvironment}
                        />
                      </div>
                      <p className="text-xs font-medium mt-3 px-2 py-1 rounded bg-muted/50 dark:bg-muted/20 inline-block">
                        {useSandbox
                          ? "Using test environment - no real transactions will occur"
                          : "Using production environment - real transactions will occur"}
                      </p>
                    </div>

                    <Button
                      onClick={handleConnectAccount}
                      className="flex items-center mt-2 w-full"
                      size="lg"
                    >
                      <Link className="mr-2 h-4 w-4" />
                      Connect TradeMe Account
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );



  // Permission check
  if (!hasPermission) {
    return (
      <Card className="mb-4">
        <CardHeader>
          <CardTitle>Trademe Settings</CardTitle>
          <CardDescription>Manage your Trademe settings</CardDescription>
        </CardHeader>
        <CardContent>
          <p>You don't have permission to view this page.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="mb-4">
      <CardHeader>
        <CardTitle>Trademe Settings</CardTitle>
        <CardDescription>Manage your Trademe settings</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {loading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        ) : (
          <div className="space-y-6">
            <AccountStatusSection />
            {accountStatus?.connected && (
              <>
                <Separator />
                <MemberSummarySection />
                <Separator className="my-6" />

                {/* Import Section */}
                <div className="mb-6">
                  <h3 className="text-lg font-medium mb-4">Import TradeMe Listings</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Card className="shadow-sm border-muted bg-card">
                      <CardContent className="p-6">
                        <div className="flex flex-col items-center text-center space-y-3">
                          <div className="p-3 bg-primary/10 rounded-full">
                            <Download className="h-6 w-6 text-primary" />
                          </div>
                          <h4 className="font-medium">Import Active Listings</h4>
                          <p className="text-sm text-muted-foreground">
                            Import your active TradeMe listings into the system
                          </p>
                          <Button
                            className="w-full mt-2"
                            onClick={() => navigate('/trademe/import')}
                          >
                            Import Active Listings
                          </Button>
                        </div>
                      </CardContent>
                    </Card>

                    <Card className="shadow-sm border-muted bg-card">
                      <CardContent className="p-6">
                        <div className="flex flex-col items-center text-center space-y-3">
                          <div className="p-3 bg-green-500/10 rounded-full">
                            <Archive className="h-6 w-6 text-green-500" />
                          </div>
                          <h4 className="font-medium">Import Sold Listings</h4>
                          <p className="text-sm text-muted-foreground">
                            Import your sold TradeMe listings into the system
                          </p>
                          <Button
                            className="w-full mt-2"
                            variant="outline"
                            onClick={() => navigate('/trademe/import?tab=sold')}
                          >
                            Import Sold Listings
                          </Button>
                        </div>
                      </CardContent>
                    </Card>

                    <Card className="shadow-sm border-muted bg-card">
                      <CardContent className="p-6">
                        <div className="flex flex-col items-center text-center space-y-3">
                          <div className="p-3 bg-blue-500/10 rounded-full">
                            <Upload className="h-6 w-6 text-blue-500" />
                          </div>
                          <h4 className="font-medium">Manage Imported Items</h4>
                          <p className="text-sm text-muted-foreground">
                            View and manage your previously imported items
                          </p>
                          <Button
                            className="w-full mt-2"
                            variant="outline"
                            onClick={() => navigate('/trademe/import/items')}
                          >
                            Manage Imported Items
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>

                {/* Template Management Section */}
                <div>
                  <h3 className="text-lg font-medium mb-4">Template Management</h3>
                  <Tabs defaultValue="question" className="w-full">
                    <TabsList className="grid w-full grid-cols-3">
                      <TabsTrigger value="question">Question Templates</TabsTrigger>
                      <TabsTrigger value="footer">Footer Templates</TabsTrigger>
                      <TabsTrigger value="shipping">Shipping Templates</TabsTrigger>
                    </TabsList>
                    <TabsContent value="question" className="mt-4">
                      <TradeMeTemplateManager defaultType="question" />
                    </TabsContent>
                    <TabsContent value="footer" className="mt-4">
                      <TradeMeTemplateManager defaultType="footer" />
                    </TabsContent>
                    <TabsContent value="shipping" className="mt-4">
                      <TradeMeTemplateManager defaultType="shipping" />
                    </TabsContent>
                  </Tabs>
                </div>
              </>
            )}
          </div>
        )}
      </CardContent>

      {/* OAuth Verification Dialog */}
      <Dialog open={showVerifierDialog} onOpenChange={setShowVerifierDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Enter OAuth Details</DialogTitle>
            <DialogDescription>
              Please enter the OAuth token and verifier provided in the callback URL.
              Look for parameters like <code>oauth_token</code> and <code>oauth_verifier</code>.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4 space-y-4">
            <div>
              <Label htmlFor="oauth-token">OAuth Token</Label>
              <Input
                id="oauth-token"
                value={oauthToken}
                onChange={(e) => setOauthToken(e.target.value)}
                placeholder="Enter the oauth_token value"
                className="mt-2"
              />
            </div>
            <div>
              <Label htmlFor="verifier-code">Verification Code</Label>
              <Input
                id="verifier-code"
                value={verifier}
                onChange={(e) => setVerifier(e.target.value)}
                placeholder="Enter the oauth_verifier value"
                className="mt-2"
              />
            </div>
            <div className="flex items-center mt-4 text-amber-500">
              <span className="text-sm">
                If you closed the authorization window, you can{" "}
                <Button
                  variant="link"
                  className="p-0 h-auto text-amber-500 underline"
                  onClick={() => authUrl && window.open(authUrl, "_blank", "width=800,height=600")}
                >
                  open it again
                </Button>
              </span>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowVerifierDialog(false);
                setOauthToken("");
                setVerifier("");
              }}
            >
              Cancel
            </Button>
            <Button onClick={handleVerifierSubmit} disabled={verifying}>
              {verifying && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Complete Connection
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}