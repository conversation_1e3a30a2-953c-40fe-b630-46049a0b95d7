/**
 * TradeMe Feedback Service
 *
 * Handles syncing and managing TradeMe feedback
 */

const axios = require('axios');
const TradeMeSettings = require('../models/TradeMeSettings');
const { TradeMeFeedback, TradeMeFeedbackStats } = require('../models/TradeMeFeedback');
const { getOAuth, TRADEME_API, parseTradeMeDate } = require('./tradeMeUtils');

/**
 * Sync feedback from TradeMe API
 * @param {Object} options - Options for the sync operation
 * @param {number} options.maxPagesPerEndpoint - Maximum number of pages to fetch
 *                 For automatic background syncs, this is set to 2 (pages 1-2 only)
 *                 For manual syncs triggered by the user, this can be up to 2000 to retrieve all historical feedback
 * @param {Object} options.syncLog - Sync log object for tracking
 * @returns {Promise<Object>} Result of the sync operation
 */
async function syncFeedbackFromTradeMe(options = {}) {
  try {
    // Default options
    const { maxPagesPerEndpoint = 2, syncLog = null } = options; // Default to 2 pages for automatic syncs

    // Track API calls and stats
    let apiCallCount = 0;
    // For feedback sync, we want to allow an extremely high number of API calls
    // to ensure we get all feedback items (up to 20,000 items)
    // With a typical page size of 20 items, we need up to 2000 API calls
    const MAX_API_CALLS = maxPagesPerEndpoint > 50 ? 2000 : 20;

    console.log(`Starting Feedback Sync`);

    // Log whether this is an automatic or manual sync based on page count
    if (maxPagesPerEndpoint <= 2) {
      console.log('This is an automatic background sync (retrieving only recent feedback)');
    } else {
      console.log('This is a manual sync (retrieving all feedback)');
    }

    // Track sync statistics
    const stats = {
      itemsProcessed: 0,
      newItems: 0,
      updatedItems: 0,
      apiCallsMade: 0
    };

    // Get current settings
    let settings = await TradeMeSettings.findOne();

    if (!settings || !settings.connected) {
      return {
        success: false,
        error: 'No active TradeMe connection'
      };
    }

    const environment = settings.environment;
    const accessToken = settings.accessToken;
    const accessTokenSecret = settings.accessSecret;

    // Create OAuth instance
    const oauth = getOAuth(environment);
    const apiEndpoints = TRADEME_API[environment];

    // Helper function to make authenticated API calls
    const makeAuthenticatedRequest = async (endpoint, page = 1) => {
      apiCallCount++;
      stats.apiCallsMade++;
      console.log(`TradeMe API Call #${apiCallCount}: ${endpoint} (page ${page})`);

      // Add page parameter if specified
      const url = page > 1 ? `${apiEndpoints.apiBase}${endpoint}?page=${page}` : `${apiEndpoints.apiBase}${endpoint}`;

      // Update the syncLog if provided
      if (syncLog) {
        syncLog.apiCallsMade = stats.apiCallsMade;
        await syncLog.save();
      }

      return axios({
        method: 'get',
        url: url,
        headers: oauth.toHeader(
          oauth.authorize(
            {
              url: url,
              method: 'GET'
            },
            {
              key: accessToken,
              secret: accessTokenSecret
            }
          )
        )
      });
    };

    // Fetch feedback received
    console.log('Starting to fetch TradeMe feedback received...');
    // Get the member ID from settings
    const memberId = settings.memberId || '';
    // Make the API call to get feedback
    const response = await makeAuthenticatedRequest(`/Member/${memberId}/Feedback/All.json`);
    // Extract feedback items and feedback statistics
    const feedbackItems = response.data.List || [];
    const feedbackStats = response.data.FeedbackCount || null;

    console.log(`Retrieved ${feedbackItems.length} feedback items from TradeMe API`);

    // Save feedback statistics if available
    if (feedbackStats) {
      try {
        console.log('Saving feedback statistics...');

        // Find or create stats document
        let stats = await TradeMeFeedbackStats.findOne({ accountId: memberId });

        if (!stats) {
          stats = new TradeMeFeedbackStats({
            accountId: memberId
          });
        }

        // Update stats with values from API
        stats.totalCount = feedbackStats.TotalCount || 0;
        stats.totalPositive = feedbackStats.TotalPositive || 0;
        stats.uniquePositive = feedbackStats.UniquePositive || 0;
        stats.totalNeutral = feedbackStats.TotalNeutral || 0;
        stats.totalNegative = feedbackStats.TotalNegative || 0;
        stats.uniqueNegative = feedbackStats.UniqueNegative || 0;
        stats.nickname = feedbackStats.Nickname || '';
        stats.lastLoggedIn = parseTradeMeDate(feedbackStats.LastLoggedIn);
        stats.dateJoined = parseTradeMeDate(feedbackStats.DateJoined);
        stats.twoMonthListingCount = feedbackStats.TwoMonthListingCount || 0;
        stats.isAuthenticated = feedbackStats.IsAuthenticated || false;
        stats.lastUpdated = new Date();

        await stats.save();
        console.log('Feedback statistics saved successfully');
      } catch (error) {
        console.error('Error saving feedback statistics:', error);
      }
    }

    // Process all feedback items
    if (feedbackItems.length > 0) {
      console.log(`Processing ${feedbackItems.length} feedback items...`);

      for (const feedback of feedbackItems) {
        try {
          // Check if feedback already exists in our database
          let existingFeedback = await TradeMeFeedback.findOne({
            feedbackId: feedback.FeedbackId.toString()
          });

          // Determine if this is feedback we received as a seller or gave as a buyer
          const feedbackType = feedback.IsSeller ? 'Buyer' : 'Seller';
          // If we're the seller, then the feedback is from the buyer, otherwise it's from us
          const submittedBy = feedback.IsSeller ? feedback.FeedbackFrom?.Nickname || 'Unknown' : settings.username || 'Me';
          // If we submitted it, it's already read
          const isRead = !feedback.IsSeller;

          // Map TradeMe feedback type to our enum values
          let rating = 'Neutral';

          // According to TradeMe API documentation:
          // 0: Negative
          // 1: Positive
          // 2: Neutral
          // 3: TradeMe
          if (feedback.FeedbackType === 1 || feedback.FeedbackType === '1' || feedback.FeedbackType === 'Positive') {
            rating = 'Positive';
          } else if (feedback.FeedbackType === 2 || feedback.FeedbackType === '2' || feedback.FeedbackType === 'Neutral') {
            rating = 'Neutral';
          } else if (feedback.FeedbackType === 0 || feedback.FeedbackType === '0' || feedback.FeedbackType === 'Negative') {
            rating = 'Negative';
          }

          // Handle any other unexpected values
          if (!['Positive', 'Neutral', 'Negative'].includes(rating)) {
            console.warn(`Unexpected feedback type value: ${feedback.FeedbackType}, defaulting to Neutral`);
            rating = 'Neutral';
          }

          // Prepare feedback data object with all fields from API
          const feedbackData = {
            feedbackId: feedback.FeedbackId.toString(),
            listingId: feedback.ListingId?.toString() || '',
            listingTitle: feedback.Title || 'Unknown Listing',
            feedbackType: feedbackType,
            rating: rating,
            comment: feedback.Text || '',
            submittedBy: submittedBy,
            submittedDate: parseTradeMeDate(feedback.DateEntered),
            response: feedback.Response || '',
            responseDate: feedback.DateResponded ? parseTradeMeDate(feedback.DateResponded) : null,

            // Additional fields from API
            isSeller: feedback.IsSeller || false,
            buyNowPrice: feedback.BuyNowPrice || null,
            isAnOffer: feedback.IsAnOffer || false,
            maximumBidAmount: feedback.MaximumBidAmount || null,
            offerPrice: feedback.OfferPrice || null,
            hasReturnedFeedback: feedback.HasReturnedFeedback || false,
            isEdited: feedback.IsEdited || false,
            sellerId: feedback.SellerId?.toString() || '',
            purchaseId: feedback.PurchaseId?.toString() || '',

            // Feedback submitter details
            feedbackFrom: feedback.FeedbackFrom ? {
              memberId: feedback.FeedbackFrom.MemberId || 0,
              nickname: feedback.FeedbackFrom.Nickname || '',
              dateAddressVerified: feedback.FeedbackFrom.DateAddressVerified ? parseTradeMeDate(feedback.FeedbackFrom.DateAddressVerified) : null,
              dateJoined: feedback.FeedbackFrom.DateJoined ? parseTradeMeDate(feedback.FeedbackFrom.DateJoined) : null,
              uniqueNegative: feedback.FeedbackFrom.UniqueNegative || 0,
              uniquePositive: feedback.FeedbackFrom.UniquePositive || 0,
              feedbackCount: feedback.FeedbackFrom.FeedbackCount || 0,
              isAddressVerified: feedback.FeedbackFrom.IsAddressVerified || false,
              isDealer: feedback.FeedbackFrom.IsDealer || false,
              isAuthenticated: feedback.FeedbackFrom.IsAuthenticated || false
            } : null,

            // Application-specific fields
            isRead: isRead
          };

          if (existingFeedback) {
            // Update existing feedback with all fields
            Object.assign(existingFeedback, feedbackData);

            try {
              await existingFeedback.save();

              // Update statistics
              stats.itemsProcessed++;
              stats.updatedItems++;
            } catch (saveError) {
              console.error(`Error saving feedback ${feedback.FeedbackId}:`, saveError);
            }
          } else {
            // Create new feedback entry
            const newFeedback = new TradeMeFeedback(feedbackData);

            try {
              await newFeedback.save();

              // Update statistics
              stats.itemsProcessed++;
              stats.newItems++;
            } catch (saveError) {
              console.error(`Error saving new feedback ${feedback.FeedbackId}:`, saveError);
            }
          }
        } catch (error) {
          console.error(`Error processing feedback ${feedback.FeedbackId}:`, error);
        }
      }
    }

    // Fetch additional pages if needed
    if (maxPagesPerEndpoint > 1) {
      // Get total count and page size from the response
      const totalCount = response.data.TotalCount || 0;
      const pageSize = response.data.PageSize || feedbackItems.length;
      const totalPages = Math.ceil(totalCount / pageSize);

      console.log(`Total feedback items: ${totalCount}, Page size: ${pageSize}, Total pages: ${totalPages}`);

      // Update the syncLog if provided
      if (syncLog) {
        syncLog.totalItems = totalCount;
        syncLog.totalPages = totalPages;
        syncLog.currentPage = 1;
        syncLog.itemsProcessed = stats.itemsProcessed;
        await syncLog.save();
      }

      // Fetch additional pages
      const pagesToFetch = Math.min(totalPages - 1, maxPagesPerEndpoint - 1);

      if (pagesToFetch > 0) {
        console.log(`Fetching ${pagesToFetch} additional pages of feedback (up to ${Math.min(totalPages, maxPagesPerEndpoint)} total pages)...`);
        console.log(`This will allow retrieving up to ${Math.min(totalPages, maxPagesPerEndpoint) * pageSize} feedback items`);

        for (let page = 2; page <= pagesToFetch + 1; page++) {
          // Log progress every 10 pages
          if (page % 10 === 0) {
            console.log(`Progress: Fetching page ${page} of ${Math.min(totalPages, pagesToFetch + 1)} (${Math.round((page / Math.min(totalPages, pagesToFetch + 1)) * 100)}%)`);

            // Update the syncLog if provided
            if (syncLog) {
              syncLog.currentPage = page;
              syncLog.itemsProcessed = stats.itemsProcessed;
              await syncLog.save();
            }
          }
          try {
            const pageResponse = await makeAuthenticatedRequest(`/Member/${memberId}/Feedback/All.json`, page);
            const pageItems = pageResponse.data.List || [];

            console.log(`Retrieved ${pageItems.length} feedback items from page ${page}`);

            // Process items from this page
            for (const feedback of pageItems) {
              try {
                // Check if feedback already exists in our database
                let existingFeedback = await TradeMeFeedback.findOne({
                  feedbackId: feedback.FeedbackId.toString()
                });

                // Skip processing if we already have this feedback
                if (existingFeedback) {
                  continue;
                }

                // Determine if this is feedback we received as a seller or gave as a buyer
                const feedbackType = feedback.IsSeller ? 'Buyer' : 'Seller';
                // If we're the seller, then the feedback is from the buyer, otherwise it's from us
                const submittedBy = feedback.IsSeller ? feedback.FeedbackFrom?.Nickname || 'Unknown' : settings.username || 'Me';
                // If we submitted it, it's already read
                const isRead = !feedback.IsSeller;

                // Map TradeMe feedback type to our enum values
                let rating = 'Neutral';

                if (feedback.FeedbackType === 1 || feedback.FeedbackType === '1' || feedback.FeedbackType === 'Positive') {
                  rating = 'Positive';
                } else if (feedback.FeedbackType === 2 || feedback.FeedbackType === '2' || feedback.FeedbackType === 'Neutral') {
                  rating = 'Neutral';
                } else if (feedback.FeedbackType === 0 || feedback.FeedbackType === '0' || feedback.FeedbackType === 'Negative') {
                  rating = 'Negative';
                }

                // Create new feedback entry with all fields
                const newFeedback = new TradeMeFeedback({
                  feedbackId: feedback.FeedbackId.toString(),
                  listingId: feedback.ListingId?.toString() || '',
                  listingTitle: feedback.Title || 'Unknown Listing',
                  feedbackType: feedbackType,
                  rating: rating,
                  comment: feedback.Text || '',
                  submittedBy: submittedBy,
                  submittedDate: parseTradeMeDate(feedback.DateEntered),
                  response: feedback.Response || '',
                  responseDate: feedback.DateResponded ? parseTradeMeDate(feedback.DateResponded) : null,
                  isSeller: feedback.IsSeller || false,
                  buyNowPrice: feedback.BuyNowPrice || null,
                  isAnOffer: feedback.IsAnOffer || false,
                  maximumBidAmount: feedback.MaximumBidAmount || null,
                  offerPrice: feedback.OfferPrice || null,
                  hasReturnedFeedback: feedback.HasReturnedFeedback || false,
                  isEdited: feedback.IsEdited || false,
                  sellerId: feedback.SellerId?.toString() || '',
                  purchaseId: feedback.PurchaseId?.toString() || '',

                  // Feedback submitter details
                  feedbackFrom: feedback.FeedbackFrom ? {
                    memberId: feedback.FeedbackFrom.MemberId || 0,
                    nickname: feedback.FeedbackFrom.Nickname || '',
                    dateAddressVerified: feedback.FeedbackFrom.DateAddressVerified ? parseTradeMeDate(feedback.FeedbackFrom.DateAddressVerified) : null,
                    dateJoined: feedback.FeedbackFrom.DateJoined ? parseTradeMeDate(feedback.FeedbackFrom.DateJoined) : null,
                    uniqueNegative: feedback.FeedbackFrom.UniqueNegative || 0,
                    uniquePositive: feedback.FeedbackFrom.UniquePositive || 0,
                    feedbackCount: feedback.FeedbackFrom.FeedbackCount || 0,
                    isAddressVerified: feedback.FeedbackFrom.IsAddressVerified || false,
                    isDealer: feedback.FeedbackFrom.IsDealer || false,
                    isAuthenticated: feedback.FeedbackFrom.IsAuthenticated || false
                  } : null,

                  // Application-specific fields
                  isRead: isRead
                });

                await newFeedback.save();

                // Update statistics
                stats.itemsProcessed++;
                stats.newItems++;
              } catch (error) {
                console.error(`Error processing feedback ${feedback.FeedbackId} from page ${page}:`, error);
              }
            }

            // Add a small delay to avoid hitting rate limits
            // Use a shorter delay for feedback sync with high page counts to improve performance
            const delayMs = maxPagesPerEndpoint > 100 ? 100 : 200;
            await new Promise(resolve => setTimeout(resolve, delayMs));
          } catch (error) {
            console.error(`Error fetching page ${page} of feedback:`, error.message);
            break;
          }
        }
      }
    }

    // Log the statistics
    console.log('TradeMe feedback sync completed with the following statistics:');
    console.log(`- Items processed: ${stats.itemsProcessed}`);
    console.log(`- New items: ${stats.newItems}`);
    console.log(`- Updated items: ${stats.updatedItems}`);
    console.log(`- API calls made: ${stats.apiCallsMade}`);

    return {
      success: true,
      message: 'TradeMe feedback sync completed',
      stats: stats
    };
  } catch (error) {
    console.error('Error syncing TradeMe feedback:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Get feedback from the database
 * @param {Object} options - Query options
 * @param {string} options.type - Type of feedback ('received', 'given', 'all')
 * @param {string} options.rating - Filter by rating ('Positive', 'Neutral', 'Negative')
 * @param {number} options.page - Page number
 * @param {number} options.limit - Items per page
 * @returns {Promise<Object>} Feedback items and pagination info
 */
async function getFeedback(options = {}) {
  try {
    const { type = 'all', rating, page = 1, limit = 10 } = options;

    // Ensure limit is a number and has a reasonable value
    const parsedLimit = typeof limit === 'number' ? limit : parseInt(limit);
    const validLimit = isNaN(parsedLimit) ? 10 : Math.min(Math.max(parsedLimit, 1), 100);

    console.log(`TradeMe Feedback Service - Processing request with limit=${limit}, parsed=${parsedLimit}, valid=${validLimit}`);

    // Build query based on type and rating
    let query = {};

    // Filter by type
    if (type === 'received') {
      query.feedbackType = 'Seller';
    } else if (type === 'given') {
      query.feedbackType = 'Buyer';
    }

    // Filter by rating if provided
    if (rating && ['Positive', 'Neutral', 'Negative'].includes(rating)) {
      query.rating = rating;
    }

    // Calculate pagination
    const skip = (page - 1) * validLimit;

    // Get total count
    const total = await TradeMeFeedback.countDocuments(query);

    console.log(`TradeMe Feedback Service - Found ${total} total items, fetching page ${page} with limit ${validLimit}`);

    // Get feedback items
    const feedback = await TradeMeFeedback.find(query)
      .sort({ submittedDate: -1 })
      .skip(skip)
      .limit(validLimit);

    // Get counts by rating for the current filter
    const positiveCount = await TradeMeFeedback.countDocuments({ ...query, rating: 'Positive' });
    const neutralCount = await TradeMeFeedback.countDocuments({ ...query, rating: 'Neutral' });
    const negativeCount = await TradeMeFeedback.countDocuments({ ...query, rating: 'Negative' });

    return {
      success: true,
      feedback,
      pagination: {
        total,
        page: parseInt(page),
        limit: validLimit,
        pages: Math.ceil(total / validLimit)
      },
      counts: {
        positive: positiveCount,
        neutral: neutralCount,
        negative: negativeCount
      }
    };
  } catch (error) {
    console.error('Error getting TradeMe feedback:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Mark feedback as read
 * @param {string} feedbackId - ID of the feedback to mark as read
 * @returns {Promise<Object>} Result of the operation
 */
async function markFeedbackAsRead(feedbackId) {
  try {
    const feedback = await TradeMeFeedback.findOne({ feedbackId });

    if (!feedback) {
      return {
        success: false,
        error: 'Feedback not found'
      };
    }

    feedback.isRead = true;
    await feedback.save();

    return {
      success: true,
      message: 'Feedback marked as read'
    };
  } catch (error) {
    console.error('Error marking TradeMe feedback as read:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Get feedback statistics
 * @returns {Promise<Object>} Feedback statistics
 */
async function getFeedbackStats() {
  try {
    // Get settings to get the member ID
    const settings = await TradeMeSettings.findOne();

    if (!settings || !settings.connected) {
      return {
        success: false,
        error: 'No active TradeMe connection'
      };
    }

    const memberId = settings.memberId || '';

    // Get feedback statistics from database
    const stats = await TradeMeFeedbackStats.findOne({ accountId: memberId });

    if (!stats) {
      return {
        success: false,
        error: 'No feedback statistics found'
      };
    }

    // Calculate positive feedback percentage with 4 decimal places
    const totalFeedback = stats.uniquePositive + stats.uniqueNegative;
    const positiveFeedbackPercentage = totalFeedback > 0
      ? Number(((stats.uniquePositive / totalFeedback) * 100).toFixed(4))
      : 0;

    // Get counts by rating
    const positiveCount = await TradeMeFeedback.countDocuments({ rating: 'Positive' });
    const neutralCount = await TradeMeFeedback.countDocuments({ rating: 'Neutral' });
    const negativeCount = await TradeMeFeedback.countDocuments({ rating: 'Negative' });

    // Get counts by type
    const receivedCount = await TradeMeFeedback.countDocuments({ feedbackType: 'Seller' });
    const givenCount = await TradeMeFeedback.countDocuments({ feedbackType: 'Buyer' });

    return {
      success: true,
      stats: {
        ...stats.toObject(),
        positiveFeedbackPercentage,
        counts: {
          byRating: {
            positive: positiveCount,
            neutral: neutralCount,
            negative: negativeCount
          },
          byType: {
            received: receivedCount,
            given: givenCount
          }
        }
      }
    };
  } catch (error) {
    console.error('Error getting TradeMe feedback statistics:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

module.exports = {
  syncFeedbackFromTradeMe,
  getFeedback,
  markFeedbackAsRead,
  getFeedbackStats
};
