import React, { createContext, useContext, useState, useEffect } from 'react';
import { ColorTheme, getStoredTheme, saveThemeToStorage, getNextTheme } from '@/utils/colorThemes';

interface ColorThemeContextType {
  currentTheme: ColorTheme;
  changeTheme: (themeId: string) => void;
  cycleToNextTheme: () => void;
}

const ColorThemeContext = createContext<ColorThemeContextType | undefined>(undefined);

export const ColorThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentTheme, setCurrentTheme] = useState<ColorTheme>(getStoredTheme());

  // Initialize theme from local storage on mount
  useEffect(() => {
    setCurrentTheme(getStoredTheme());
  }, []);

  const changeTheme = (themeId: string) => {
    import('@/utils/colorThemes').then(({ getThemeById }) => {
      const theme = getThemeById(themeId);
      setCurrentTheme(theme);
      saveThemeToStorage(theme.id);
    });
  };

  const cycleToNextTheme = () => {
    const nextTheme = getNextTheme(currentTheme.id);
    setCurrentTheme(nextTheme);
    saveThemeToStorage(nextTheme.id);
  };

  return (
    <ColorThemeContext.Provider value={{ currentTheme, changeTheme, cycleToNextTheme }}>
      {children}
    </ColorThemeContext.Provider>
  );
};

export const useColorTheme = (): ColorThemeContextType => {
  const context = useContext(ColorThemeContext);
  if (context === undefined) {
    throw new Error('useColorTheme must be used within a ColorThemeProvider');
  }
  return context;
};
