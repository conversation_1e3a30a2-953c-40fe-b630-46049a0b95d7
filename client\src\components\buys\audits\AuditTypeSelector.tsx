import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { FormControl, FormField, FormItem, FormLabel } from '@/components/ui/form';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { ShoppingCart, DollarSign, BarChart3 } from 'lucide-react';

interface AuditTypeSelectorProps {
  control: any;
  disabled?: boolean;
}

/**
 * Component for selecting the type of audit to perform
 * Displays options horizontally with icons
 */
export function AuditTypeSelector({ control, disabled = false }: AuditTypeSelectorProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Select Audit Type</CardTitle>
        <CardDescription>Choose the type of audit you want to create</CardDescription>
      </CardHeader>
      <CardContent>
        <FormField
          control={control}
          name="auditType"
          render={({ field }) => (
            <FormItem className="space-y-4">
              <FormControl>
                <RadioGroup
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  className="flex flex-col md:flex-row gap-4"
                  disabled={disabled}
                >
                  <div className="flex-1">
                    <FormItem className="flex flex-col items-center space-y-3 h-full">
                      <FormLabel
                        htmlFor="auditTypeBuy"
                        className="w-full border rounded-md p-4 cursor-pointer hover:bg-accent transition-colors relative flex flex-col items-center space-y-2 h-full hover:border-primary"
                      >
                        <FormControl>
                          <RadioGroupItem value="buy" id="auditTypeBuy" className="absolute top-4 left-4" />
                        </FormControl>
                        <ShoppingCart className="h-10 w-10 mb-2 text-primary" />
                        <div className="text-center">
                          <span className="font-medium text-lg">
                            Buy Audit
                          </span>
                          <p className="text-sm text-muted-foreground mt-1">
                            Audit a buy transaction to assess compliance and quality
                          </p>
                        </div>
                      </FormLabel>
                    </FormItem>
                  </div>

                  <div className="flex-1">
                    <FormItem className="flex flex-col items-center space-y-3 h-full">
                      <FormLabel
                        htmlFor="auditTypePawn"
                        className="w-full border rounded-md p-4 cursor-pointer hover:bg-accent transition-colors relative flex flex-col items-center space-y-2 h-full hover:border-primary"
                      >
                        <FormControl>
                          <RadioGroupItem value="pawn" id="auditTypePawn" className="absolute top-4 left-4" />
                        </FormControl>
                        <DollarSign className="h-10 w-10 mb-2 text-primary" />
                        <div className="text-center">
                          <span className="font-medium text-lg">
                            Pawn Loan Audit
                          </span>
                          <p className="text-sm text-muted-foreground mt-1">
                            Audit a pawn loan transaction to assess compliance with lending standards
                          </p>
                        </div>
                      </FormLabel>
                    </FormItem>
                  </div>

                  <div className="flex-1">
                    <FormItem className="flex flex-col items-center space-y-3 h-full">
                      <FormLabel
                        htmlFor="auditTypePrice"
                        className="w-full border rounded-md p-4 cursor-pointer hover:bg-accent transition-colors relative flex flex-col items-center space-y-2 h-full hover:border-primary"
                      >
                        <FormControl>
                          <RadioGroupItem value="price" id="auditTypePrice" className="absolute top-4 left-4" />
                        </FormControl>
                        <BarChart3 className="h-10 w-10 mb-2 text-primary" />
                        <div className="text-center">
                          <span className="font-medium text-lg">
                            Price Audit
                          </span>
                          <p className="text-sm text-muted-foreground mt-1">
                            Track overpayment incidents and document reasons for follow-up conversations
                          </p>
                        </div>
                      </FormLabel>
                    </FormItem>
                  </div>
                </RadioGroup>
              </FormControl>
            </FormItem>
          )}
        />
      </CardContent>
    </Card>
  );
}
