/**
 * TradeMe Sync Orchestrator
 *
 * Handles scheduling and coordination of TradeMe synchronization tasks
 * including listings, questions, feedback, and categories.
 */

const tradeMeItemsSyncService = require('./tradeMeItemsSyncService');
const tradeMeService = require('./tradeMeService');
const tradeMeCategoryService = require('./tradeMeCategoryService');
const TradeMeSettings = require('../models/TradeMeSettings');
const SyncLog = require('../models/SyncLog');
const mongoose = require('mongoose');

// Track the sync state
let isSyncing = false;
let lastSyncTime = null;
let syncIntervals = [];

/**
 * Initialize the sync orchestrator
 * @param {Object} options - Configuration options
 * @param {number} options.listingsIntervalMinutes - How often to sync listings (in minutes)
 * @param {number} options.questionsIntervalMinutes - How often to sync questions (in minutes)
 * @param {number} options.feedbackIntervalMinutes - How often to sync feedback (in minutes)
 * @param {number} options.categoriesIntervalMinutes - How often to sync categories (in minutes)
 */
async function initialize(options = {}) {
  const {
    listingsIntervalMinutes = 30,
    questionsIntervalMinutes = 15,
    feedbackIntervalMinutes = 60,
    categoriesIntervalMinutes = 1440 // Once a day by default
  } = options;

  // Clear any existing intervals
  if (global.tradeMeSyncIntervals) {
    global.tradeMeSyncIntervals.forEach(interval => clearInterval(interval));
  }

  // Initialize the intervals array
  global.tradeMeSyncIntervals = [];

  console.log('TradeMe sync orchestrator initialized with the following schedule:');
  console.log(`- Listings: every ${listingsIntervalMinutes} minutes`);
  console.log(`- Questions: every ${questionsIntervalMinutes} minutes`);
  console.log(`- Feedback: every ${feedbackIntervalMinutes} minutes`);
  console.log(`- Categories: every ${categoriesIntervalMinutes} minutes`);

  // Run initial syncs
  await performSync({ syncType: 'listings', maxPagesPerEndpoint: 5 });
  await performSync({ syncType: 'questions', maxPagesPerEndpoint: 3 });
  await performSync({ syncType: 'feedback', maxPagesPerEndpoint: 2 }); // Only grab pages 1-2 for automatic sync
  await performOptimizedCategorySync();

  // Set up intervals for each sync type

  // Listings sync interval
  const listingsIntervalMs = listingsIntervalMinutes * 60 * 1000;
  const listingsInterval = setInterval(async () => {
    await performSync({ syncType: 'listings', maxPagesPerEndpoint: 5 });
  }, listingsIntervalMs);
  global.tradeMeSyncIntervals.push(listingsInterval);

  // Questions sync interval
  const questionsIntervalMs = questionsIntervalMinutes * 60 * 1000;
  const questionsInterval = setInterval(async () => {
    await performSync({ syncType: 'questions', maxPagesPerEndpoint: 3 });
  }, questionsIntervalMs);
  global.tradeMeSyncIntervals.push(questionsInterval);

  // Feedback sync interval
  const feedbackIntervalMs = feedbackIntervalMinutes * 60 * 1000;
  const feedbackInterval = setInterval(async () => {
    await performSync({ syncType: 'feedback', maxPagesPerEndpoint: 2 }); // Only grab pages 1-2 for automatic sync
  }, feedbackIntervalMs);
  global.tradeMeSyncIntervals.push(feedbackInterval);

  // Categories sync interval
  const categoriesIntervalMs = categoriesIntervalMinutes * 60 * 1000;
  const categoriesInterval = setInterval(async () => {
    await performOptimizedCategorySync();
  }, categoriesIntervalMs);
  global.tradeMeSyncIntervals.push(categoriesInterval);
}

/**
 * Perform an optimized category sync
 * @returns {Promise<Object>} Result of the sync operation
 */
async function performOptimizedCategorySync() {
  // Check if we're already syncing
  if (isSyncing) {
    console.log('Sync already in progress, skipping optimized category sync');
    return {
      success: false,
      error: 'Sync already in progress'
    };
  }

  // Check if we have a TradeMe connection
  const settings = await TradeMeSettings.findOne();
  if (!settings || !settings.connected) {
    console.log('No active TradeMe connection, skipping optimized category sync');
    return {
      success: false,
      error: 'No active TradeMe connection'
    };
  }

  // Set syncing flag
  isSyncing = true;
  lastSyncTime = new Date();

  // Create a sync log entry
  const syncLog = new SyncLog({
    syncType: 'categories',
    startTime: new Date(),
    status: 'in_progress'
  });
  await syncLog.save();

  try {
    console.log('Starting optimized category sync...');

    // Perform the optimized category sync
    const result = await tradeMeCategoryService.optimizedSync();

    // Update the sync log
    syncLog.endTime = new Date();
    syncLog.status = result.success ? 'completed' : 'failed';
    syncLog.itemsProcessed = result.stats?.categoriesChecked || 0;
    syncLog.itemsAdded = result.stats?.categoriesAdded || 0;
    syncLog.itemsUpdated = result.stats?.categoriesUpdated || 0;
    syncLog.apiCallsMade = result.stats?.apiCallsMade || 0;
    syncLog.error = result.error || null;
    await syncLog.save();

    // Reset syncing flag
    isSyncing = false;

    console.log('Optimized category sync completed:', result.success ? 'success' : 'failed');
    return result;
  } catch (error) {
    console.error('Error in optimized category sync:', error);

    // Update the sync log
    syncLog.endTime = new Date();
    syncLog.status = 'failed';
    syncLog.error = error.message;
    await syncLog.save();

    // Reset syncing flag
    isSyncing = false;

    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Perform a sync operation
 * @param {Object} options - Sync options
 * @param {string} options.syncType - Type of sync to perform (listings, questions, feedback, categories)
 * @param {number} options.maxPagesPerEndpoint - Maximum number of pages to fetch per endpoint
 * @returns {Promise<Object>} Result of the sync operation
 */
async function performSync(options = {}) {
  const { syncType, maxPagesPerEndpoint = 5 } = options;

  // Check if we're already syncing
  if (isSyncing) {
    console.log(`Sync already in progress, skipping ${syncType} sync`);
    return {
      success: false,
      error: 'Sync already in progress'
    };
  }

  // Check if we have a valid sync type
  if (!syncType || !['listings', 'questions', 'feedback', 'categories'].includes(syncType)) {
    console.error(`Invalid sync type: ${syncType}`);
    return {
      success: false,
      error: 'Invalid sync type'
    };
  }

  // Check if we have a TradeMe connection
  const settings = await TradeMeSettings.findOne();
  if (!settings || !settings.connected) {
    console.log('No active TradeMe connection, skipping sync');
    return {
      success: false,
      error: 'No active TradeMe connection'
    };
  }

  // Set syncing flag
  isSyncing = true;
  lastSyncTime = new Date();

  // Create a sync log entry
  const syncLog = new SyncLog({
    syncType,
    startTime: new Date(),
    status: 'in_progress'
  });
  await syncLog.save();

  try {
    console.log(`Starting ${syncType} sync...`);
    let result;

    // Perform the appropriate sync based on type
    switch (syncType) {
      case 'listings':
        result = await tradeMeItemsSyncService.syncListingsFromTradeMe({
          maxPagesPerEndpoint,
          syncLog
        });
        break;
      case 'questions':
        result = await tradeMeItemsSyncService.syncQuestionsFromTradeMe({
          maxPagesPerEndpoint,
          syncLog
        });
        break;
      case 'feedback':
        result = await tradeMeService.syncFeedbackFromTradeMe({
          maxPagesPerEndpoint,
          syncLog
        });
        break;
      case 'categories':
        // Use the optimized category sync instead of the regular one
        isSyncing = false; // Reset the flag so we can call performOptimizedCategorySync
        result = await performOptimizedCategorySync();
        break;
    }

    // Update the sync log
    syncLog.endTime = new Date();
    syncLog.success = result.success;
    syncLog.itemsProcessed = result.stats?.itemsProcessed || 0;
    syncLog.newItems = result.stats?.newItems || 0;
    syncLog.updatedItems = result.stats?.updatedItems || 0;
    syncLog.apiCallsMade = result.stats?.apiCallsMade || 0;
    syncLog.error = result.error;
    syncLog.status = result.success ? 'completed' : 'failed';
    await syncLog.save();

    console.log(`${syncType} sync completed with ${result.success ? 'success' : 'errors'}`);

    return result;
  } catch (error) {
    console.error(`Error during ${syncType} sync:`, error);

    // Update the sync log
    syncLog.endTime = new Date();
    syncLog.success = false;
    syncLog.error = error.message;
    syncLog.status = 'failed';
    await syncLog.save();

    return {
      success: false,
      error: error.message
    };
  } finally {
    // Reset syncing flag
    isSyncing = false;
  }
}

/**
 * Get the current sync status
 * @returns {Object} Current sync status
 */
function getSyncStatus() {
  return {
    isSyncing,
    lastSyncTime
  };
}

/**
 * Get recent sync logs
 * @param {Object} options - Options for fetching logs
 * @param {number} options.limit - Maximum number of logs to return
 * @param {string} options.syncType - Type of sync to filter by
 * @returns {Promise<Array>} Array of sync logs
 */
async function getSyncLogs(options = {}) {
  const { limit = 20, syncType } = options;

  // Build query
  const query = {};
  if (syncType) {
    query.syncType = syncType;
  }

  // Fetch logs
  const logs = await SyncLog.find(query)
    .sort({ startTime: -1 })
    .limit(limit);

  return logs;
}

/**
 * Stop all sync intervals
 */
function stopAllSyncs() {
  if (global.tradeMeSyncIntervals) {
    global.tradeMeSyncIntervals.forEach(interval => clearInterval(interval));
    global.tradeMeSyncIntervals = [];
  }
  console.log('All TradeMe sync intervals stopped');
}

module.exports = {
  initialize,
  performSync,
  performOptimizedCategorySync,
  getSyncStatus,
  getSyncLogs,
  stopAllSyncs
};
