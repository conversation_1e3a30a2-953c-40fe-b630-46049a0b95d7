import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  BarChart3,
  AlertTriangle,
  CheckCircle,
  Clock,
  Flag,
  TrendingUp,
  TrendingDown
} from 'lucide-react';

interface AuditStats {
  totalAudits: number;
  compliantCount: number;
  nonCompliantCount: number;
  flaggedCount: number;
  pendingFollowupCount: number;
  complianceRate: number;
  complianceChange: number;
  recentAudits: number;
}

interface AuditStatsCardsProps {
  stats: AuditStats;
}

/**
 * Component to display key audit statistics in card format
 */
export function AuditStatsCards({ stats }: AuditStatsCardsProps) {
  // Ensure stats has default values to prevent errors
  const safeStats = {
    totalAudits: stats?.totalAudits || 0,
    compliantCount: stats?.compliantCount || 0,
    nonCompliantCount: stats?.nonCompliantCount || 0,
    flaggedCount: stats?.flaggedCount || 0,
    pendingFollowupCount: stats?.pendingFollowupCount || 0,
    complianceRate: stats?.complianceRate || 0,
    complianceChange: stats?.complianceChange || 0,
    recentAudits: stats?.recentAudits || 0
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Audits</CardTitle>
          <BarChart3 className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{safeStats.totalAudits}</div>
          <p className="text-xs text-muted-foreground">
            {safeStats.recentAudits} in the last 30 days
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Compliance Rate</CardTitle>
          {safeStats.complianceChange >= 0 ? (
            <TrendingUp className="h-4 w-4 text-green-500" />
          ) : (
            <TrendingDown className="h-4 w-4 text-red-500" />
          )}
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{safeStats.complianceRate}%</div>
          <p className="text-xs text-muted-foreground flex items-center">
            {safeStats.complianceChange >= 0 ? (
              <>
                <span className="text-green-500 mr-1">+{safeStats.complianceChange}%</span>
                from previous period
              </>
            ) : (
              <>
                <span className="text-red-500 mr-1">{safeStats.complianceChange}%</span>
                from previous period
              </>
            )}
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Compliance Status</CardTitle>
          <div className="flex space-x-1">
            <CheckCircle className="h-4 w-4 text-green-500" />
            <AlertTriangle className="h-4 w-4 text-red-500" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between items-center">
            <div>
              <div className="text-xl font-bold">{safeStats.compliantCount}</div>
              <p className="text-xs text-muted-foreground">Compliant</p>
            </div>
            <div>
              <div className="text-xl font-bold">{safeStats.nonCompliantCount}</div>
              <p className="text-xs text-muted-foreground">Non-Compliant</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Follow-ups</CardTitle>
          <div className="flex space-x-1">
            <Flag className="h-4 w-4 text-yellow-500" />
            <Clock className="h-4 w-4 text-blue-500" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between items-center">
            <div>
              <div className="text-xl font-bold">{safeStats.flaggedCount}</div>
              <p className="text-xs text-muted-foreground">Flagged</p>
            </div>
            <div>
              <div className="text-xl font-bold">{safeStats.pendingFollowupCount}</div>
              <p className="text-xs text-muted-foreground">Pending</p>
            </div>
          </div>
          {safeStats.pendingFollowupCount > 0 && (
            <Badge variant="outline" className="mt-2 bg-yellow-500/10 text-yellow-500">
              <Flag className="mr-1 h-3 w-3" /> Requires Attention
            </Badge>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
