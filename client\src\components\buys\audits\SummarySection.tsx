import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Input } from '@/components/ui/input';

interface SummarySectionProps {
  control: any;
  disabled?: boolean;
  auditType: 'buy' | 'pawn' | 'price';
}

/**
 * Component for the Summary section of the audit form
 * This section is shown for all audit types
 */
export function SummarySection({ control, disabled = false, auditType }: SummarySectionProps) {
  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle>Audit Summary</CardTitle>
        <CardDescription>
          Provide an overall assessment and summary of the audit
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Overall Assessment */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Overall Assessment</h3>
          
          <FormField
            control={control}
            name="summary.overallAssessment"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>Assessment</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    value={field.value}
                    className="flex flex-col space-y-1"
                    disabled={disabled}
                  >
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="pass" id="overallPass" />
                      </FormControl>
                      <FormLabel className="font-normal" htmlFor="overallPass">
                        Pass
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="fail" id="overallFail" />
                      </FormControl>
                      <FormLabel className="font-normal" htmlFor="overallFail">
                        Fail
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="partial" id="overallPartial" />
                      </FormControl>
                      <FormLabel className="font-normal" htmlFor="overallPartial">
                        Partial Pass
                      </FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={control}
            name="summary.score"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Score</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    min="0"
                    max="100"
                    placeholder="Enter score (0-100)"
                    {...field}
                    disabled={disabled}
                  />
                </FormControl>
                <FormDescription>
                  Enter a score from 0 to 100 based on the audit results
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        {/* Strengths */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Strengths</h3>
          
          <FormField
            control={control}
            name="summary.strengths"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Strengths</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Enter the strengths identified during the audit"
                    className="min-h-[100px]"
                    {...field}
                    disabled={disabled}
                  />
                </FormControl>
                <FormDescription>
                  Highlight what was done well in this transaction
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        {/* Areas for Improvement */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Areas for Improvement</h3>
          
          <FormField
            control={control}
            name="summary.areasForImprovement"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Areas for Improvement</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Enter the areas for improvement identified during the audit"
                    className="min-h-[100px]"
                    {...field}
                    disabled={disabled}
                  />
                </FormControl>
                <FormDescription>
                  Identify what could have been done better in this transaction
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        {/* Recommendations */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Recommendations</h3>
          
          <FormField
            control={control}
            name="summary.recommendations"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Recommendations</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Enter recommendations for improvement"
                    className="min-h-[100px]"
                    {...field}
                    disabled={disabled}
                  />
                </FormControl>
                <FormDescription>
                  Provide specific recommendations for improvement
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        {/* Additional Notes */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Additional Notes</h3>
          
          <FormField
            control={control}
            name="summary.additionalNotes"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Additional Notes</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Enter any additional notes about the audit"
                    className="min-h-[100px]"
                    {...field}
                    disabled={disabled}
                  />
                </FormControl>
                <FormDescription>
                  Include any other relevant information about the audit
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        {/* Follow-up Required */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Follow-up</h3>
          
          <FormField
            control={control}
            name="summary.followUpRequired"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>Follow-up Required</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    value={field.value}
                    className="flex flex-col space-y-1"
                    disabled={disabled}
                  >
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="yes" id="followUpYes" />
                      </FormControl>
                      <FormLabel className="font-normal" htmlFor="followUpYes">
                        Yes
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="no" id="followUpNo" />
                      </FormControl>
                      <FormLabel className="font-normal" htmlFor="followUpNo">
                        No
                      </FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={control}
            name="summary.followUpNotes"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Follow-up Notes</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Enter details about required follow-up actions"
                    className="min-h-[100px]"
                    {...field}
                    disabled={disabled}
                  />
                </FormControl>
                <FormDescription>
                  If follow-up is required, provide details about what needs to be done
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </CardContent>
    </Card>
  );
}
