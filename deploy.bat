@echo off
echo GH0ST Application Deployment Script
echo ================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo This script requires administrator privileges.
    echo Please run as administrator.
    pause
    exit /b 1
)

echo Step 1: Installing dependencies...
cd client
call npm install --production
cd ..\server
call npm install --production
call npm install node-windows --save

echo.
echo Step 2: Building client application...
cd ..
call build.bat

echo.
echo Step 3: Setting up production environment...
copy /Y server\.env.production server\.env

echo.
echo Step 4: Creating required directories...
if not exist "server\uploads" mkdir "server\uploads"
if not exist "server\logs" mkdir "server\logs"

echo.
echo Step 5: Installing as Windows service...
cd server
node install-service.js

echo.
echo Deployment complete!
echo The application should now be running as a Windows service.
echo.
echo Would you like to set up the domain and SSL now? (Y/N)
set /p SETUP_DOMAIN=

if /i "%SETUP_DOMAIN%"=="Y" (
    echo.
    echo Setting up domain and SSL...
    call install-caddy-dns.bat
    echo.
    echo Domain setup complete!
    echo Please follow the instructions in DOMAIN-SETUP.md to complete the configuration.
    echo.
    echo You can access your application at:
    echo - Locally: http://localhost:3000
    echo - With domain: https://rotorua.ghst.nz (after DNS and port forwarding setup)
) else (
    echo.
    echo You can access your application at http://localhost:3000
    echo.
    echo To set up your domain later, run: install-caddy.bat
    echo And follow the instructions in DOMAIN-SETUP.md
)

echo.
echo To uninstall the service, run: node server\uninstall-service.js
echo.
pause
