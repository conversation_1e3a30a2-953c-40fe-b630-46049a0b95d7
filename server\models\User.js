const mongoose = require('mongoose');

const { validatePassword, isPasswordHash } = require('../utils/password.js');
const {randomUUID} = require("crypto");

const schema = new mongoose.Schema({
  email: {
    type: String,
    required: true,
    index: true,
    unique: true,
    lowercase: true,
  },
  username: {
    type: String,
    index: true,
    unique: true,
    sparse: true, // Allow null/undefined values
  },
  fullName: {
    type: String,
    required: true,
    sparse: true, // Allow null/undefined values
  },
  password: {
    type: String,
    required: true,
    validate: { validator: isPasswordHash, message: 'Invalid password hash' },
  },
  // PIN login fields
  pin: {
    type: String,
    default: null,
    validate: {
      validator: function(v) {
        return v === null || isPasswordHash(v);
      },
      message: 'Invalid PIN hash'
    },
  },
  pinCreatedAt: {
    type: Date,
    default: null,
  },
  previousPins: {
    type: [String],
    default: [],
  },
  pinFailedAttempts: {
    type: Number,
    default: 0,
  },
  pinLockedUntil: {
    type: Date,
    default: null,
  },
  role: {
    type: String,
    enum: ['admin', 'manager', 'employee'],
    default: 'employee',
  },
  location: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Location',
    default: null,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  createdAt: {
    type: Date,
    default: Date.now,
    immutable: true,
  },
  lastLoginAt: {
    type: Date,
    default: Date.now,
  },
  deactivatedAt: {
    type: Date,
    default: null,
  },
  refreshToken: {
    type: String,
    unique: true,
    index: true,
    default: () => randomUUID(),
  },
}, {
  versionKey: false,
});

schema.set('toJSON', {
  /* eslint-disable */
  transform: (doc, ret, options) => {
    delete ret.password;
    return ret;
  },
  /* eslint-enable */
});

const User = mongoose.model('User', schema);

module.exports = User;
