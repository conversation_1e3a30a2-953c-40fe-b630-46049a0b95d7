const express = require('express');
const router = express.Router();
const { requireUser } = require('./middleware/auth');
const tradeMeTemplatesService = require('../services/tradeMeTemplatesService');

/**
 * @route GET /api/trademe/templates
 * @description Get all templates with optional filtering
 * @access Private
 */
router.get('/', requireUser, async (req, res) => {
  try {
    const { type, category, search } = req.query;

    const result = await tradeMeTemplatesService.getTemplates({
      type,
      category,
      search
    });

    if (result.success) {
      return res.status(200).json(result);
    } else {
      return res.status(400).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    console.error('Error getting TradeMe templates:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/trademe/templates/:id
 * @description Get a template by ID
 * @access Private
 */
router.get('/:id', requireUser, async (req, res) => {
  try {
    const { id } = req.params;

    const result = await tradeMeTemplatesService.getTemplateById(id);

    if (result.success) {
      return res.status(200).json(result);
    } else {
      return res.status(404).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    console.error('Error getting TradeMe template:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route POST /api/trademe/templates
 * @description Create a new template
 * @access Private
 */
router.post('/', requireUser, async (req, res) => {
  try {
    const result = await tradeMeTemplatesService.createTemplate(req.body, req.user);

    if (result.success) {
      return res.status(201).json(result);
    } else {
      return res.status(400).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    console.error('Error creating TradeMe template:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route PUT /api/trademe/templates/:id
 * @description Update a template
 * @access Private
 */
router.put('/:id', requireUser, async (req, res) => {
  try {
    const { id } = req.params;

    const result = await tradeMeTemplatesService.updateTemplate(id, req.body, req.user);

    if (result.success) {
      return res.status(200).json(result);
    } else {
      return res.status(400).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    console.error('Error updating TradeMe template:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route DELETE /api/trademe/templates/:id
 * @description Delete a template
 * @access Private
 */
router.delete('/:id', requireUser, async (req, res) => {
  try {
    const { id } = req.params;

    const result = await tradeMeTemplatesService.deleteTemplate(id, req.user);

    if (result.success) {
      return res.status(200).json(result);
    } else {
      return res.status(400).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    console.error('Error deleting TradeMe template:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route PUT /api/trademe/templates/:id/default
 * @description Set a template as default for its type
 * @access Private
 */
router.put('/:id/default', requireUser, async (req, res) => {
  try {
    const { id } = req.params;

    const result = await tradeMeTemplatesService.setDefaultTemplate(id, req.user);

    if (result.success) {
      return res.status(200).json(result);
    } else {
      return res.status(400).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    console.error('Error setting default TradeMe template:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = router;
