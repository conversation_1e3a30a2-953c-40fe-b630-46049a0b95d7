import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  ClipboardCheck,
  Package,
  DollarSign,
  Shield,
  Users,
  Heart,
  AlertTriangle
} from 'lucide-react';

interface AuditAssessmentsViewProps {
  audit: any;
}

export function AuditAssessmentsView({ audit }: AuditAssessmentsViewProps) {
  // Helper function to render status badge
  const renderStatusBadge = (status: string) => {
    switch (status) {
      case 'pass':
        return <Badge variant="success" className="flex items-center gap-1"><CheckCircle className="h-3 w-3" />Pass</Badge>;
      case 'fail':
        return <Badge variant="destructive" className="flex items-center gap-1"><XCircle className="h-3 w-3" />Fail</Badge>;
      default:
        return <Badge variant="secondary" className="flex items-center gap-1"><AlertCircle className="h-3 w-3" />Not Assessed</Badge>;
    }
  };

  // Helper function to render fail reasons
  const renderFailReasons = (failReasons: string[]) => {
    if (!failReasons || failReasons.length === 0) return null;
    
    return (
      <div className="mt-3">
        <p className="text-sm text-muted-foreground mb-2">Fail Reasons:</p>
        <div className="flex flex-wrap gap-1">
          {failReasons.map((reason, index) => (
            <Badge key={index} variant="destructive" className="text-xs">
              {reason.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
            </Badge>
          ))}
        </div>
      </div>
    );
  };

  // Helper function to render assessment section
  const renderAssessmentSection = (
    title: string, 
    icon: React.ReactNode, 
    assessment: any, 
    description?: string
  ) => {
    if (!assessment) return null;

    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {icon}
            {title}
          </CardTitle>
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Assessment Result</span>
            {renderStatusBadge(assessment.status)}
          </div>
          
          {assessment.failReasons && renderFailReasons(assessment.failReasons)}
          
          {assessment.notes && (
            <div>
              <p className="text-sm text-muted-foreground">Notes:</p>
              <p className="text-sm mt-1">{assessment.notes}</p>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      {/* Data Entry Quality Assessment */}
      {audit.auditType !== 'price' && renderAssessmentSection(
        'Data Entry Quality',
        <ClipboardCheck className="h-5 w-5" />,
        audit.dataEntryQuality,
        'Assessment of data entry accuracy and completeness'
      )}

      {/* Item Condition Check */}
      {audit.auditType !== 'price' && renderAssessmentSection(
        'Item Condition Check',
        <Package className="h-5 w-5" />,
        audit.itemConditionCheck,
        'Assessment of item condition documentation and accuracy'
      )}

      {/* Pricing Assessment */}
      {audit.pricing && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Pricing Assessment
            </CardTitle>
            <CardDescription>
              {audit.auditType === 'price' 
                ? 'Overpayment incident analysis and pricing review'
                : 'Assessment of pricing accuracy and compliance'
              }
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Assessment Result</span>
              {renderStatusBadge(audit.pricing.status)}
            </div>

            {/* Price Audit Specific Fields */}
            {audit.auditType === 'price' && (
              <>
                {audit.pricing.transactionType && (
                  <div>
                    <p className="text-sm text-muted-foreground">Transaction Type</p>
                    <Badge variant="outline" className="capitalize">
                      {audit.pricing.transactionType}
                    </Badge>
                  </div>
                )}
                
                {audit.pricing.overpaymentReason && (
                  <div>
                    <p className="text-sm text-muted-foreground">Overpayment Reason</p>
                    <Badge variant="destructive">
                      {audit.pricing.overpaymentReason.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </Badge>
                  </div>
                )}
                
                {audit.pricing.customOverpaymentReason && (
                  <div>
                    <p className="text-sm text-muted-foreground">Custom Reason</p>
                    <p className="text-sm">{audit.pricing.customOverpaymentReason}</p>
                  </div>
                )}
              </>
            )}

            {/* Pricing Details */}
            {(audit.pricing.suggestedPrice || audit.pricing.costPrice || audit.pricing.ticketPrice) && (
              <>
                <Separator />
                <div>
                  <h4 className="font-medium mb-3">Pricing Details</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {audit.pricing.suggestedPrice && (
                      <div>
                        <p className="text-sm text-muted-foreground">Suggested Price</p>
                        <p className="font-medium">${audit.pricing.suggestedPrice}</p>
                      </div>
                    )}
                    {audit.pricing.costPrice && (
                      <div>
                        <p className="text-sm text-muted-foreground">Cost Price</p>
                        <p className="font-medium">${audit.pricing.costPrice}</p>
                      </div>
                    )}
                    {audit.pricing.ticketPrice && (
                      <div>
                        <p className="text-sm text-muted-foreground">Ticket Price</p>
                        <p className="font-medium">${audit.pricing.ticketPrice}</p>
                      </div>
                    )}
                  </div>
                </div>
              </>
            )}

            {audit.pricing.failReasons && renderFailReasons(audit.pricing.failReasons)}
            
            {audit.pricing.notes && (
              <div>
                <p className="text-sm text-muted-foreground">Notes:</p>
                <p className="text-sm mt-1">{audit.pricing.notes}</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Authorized Limit Check */}
      {audit.auditType !== 'price' && renderAssessmentSection(
        'Authorized Limit Check',
        <Shield className="h-5 w-5" />,
        audit.authorizedLimitCheck,
        'Assessment of transaction authorization and limits compliance'
      )}

      {/* Pawn-Specific Assessments */}
      {audit.auditType === 'pawn' && (
        <>
          {/* POL Suitability */}
          {audit.polSuitability && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  POL Suitability Assessment
                </CardTitle>
                <CardDescription>
                  Assessment of Point of Loan suitability and responsible lending practices
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Assessment Result</span>
                  {renderStatusBadge(audit.polSuitability.status)}
                </div>
                
                {audit.polSuitability.polText && (
                  <div>
                    <p className="text-sm text-muted-foreground">POL Text:</p>
                    <p className="text-sm mt-1">{audit.polSuitability.polText}</p>
                  </div>
                )}
                
                {audit.polSuitability.failReasons && renderFailReasons(audit.polSuitability.failReasons)}
                
                {audit.polSuitability.notes && (
                  <div>
                    <p className="text-sm text-muted-foreground">Notes:</p>
                    <p className="text-sm mt-1">{audit.polSuitability.notes}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Customer Understanding */}
          {renderAssessmentSection(
            'Customer Understanding',
            <Users className="h-5 w-5" />,
            audit.customerUnderstanding,
            'Assessment of customer understanding of loan terms and conditions'
          )}

          {/* Vulnerable Customer */}
          {renderAssessmentSection(
            'Vulnerable Customer Assessment',
            <Heart className="h-5 w-5" />,
            audit.vulnerableCustomer,
            'Assessment of vulnerable customer identification and protection measures'
          )}

          {/* Essential Item Check */}
          {audit.essentialItemCheck && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5" />
                  Essential Item Check
                </CardTitle>
                <CardDescription>
                  Assessment of essential item identification and handling procedures
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Assessment Result</span>
                  {renderStatusBadge(audit.essentialItemCheck.status)}
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Is Essential Item</span>
                  <Badge variant={audit.essentialItemCheck.isEssential ? 'destructive' : 'success'}>
                    {audit.essentialItemCheck.isEssential ? 'Yes' : 'No'}
                  </Badge>
                </div>
                
                {audit.essentialItemCheck.compliance && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Compliance</span>
                    {renderStatusBadge(audit.essentialItemCheck.compliance)}
                  </div>
                )}
                
                {audit.essentialItemCheck.failReasons && renderFailReasons(audit.essentialItemCheck.failReasons)}
                
                {audit.essentialItemCheck.notes && (
                  <div>
                    <p className="text-sm text-muted-foreground">Notes:</p>
                    <p className="text-sm mt-1">{audit.essentialItemCheck.notes}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </>
      )}

      {/* Overall Assessment Summary */}
      <Card className="border-2 border-primary/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5" />
            Overall Assessment Summary
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Overall Compliance</span>
              {renderStatusBadge(audit.overallCompliance)}
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Overall Score</span>
              <Badge variant="outline" className="text-lg font-bold">
                {audit.overallScore || 0}%
              </Badge>
            </div>
          </div>
          
          {audit.auditNotes && (
            <div>
              <p className="text-sm text-muted-foreground">Overall Notes:</p>
              <p className="text-sm mt-1">{audit.auditNotes}</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
