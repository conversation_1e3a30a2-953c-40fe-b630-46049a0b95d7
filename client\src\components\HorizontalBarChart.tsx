import React from 'react';
import {
  <PERSON>sponsive<PERSON>ontainer,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  Cell,
  LabelList
} from 'recharts';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';

interface DataItem {
  [key: string]: any;
}

interface HorizontalBarChartProps {
  data: DataItem[];
  title: string;
  description?: string;
  nameKey: string;
  dataKeys: {
    key: string;
    name: string;
    color: string;
  }[];
  height?: number;
  className?: string;
  valueFormatter?: (value: number) => string;
  dataTransformer?: (data: DataItem[]) => DataItem[];
}

export function HorizontalBarChart({
  data,
  title,
  description,
  nameKey,
  dataKeys,
  height = 300,
  className = '',
  valueFormatter = (value: number) => value.toString(),
  dataTransformer,
}: HorizontalBarChartProps) {
  // Transform data if a transformer is provided
  const chartData = dataTransformer ? dataTransformer(data) : data;
  // Custom tooltip formatter
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border rounded-md shadow-md p-2 text-sm">
          <p className="font-medium">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }}>
              {entry.name}: {valueFormatter(entry.value)}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  // Truncate long names
  const truncateName = (name: string, maxLength = 15) => {
    if (name.length <= maxLength) return name;
    return `${name.substring(0, maxLength)}...`;
  };

  return (
    <Card className={className}>
      <CardHeader className="pb-2">
        <CardTitle className="text-base font-medium">{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        <div style={{ width: '100%', height }}>
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={chartData}
              layout="vertical"
              margin={{ top: 5, right: 30, left: 80, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" horizontal={true} vertical={false} />
              <XAxis
                type="number"
                tick={{ fontSize: 12 }}
                tickLine={false}
                axisLine={{ stroke: '#e5e7eb' }}
                tickFormatter={valueFormatter}
              />
              <YAxis
                type="category"
                dataKey={nameKey}
                tick={{ fontSize: 12 }}
                tickLine={false}
                axisLine={{ stroke: '#e5e7eb' }}
                width={80}
                tickFormatter={truncateName}
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              {dataKeys.map((dataKey, index) => (
                <Bar
                  key={index}
                  dataKey={dataKey.key}
                  name={dataKey.name}
                  fill={dataKey.color}
                  radius={[0, 4, 4, 0]}
                  barSize={20}
                />
              ))}
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}
