export class IdleTimer {
  timeout: number;
  onTimeout: () => void;
  onWarning?: () => void;
  eventHandler: () => void;
  timeoutTracker: NodeJS.Timeout | null;
  warningTracker: NodeJS.Timeout | null;
  lastActivity: number;
  warningTime: number;

  constructor(timeout: number, onTimeout: () => void, onWarning?: () => void, warningTime: number = 300000) { // 5 minutes warning by default
    this.timeout = timeout;
    this.onTimeout = onTimeout;
    this.onWarning = onWarning;
    this.warningTime = warningTime;
    this.eventHandler = this.resetTimer.bind(this);
    this.timeoutTracker = null;
    this.warningTracker = null;
    this.lastActivity = Date.now();

    // Events that reset the timer - expanded list for better detection
    const events = [
      'mousedown',
      'mousemove',
      'keypress',
      'keydown',
      'keyup',
      'scroll',
      'touchstart',
      'touchmove',
      'touchend',
      'click',
      'focus',
      'blur',
      'visibilitychange'
    ];

    events.forEach(event => {
      document.addEventListener(event, this.eventHandler, { passive: true });
    });

    // Track API activity by monitoring fetch/XMLHttpRequest
    this.setupApiActivityTracking();

    this.resetTimer();
  }

  resetTimer() {
    this.lastActivity = Date.now();

    // Clear existing timers
    if (this.timeoutTracker) clearTimeout(this.timeoutTracker);
    if (this.warningTracker) clearTimeout(this.warningTracker);

    // Set warning timer if warning callback is provided
    if (this.onWarning && this.warningTime < this.timeout) {
      this.warningTracker = setTimeout(this.onWarning, this.timeout - this.warningTime);
    }

    // Set timeout timer
    this.timeoutTracker = setTimeout(this.onTimeout, this.timeout);
  }

  setupApiActivityTracking() {
    // Track fetch API calls
    const originalFetch = window.fetch;
    window.fetch = (...args) => {
      this.resetTimer();
      return originalFetch.apply(window, args);
    };

    // Track XMLHttpRequest
    const originalXHROpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function(...args) {
      this.addEventListener('loadstart', () => {
        // Reset timer when API call starts
        if (this.resetTimer) this.resetTimer();
      });
      return originalXHROpen.apply(this, args);
    };
  }

  cleanup() {
    const events = [
      'mousedown',
      'mousemove',
      'keypress',
      'keydown',
      'keyup',
      'scroll',
      'touchstart',
      'touchmove',
      'touchend',
      'click',
      'focus',
      'blur',
      'visibilitychange'
    ];
    events.forEach(event => {
      document.removeEventListener(event, this.eventHandler);
    });
    if (this.timeoutTracker) clearTimeout(this.timeoutTracker);
    if (this.warningTracker) clearTimeout(this.warningTracker);
  }

  getTimeSinceLastActivity(): number {
    return Date.now() - this.lastActivity;
  }
}