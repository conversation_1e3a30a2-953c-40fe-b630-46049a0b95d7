import { useState, useRef, useEffect } from "react"
import { useForm } from "react-hook-form"
import { useNavigate, useLocation } from "react-router-dom"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { LogIn, Ghost, AlertCircle } from "lucide-react"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { useAuth } from "@/contexts/AuthContext"
import { useColorTheme } from "@/contexts/ColorThemeContext"
import { AnimatedGhosts } from "@/components/AnimatedGhosts"
import { cn } from "@/lib/utils"
import "@/styles/login-animations.css"

type LoginFormData = {
  emailOrUsername: string
  password: string
  pin: string
}

export function Login() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [fieldErrors, setFieldErrors] = useState<{emailOrUsername?: string; password?: string; pin?: string}>({});
  const [loginMethod, setLoginMethod] = useState<"password" | "pin">("pin"); // Track which login method is being used
  const pinInputRef = useRef<HTMLInputElement>(null);
  const { login } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const { register, handleSubmit, setValue, watch } = useForm<LoginFormData>();

  const pinValue = watch("pin", "");

  // Auto-focus PIN input when component mounts
  useEffect(() => {
    if (pinInputRef.current) {
      setTimeout(() => {
        pinInputRef.current?.focus();
      }, 300);
    }
  }, []);

  // Define handlePinLogin function
  const handlePinLogin = async (data: LoginFormData) => {
    try {
      setLoginMethod("pin");
      setError(null);
      setFieldErrors({});
      setLoading(true);

      // PIN login - no username/email required
      await login("", "", data.pin);

      // Navigate after successful login
      const from = location.state?.from?.pathname || "/";
      navigate(from, { replace: true });
    } catch (error) {
      // Reset PIN field on error - ensure it's completely cleared
      setValue("pin", "", { shouldValidate: true });
      
      // Clear the actual DOM input element value as well
      if (pinInputRef.current) {
        pinInputRef.current.value = "";
      }
      
      // Focus the PIN input field again
      setTimeout(() => {
        pinInputRef.current?.focus();
      }, 100);
      
      const errorMessage = error instanceof Error
        ? error.message
        : "An unexpected error occurred";

      setError(errorMessage);

      // Set field-specific errors for PIN
      const newFieldErrors: {pin?: string} = {};

      if (errorMessage.toLowerCase().includes("pin")) {
        newFieldErrors.pin = errorMessage;
      } else if (errorMessage.toLowerCase().includes("locked")) {
        newFieldErrors.pin = errorMessage;
      } else {
        newFieldErrors.pin = "Invalid PIN";
      }

      setFieldErrors(newFieldErrors);
      setLoading(false);
    }
  };

  // Auto-submit when PIN is 6 digits
  useEffect(() => {
    // Only run this effect if pinValue changes and is exactly 6 digits
    if (pinValue && pinValue.length === 6) {
      console.log("PIN has 6 digits, attempting login...");
      
      // Reset any previous errors before attempting login
      setError(null);
      setFieldErrors({});
      
      // Immediate login attempt
      handlePinLogin({ emailOrUsername: "", password: "", pin: pinValue });
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pinValue]);



  const handlePasswordLogin = async (data: LoginFormData) => {
    try {
      setLoginMethod("password");
      setError(null);
      setFieldErrors({});
      setLoading(true);

      // Regular password login
      await login(data.emailOrUsername, data.password);

      // Navigate after successful login
      const from = location.state?.from?.pathname || "/";
      navigate(from, { replace: true });
    } catch (error) {
      // Reset password field on error
      setValue("password", "");

      const errorMessage = error instanceof Error
        ? error.message
        : "An unexpected error occurred";

      setError(errorMessage);

      // Set field-specific errors based on error message content
      const newFieldErrors: {emailOrUsername?: string; password?: string} = {};

      if (errorMessage.toLowerCase().includes("username") || errorMessage.toLowerCase().includes("email")) {
        newFieldErrors.emailOrUsername = "Invalid username or email";
      }

      if (errorMessage.toLowerCase().includes("password")) {
        newFieldErrors.password = "Invalid password";
      }

      // Default to generic error if no specific errors identified
      if (Object.keys(newFieldErrors).length === 0) {
        newFieldErrors.emailOrUsername = "Invalid credentials";
        newFieldErrors.password = "Invalid credentials";
      }

      setFieldErrors(newFieldErrors);
    } finally {
      setLoading(false);
    }
  };



  const onSubmit = async (data: LoginFormData) => {
    // Determine which login method to use based on which fields are filled
    if (data.pin && data.pin.trim() !== "") {
      await handlePinLogin(data);
    } else if (data.emailOrUsername && data.password) {
      await handlePasswordLogin(data);
    } else {
      // If neither method has complete data, show appropriate errors
      const newFieldErrors: {emailOrUsername?: string; password?: string; pin?: string} = {};

      if (!data.pin || data.pin.trim() === "") {
        if (!data.emailOrUsername) {
          newFieldErrors.emailOrUsername = "Email or username is required";
        }
        if (!data.password) {
          newFieldErrors.password = "Password is required";
        }
      }

      setFieldErrors(newFieldErrors);
    }
  };


  const { currentTheme, cycleToNextTheme } = useColorTheme();

  return (
    <div className="min-h-screen h-screen flex items-center justify-center overflow-hidden relative">
      {/* Animated background with gradient */}
      <div
        className="absolute inset-0 bg-gradient-to-br z-0"
        style={{
          backgroundImage: `linear-gradient(to bottom right, ${currentTheme.gradientFrom}, ${currentTheme.gradientVia}, ${currentTheme.gradientTo})`
        }}
      ></div>

      {/* Subtle animated particles/stars effect - positioned above the gradient */}
      <div className="absolute inset-0 overflow-hidden z-[1]">
        <div className="stars-container"></div>
      </div>

      {/* Dark overlay for more subtle appearance - positioned above stars */}
      <div
        className="absolute inset-0 bg-gradient-to-b from-black/30 to-black/60 z-[2]"
        style={{ opacity: currentTheme.overlayOpacity || 0.7 }}
      ></div>

      {/* Animated ghost background - positioned above the dark overlay */}
      <div className="z-[3] relative">
        <AnimatedGhosts />
      </div>

      {/* Login form */}
      <div className={cn("flex flex-col gap-6 relative z-10")}>
        <Card className="shadow-xl backdrop-blur-sm bg-black/70 border border-gray-800 floating-card" style={{ width: "400px" }}>
          <CardHeader className="text-center space-y-1">
            <div className="flex items-center justify-center mb-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button
                      onClick={(e) => {
                        e.preventDefault();
                        cycleToNextTheme();
                      }}
                      className="bg-transparent border-0 cursor-pointer p-1 rounded-full hover:bg-black/20 transition-colors theme-switcher"
                      aria-label={`Current theme: ${currentTheme.name}. Click to change background color.`}
                    >
                      <Ghost className="h-10 w-10 text-white" />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent className="bg-black/90 border-gray-700 text-white">
                    <div className="flex flex-col">
                      <span className="font-medium">Current theme: {currentTheme.name}</span>
                      <span className="text-xs text-gray-400">Click to change background color</span>
                    </div>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            <CardTitle className="text-xl">Welcome Back</CardTitle>
            <CardDescription>
              Sign in to continue
            </CardDescription>
          </CardHeader>
          <CardContent className="w-full">
            <form onSubmit={handleSubmit(onSubmit)}>
              <div className="grid gap-6 max-w-full">
                {/* Error alert */}
                {error && (
                  <div className="bg-red-900/30 border border-red-800 rounded-md p-3 flex items-start">
                    <AlertCircle className="h-5 w-5 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
                    <span className="text-red-200 text-sm">{error}</span>
                  </div>
                )}

                {/* PIN Login Section - First */}
                <div className="grid gap-4">
                  <div>
                    <div className="grid gap-2">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="pin">PIN</Label>
                        {fieldErrors.pin && <span className="text-red-400 text-xs">{fieldErrors.pin}</span>}
                      </div>
                      <div className="relative">
                        <Input
                          id="pin"
                          type="password"
                          inputMode="numeric"
                          maxLength={6}
                          pattern="[0-9]*"
                          placeholder="Enter your 6-digit PIN"
                          className={fieldErrors.pin ? 'border-red-500' : ''}
                          {...register("pin", {
                            onChange: (e) => {
                              // Only allow numeric input
                              const value = e.target.value;
                              if (value && !/^\d*$/.test(value)) {
                                e.target.value = value.replace(/[^\d]/g, '');
                              }
                            }
                          })}
                          ref={pinInputRef}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              e.preventDefault();
                              const pinValue = (e.target as HTMLInputElement).value;
                              handlePinLogin({ emailOrUsername: "", password: "", pin: pinValue });
                            }
                          }}
                        />
                        {loading && loginMethod === "pin" && (
                          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                            <svg className="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Divider */}
                <div className="relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t after:border-border">
                  <span className="relative z-10 px-2 text-muted-foreground" style={{ background: "none" }}>
                    Or continue with
                  </span>
                </div>

                {/* Password Login Section */}
                <div className="grid gap-4">
                  <div>
                    <div className="grid gap-2">
                      <Label htmlFor="emailOrUsername">Email or Username</Label>
                      <Input
                        id="emailOrUsername"
                        type="text"
                        placeholder="Enter your email or username"
                        className={fieldErrors.emailOrUsername ? 'border-red-500' : ''}
                        {...register("emailOrUsername")}
                      />
                      {fieldErrors.emailOrUsername && (
                        <p className="text-xs text-red-500">{fieldErrors.emailOrUsername}</p>
                      )}
                    </div>
                    <div className="grid gap-2 mt-4">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="password">Password</Label>
                        {fieldErrors.password && <span className="text-red-400 text-xs">{fieldErrors.password}</span>}
                      </div>
                      <Input
                        id="password"
                        type="password"
                        placeholder="Enter your password"
                        className={fieldErrors.password ? 'border-red-500' : ''}
                        {...register("password")}
                      />
                    </div>
                    <Button
                      type="button"
                      onClick={() => handlePasswordLogin({
                        emailOrUsername: document.getElementById('emailOrUsername') ? (document.getElementById('emailOrUsername') as HTMLInputElement).value : '',
                        password: document.getElementById('password') ? (document.getElementById('password') as HTMLInputElement).value : '',
                        pin: ''
                      })}
                      disabled={loading && loginMethod === "password"}
                      className="w-full mt-4"
                    >
                      {loading && loginMethod === "password" ? (
                        <>
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Signing in...
                        </>
                      ) : (
                        <>
                          <LogIn className="mr-2 h-4 w-4" />
                          Login
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
