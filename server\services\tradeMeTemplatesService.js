/**
 * TradeMe Templates Service
 *
 * Handles operations related to TradeMe templates, including:
 * - Question templates
 * - Footer templates
 * - Shipping templates
 * - Withdraw templates
 */

const TradeMeTemplates = require('../models/TradeMeTemplates');

/**
 * Get templates with optional filtering
 * @param {Object} options - Filter options
 * @param {string} options.type - Template type (question, footer, shipping, withdraw)
 * @param {string} options.category - Template category
 * @param {string} options.search - Search term
 * @returns {Promise<Object>} Result with templates
 */
async function getTemplates(options = {}) {
  try {
    const { type, category, search } = options;

    // Build query
    const query = {};

    // Filter by type if provided
    if (type) {
      query.type = type;
    }

    // Filter by category if provided
    if (category && category !== 'all') {
      query.category = category;
    }

    // Add search functionality if provided
    if (search) {
      query.$text = { $search: search };
    }

    // Get templates
    const templates = await TradeMeTemplates.find(query)
      .sort({ category: 1, title: 1 })
      .populate('createdBy', 'username email fullName')
      .populate('updatedBy', 'username email fullName');

    return {
      success: true,
      templates
    };
  } catch (error) {
    console.error('Error getting TradeMe templates:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Get a template by ID
 * @param {string} id - Template ID
 * @returns {Promise<Object>} Result with template
 */
async function getTemplateById(id) {
  try {
    const template = await TradeMeTemplates.findById(id)
      .populate('createdBy', 'username email fullName')
      .populate('updatedBy', 'username email fullName');

    if (!template) {
      return {
        success: false,
        error: 'Template not found'
      };
    }

    return {
      success: true,
      template
    };
  } catch (error) {
    console.error('Error getting TradeMe template:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Create a new template
 * @param {Object} templateData - Template data
 * @param {Object} user - User creating the template
 * @returns {Promise<Object>} Result with created template
 */
async function createTemplate(templateData, user) {
  try {
    const { type, title, content, category, price, method } = templateData;

    // Validate required fields
    if (!type || !title || !content) {
      return {
        success: false,
        error: 'Type, title, and content are required'
      };
    }

    // Create template
    const template = new TradeMeTemplates({
      type,
      title,
      content,
      category: category || 'General',
      createdBy: user._id,
      updatedBy: user._id
    });

    // Add shipping-specific fields if applicable
    if (type === 'shipping') {
      template.price = price || 0;
      template.method = method || '';
    }

    await template.save();

    return {
      success: true,
      template,
      message: 'Template created successfully'
    };
  } catch (error) {
    console.error('Error creating TradeMe template:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Update a template
 * @param {string} id - Template ID
 * @param {Object} templateData - Updated template data
 * @param {Object} user - User updating the template
 * @returns {Promise<Object>} Result with updated template
 */
async function updateTemplate(id, templateData, user) {
  try {
    const { title, content, category, price, method } = templateData;

    // Validate required fields
    if (!title || !content) {
      return {
        success: false,
        error: 'Title and content are required'
      };
    }

    // Find template
    const template = await TradeMeTemplates.findById(id);

    if (!template) {
      return {
        success: false,
        error: 'Template not found'
      };
    }

    // Check permissions
    if (user.role !== 'admin' && user.role !== 'manager' &&
        template.createdBy.toString() !== user._id.toString()) {
      return {
        success: false,
        error: 'You do not have permission to update this template'
      };
    }

    // Update fields
    template.title = title;
    template.content = content;
    template.category = category || template.category;
    template.updatedBy = user._id;

    // Update shipping-specific fields if applicable
    if (template.type === 'shipping') {
      template.price = price !== undefined ? price : template.price;
      template.method = method !== undefined ? method : template.method;
    }

    await template.save();

    return {
      success: true,
      template,
      message: 'Template updated successfully'
    };
  } catch (error) {
    console.error('Error updating TradeMe template:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Delete a template
 * @param {string} id - Template ID
 * @param {Object} user - User deleting the template
 * @returns {Promise<Object>} Result of the deletion
 */
async function deleteTemplate(id, user) {
  try {
    // Find template
    const template = await TradeMeTemplates.findById(id);

    if (!template) {
      return {
        success: false,
        error: 'Template not found'
      };
    }

    // Check permissions
    if (user.role !== 'admin' && user.role !== 'manager' &&
        template.createdBy.toString() !== user._id.toString()) {
      return {
        success: false,
        error: 'You do not have permission to delete this template'
      };
    }

    // Delete template
    await TradeMeTemplates.deleteOne({ _id: id });

    return {
      success: true,
      message: 'Template deleted successfully'
    };
  } catch (error) {
    console.error('Error deleting TradeMe template:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Set a template as default for its type
 * @param {string} id - Template ID
 * @param {Object} user - User setting the default template
 * @returns {Promise<Object>} Result of the operation
 */
async function setDefaultTemplate(id, user) {
  try {
    // Find template
    const template = await TradeMeTemplates.findById(id);

    if (!template) {
      return {
        success: false,
        error: 'Template not found'
      };
    }

    // Check permissions
    if (user.role !== 'admin' && user.role !== 'manager' &&
        template.createdBy.toString() !== user._id.toString()) {
      return {
        success: false,
        error: 'You do not have permission to set this template as default'
      };
    }

    // Reset all templates of the same type
    await TradeMeTemplates.updateMany(
      { type: template.type },
      { isDefault: false }
    );

    // Set this template as default
    template.isDefault = true;
    template.updatedBy = user._id;
    await template.save();

    return {
      success: true,
      template,
      message: 'Template set as default successfully'
    };
  } catch (error) {
    console.error('Error setting default TradeMe template:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

module.exports = {
  getTemplates,
  getTemplateById,
  createTemplate,
  updateTemplate,
  deleteTemplate,
  setDefaultTemplate
};
