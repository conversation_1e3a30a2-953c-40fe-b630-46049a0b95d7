import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Loader2 } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { LoanApplicationFormData } from '@/api/loanApplications';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { InfoIcon } from 'lucide-react';

interface LoanStatusFormProps {
  onSubmit: (data: LoanApplicationFormData) => void;
  initialData: LoanApplicationFormData;
}

export function LoanStatusForm({ onSubmit, initialData }: LoanStatusFormProps) {
  const { user } = useAuth();
  const isAdminOrManager = user?.role === 'admin' || user?.role === 'manager';

  const [formData, setFormData] = useState<LoanApplicationFormData>({
    ...initialData
  });

  const [submitting, setSubmitting] = useState(false);

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle notes change
  const handleNotesChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setFormData(prev => ({ ...prev, notes: e.target.value }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);

    try {
      // Only send status and notes fields for status-only updates
      const statusUpdateData = {
        status: formData.status,
        notes: formData.notes
      };
      await onSubmit(statusUpdateData);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <Alert className="bg-amber-500/10 border-amber-500/50 text-amber-500">
        <InfoIcon className="h-4 w-4" />
        <AlertDescription>
          As you didn't submit this application, you can only update the loan status. Only managers or the employee who submitted this loan can edit other details.
        </AlertDescription>
      </Alert>

      <div className="grid grid-cols-1 gap-4">
        {/* Status */}
        <div className="space-y-2">
          <Label htmlFor="status">Status</Label>
          <Select
            value={formData.status}
            onValueChange={(value) => handleSelectChange('status', value)}
            disabled={!isAdminOrManager && formData.status === 'approvedPaid'}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="awaitingDecision">Awaiting Decision</SelectItem>
              <SelectItem value="approved">Approved</SelectItem>
              <SelectItem value="declined">Declined</SelectItem>
              <SelectItem value="cancelled">Cancelled</SelectItem>
              {isAdminOrManager && (
                <SelectItem value="approvedPaid">Approved & Paid</SelectItem>
              )}
            </SelectContent>
          </Select>
        </div>

        {/* Notes */}
        <div className="space-y-2">
          <Label htmlFor="notes">Notes</Label>
          <Textarea
            id="notes"
            name="notes"
            value={formData.notes}
            onChange={handleNotesChange}
            rows={3}
            placeholder="Add any additional notes about this status change"
          />
        </div>
      </div>

      {/* Submit Button */}
      <div className="flex justify-end">
        <Button type="submit" disabled={submitting}>
          {submitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          Update Status
        </Button>
      </div>
    </form>
  );
}
