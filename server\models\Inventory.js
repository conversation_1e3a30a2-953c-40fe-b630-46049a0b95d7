const mongoose = require('mongoose');

const schema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    index: true,
  },
  category: {
    type: String,
    required: true,
    index: true,
  },
  brand: {
    type: String,
    default: '',
  },
  model: {
    type: String,
    default: '',
  },
  modelNumber: {
    type: String,
    default: '',
  },
  releaseYear: {
    type: Number,
    default: null,
  },
  lastRRP: {
    type: Number,
    default: 0,
  },
  currentPrice: {
    type: Number,
    required: true,
  },
  location: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Location',
    required: false, // Changed to false to make location optional
  },
  status: {
    type: String,
    enum: ['in_stock', 'listed', 'sold'],
    default: 'in_stock',
  },
  lastUpdated: {
    type: Date,
    default: Date.now,
  },
  createdAt: {
    type: Date,
    default: Date.now,
    immutable: true,
  },
}, {
  versionKey: false,
});

// Create indexes for common queries
schema.index({ status: 1 });
schema.index({ category: 1, brand: 1 });

const Inventory = mongoose.model('Inventory', schema);

module.exports = Inventory;