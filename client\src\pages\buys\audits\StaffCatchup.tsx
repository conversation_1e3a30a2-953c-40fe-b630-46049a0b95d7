import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useToast } from '@/hooks/useToast';
import { useAuth } from '@/contexts/AuthContext';
import { getAuditById, BuyPawnAudit } from '@/api/buyPawnAudits';
import { getCatchupsByAuditId, createCatchup, updateCatchup, BuyPawnCatchup } from '@/api/buyPawnCatchups';
import { getUsers } from '@/api/users';

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { But<PERSON> } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Loader2, Calendar, User, CheckCircle, AlertTriangle } from 'lucide-react';
import { format } from 'date-fns';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

// Define the form schema
const catchupFormSchema = z.object({
  employee: z.string().min(1, 'Employee is required'),
  findingsDiscussed: z.string().min(1, 'Findings discussed is required'),
  commentsBefore: z.string().optional(),
  discussionPoints: z.string().min(1, 'Discussion points are required'),
  employeeResponse: z.string().optional(),
  agreedActions: z.string().optional(),
  commentsAfter: z.string().optional(),
});

type CatchupFormValues = z.infer<typeof catchupFormSchema>;

interface User {
  _id: string;
  username?: string;
  fullName: string;
  isActive: boolean;
}

export function StaffCatchup() {
  const { id } = useParams<{ id: string }>();
  const { toast } = useToast();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [audit, setAudit] = useState<BuyPawnAudit | null>(null);
  const [catchups, setCatchups] = useState<BuyPawnCatchup[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [activeTab, setActiveTab] = useState('new');
  const [selectedCatchup, setSelectedCatchup] = useState<BuyPawnCatchup | null>(null);
  const [readOnly, setReadOnly] = useState(false);

  // Initialize form
  const form = useForm<CatchupFormValues>({
    resolver: zodResolver(catchupFormSchema),
    defaultValues: {
      employee: '',
      findingsDiscussed: '',
      commentsBefore: '',
      discussionPoints: '',
      employeeResponse: '',
      agreedActions: '',
      commentsAfter: '',
    },
  });

  // Load audit and catchup data
  useEffect(() => {
    const loadData = async () => {
      if (!id) return;

      setIsLoading(true);
      try {
        // Load audit data
        const auditResult = await getAuditById(id);

        if (auditResult.success) {
          setAudit(auditResult.data);

          // Load catchup notes
          const catchupResult = await getCatchupsByAuditId(id);

          if (catchupResult.success) {
            setCatchups(catchupResult.data);
          }

          try {
            // Load users for employee selection
            const usersResult = await getUsers();
            setUsers(usersResult.data);
          } catch (error) {
            console.error('Error loading users:', error);
          }

          // Check user permissions
          if (user?.role === 'employee') {
            setReadOnly(true);
          }

          // If there's a deal employee, pre-select them
          if (auditResult.data.dealId && typeof auditResult.data.dealId !== 'string') {
            const dealEmployee = auditResult.data.dealId.employee;
            if (dealEmployee && typeof dealEmployee !== 'string') {
              form.setValue('employee', dealEmployee._id);
            }
          }
        } else {
          toast({
            title: 'Error',
            description: auditResult.error || 'Failed to load audit.',
            variant: 'destructive',
          });
          navigate('/buys/audits');
        }
      } catch (error: any) {
        toast({
          title: 'Error',
          description: error.message || 'An unexpected error occurred.',
          variant: 'destructive',
        });
        navigate('/buys/audits');
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [id, navigate, toast, user?.role]);

  // Handle form submission
  const onSubmit = async (values: CatchupFormValues) => {
    if (!id || !audit) return;

    setIsSaving(true);
    try {
      // Create new catchup note
      const result = await createCatchup({
        auditId: id,
        ...values,
      });

      if (result.success) {
        toast({
          title: 'Catchup Note Created',
          description: 'The staff catchup note has been created successfully.',
        });

        // Reset form
        form.reset({
          employee: values.employee, // Keep the same employee selected
          findingsDiscussed: '',
          commentsBefore: '',
          discussionPoints: '',
          employeeResponse: '',
          agreedActions: '',
          commentsAfter: '',
        });

        // Refresh catchup notes
        const catchupResult = await getCatchupsByAuditId(id);

        if (catchupResult.success) {
          setCatchups(catchupResult.data);
        }

        // Switch to history tab
        setActiveTab('history');
      } else {
        toast({
          title: 'Error',
          description: result.error || 'Failed to create catchup note.',
          variant: 'destructive',
        });
      }
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'An unexpected error occurred.',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Handle finalize catchup
  const handleFinalizeCatchup = async (catchupId: string) => {
    setIsSaving(true);
    try {
      const result = await updateCatchup(catchupId, {
        status: 'finalized',
      });

      if (result.success) {
        toast({
          title: 'Catchup Note Finalized',
          description: 'The staff catchup note has been finalized successfully.',
        });

        // Refresh catchup notes
        const catchupResult = await getCatchupsByAuditId(id!);

        if (catchupResult.success) {
          setCatchups(catchupResult.data);
        }
      } else {
        toast({
          title: 'Error',
          description: result.error || 'Failed to finalize catchup note.',
          variant: 'destructive',
        });
      }
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'An unexpected error occurred.',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'MMM d, yyyy');
  };

  // View catchup details
  const viewCatchupDetails = (catchup: BuyPawnCatchup) => {
    setSelectedCatchup(catchup);
    setActiveTab('view');
  };

  // Render loading state
  if (isLoading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex justify-center py-12">
          <Loader2 className="h-12 w-12 animate-spin text-primary" />
        </div>
      </div>
    );
  }

  // Render 404 if audit not found
  if (!audit) {
    return (
      <div className="container mx-auto py-6">
        <Card>
          <CardHeader>
            <CardTitle>Audit Not Found</CardTitle>
            <CardDescription>The requested audit could not be found.</CardDescription>
          </CardHeader>
          <CardFooter>
            <Button onClick={() => navigate('/buys/audits')}>Back to Audits</Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <div>
          <h1 className="text-3xl font-bold">Staff Catchup Notes</h1>
          <p className="text-muted-foreground">
            Transaction ID: {audit.transactionId}
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => navigate(`/buys/audits/${id}`)}>
            Back to Audit
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Staff Catchup Notes</CardTitle>
          <CardDescription>
            Document discussions with staff about this audited transaction
          </CardDescription>
          <Tabs defaultValue="new" className="mt-4" onValueChange={setActiveTab} value={activeTab}>
            <TabsList className="grid grid-cols-2">
              <TabsTrigger value="new" disabled={readOnly}>New Catchup</TabsTrigger>
              <TabsTrigger value="history">History</TabsTrigger>
            </TabsList>
          </Tabs>
        </CardHeader>
        <CardContent>
          {/* New Catchup Tab */}
          <TabsContent value="new" className="space-y-6">
            {readOnly ? (
              <div className="text-center py-8 text-muted-foreground">
                You do not have permission to create new catchup notes.
              </div>
            ) : (
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <FormField
                    control={form.control}
                    name="employee"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Employee</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          disabled={readOnly}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select employee" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {users.map((user) => (
                              <SelectItem key={user._id} value={user._id}>
                                {user.fullName} {!user.isActive && "(Inactive)"}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Select the employee involved in this transaction
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="findingsDiscussed"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Findings Discussed</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter the audit findings that were discussed"
                            className="min-h-[100px]"
                            {...field}
                            disabled={readOnly}
                          />
                        </FormControl>
                        <FormDescription>
                          Summarize the key audit findings that were discussed
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="commentsBefore"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Comments Before</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter any comments before the discussion"
                            {...field}
                            disabled={readOnly}
                          />
                        </FormControl>
                        <FormDescription>
                          Optional notes before the discussion
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="discussionPoints"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Discussion Points</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter the key points discussed during the meeting"
                            className="min-h-[100px]"
                            {...field}
                            disabled={readOnly}
                          />
                        </FormControl>
                        <FormDescription>
                          Document the main points covered during the discussion
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="employeeResponse"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Employee Response</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter the employee's response to the findings"
                            {...field}
                            disabled={readOnly}
                          />
                        </FormControl>
                        <FormDescription>
                          Document how the employee responded to the discussion
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="agreedActions"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Agreed Actions</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter any actions agreed upon during the discussion"
                            {...field}
                            disabled={readOnly}
                          />
                        </FormControl>
                        <FormDescription>
                          Document any specific actions that were agreed to be taken
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="commentsAfter"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Comments After</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter any comments after the discussion"
                            {...field}
                            disabled={readOnly}
                          />
                        </FormControl>
                        <FormDescription>
                          Optional notes after the discussion
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="flex justify-end space-x-2">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => navigate(`/buys/audits/${id}`)}
                    >
                      Cancel
                    </Button>
                    <Button type="submit" disabled={isSaving}>
                      {isSaving ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        'Save Catchup Note'
                      )}
                    </Button>
                  </div>
                </form>
              </Form>
            )}
          </TabsContent>

          {/* History Tab */}
          <TabsContent value="history" className="space-y-6">
            {catchups.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No catchup notes found for this audit.
              </div>
            ) : (
              <div className="space-y-4">
                {catchups.map((catchup) => (
                  <Card key={catchup._id} className="overflow-hidden">
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle className="text-base">
                            Catchup with {catchup.employee.fullName}
                          </CardTitle>
                          <CardDescription>
                            {formatDate(catchup.date)} by {catchup.conductedBy.fullName}
                          </CardDescription>
                        </div>
                        <Badge variant={catchup.status === 'finalized' ? 'default' : 'outline'}>
                          {catchup.status === 'finalized' ? 'Finalized' : 'Draft'}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent className="pb-2">
                      <p className="line-clamp-2 text-sm">
                        {catchup.findingsDiscussed}
                      </p>
                    </CardContent>
                    <CardFooter className="flex justify-between pt-0">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => viewCatchupDetails(catchup)}
                      >
                        View Details
                      </Button>
                      {!readOnly && catchup.status === 'draft' && (
                        <Button
                          size="sm"
                          onClick={() => handleFinalizeCatchup(catchup._id)}
                          disabled={isSaving}
                        >
                          {isSaving ? (
                            <>
                              <Loader2 className="mr-2 h-3 w-3 animate-spin" />
                              Finalizing...
                            </>
                          ) : (
                            'Finalize'
                          )}
                        </Button>
                      )}
                    </CardFooter>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>

          {/* View Catchup Tab */}
          <TabsContent value="view" className="space-y-6">
            {selectedCatchup ? (
              <div className="space-y-6">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="text-lg font-medium">
                      Catchup with {selectedCatchup.employee.fullName}
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      {formatDate(selectedCatchup.date)}
                    </p>
                  </div>
                  <Badge variant={selectedCatchup.status === 'finalized' ? 'default' : 'outline'}>
                    {selectedCatchup.status === 'finalized' ? 'Finalized' : 'Draft'}
                  </Badge>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <p className="text-sm font-medium">Conducted By</p>
                    <p>{selectedCatchup.conductedBy.fullName}</p>
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm font-medium">Date</p>
                    <p>{formatDate(selectedCatchup.date)}</p>
                  </div>
                </div>

                <Separator />

                <div className="space-y-4">
                  <div className="space-y-2">
                    <p className="text-sm font-medium">Findings Discussed</p>
                    <div className="p-3 bg-muted rounded-md">
                      <p className="whitespace-pre-wrap">{selectedCatchup.findingsDiscussed}</p>
                    </div>
                  </div>

                  {selectedCatchup.commentsBefore && (
                    <div className="space-y-2">
                      <p className="text-sm font-medium">Comments Before</p>
                      <div className="p-3 bg-muted rounded-md">
                        <p className="whitespace-pre-wrap">{selectedCatchup.commentsBefore}</p>
                      </div>
                    </div>
                  )}

                  <div className="space-y-2">
                    <p className="text-sm font-medium">Discussion Points</p>
                    <div className="p-3 bg-muted rounded-md">
                      <p className="whitespace-pre-wrap">{selectedCatchup.discussionPoints}</p>
                    </div>
                  </div>

                  {selectedCatchup.employeeResponse && (
                    <div className="space-y-2">
                      <p className="text-sm font-medium">Employee Response</p>
                      <div className="p-3 bg-muted rounded-md">
                        <p className="whitespace-pre-wrap">{selectedCatchup.employeeResponse}</p>
                      </div>
                    </div>
                  )}

                  {selectedCatchup.agreedActions && (
                    <div className="space-y-2">
                      <p className="text-sm font-medium">Agreed Actions</p>
                      <div className="p-3 bg-muted rounded-md">
                        <p className="whitespace-pre-wrap">{selectedCatchup.agreedActions}</p>
                      </div>
                    </div>
                  )}

                  {selectedCatchup.commentsAfter && (
                    <div className="space-y-2">
                      <p className="text-sm font-medium">Comments After</p>
                      <div className="p-3 bg-muted rounded-md">
                        <p className="whitespace-pre-wrap">{selectedCatchup.commentsAfter}</p>
                      </div>
                    </div>
                  )}
                </div>

                <div className="flex justify-between">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setSelectedCatchup(null);
                      setActiveTab('history');
                    }}
                  >
                    Back to History
                  </Button>

                  {!readOnly && selectedCatchup.status === 'draft' && (
                    <Button
                      onClick={() => handleFinalizeCatchup(selectedCatchup._id)}
                      disabled={isSaving}
                    >
                      {isSaving ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Finalizing...
                        </>
                      ) : (
                        'Finalize Catchup Note'
                      )}
                    </Button>
                  )}
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                No catchup note selected. Please select a catchup note from the history tab.
              </div>
            )}
          </TabsContent>
        </CardContent>
      </Card>
    </div>
  );
}
