import api from './api';

export interface User {
  _id: string;
  username?: string;
  fullName: string;
  email?: string;
  role?: 'admin' | 'manager' | 'employee';
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
}

// Get all users
export const getUsers = async () => {
  try {
    console.log('Fetching users from: /auth/users-list');
    const response = await api.get('/auth/users-list');
    console.log('Users API response:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('Error getting users:', error);
    console.error('Error details:', {
      status: error?.response?.status,
      statusText: error?.response?.statusText,
      data: error?.response?.data,
      url: error?.config?.url
    });
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Get a user by ID
export const getUserById = async (id: string) => {
  try {
    const response = await api.get(`/users/${id}`);
    return response.data;
  } catch (error: any) {
    console.error('Error getting user by ID:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Get active users
export const getActiveUsers = async () => {
  try {
    const response = await api.get('/users', { params: { isActive: true } });
    return response.data;
  } catch (error: any) {
    console.error('Error getting active users:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};
