const express = require('express');
const router = express.Router();
const { requireUser } = require('./middleware/auth');
const BannedItem = require('../models/BannedItem');
const mongoose = require('mongoose');

// Get all banned items with optional filtering
router.get('/', requireUser, async (req, res) => {
  try {
    const { category, search } = req.query;

    // Build query
    const query = {};

    // Filter by category if provided
    if (category && category !== 'all') {
      query.category = category;
    }

    // Add search functionality if provided
    if (search) {
      query.$text = { $search: search };
    }

    // Get items with pagination
    const items = await BannedItem.find(query)
      .sort({ title: 1 }) // Sort alphabetically by title
      .populate('createdBy', 'username fullName')
      .populate('updatedBy', 'username fullName');

    return res.status(200).json({
      success: true,
      data: items
    });
  } catch (error) {
    console.error('Error getting banned items:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get a single banned item by ID
router.get('/:id', requireUser, async (req, res) => {
  try {
    const { id } = req.params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid item ID'
      });
    }

    const item = await BannedItem.findById(id)
      .populate('createdBy', 'username fullName')
      .populate('updatedBy', 'username fullName');

    if (!item) {
      return res.status(404).json({
        success: false,
        error: 'Item not found'
      });
    }

    return res.status(200).json({
      success: true,
      data: item
    });
  } catch (error) {
    console.error('Error getting banned item:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Create a new banned item
router.post('/', requireUser, async (req, res) => {
  try {
    // Check if user has permission (admin or manager)
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to create banned items'
      });
    }

    const { title, description, category } = req.body;

    // Validate required fields
    if (!title || !description || !category) {
      return res.status(400).json({
        success: false,
        error: 'Title, description, and category are required'
      });
    }

    // Validate category
    if (category !== 'banned' && category !== 'restricted') {
      return res.status(400).json({
        success: false,
        error: 'Category must be either "banned" or "restricted"'
      });
    }

    // Create new item
    const newItem = new BannedItem({
      title,
      description,
      category,
      createdBy: req.user._id,
      updatedBy: req.user._id
    });

    await newItem.save();

    // Populate user details
    await newItem.populate('createdBy', 'username fullName');
    await newItem.populate('updatedBy', 'username fullName');

    return res.status(201).json({
      success: true,
      data: newItem
    });
  } catch (error) {
    console.error('Error creating banned item:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Update a banned item
router.put('/:id', requireUser, async (req, res) => {
  try {
    // Check if user has permission (admin or manager)
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to update banned items'
      });
    }

    const { id } = req.params;
    const { title, description, category } = req.body;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid item ID'
      });
    }

    // Validate required fields
    if (!title || !description || !category) {
      return res.status(400).json({
        success: false,
        error: 'Title, description, and category are required'
      });
    }

    // Validate category
    if (category !== 'banned' && category !== 'restricted') {
      return res.status(400).json({
        success: false,
        error: 'Category must be either "banned" or "restricted"'
      });
    }

    // Find and update the item
    const updatedItem = await BannedItem.findByIdAndUpdate(
      id,
      {
        title,
        description,
        category,
        updatedBy: req.user._id,
        updatedAt: Date.now()
      },
      { new: true }
    )
      .populate('createdBy', 'username fullName')
      .populate('updatedBy', 'username fullName');

    if (!updatedItem) {
      return res.status(404).json({
        success: false,
        error: 'Item not found'
      });
    }

    return res.status(200).json({
      success: true,
      data: updatedItem
    });
  } catch (error) {
    console.error('Error updating banned item:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Delete a banned item
router.delete('/:id', requireUser, async (req, res) => {
  try {
    // Check if user has permission (admin or manager)
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to delete banned items'
      });
    }

    const { id } = req.params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid item ID'
      });
    }

    const deletedItem = await BannedItem.findByIdAndDelete(id);

    if (!deletedItem) {
      return res.status(404).json({
        success: false,
        error: 'Item not found'
      });
    }

    return res.status(200).json({
      success: true,
      message: 'Item deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting banned item:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = router;
