import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { DollarSign, CheckCircle, XCircle, AlertCircle, Package } from 'lucide-react';
import { useFormContext, useWatch } from 'react-hook-form';
import { useEffect } from 'react';

interface MultiItemPricingSectionProps {
  control: any;
  disabled?: boolean;
  auditType: 'buy' | 'pawn' | 'price';
}

/**
 * Multi-item pricing section component that shows separate pricing forms for each item
 * Auto-populates cost price from item cost and displays item identification info
 */
export function MultiItemPricingSection({ control, disabled = false, auditType }: MultiItemPricingSectionProps) {
  const { setValue, watch } = useFormContext();
  const items = useWatch({ control, name: 'items' }) || [];

  // Common fail reasons for pricing
  const pricingFailReasons = [
    { id: 'overpaid', label: 'Overpaid for item' },
    { id: 'underpaid', label: 'Underpaid for item' },
    { id: 'no_market_research', label: 'No market research conducted' },
    { id: 'incorrect_valuation', label: 'Incorrect valuation method' },
    { id: 'pricing_policy_violation', label: 'Pricing policy violation' },
  ];

  // Auto-populate cost price for each item when item cost changes
  useEffect(() => {
    items.forEach((item: any, index: number) => {
      if (item.cost && (!item.pricing?.costPrice || item.pricing.costPrice === '')) {
        setValue(`items.${index}.pricing.costPrice`, item.cost);
      }
    });
  }, [items, setValue]);

  // Helper function to render status badge
  const renderStatusBadge = (status: string) => {
    switch (status) {
      case 'pass':
        return <Badge variant="success" className="flex items-center gap-1"><CheckCircle className="h-3 w-3" />Pass</Badge>;
      case 'fail':
        return <Badge variant="destructive" className="flex items-center gap-1"><XCircle className="h-3 w-3" />Fail</Badge>;
      default:
        return <Badge variant="secondary" className="flex items-center gap-1"><AlertCircle className="h-3 w-3" />Not Assessed</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <DollarSign className="h-5 w-5 text-primary" />
            <CardTitle>Pricing Assessment</CardTitle>
          </div>
          <CardDescription>
            Assess pricing for each item individually. Cost prices are auto-populated from item costs.
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Overall Pricing Assessment */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Overall Pricing Assessment</CardTitle>
            <FormField
              control={control}
              name="pricing.status"
              render={({ field }) => renderStatusBadge(field.value)}
            />
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <FormField
            control={control}
            name="pricing.status"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>Overall Assessment</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    className="flex flex-col space-y-2"
                    disabled={disabled}
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="pass" id="pricing-pass" />
                      <FormLabel htmlFor="pricing-pass" className="cursor-pointer">Pass</FormLabel>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="fail" id="pricing-fail" />
                      <FormLabel htmlFor="pricing-fail" className="cursor-pointer">Fail</FormLabel>
                    </div>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Show fail reasons if status is "fail" */}
          <FormField
            control={control}
            name="pricing.status"
            render={({ field }) => (
              <div>
                {field.value === 'fail' && (
                  <FormField
                    control={control}
                    name="pricing.failReasons"
                    render={({ field: failReasonsField }) => (
                      <FormItem>
                        <FormLabel>Fail Reasons</FormLabel>
                        <FormDescription>Select all that apply</FormDescription>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 border rounded-md p-3">
                          {pricingFailReasons.map((reason) => (
                            <FormItem
                              key={reason.id}
                              className="flex flex-row items-start space-x-3 space-y-0 p-2 hover:bg-accent rounded-md"
                            >
                              <FormControl>
                                <Checkbox
                                  checked={failReasonsField.value?.includes(reason.id)}
                                  onCheckedChange={(checked) => {
                                    const currentReasons = failReasonsField.value || [];
                                    if (checked) {
                                      failReasonsField.onChange([...currentReasons, reason.id]);
                                    } else {
                                      failReasonsField.onChange(currentReasons.filter((r: string) => r !== reason.id));
                                    }
                                  }}
                                  disabled={disabled}
                                />
                              </FormControl>
                              <FormLabel className="text-sm font-normal cursor-pointer">
                                {reason.label}
                              </FormLabel>
                            </FormItem>
                          ))}
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </div>
            )}
          />

          {/* Overall pricing notes */}
          <FormField
            control={control}
            name="pricing.notes"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Overall Pricing Notes</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Enter any overall notes about pricing assessment"
                    className="min-h-[80px]"
                    {...field}
                    disabled={disabled}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </CardContent>
      </Card>

      {/* Individual Item Pricing */}
      {items.map((item: any, itemIndex: number) => (
        <Card key={itemIndex} className="border-l-4 border-l-primary">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Package className="h-5 w-5 text-primary" />
                <div>
                  <CardTitle className="text-lg">Item {itemIndex + 1}: {item.brand} - {item.description}</CardTitle>
                  <CardDescription>Stockcode: {item.stockcode} | Transaction Cost: ${item.cost}</CardDescription>
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Suggested Price */}
              <FormField
                control={control}
                name={`items.${itemIndex}.pricing.suggestedPrice`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Suggested Price</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="Enter suggested price"
                        {...field}
                        disabled={disabled}
                      />
                    </FormControl>
                    <FormDescription>
                      Recommended market price
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Cost Price (Auto-populated) */}
              <FormField
                control={control}
                name={`items.${itemIndex}.pricing.costPrice`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Cost Price</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="Auto-populated from item cost"
                        {...field}
                        disabled={disabled}
                        className="bg-muted/50"
                      />
                    </FormControl>
                    <FormDescription>
                      Auto-populated from transaction cost
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Ticket Price */}
              <FormField
                control={control}
                name={`items.${itemIndex}.pricing.ticketPrice`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Ticket Price</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="Enter ticket price"
                        {...field}
                        disabled={disabled}
                      />
                    </FormControl>
                    <FormDescription>
                      Intended selling price
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Item-specific notes */}
            <FormField
              control={control}
              name={`items.${itemIndex}.pricing.notes`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Item Notes</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter any notes specific to this item's pricing"
                      className="min-h-[60px]"
                      {...field}
                      disabled={disabled}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
