const mongoose = require('mongoose');

const syncLogSchema = new mongoose.Schema({
  syncType: {
    type: String,
    enum: ['listings', 'questions', 'feedback', 'categories', 'all'],
    default: 'all',
    required: true
  },
  startTime: {
    type: Date,
    required: true
  },
  endTime: {
    type: Date
  },
  success: {
    type: Boolean
  },
  status: {
    type: String,
    enum: ['pending', 'in_progress', 'completed', 'failed'],
    default: 'in_progress'
  },
  itemsProcessed: {
    type: Number,
    default: 0
  },
  newItems: {
    type: Number,
    default: 0
  },
  updatedItems: {
    type: Number,
    default: 0
  },
  error: {
    type: String
  },
  apiCallsMade: {
    type: Number,
    default: 0
  },
  // Additional fields for tracking progress of large feedback syncs
  totalItems: {
    type: Number,
    default: 0
  },
  totalPages: {
    type: Number,
    default: 0
  },
  currentPage: {
    type: Number,
    default: 0
  },
  maxPagesPerEndpoint: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

const SyncLog = mongoose.model('SyncLog', syncLogSchema);

module.exports = SyncLog;
