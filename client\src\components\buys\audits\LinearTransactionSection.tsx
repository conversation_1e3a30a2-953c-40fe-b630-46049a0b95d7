import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { FileText, ClipboardCheck, CheckCircle, XCircle, AlertCircle } from 'lucide-react';

interface LinearTransactionSectionProps {
  control: any;
  disabled?: boolean;
}

/**
 * Linear transaction section component that replaces the tabbed interface
 * Shows all assessments in a single flow for better usability
 */
export function LinearTransactionSection({ control, disabled = false }: LinearTransactionSectionProps) {
  // Common fail reasons for data entry quality
  const dataEntryFailReasons = [
    { id: 'missing_info', label: 'Missing information' },
    { id: 'missing_serial_number', label: 'Missing Serial Number' },
    { id: 'incorrect_info', label: 'Incorrect information' },
    { id: 'incomplete_info', label: 'Incomplete information' },
    { id: 'wrong_category', label: 'Wrong category' },
    { id: 'wrong_description', label: 'Wrong description' },
  ];

  // Common fail reasons for item condition check
  const itemConditionFailReasons = [
    { id: 'condition_not_checked', label: 'Condition not checked' },
    { id: 'condition_misrepresented', label: 'Condition misrepresented' },
    { id: 'damage_not_noted', label: 'Damage not noted' },
    { id: 'missing_parts_not_noted', label: 'Missing parts not noted' },
    { id: 'functionality_not_tested', label: 'Functionality not tested' },
  ];

  // Function to render status badge
  const renderStatusBadge = (status: string) => {
    if (status === 'pass') {
      return (
        <Badge className="bg-green-500 hover:bg-green-600 flex items-center gap-1">
          <CheckCircle className="h-3 w-3" />
          Pass
        </Badge>
      );
    } else if (status === 'fail') {
      return (
        <Badge className="bg-red-500 hover:bg-red-600 flex items-center gap-1">
          <XCircle className="h-3 w-3" />
          Fail
        </Badge>
      );
    } else {
      return (
        <Badge variant="outline" className="flex items-center gap-1">
          <AlertCircle className="h-3 w-3" />
          Not Assessed
        </Badge>
      );
    }
  };

  return (
    <div className="space-y-6">
      {/* Data Entry Quality Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <FileText className="h-5 w-5 text-primary" />
              <CardTitle>Data Entry Quality</CardTitle>
            </div>
            <FormField
              control={control}
              name="dataEntryQuality.status"
              render={({ field }) => renderStatusBadge(field.value)}
            />
          </div>
          <CardDescription>
            Assess the quality and accuracy of data entry for this transaction
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <FormField
            control={control}
            name="dataEntryQuality.status"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>Assessment</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    value={field.value}
                    className="flex flex-row space-x-6"
                    disabled={disabled}
                  >
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="pass" id="dataEntryPass" />
                      </FormControl>
                      <FormLabel className="font-normal cursor-pointer" htmlFor="dataEntryPass">
                        Pass
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="fail" id="dataEntryFail" />
                      </FormControl>
                      <FormLabel className="font-normal cursor-pointer" htmlFor="dataEntryFail">
                        Fail
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="not_assessed" id="dataEntryNotAssessed" />
                      </FormControl>
                      <FormLabel className="font-normal cursor-pointer" htmlFor="dataEntryNotAssessed">
                        Not Assessed
                      </FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Show fail reasons if status is "fail" */}
          <FormField
            control={control}
            name="dataEntryQuality.status"
            render={({ field }) => (
              field.value === 'fail' && (
                <FormField
                  control={control}
                  name="dataEntryQuality.failReasons"
                  render={({ field: failReasonsField }) => (
                    <FormItem>
                      <FormLabel>Fail Reasons</FormLabel>
                      <FormDescription>Select all that apply</FormDescription>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 border rounded-md p-3">
                        {dataEntryFailReasons.map((reason) => (
                          <FormItem
                            key={reason.id}
                            className="flex flex-row items-start space-x-3 space-y-0 p-2 hover:bg-accent rounded-md"
                          >
                            <FormControl>
                              <Checkbox
                                checked={failReasonsField.value?.includes(reason.id)}
                                onCheckedChange={(checked) => {
                                  const currentValue = failReasonsField.value || [];
                                  if (checked) {
                                    failReasonsField.onChange([...currentValue, reason.id]);
                                  } else {
                                    failReasonsField.onChange(
                                      currentValue.filter((value: string) => value !== reason.id)
                                    );
                                  }
                                }}
                                disabled={disabled}
                              />
                            </FormControl>
                            <FormLabel className="font-normal cursor-pointer">
                              {reason.label}
                            </FormLabel>
                          </FormItem>
                        ))}
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )
            )}
          />

          <FormField
            control={control}
            name="dataEntryQuality.notes"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Notes</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Enter any additional notes about data entry quality"
                    className="min-h-[100px]"
                    {...field}
                    disabled={disabled}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </CardContent>
      </Card>

      {/* Item Condition Check Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <ClipboardCheck className="h-5 w-5 text-primary" />
              <CardTitle>Item Condition Check</CardTitle>
            </div>
            <FormField
              control={control}
              name="itemConditionCheck.status"
              render={({ field }) => renderStatusBadge(field.value)}
            />
          </div>
          <CardDescription>
            Assess whether the item condition was properly checked and documented
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <FormField
            control={control}
            name="itemConditionCheck.status"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>Assessment</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    value={field.value}
                    className="flex flex-row space-x-6"
                    disabled={disabled}
                  >
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="pass" id="itemConditionPass" />
                      </FormControl>
                      <FormLabel className="font-normal cursor-pointer" htmlFor="itemConditionPass">
                        Pass
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="fail" id="itemConditionFail" />
                      </FormControl>
                      <FormLabel className="font-normal cursor-pointer" htmlFor="itemConditionFail">
                        Fail
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="not_assessed" id="itemConditionNotAssessed" />
                      </FormControl>
                      <FormLabel className="font-normal cursor-pointer" htmlFor="itemConditionNotAssessed">
                        Not Assessed
                      </FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Show fail reasons if status is "fail" */}
          <FormField
            control={control}
            name="itemConditionCheck.status"
            render={({ field }) => (
              field.value === 'fail' && (
                <FormField
                  control={control}
                  name="itemConditionCheck.failReasons"
                  render={({ field: failReasonsField }) => (
                    <FormItem>
                      <FormLabel>Fail Reasons</FormLabel>
                      <FormDescription>Select all that apply</FormDescription>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 border rounded-md p-3">
                        {itemConditionFailReasons.map((reason) => (
                          <FormItem
                            key={reason.id}
                            className="flex flex-row items-start space-x-3 space-y-0 p-2 hover:bg-accent rounded-md"
                          >
                            <FormControl>
                              <Checkbox
                                checked={failReasonsField.value?.includes(reason.id)}
                                onCheckedChange={(checked) => {
                                  const currentValue = failReasonsField.value || [];
                                  if (checked) {
                                    failReasonsField.onChange([...currentValue, reason.id]);
                                  } else {
                                    failReasonsField.onChange(
                                      currentValue.filter((value: string) => value !== reason.id)
                                    );
                                  }
                                }}
                                disabled={disabled}
                              />
                            </FormControl>
                            <FormLabel className="font-normal cursor-pointer">
                              {reason.label}
                            </FormLabel>
                          </FormItem>
                        ))}
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )
            )}
          />

          <FormField
            control={control}
            name="itemConditionCheck.notes"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Notes</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Enter any additional notes about item condition"
                    className="min-h-[100px]"
                    {...field}
                    disabled={disabled}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </CardContent>
      </Card>
    </div>
  );
}
