import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2 } from 'lucide-react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Cell } from 'recharts';
import { FollowUpResponseData } from '@/api/happyOrNot';

interface FollowUpResponseChartProps {
  data: FollowUpResponseData | null;
  loading: boolean;
  title: string;
  description: string;
  color: string;
  lightColor: string;
}

export const FollowUpResponseChart: React.FC<FollowUpResponseChartProps> = ({
  data,
  loading,
  title,
  description,
  color,
  lightColor
}) => {
  // Format data for the chart
  const chartData = data?.responses.map(item => ({
    option: item.option,
    count: item.count,
    percentage: parseFloat(item.percentage as string),
    formattedPercentage: `${item.percentage}%`,
    formattedCount: `${item.count} resp.`,
    totalResponses: data.totalResponses
  })) || [];

  // Custom tooltip for the chart
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-background border rounded-md shadow-md p-2 text-sm">
          <p className="font-medium">{data.option}</p>
          <p>{data.formattedPercentage} - {data.formattedCount}</p>
          {data.totalResponses && (
            <p className="text-xs text-muted-foreground mt-1">
              Total responses: {data.totalResponses}
            </p>
          )}
        </div>
      );
    }
    return null;
  };

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex justify-center items-center h-80">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        ) : data && data.responses.length > 0 ? (
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={chartData}
                layout="vertical"
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" horizontal={false} />
                <XAxis type="number" domain={[0, 100]} tickFormatter={(value) => `${value}%`} />
                <YAxis
                  type="category"
                  dataKey="option"
                  width={150}
                  tick={{ fontSize: 12 }}
                />
                <Tooltip content={<CustomTooltip />} />
                <Bar dataKey="percentage" name="Percentage">
                  {chartData.map((entry, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={index % 2 === 0 ? color : lightColor}
                    />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
            <div className="text-xs text-muted-foreground text-right mt-2">
              {data.totalResponses > 0 ? (
                <>Question skipped: {data.totalResponses - chartData.reduce((sum, item) => sum + item.count, 0)}</>
              ) : null}
            </div>
          </div>
        ) : (
          <div className="flex justify-center items-center h-80 text-muted-foreground">
            No follow-up response data available for the selected period
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default FollowUpResponseChart;
