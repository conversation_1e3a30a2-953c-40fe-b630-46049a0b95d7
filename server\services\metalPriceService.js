const mongoose = require('mongoose');
const MetalPrice = require('../models/MetalPrice');
const MetalPriceHistory = require('../models/MetalPriceHistory');
const MetalPriceSettings = require('../models/MetalPriceSettings');
const metalApiService = require('./metalApiService');

class MetalPriceService {
  /**
   * Get all metal prices or filter by metal type
   * @param {Object} filter - Optional filter criteria
   * @returns {Promise<Object>} Result with metal prices and next update time
   */
  async getMetalPrices(filter = {}) {
    try {

      // Custom sort for metal prices
      const prices = await MetalPrice.aggregate([
        { $match: filter },
        {
          $addFields: {
            sortOrder: {
              $switch: {
                branches: [
                  { case: { $eq: ["$metal", "Gold"] }, then: 1 }
                ],
                default: 2
              }
            },
            purityNum: {
              $let: {
                vars: {
                  // Remove 'k' and convert to string first
                  numStr: {
                    $replaceAll: {
                      input: { $toLower: "$purity" },
                      find: "k",
                      replacement: ""
                    }
                  }
                },
                in: {
                  $convert: {
                    input: "$$numStr",
                    to: "decimal",
                    onError: 999 // High number for non-numeric purities
                  }
                }
              }
            }
          }
        },
        {
          $sort: {
            sortOrder: 1,
            metal: 1,
            purityNum: 1
          }
        },
        {
          $project: {
            sortOrder: 0,
            purityNum: 0
          }
        }
      ]);

      // Get next update time from the first record (they should all be the same)
      const nextUpdateAt = prices.length > 0 ? prices[0].nextUpdateAt : null;

      return {
        success: true,
        prices,
        nextUpdateAt
      };
    } catch (error) {
      console.error('Error fetching metal prices:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get pricing settings
   * @returns {Promise<Object>} Result with settings or error
   */
  async getSettings() {
    try {
      const settings = await MetalPriceSettings.getSettings();
      return {
        success: true,
        settings
      };
    } catch (error) {
      console.error('Error fetching metal price settings:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Update pricing settings
   * @param {Object} updateData - Settings data to update
   * @returns {Promise<Object>} Result with updated settings or error
   */
  async updateSettings(updateData) {
    try {
      // Validate settings are within acceptable ranges
      const validatedData = {};

      if (updateData.minBuyPercentage !== undefined) {
        if (updateData.minBuyPercentage < 0 || updateData.minBuyPercentage > 100) {
          return {
            success: false,
            error: 'Min buy percentage must be between 0 and 100'
          };
        }
        validatedData.minBuyPercentage = updateData.minBuyPercentage;
      }

      if (updateData.maxBuyPercentage !== undefined) {
        if (updateData.maxBuyPercentage < 0 || updateData.maxBuyPercentage > 100) {
          return {
            success: false,
            error: 'Max buy percentage must be between 0 and 100'
          };
        }
        validatedData.maxBuyPercentage = updateData.maxBuyPercentage;
      }

      if (updateData.meltPercentage !== undefined) {
        if (updateData.meltPercentage < 0) {
          return {
            success: false,
            error: 'Melt percentage must be positive'
          };
        }
        validatedData.meltPercentage = updateData.meltPercentage;
      }

      if (updateData.mhjMinPercentage !== undefined) {
        if (updateData.mhjMinPercentage < 0 || updateData.mhjMinPercentage > 100) {
          return {
            success: false,
            error: 'MHJ min percentage must be between 0 and 100'
          };
        }
        validatedData.mhjMinPercentage = updateData.mhjMinPercentage;
      }

      if (updateData.mhjMaxPercentage !== undefined) {
        if (updateData.mhjMaxPercentage < 0 || updateData.mhjMaxPercentage > 100) {
          return {
            success: false,
            error: 'MHJ max percentage must be between 0 and 100'
          };
        }
        validatedData.mhjMaxPercentage = updateData.mhjMaxPercentage;
      }

      if (updateData.apiKey !== undefined) {
        validatedData.apiKey = updateData.apiKey;
      }

      if (updateData.updateFrequencyMinutes !== undefined) {
        if (updateData.updateFrequencyMinutes < 5) {
          return {
            success: false,
            error: 'Update frequency must be at least 5 minutes'
          };
        }
        validatedData.updateFrequencyMinutes = updateData.updateFrequencyMinutes;
      }

      // Update the settings
      validatedData.lastUpdated = new Date();
      const settings = await MetalPriceSettings.findOneAndUpdate(
        {},
        { $set: validatedData },
        { new: true, upsert: true }
      );

      // If API key was updated, refresh prices immediately
      if (updateData.apiKey !== undefined) {
        this.updatePricesFromApi();
      }

      return {
        success: true,
        settings
      };
    } catch (error) {
      console.error('Error updating metal price settings:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Update metal prices from the API
   * @returns {Promise<Object>} Result of the update operation
   */
  async updatePricesFromApi() {
    try {
      console.log('Updating metal prices from API');

      // Get current settings
      const settingsResult = await this.getSettings();
      if (!settingsResult.success) {
        return settingsResult;
      }

      const settings = settingsResult.settings;

      // Calculate next update time
      const nextUpdateAt = new Date();
      nextUpdateAt.setMinutes(nextUpdateAt.getMinutes() + settings.updateFrequencyMinutes);

      // Fetch gold prices by carat
      const goldResult = await metalApiService.getGoldPricesByCarat();

      if (!goldResult.success) {
        return {
          success: false,
          error: 'Failed to fetch gold prices: ' + goldResult.error
        };
      }

      // Fetch other metal prices
      const otherMetalsResult = await metalApiService.getLatestOtherMetalPrices();

      if (!otherMetalsResult.success) {
        return {
          success: false,
          error: 'Failed to fetch other metal prices: ' + otherMetalsResult.error
        };
      }

      // Helper function to get numeric value from purity string
      const getPurityValue = (purity) => {
        const match = purity.toLowerCase().match(/(\d+)k/);
        return match ? parseInt(match[1]) : 0;
      };

      // Sort gold purities and prepare prices
      const goldPurities = Object.entries(goldResult.prices)
        .sort(([purityA], [purityB]) => getPurityValue(purityA) - getPurityValue(purityB));

      // Prepare updated prices for gold
      const goldPrices = goldPurities.map(([purity, spotPrice]) => ({
        metal: 'Gold',
        purity,
        spotPrice,
        minBuyPrice: spotPrice * settings.minBuyPercentage / 100,
        maxBuyPrice: spotPrice * settings.maxBuyPercentage / 100,
        meltPrice: spotPrice * settings.meltPercentage / 100,
        lastUpdated: new Date(),
        nextUpdateAt
      }));

      // Prepare updated prices for other metals
      const otherPrices = Object.entries(otherMetalsResult.prices)
        .sort(([metalA], [metalB]) => metalA.localeCompare(metalB))
        .map(([metal, spotPrice]) => ({
          metal,
          purity: '999', // Standard purity for other metals
          spotPrice,
          minBuyPrice: spotPrice * settings.minBuyPercentage / 100,
          maxBuyPrice: spotPrice * settings.maxBuyPercentage / 100,
          meltPrice: spotPrice * settings.meltPercentage / 100,
          lastUpdated: new Date(),
          nextUpdateAt
        }));

      // Combine all prices with gold first, then other metals
      const allPrices = [...goldPrices, ...otherPrices];

      // Update or create price documents
      const updatePromises = allPrices.map(async (price) => {
        return MetalPrice.findOneAndUpdate(
          { metal: price.metal, purity: price.purity },
          price,
          { upsert: true, new: true }
        );
      });

      await Promise.all(updatePromises);

      console.log(`Successfully updated ${allPrices.length} metal prices`);

      return {
        success: true,
        message: `Successfully updated ${allPrices.length} metal prices`,
        count: allPrices.length,
        nextUpdateAt
      };
    } catch (error) {
      console.error('Error updating metal prices from API:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get historical price data
   * @param {string} metal - Metal type (Gold, Silver, etc.)
   * @param {number} days - Number of days of history to retrieve
   * @returns {Promise<Object>} Result with historical data or error
   */
  async getHistoricalPrices(metal, days = 365) {
    try {
      // Limit days to 365 to match API limitations
      const validDays = Math.min(days, 365);

      // Validate parameters
      if (!metal) {
        return {
          success: false,
          error: 'Metal type is required'
        };
      }

      // Calculate date range
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - validDays);

      // Query the database for historical prices
      const prices = await MetalPriceHistory.find({
        metal,
        date: { $gte: startDate, $lte: endDate }
      }).sort({ date: 1 });

      // If no data in the database or very little data, try to update it
      if (prices.length < validDays / 5) { // If we have less than 20% of expected data points
        console.log(`Insufficient data in database (${prices.length} records). Attempting to update from API...`);
        const updateResult = await this.updateHistoricalPrices(metal, validDays);

        // Pass along any warnings
        if (updateResult.warning) {
          return {
            success: true,
            history: updateResult.history,
            warning: updateResult.warning
          };
        }

        return updateResult;
      }

      // Check if data is recent (within the last 24 hours)
      const latestDate = prices.length > 0 ? new Date(prices[prices.length - 1].date) : null;
      const now = new Date();
      const isDataRecent = latestDate && ((now.getTime() - latestDate.getTime()) / (1000 * 60 * 60) <= 24);

      if (!isDataRecent && prices.length > 0) {
        console.log(`Data is not recent. Latest record is from ${latestDate}. Attempting to update from API...`);
        try {
          const updateResult = await this.updateHistoricalPrices(metal, validDays);

          // If update was successful, return the updated data
          if (updateResult.success) {
            return updateResult;
          }

          // If update failed, return existing data with a warning
          return {
            success: true,
            history: prices,
            warning: updateResult.error || 'Failed to update historical data from API'
          };
        } catch (error) {
          console.error('Error updating historical data:', error);
          return {
            success: true,
            history: prices,
            warning: 'Failed to update historical data: ' + error.message
          };
        }
      }

      return {
        success: true,
        history: prices
      };
    } catch (error) {
      console.error('Error fetching historical metal prices:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Update historical price data from the API
   * @param {string} metal - Metal type (Gold, Silver, etc.) or 'all' for all metals
   * @param {number} days - Number of days of history to update
   * @returns {Promise<Object>} Result of the update operation
   */
  async updateHistoricalPrices(metal, days = 365) { // Limit to 365 days
    try {
      // Limit days to 365 to match API limitations
      const validDays = Math.min(days, 365);
      console.log(`Updating ${validDays} days of historical prices for ${metal || 'all metals'}`);

      // Get API key from settings
      const settings = await MetalPriceSettings.getSettings();
      if (!settings.apiKey) {
        return {
          success: false,
          error: 'API key is not configured'
        };
      }

      // Calculate date range for API call (max 365 days)
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - validDays);

      const endDateStr = endDate.toISOString().split('T')[0];
      const startDateStr = startDate.toISOString().split('T')[0];

      console.log(`Fetching historical data: ${startDateStr} to ${endDateStr}`);

      // Fetch historical data from API
      let result;
      try {
        // Use actual API call
        result = await metalApiService.getHistoricalPrices(startDateStr, endDateStr);

        if (!result.success) {
          console.error(`Failed to fetch historical prices: ${result.error}`);

          // Return the existing database data with a warning
          console.log('Returning existing database data');
          const query = {};
          if (metal && metal !== 'all') {
            query.metal = metal;
          }
          query.date = { $gte: startDate, $lte: endDate };

          const existingData = await MetalPriceHistory.find(query).sort({ date: 1 });

          if (existingData.length > 0) {
            return {
              success: true,
              history: existingData,
              warning: 'Using existing data. Failed to update from API: ' + result.error
            };
          }

          return {
            success: false,
            error: 'Failed to retrieve historical price data: ' + result.error
          };
        }

        // Filter for requested metal if specified
        const history = metal && metal !== 'all'
          ? result.history.filter(item => item.metal === metal)
          : result.history;

        console.log(`Retrieved ${history.length} historical prices`);

        // Insert or update historical prices in bulk
        const bulkOps = history.map(item => ({
          updateOne: {
            filter: { metal: item.metal, date: new Date(item.date) },
            update: {
              metal: item.metal,
              date: new Date(item.date),
              price: item.price
            },
            upsert: true
          }
        }));

        console.log(`Performing bulk write operation for ${bulkOps.length} records`);
        const bulkResult = await MetalPriceHistory.bulkWrite(bulkOps);
        console.log(`Bulk update results:`, {
          matched: bulkResult.matchedCount,
          modified: bulkResult.modifiedCount,
          upserted: bulkResult.upsertedCount
        });

        // Query the updated data
        let query = {};
        if (metal && metal !== 'all') {
          query.metal = metal;
        }

        // Calculate date range for query
        const queryEndDate = new Date();
        const queryStartDate = new Date();
        queryStartDate.setDate(queryStartDate.getDate() - validDays);

        query.date = { $gte: queryStartDate, $lte: queryEndDate };

        const updatedPrices = await MetalPriceHistory.find(query).sort({ date: 1 });
        console.log(`Retrieved ${updatedPrices.length} records from database after update`);

        return {
          success: true,
          history: updatedPrices,
          message: `Successfully updated ${bulkResult.upsertedCount + bulkResult.modifiedCount} historical prices`
        };
      } catch (error) {
        console.error('Error updating historical metal prices:', error);
        return {
          success: false,
          error: error.message
        };
      }
    } catch (error) {
      console.error('Error in updateHistoricalPrices:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get API usage information
   * @returns {Promise<Object>} Result with API usage or error
   */
  async getApiUsage() {
    try {
      // Use actual API call
      const result = await metalApiService.getApiUsage();

      if (!result.success) {
        return {
          success: false,
          error: 'Failed to fetch API usage: ' + result.error
        };
      }

      return {
        success: true,
        usage: result.usage
      };
    } catch (error) {
      console.error('Error fetching API usage:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Seeds the database with initial metal prices and settings
   * @returns {Promise<Object>} Result of the seeding operation
   */
  async seedMetalPrices() {
    try {
      console.log('Starting metal prices seeding process');

      // Check if metal prices already exist
      const existingCount = await MetalPrice.countDocuments();
      console.log(`Found ${existingCount} existing metal prices`);

      if (existingCount > 0) {
        console.log('Skipping seed as metal prices already exist');
        return {
          success: true,
          message: `Database already has ${existingCount} metal prices. Skipping seed.`,
        };
      }

      // Check if settings exist, create default if not
      const settings = await MetalPriceSettings.getSettings();
      console.log('Using settings:', settings);

      // Update prices from API (or mock)
      const updateResult = await this.updatePricesFromApi();

      if (!updateResult.success) {
        return {
          success: false,
          error: 'Failed to seed metal prices: ' + updateResult.error
        };
      }

      return {
        success: true,
        message: updateResult.message,
        count: updateResult.count
      };
    } catch (error) {
      console.error('Error seeding metal prices:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Update a specific metal price
   * @param {string} id - Metal price document ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object>} Result with updated metal price or error
   */
  async updateMetalPrice(id, updateData) {
    try {
      console.log(`Updating metal price with ID: ${id}`);

      // Validate ID
      if (!id || !mongoose.Types.ObjectId.isValid(id)) {
        return {
          success: false,
          error: 'Invalid metal price ID'
        };
      }

      // Find the metal price
      const metalPrice = await MetalPrice.findById(id);
      if (!metalPrice) {
        return {
          success: false,
          error: 'Metal price not found'
        };
      }

      // Validate the update data
      const validatedData = {};

      if (updateData.spotPrice !== undefined) {
        if (updateData.spotPrice < 0) {
          return {
            success: false,
            error: 'Spot price must be a positive number'
          };
        }
        validatedData.spotPrice = updateData.spotPrice;
      }

      if (updateData.minBuyPrice !== undefined) {
        if (updateData.minBuyPrice < 0) {
          return {
            success: false,
            error: 'Min buy price must be a positive number'
          };
        }
        validatedData.minBuyPrice = updateData.minBuyPrice;
      }

      if (updateData.maxBuyPrice !== undefined) {
        if (updateData.maxBuyPrice < 0) {
          return {
            success: false,
            error: 'Max buy price must be a positive number'
          };
        }
        validatedData.maxBuyPrice = updateData.maxBuyPrice;
      }

      if (updateData.meltPrice !== undefined) {
        if (updateData.meltPrice < 0) {
          return {
            success: false,
            error: 'Melt price must be a positive number'
          };
        }
        validatedData.meltPrice = updateData.meltPrice;
      }

      if (updateData.nextUpdateAt !== undefined) {
        try {
          const nextUpdateDate = new Date(updateData.nextUpdateAt);
          validatedData.nextUpdateAt = nextUpdateDate;
        } catch (error) {
          return {
            success: false,
            error: 'Invalid date format for nextUpdateAt'
          };
        }
      }

      // Update the timestamp
      validatedData.lastUpdated = new Date();

      // Update the metal price
      const updatedMetalPrice = await MetalPrice.findByIdAndUpdate(
        id,
        { $set: validatedData },
        { new: true }
      );

      console.log(`Successfully updated metal price with ID: ${id}`);
      return {
        success: true,
        metalPrice: updatedMetalPrice
      };
    } catch (error) {
      console.error('Error updating metal price:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

module.exports = new MetalPriceService();
