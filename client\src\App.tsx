import { ThemeProvider } from "@/components/ui/theme-provider"
import { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from "react-router-dom"
import { AuthProvider, useAuth } from "@/contexts/AuthContext"
import { ColorThemeProvider } from "@/contexts/ColorThemeContext"
import { Toaster } from "sonner"
import { HelmetProvider } from 'react-helmet-async'
import { ScrollToTop } from "./components/ScrollToTop"
import { Login } from "./pages/Login"
import { Dashboard } from "./pages/Dashboard"
import { Locations } from "./pages/Locations"
import { Inventory } from "./pages/Inventory"
import { GoldCalculator } from "./pages/GoldCalculator"
import { TradeMeSelling } from "./pages/trademe/TradeMeSelling"
import { TradeMeSold } from "./pages/trademe/TradeMeSold"
import { TradeMeUnsold } from "./pages/trademe/TradeMeUnsold"
import { TradeMeArchived } from "./pages/trademe/TradeMeArchived"
import { TradeMeQuestions } from "./pages/trademe/TradeMeQuestions"
import { TradeMeListingDetail } from "./pages/trademe/TradeMeListingDetail"
import { TradeMeListingEdit } from "./pages/trademe/TradeMeListingEdit"
import TradeMeSyncStatus from "./pages/trademe/TradeMeSyncStatus"
import { TradeMeFeedback } from "./pages/trademe/TradeMeFeedback"
import TradeMeTemplates from "./pages/trademe/TradeMeTemplates"
import TradeMeImport from "./pages/trademe/TradeMeImport"
import TradeMeImportLogs from "./pages/trademe/TradeMeImportLogs"
import TradeMeImportLogDetail from "./pages/trademe/TradeMeImportLogDetail"
import TradeMeImportedItems from "./pages/trademe/TradeMeImportedItems"
import TradeMeImportWizard from "./pages/trademe/TradeMeImportWizard"
import TradeMeDraft from "./pages/trademe/TradeMeDraft"
import { TradeMeLayout } from "./components/trademe/TradeMeLayout"
import { CustomerDetails } from "./pages/CustomerDetails"
import { CustomerDetailEdit } from "./pages/CustomerDetailEdit"
import { Reports } from "./pages/Reports"
import { Profile } from "./pages/Profile"
import { Settings } from "./pages/Settings"
import { Layout } from "./components/Layout"
import { ProtectedRoute } from "./components/ProtectedRoute"
import { AddressBook } from "./pages/AddressBook"
import { NotFound } from "./pages/NotFound"
import AdminLogs from "./pages/AdminLogs"
import { EmailTemplates } from "./pages/EmailTemplates"
import { DeviceChecker } from "./pages/DeviceChecker"
import { LoanTracker } from "./pages/LoanTracker"
import { BannedItems } from "./pages/BannedItems"
import { HappyOrNotFeedback } from "./pages/HappyOrNotFeedback"
import { HappyOrNotFeedbackDetails } from "./pages/HappyOrNotFeedbackDetails"
import ControllerTester from "./pages/ControllerTester"
import { BuyPawnAudits } from "./pages/buys/audits"


function App() {
  return (
    <ThemeProvider defaultTheme="dark" storageKey="ui-theme">
      <ColorThemeProvider>
        <HelmetProvider>
          <Router>
            <ScrollToTop />
            <AuthProvider>
              <Routes>
                <Route
                  path="/login"
                  element={
                    <PublicRoute>
                      <Login />
                    </PublicRoute>
                  }
                />
                <Route
                  path="/"
                  element={
                    <ProtectedRoute>
                      <Layout />
                    </ProtectedRoute>
                  }
                >
                  <Route index element={<Dashboard />} />
                  <Route path="locations" element={<Locations />} />
                  <Route path="inventory" element={<Inventory />} />
                  <Route path="gold-calculator" element={<GoldCalculator />} />
                  <Route path="trademe" element={<TradeMeLayout />}>
                    <Route index element={<TradeMeSelling />} />
                    <Route path="selling" element={<TradeMeSelling />} />
                    <Route path="sold" element={<TradeMeSold />} />
                    <Route path="unsold" element={<TradeMeUnsold />} />
                    <Route path="archived" element={<TradeMeArchived />} />
                    <Route path="draft" element={<TradeMeDraft />} />
                    <Route path="questions" element={<TradeMeQuestions />} />
                    <Route path="sync" element={<TradeMeSyncStatus />} />
                    <Route path="feedback" element={<TradeMeFeedback />} />
                    <Route path="templates" element={<TradeMeTemplates />} />
                    <Route path="import/logs" element={<TradeMeImportLogs />} />
                    <Route path="import/logs/:id" element={<TradeMeImportLogDetail />} />
                    <Route path="import/items" element={<TradeMeImportedItems />} />
                    <Route path="import/wizard/:logId" element={<TradeMeImportWizard />} />
                    <Route path="listing/:listingId" element={<TradeMeListingDetail />} />
                    <Route path="listing/new" element={<TradeMeListingEdit />} />
                    <Route path="listing/edit/:id" element={<TradeMeListingEdit />} />
                  </Route>
                  <Route path="address-book" element={<AddressBook />} />
                  <Route path="address-book/:id" element={<CustomerDetails />} />
                  <Route path="address-book/edit/:id" element={<CustomerDetailEdit />} />
                  <Route path="customers" element={<Navigate to="/address-book" replace />} />
                  <Route path="customers/:id" element={<Navigate to={({ params }) => `/address-book/${params.id}`} replace />} />
                  <Route path="customers/edit/:id" element={<Navigate to={({ params }) => `/address-book/edit/${params.id}`} replace />} />
                  <Route path="contacts/:id" element={<Navigate to={({ params }) => `/address-book/${params.id}`} replace />} />
                  <Route path="reports" element={<Reports />} />
                  <Route path="profile" element={<Profile />} />
                  <Route path="settings" element={<Settings />} />
                  <Route path="email-templates" element={<EmailTemplates />} />
                  <Route path="admin/logs" element={<AdminLogs />} />
                  <Route path="tools/device-checker" element={<DeviceChecker />} />
                  <Route path="tools/controller-tester" element={<ControllerTester />} />
                  <Route path="pf/tracker" element={<LoanTracker />} />
                  <Route path="buys/banned-restricted-items" element={<BannedItems />} />
                  <Route path="buys/audits/*" element={<BuyPawnAudits />} />
                  <Route path="feedback" element={<HappyOrNotFeedback />} />
                  <Route path="feedback-details" element={<HappyOrNotFeedbackDetails />} />
                </Route>

                <Route path="*" element={<NotFound />} />
              </Routes>
              <Toaster
                position="bottom-right"
                theme="dark"
                richColors
                closeButton
                toastOptions={{
                  style: {
                    background: 'hsl(var(--background))',
                    color: 'hsl(var(--foreground))',
                    border: '1px solid hsl(var(--border))',
                  },
                  className: 'dark:bg-gray-800 dark:text-white dark:border-gray-700',
                }}
              />
            </AuthProvider>
          </Router>
        </HelmetProvider>
      </ColorThemeProvider>
    </ThemeProvider>
  );
}

function PublicRoute({ children }: { children: React.ReactNode }) {
  const { isAuthenticated } = useAuth();
  const location = useLocation();

  if (isAuthenticated) {
    return <Navigate to="/" state={{ from: location }} replace />;
  }

  return <>{children}</>;
}

export default App
