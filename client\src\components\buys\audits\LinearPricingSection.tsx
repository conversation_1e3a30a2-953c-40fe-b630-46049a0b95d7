import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { DollarSign, Shield, CheckCircle, XCircle, AlertCircle } from 'lucide-react';

interface LinearPricingSectionProps {
  control: any;
  disabled?: boolean;
  auditType: 'buy' | 'pawn' | 'price';
}

/**
 * Linear pricing section component that replaces the tabbed interface
 * Shows pricing and authorized limit checks in a single flow
 */
export function LinearPricingSection({ control, disabled = false, auditType }: LinearPricingSectionProps) {
  // Common fail reasons for pricing
  const pricingFailReasons = [
    { id: 'overpaid', label: 'Overpaid for item' },
    { id: 'underpaid', label: 'Underpaid for item' },
    { id: 'no_market_research', label: 'No market research conducted' },
    { id: 'incorrect_valuation', label: 'Incorrect valuation method' },
    { id: 'pricing_policy_violation', label: 'Pricing policy violation' },
  ];

  // Common fail reasons for authorized limit check
  const limitFailReasons = [
    { id: 'exceeded_limit', label: 'Exceeded authorized limit' },
    { id: 'no_authorization', label: 'No authorization obtained' },
    { id: 'wrong_approver', label: 'Wrong approver used' },
    { id: 'missing_documentation', label: 'Missing authorization documentation' },
  ];

  // Function to render status badge
  const renderStatusBadge = (status: string) => {
    if (status === 'pass') {
      return (
        <Badge className="bg-green-500 hover:bg-green-600 flex items-center gap-1">
          <CheckCircle className="h-3 w-3" />
          Pass
        </Badge>
      );
    } else if (status === 'fail') {
      return (
        <Badge className="bg-red-500 hover:bg-red-600 flex items-center gap-1">
          <XCircle className="h-3 w-3" />
          Fail
        </Badge>
      );
    } else {
      return (
        <Badge variant="outline" className="flex items-center gap-1">
          <AlertCircle className="h-3 w-3" />
          Not Assessed
        </Badge>
      );
    }
  };

  return (
    <div className="space-y-6">
      {/* Pricing Assessment Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-5 w-5 text-primary" />
              <CardTitle>Pricing Assessment</CardTitle>
            </div>
            <FormField
              control={control}
              name="pricing.status"
              render={({ field }) => renderStatusBadge(field.value)}
            />
          </div>
          <CardDescription>
            Assess whether the pricing was appropriate and followed company guidelines
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <FormField
            control={control}
            name="pricing.status"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>Assessment</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    value={field.value}
                    className="flex flex-row space-x-6"
                    disabled={disabled}
                  >
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="pass" id="pricingPass" />
                      </FormControl>
                      <FormLabel className="font-normal cursor-pointer" htmlFor="pricingPass">
                        Pass
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="fail" id="pricingFail" />
                      </FormControl>
                      <FormLabel className="font-normal cursor-pointer" htmlFor="pricingFail">
                        Fail
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="not_assessed" id="pricingNotAssessed" />
                      </FormControl>
                      <FormLabel className="font-normal cursor-pointer" htmlFor="pricingNotAssessed">
                        Not Assessed
                      </FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Show fail reasons and suggested price if status is "fail" */}
          <FormField
            control={control}
            name="pricing.status"
            render={({ field }) => (
              <div>
                {field.value === 'fail' && (
                  <div className="space-y-4">
                    <FormField
                      control={control}
                      name="pricing.failReasons"
                      render={({ field: failReasonsField }) => (
                        <FormItem>
                          <FormLabel>Fail Reasons</FormLabel>
                          <FormDescription>Select all that apply</FormDescription>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 border rounded-md p-3">
                            {pricingFailReasons.map((reason) => (
                              <FormItem
                                key={reason.id}
                                className="flex flex-row items-start space-x-3 space-y-0 p-2 hover:bg-accent rounded-md"
                              >
                                <FormControl>
                                  <Checkbox
                                    checked={failReasonsField.value?.includes(reason.id)}
                                    onCheckedChange={(checked) => {
                                      const currentValue = failReasonsField.value || [];
                                      if (checked) {
                                        failReasonsField.onChange([...currentValue, reason.id]);
                                      } else {
                                        failReasonsField.onChange(
                                          currentValue.filter((value: string) => value !== reason.id)
                                        );
                                      }
                                    }}
                                    disabled={disabled}
                                  />
                                </FormControl>
                                <FormLabel className="font-normal cursor-pointer">
                                  {reason.label}
                                </FormLabel>
                              </FormItem>
                            ))}
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <FormField
                        control={control}
                        name="pricing.suggestedPrice"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Suggested Price</FormLabel>
                            <FormControl>
                              <Input
                                type="text"
                                placeholder="Enter suggested price"
                                {...field}
                                disabled={disabled}
                              />
                            </FormControl>
                            <FormDescription>
                              What should have been paid/offered
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={control}
                        name="pricing.costPrice"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Cost Price</FormLabel>
                            <FormControl>
                              <Input
                                type="text"
                                placeholder="Enter cost price"
                                {...field}
                                disabled={disabled}
                              />
                            </FormControl>
                            <FormDescription>
                              Actual cost to business
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={control}
                        name="pricing.ticketPrice"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Ticket Price</FormLabel>
                            <FormControl>
                              <Input
                                type="text"
                                placeholder="Enter ticket price"
                                {...field}
                                disabled={disabled}
                              />
                            </FormControl>
                            <FormDescription>
                              Intended selling price
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                )}
              </div>
            )}
          />

          <FormField
            control={control}
            name="pricing.notes"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Notes</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Enter any additional notes about pricing"
                    className="min-h-[100px]"
                    {...field}
                    disabled={disabled}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </CardContent>
      </Card>

      {/* Authorized Limit Check Section - Only for Buy and Pawn audits */}
      {auditType !== 'price' && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Shield className="h-5 w-5 text-primary" />
                <CardTitle>Authorized Limit Check</CardTitle>
              </div>
              <FormField
                control={control}
                name="authorizedLimitCheck.status"
                render={({ field }) => renderStatusBadge(field.value)}
              />
            </div>
            <CardDescription>
              Verify that the transaction was within authorized limits and properly approved
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={control}
              name="authorizedLimitCheck.status"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>Assessment</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="flex flex-row space-x-6"
                      disabled={disabled}
                    >
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="pass" id="limitPass" />
                        </FormControl>
                        <FormLabel className="font-normal cursor-pointer" htmlFor="limitPass">
                          Pass
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="fail" id="limitFail" />
                        </FormControl>
                        <FormLabel className="font-normal cursor-pointer" htmlFor="limitFail">
                          Fail
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="not_assessed" id="limitNotAssessed" />
                        </FormControl>
                        <FormLabel className="font-normal cursor-pointer" htmlFor="limitNotAssessed">
                          Not Assessed
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Show fail reasons if status is "fail" */}
            <FormField
              control={control}
              name="authorizedLimitCheck.status"
              render={({ field }) => (
                <div>
                  {field.value === 'fail' && (
                    <FormField
                      control={control}
                      name="authorizedLimitCheck.failReasons"
                      render={({ field: failReasonsField }) => (
                        <FormItem>
                          <FormLabel>Fail Reasons</FormLabel>
                          <FormDescription>Select all that apply</FormDescription>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 border rounded-md p-3">
                            {limitFailReasons.map((reason) => (
                              <FormItem
                                key={reason.id}
                                className="flex flex-row items-start space-x-3 space-y-0 p-2 hover:bg-accent rounded-md"
                              >
                                <FormControl>
                                  <Checkbox
                                    checked={failReasonsField.value?.includes(reason.id)}
                                    onCheckedChange={(checked) => {
                                      const currentValue = failReasonsField.value || [];
                                      if (checked) {
                                        failReasonsField.onChange([...currentValue, reason.id]);
                                      } else {
                                        failReasonsField.onChange(
                                          currentValue.filter((value: string) => value !== reason.id)
                                        );
                                      }
                                    }}
                                    disabled={disabled}
                                  />
                                </FormControl>
                                <FormLabel className="font-normal cursor-pointer">
                                  {reason.label}
                                </FormLabel>
                              </FormItem>
                            ))}
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}
                </div>
              )}
            />

            <FormField
              control={control}
              name="authorizedLimitCheck.notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter any additional notes about authorization limits"
                      className="min-h-[100px]"
                      {...field}
                      disabled={disabled}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>
      )}
    </div>
  );
}
