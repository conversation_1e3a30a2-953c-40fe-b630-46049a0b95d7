import api from './api';
import { BuyPawnAudit } from './buyPawnAudits';
import { BuyPawnDeal } from './buyPawnDeals';

export interface BuyPawnCatchup {
  _id: string;
  auditId: string | BuyPawnAudit;
  dealId: string | BuyPawnDeal;
  transactionId: string;
  employee: {
    _id: string;
    username?: string;
    fullName: string;
  };
  date: string;
  conductedBy: {
    _id: string;
    username?: string;
    fullName: string;
  };
  findingsDiscussed: string;
  commentsBefore?: string;
  discussionPoints: string;
  employeeResponse?: string;
  agreedActions?: string;
  commentsAfter?: string;
  status: 'draft' | 'finalized';
  createdAt: string;
  updatedAt: string;
  finalizedAt?: string;
}

export interface CatchupFilterOptions {
  status?: 'draft' | 'finalized';
  employee?: string;
  conductedBy?: string;
  startDate?: string;
  endDate?: string;
  page?: number;
  limit?: number;
  sort?: string;
  sortDirection?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  success: boolean;
  data: T[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
}

export interface CreateCatchupData {
  auditId: string;
  employee: string;
  date?: string;
  findingsDiscussed: string;
  commentsBefore?: string;
  discussionPoints: string;
  employeeResponse?: string;
  agreedActions?: string;
  commentsAfter?: string;
}

// Create a new catch-up note for an audit
export const createCatchup = async (catchupData: CreateCatchupData) => {
  try {
    const response = await api.post('/buys/catchups', catchupData);
    return response.data;
  } catch (error: any) {
    console.error('Error creating buy/pawn catchup note:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Get a catch-up note by ID
export const getCatchupById = async (id: string) => {
  try {
    const response = await api.get(`/buys/catchups/${id}`);
    return response.data;
  } catch (error: any) {
    console.error('Error getting buy/pawn catchup note by ID:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Get catch-up notes by audit ID
export const getCatchupsByAuditId = async (auditId: string) => {
  try {
    const response = await api.get(`/buys/catchups/audit/${auditId}`);
    return response.data;
  } catch (error: any) {
    console.error('Error getting buy/pawn catchup notes by audit ID:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Get catch-up notes by employee ID
export const getCatchupsByEmployeeId = async (employeeId: string) => {
  try {
    const response = await api.get(`/buys/catchups/employee/${employeeId}`);
    return response.data;
  } catch (error: any) {
    console.error('Error getting buy/pawn catchup notes by employee ID:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Update a catch-up note
export const updateCatchup = async (id: string, catchupData: Partial<BuyPawnCatchup>) => {
  try {
    const response = await api.put(`/buys/catchups/${id}`, catchupData);
    return response.data;
  } catch (error: any) {
    console.error('Error updating buy/pawn catchup note:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Get catch-up notes with filtering and pagination
export const getCatchups = async (options: CatchupFilterOptions = {}): Promise<PaginatedResponse<BuyPawnCatchup>> => {
  try {
    const response = await api.get('/buys/catchups', { params: options });
    return response.data;
  } catch (error: any) {
    console.error('Error getting buy/pawn catchup notes:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};
