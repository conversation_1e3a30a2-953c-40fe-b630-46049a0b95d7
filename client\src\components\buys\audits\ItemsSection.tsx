import React from 'react';
import { Control, useFieldArray, useWatch } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Plus, Trash2 } from 'lucide-react';
import { useToast } from '@/hooks/useToast';

export interface AuditItem {
  stockcode: string;
  brand: string;
  description: string;
  cost: string; // String for form handling, will be converted to number
}

interface ItemsSectionProps {
  control: Control<any>;
  disabled?: boolean;
  transactionType: 'buy' | 'pawn' | 'price';
  transactionId: string;
  transactionAmount: number;
}

// Function to generate stockcode
const generateStockcode = (transactionType: string, transactionId: string, itemNumber: number): string => {
  const prefix = transactionType === 'pawn' ? 'A' : 'B';
  return `${prefix}${transactionId}-${itemNumber}`;
};

export function ItemsSection({
  control,
  disabled = false,
  transactionType,
  transactionId,
  transactionAmount
}: ItemsSectionProps) {
  const { toast } = useToast();
  const { fields, append, remove } = useFieldArray({
    control,
    name: 'items',
  });

  // Watch all items to calculate running total
  const watchedItems = useWatch({
    control,
    name: 'items',
  }) || [];

  // Calculate running total
  const runningTotal = watchedItems.reduce((total: number, item: AuditItem) => {
    const cost = parseFloat(item.cost || '0');
    return total + (isNaN(cost) ? 0 : cost);
  }, 0);

  // Check if totals match
  const totalsMatch = Math.abs(runningTotal - transactionAmount) < 0.01;
  const hasItems = fields.length > 0;

  // Add new item
  const addItem = () => {
    const itemNumber = fields.length + 1;
    const stockcode = generateStockcode(transactionType, transactionId, itemNumber);
    
    append({
      stockcode,
      brand: '',
      description: '',
      cost: ''
    });
  };

  // Remove item and regenerate stockcodes
  const removeItem = (index: number) => {
    if (fields.length <= 1) {
      toast({
        title: 'Cannot Remove',
        description: 'At least one item is required.',
        variant: 'destructive',
      });
      return;
    }

    remove(index);
    
    // Regenerate stockcodes for remaining items
    setTimeout(() => {
      const currentItems = control._getWatch('items');
      currentItems.forEach((item: AuditItem, idx: number) => {
        const newStockcode = generateStockcode(transactionType, transactionId, idx + 1);
        control._setValue(`items.${idx}.stockcode`, newStockcode);
      });
    }, 0);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Transaction Items</CardTitle>
        <CardDescription>
          Add items involved in this transaction. The sum of all item costs must equal the transaction amount.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Cost Validation Alert - Only show when items have costs entered and don't match */}
        {hasItems && !totalsMatch && runningTotal > 0 && (
          <div className="p-3 border border-destructive/20 bg-destructive/5 rounded-md">
            <p className="text-sm text-destructive font-medium">
              Cost Mismatch: Item costs (${runningTotal.toFixed(2)}) must equal transaction amount (${transactionAmount.toFixed(2)})
            </p>
          </div>
        )}

        {/* Items List */}
        <div className="space-y-4">
          {fields.map((field, index) => (
            <div key={field.id} className="border rounded-lg p-4 bg-muted/20">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium">Item {index + 1}</h4>
                {fields.length > 1 && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeItem(index)}
                    disabled={disabled}
                    className="text-destructive hover:text-destructive"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* Stockcode - Auto-generated, editable */}
                <FormField
                  control={control}
                  name={`items.${index}.stockcode`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Stockcode</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          disabled={disabled}
                          placeholder="Auto-generated"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Brand */}
                <FormField
                  control={control}
                  name={`items.${index}.brand`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Brand</FormLabel>
                      <FormControl>
                        <Input 
                          {...field} 
                          disabled={disabled}
                          placeholder="Enter brand"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Description */}
                <FormField
                  control={control}
                  name={`items.${index}.description`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Input 
                          {...field} 
                          disabled={disabled}
                          placeholder="Enter description"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Cost */}
                <FormField
                  control={control}
                  name={`items.${index}.cost`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Cost</FormLabel>
                      <FormControl>
                        <Input 
                          {...field} 
                          type="number"
                          step="0.01"
                          min="0"
                          disabled={disabled}
                          placeholder="0.00"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          ))}
        </div>

        {/* Add Item Button */}
        <div className="flex justify-center pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={addItem}
            disabled={disabled}
            className="w-full md:w-auto"
          >
            <Plus className="mr-2 h-4 w-4" />
            Add New Item
          </Button>
        </div>

        {/* Summary */}
        {hasItems && (
          <>
            <Separator />
            <div className="flex justify-between items-center text-sm">
              <span className="font-medium">Total Items: {fields.length}</span>
              <span className={`font-medium ${totalsMatch ? 'text-green-600' : 'text-destructive'}`}>
                Running Total: ${runningTotal.toFixed(2)}
              </span>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}
