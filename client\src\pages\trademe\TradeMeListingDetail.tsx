import { useEffect, useState } from 'react';
import { useParams, useLocation, Link, useNavigate } from 'react-router-dom';
import { getItemById as getListing, answerQuestion } from '@/api/tradeMeItems';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/useToast';
import { Textarea } from '@/components/ui/textarea';
import { TradeMeWatchlistIcon } from '@/components/trademe/TradeMeIcons';
import { TradeMeWithdrawModal } from '@/components/trademe/TradeMeWithdrawModal';

import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import {
  ShoppingBag,
  Calendar,
  Clock,
  User,
  MapPin,
  Truck,
  CreditCard,
  MessageSquare,
  ArrowLeft,
  Eye,
  Flag,
  Info,
  Package,
  AlertTriangle,
  CheckCircle,
  Send,
  ExternalLink,
  Edit,
  Trash2,
  Star,
  StarHalf,
  Tag,
  DollarSign,
  X
} from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";

export function TradeMeListingDetail() {
  const navigate = useNavigate();
  const location = useLocation();
  const params = useParams();
  const { toast } = useToast();

  // Extract the ID from the URL
  const listingId = params.listingId || (() => {
    const pathParts = location.pathname.split('/');
    return pathParts[pathParts.length - 1];
  })();

  // State variables
  const [listing, setListing] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [questions, setQuestions] = useState<any[]>([]);
  const [loadingQuestions, setLoadingQuestions] = useState(false);
  const [templates, setTemplates] = useState<any[]>([]);
  const [groupedTemplates, setGroupedTemplates] = useState<Record<string, any[]>>({});
  const [selectedTemplate, setSelectedTemplate] = useState("");
  const [answerText, setAnswerText] = useState("");
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  // Withdraw dialog state
  const [withdrawDialogOpen, setWithdrawDialogOpen] = useState(false);
  const [isWithdrawing, setIsWithdrawing] = useState(false);

  // Fetch listing data
  useEffect(() => {
    if (listingId) {
      fetchListing(listingId);
      fetchQuestions(listingId);
      fetchTemplates();
    } else {
      setLoading(false);
      console.error('No listing ID available from route parameters');
    }
  }, [listingId]);

  // Fetch listing details
  const fetchListing = async (id: string) => {
    try {
      setLoading(true);
      console.log('Fetching listing with ID:', id);
      const response = await getListing(id);
      if (response.success) {
        // Set the listing data
        setListing(response.item);
      } else {
        toast({
          title: 'Error',
          description: 'Failed to load listing details',
          variant: 'destructive',
        });
      }
    } catch (error: any) {
      console.error('Failed to fetch listing:', error);
      toast({
        title: 'Error',
        description: error.message || 'An error occurred',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch questions for this listing
  const fetchQuestions = async (id: string) => {
    try {
      setLoadingQuestions(true);
      // For now, we'll just use the questions from the listing
      // In the future, we can implement a separate API endpoint for questions
      const response = await getListing(id);
      if (response.success && response.item) {
        setQuestions(response.item.questions || []);
      }
    } catch (error: any) {
      console.error('Failed to fetch questions:', error);
      toast({
        title: 'Error',
        description: 'Failed to load questions',
        variant: 'destructive',
      });
    } finally {
      setLoadingQuestions(false);
    }
  };

  // Fetch question templates
  const fetchTemplates = async () => {
    try {
      // For now, we'll just use empty templates
      // In the future, we can implement a separate API endpoint for templates
      const templatesData: any[] = [];
      setTemplates(templatesData);

      // Group templates by category
      const grouped = templatesData.reduce((acc: any, template: any) => {
        const category = template.category || 'General';
        if (!acc[category]) {
          acc[category] = [];
        }
        acc[category].push(template);
        return acc;
      }, {});

      setGroupedTemplates(grouped);
    } catch (error) {
      console.error('Failed to fetch templates:', error);
    }
  };

  // Handle template selection
  const handleTemplateChange = (value: string) => {
    setSelectedTemplate(value);

    // If "custom" is selected, don't change the answer text
    if (value === 'custom') {
      return;
    }

    // Find the template and set the answer text
    const template = templates.find(t => t._id === value);
    if (template) {
      setAnswerText(template.content || template.title || '');
    }
  };

  // Handle submitting an answer
  const handleSubmitAnswer = async (questionId: string) => {
    if (!answerText.trim()) {
      toast({
        title: 'Error',
        description: 'Please enter an answer',
        variant: 'destructive',
      });
      return;
    }

    try {
      setSubmitting(true);
      const response = await answerQuestion(listingId, questionId, answerText);
      if (response.success) {
        toast({
          title: 'Success',
          description: 'Answer submitted successfully',
        });
        setAnswerText("");
        setReplyingTo(null);
        // Refresh questions
        fetchQuestions(listingId);
      } else {
        toast({
          title: 'Error',
          description: response.message || 'Failed to submit answer',
          variant: 'destructive',
        });
      }
    } catch (error: any) {
      console.error('Error submitting answer:', error);
      toast({
        title: 'Error',
        description: error.message || 'An error occurred',
        variant: 'destructive',
      });
    } finally {
      setSubmitting(false);
    }
  };

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-NZ', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Function to render feedback stars
  const renderFeedbackStars = (positive: number, negative: number) => {
    const total = positive + negative;
    if (!total) return <span className="text-muted-foreground">No feedback</span>;

    const percentage = Math.round((positive / total) * 100);
    let color = "text-green-500";

    if (percentage < 80) color = "text-red-500";
    else if (percentage < 95) color = "text-amber-500";

    return (
      <div className="flex items-center">
        <span className={`font-medium ${color}`}>{percentage}%</span>
        <div className="flex ml-2">
          {[...Array(5)].map((_, i) => {
            if (percentage >= (i + 1) * 20) {
              return <Star key={i} className={`h-4 w-4 ${color}`} />;
            } else if (percentage > i * 20) {
              return <StarHalf key={i} className={`h-4 w-4 ${color}`} />;
            } else {
              return <Star key={i} className="h-4 w-4 text-muted-foreground" />;
            }
          })}
        </div>
        <span className="ml-1 text-sm text-muted-foreground">
          ({total} ratings)
        </span>
      </div>
    );
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-500 hover:bg-green-600">Active</Badge>;
      case 'sold':
        return <Badge className="bg-blue-500 hover:bg-blue-600">Sold</Badge>;
      case 'ended':
      case 'unsold':
        return <Badge variant="outline">Unsold</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getSaleStatusText = (status: string | number) => {
    switch (status) {
      case 'email_sent':
      case 10: return "Email Sent";
      case 'bank_payment_received':
      case 'instant_payment_received':
      case 20: return "Payment Received";
      case 'awaiting_packaging':
      case 'awaiting_packaging_paid':
      case 30: return "Goods Shipped";
      case 40: return "Sale Completed";
      default: return "Unknown";
    }
  };

  const getSaleStatusIcon = (status: string | number) => {
    switch (status) {
      case 'email_sent':
      case 10: return <MessageSquare className="h-4 w-4 text-blue-500" />;
      case 'bank_payment_received':
      case 'instant_payment_received':
      case 20: return <CreditCard className="h-4 w-4 text-green-500" />;
      case 'awaiting_packaging':
      case 'awaiting_packaging_paid':
      case 30: return <Truck className="h-4 w-4 text-orange-500" />;
      case 40: return <CheckCircle className="h-4 w-4 text-green-500" />;
      default: return <Info className="h-4 w-4 text-gray-500" />;
    }
  };

  // Format description with Markdown-like syntax
  const formatDescription = (text: string) => {
    if (!text) return '';

    // Create a safe version of the text
    let formattedText = text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;');

    // Apply simple formatting without changing spacing
    // Bold and Italic: ***text***
    formattedText = formattedText.replace(/\*\*\*(.*?)\*\*\*/g, '<strong><em>$1</em></strong>');

    // Bold: **text**
    formattedText = formattedText.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

    // Italic: *text*
    formattedText = formattedText.replace(/\*(.*?)\*/g, '<em>$1</em>');

    // Strikethrough: ~~text~~
    formattedText = formattedText.replace(/~~(.*?)~~/g, '<del>$1</del>');

    // Simple bullet list: - item (just add a bullet point)
    formattedText = formattedText.replace(/^- (.*?)$/gm, '• $1');

    return formattedText;
  };

  // Helper function to safely parse price values from various formats
  const safeParsePrice = (value: any): number => {
    if (value === null || value === undefined) {
      return 0;
    }

    if (typeof value === 'number') {
      return value;
    }

    // Handle Decimal128 objects from MongoDB
    if (typeof value === 'object') {
      // Check if it has a toString method (like Decimal128)
      if (value.toString && typeof value.toString === 'function') {
        // Some Decimal128 objects have a toString method that returns a string like "NumberDecimal(\"123.45\")"
        const stringValue = value.toString();

        // Try to extract the number from NumberDecimal format if present
        const match = stringValue.match(/NumberDecimal\(\"?([0-9.]+)\"?\)/);
        if (match && match[1]) {
          return parseFloat(match[1]);
        }

        // Otherwise just try to parse the string representation
        const parsed = parseFloat(stringValue);
        return isNaN(parsed) ? 0 : parsed;
      }

      // If it has a value property (some MongoDB objects are wrapped)
      if (value.value !== undefined) {
        return safeParsePrice(value.value);
      }
    }

    if (typeof value === 'string') {
      const parsed = parseFloat(value);
      return isNaN(parsed) ? 0 : parsed;
    }

    return 0;
  };

  // Calculate time remaining for auction
  const getTimeRemaining = (endDate: string) => {
    if (!endDate) return null;

    const end = new Date(endDate);
    const now = new Date();
    const diff = end.getTime() - now.getTime();

    if (diff <= 0) return "Ended";

    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    // If less than 12 hours remaining, return in red text
    if (hours < 12) {
      return <span className="text-red-500 font-bold">{hours}h {minutes}m remaining</span>;
    }

    const days = Math.floor(hours / 24);
    const remainingHours = hours % 24;

    return `${days}d ${remainingHours}h ${minutes}m remaining`;
  };

  // Handle withdraw listing
  const handleWithdrawListing = async (reason: string) => {
    if (!reason.trim()) {
      toast({
        title: "Error",
        description: "Please provide a reason for withdrawing the listing",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsWithdrawing(true);

      // This function will be implemented later
      console.log(`Withdrawing listing ${listing._id} with reason: ${reason}`);

      // For now, just show a toast notification
      toast({
        title: 'Not Implemented',
        description: 'Withdraw functionality will be implemented soon.',
        variant: 'default',
      });

      // In the future, this will call the API to withdraw the listing
      // await withdrawListing(listing._id, reason);

      // Close the dialog
      setWithdrawDialogOpen(false);

      // Refresh the listing
      // fetchListing(listingId);
    } catch (error: any) {
      console.error('Error withdrawing listing:', error);
      toast({
        title: 'Error',
        description: error.message || 'An error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsWithdrawing(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-lg">Loading listing details...</p>
        </div>
      </div>
    );
  }

  if (!listing) {
    return (
      <div className="text-center py-12">
        <AlertTriangle className="h-12 w-12 mx-auto text-amber-500" />
        <h3 className="mt-4 text-lg font-medium">Listing not found</h3>
        <p className="text-muted-foreground mt-2">
          The listing you're looking for doesn't exist or has been removed.
          {!listingId && <span className="block mt-2 text-red-500">No listing ID was found in the URL.</span>}
        </p>
        <Button asChild className="mt-4" onClick={() => navigate(-1)}>
          <div>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </div>
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Withdraw Modal */}
      <TradeMeWithdrawModal
        open={withdrawDialogOpen}
        onOpenChange={setWithdrawDialogOpen}
        onWithdraw={handleWithdrawListing}
        isSubmitting={isWithdrawing}
      />

      {/* Navigation and Action Buttons */}
      <div className="flex flex-wrap gap-2 justify-between items-center">
        <Button
          variant="outline"
          onClick={() => navigate(-1)}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>

        <div className="flex gap-2">
          <Button
            variant="outline"
            disabled={!listing.isOurListing || listing.status !== 'active' || listing.status === 'sold'}
            asChild
          >
            <Link to={`/trademe/listing/edit/${listing._id}`}>
              <Edit className="mr-2 h-4 w-4" />
              Edit Listing
            </Link>
          </Button>

          <Button
            variant="outline"
            disabled={listing.status !== 'active'}
            onClick={() => setWithdrawDialogOpen(true)}
          >
            <Trash2 className="mr-2 h-4 w-4" />
            Withdraw Listing
          </Button>

          <Button variant="outline" asChild>
            <a
              href={`https://${listing.environment === 'production' ? 'www' : 'tmsandbox'}.trademe.co.nz/Browse/Listing.aspx?id=${listing.currentListingId || listing.trademeListingId}`}
              target="_blank"
              rel="noopener noreferrer"
            >
              <ExternalLink className="mr-2 h-4 w-4" />
              Show on Trademe
            </a>
          </Button>
        </div>
      </div>

      {/* Status-specific information sections */}
      {listing.status === 'sold' && (
        <Card className="border-green-500 mb-6 overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-green-50 to-white dark:from-green-950/30 dark:to-background p-4 pb-2 border-b">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <h3 className="text-lg font-semibold">Sold Information</h3>
              </div>
              <Badge className="bg-green-500">{getSaleStatusText(listing.saleStatus || listing.soldStatus)}</Badge>
            </div>
          </CardHeader>

          <CardContent className="p-0">
            <div className="flex items-center gap-2 p-3 bg-muted/50 border-b">
              {getSaleStatusIcon(listing.saleStatus || listing.soldStatus)}
              <span className="font-medium">{getSaleStatusText(listing.saleStatus || listing.soldStatus)}</span>
              {(listing.statusDate || listing.soldStatusUpdatedAt) && (
                <span className="text-sm text-muted-foreground ml-2">
                  Updated: {formatDate(listing.statusDate || listing.soldStatusUpdatedAt)}
                  {listing.soldStatusUpdatedBy && (
                    <span> by {listing.soldStatusUpdatedBy.username || listing.soldStatusUpdatedBy.fullName}</span>
                  )}
                </span>
              )}
            </div>

            {/* 2x2 Grid Layout */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-0 divide-y md:divide-y-0 md:divide-x divide-border">
              {/* Sale Details Section */}
              <div className="p-4">
                <h4 className="text-md font-medium mb-3 flex items-center">
                  <DollarSign className="mr-2 h-4 w-4 text-green-500" />
                  Sale Details
                </h4>
                <div className="grid grid-cols-1 gap-3">
                  <div>
                    <h4 className="text-sm text-muted-foreground">Sold Price</h4>
                    <p className="font-bold text-lg">
                      ${(() => {
                        try {
                          if (listing.soldPrice) {
                            const price = safeParsePrice(listing.soldPrice);
                            return price.toFixed(2);
                          } else if (listing.closingBid) {
                            const price = safeParsePrice(listing.closingBid);
                            return price.toFixed(2);
                          } else if (listing.price) {
                            const price = safeParsePrice(listing.price);
                            return price.toFixed(2);
                          }
                          return '0.00';
                        } catch (e) {
                          console.error('Error formatting sold price:', e);
                          return '0.00';
                        }
                      })()}
                    </p>
                  </div>

                  <div>
                    <h4 className="text-sm text-muted-foreground">Total Sale Price</h4>
                    <p className="font-bold text-lg">
                      ${(() => {
                        try {
                          if (listing.totalSalePrice) {
                            const price = safeParsePrice(listing.totalSalePrice);
                            return price.toFixed(2);
                          } else if (listing.soldPrice) {
                            const price = safeParsePrice(listing.soldPrice);
                            return price.toFixed(2);
                          } else if (listing.closingBid) {
                            const price = safeParsePrice(listing.closingBid);
                            return price.toFixed(2);
                          } else if (listing.price) {
                            const price = safeParsePrice(listing.price);
                            return price.toFixed(2);
                          }
                          return '0.00';
                        } catch (e) {
                          console.error('Error formatting total sale price:', e);
                          return '0.00';
                        }
                      })()}
                    </p>
                  </div>

                  <div>
                    <h4 className="text-sm text-muted-foreground">Reference Number</h4>
                    <p className="font-medium">{listing.referenceNumber || listing.buyerPaymentInfo?.referenceNumber || 'N/A'}</p>
                  </div>

                  <div>
                    <h4 className="text-sm text-muted-foreground">Sold Date</h4>
                    <p className="font-medium">{formatDate(listing.soldDate)}</p>
                  </div>
                </div>
              </div>

              {/* Buyer Information Section */}
              <div className="p-4 border-t md:border-t-0">
                <h4 className="text-md font-medium mb-3 flex items-center">
                  <User className="mr-2 h-4 w-4 text-green-500" />
                  Buyer Information
                </h4>
                <div className="grid grid-cols-1 gap-3">
                  <div className="flex items-center gap-2">
                    <div className="flex-1">
                      <h4 className="text-sm text-muted-foreground">TradeMe Username</h4>
                      <p className="font-medium">
                        {listing.buyer?.nickname ||
                         listing.buyer?.name ||
                         listing.buyerPaymentInfo?.tradeMeBuyer ||
                         'N/A'}
                      </p>
                    </div>
                    <div className="flex-1">
                      <h4 className="text-sm text-muted-foreground">Feedback Rating</h4>
                      <div>
                        {renderFeedbackStars(
                          listing.buyer?.uniquePositive || 0,
                          listing.buyer?.uniqueNegative || 0
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-2 bg-muted/30 p-2 rounded-md">
                    <div>
                      <h4 className="text-sm text-muted-foreground">Seller Feedback</h4>
                      <p className="font-medium">
                        {listing.buyerPaymentInfo?.hasSellerPlacedFeedback ? (
                          <span className="flex items-center text-green-500">
                            <CheckCircle className="h-4 w-4 mr-1" /> Placed
                          </span>
                        ) : (
                          <span className="flex items-center text-amber-500">
                            <AlertTriangle className="h-4 w-4 mr-1" /> Not Placed
                          </span>
                        )}
                      </p>
                    </div>

                    <div>
                      <h4 className="text-sm text-muted-foreground">Buyer Feedback</h4>
                      <p className="font-medium">
                        {listing.buyerPaymentInfo?.hasBuyerPlacedFeedback ? (
                          <span className="flex items-center text-green-500">
                            <CheckCircle className="h-4 w-4 mr-1" /> Placed
                          </span>
                        ) : (
                          <span className="flex items-center text-amber-500">
                            <AlertTriangle className="h-4 w-4 mr-1" /> Not Placed
                          </span>
                        )}
                      </p>
                    </div>
                  </div>

                  {/* Message from Buyer */}
                  {listing.buyerPaymentInfo?.messageFromBuyer && (
                    <div>
                      <h4 className="text-sm text-muted-foreground mb-1 flex items-center">
                        <MessageSquare className="h-3 w-3 mr-1 text-muted-foreground" />
                        Message from Buyer
                      </h4>
                      <div className="p-2 bg-muted rounded-md text-sm">
                        <p className="whitespace-pre-line line-clamp-3">{listing.buyerPaymentInfo.messageFromBuyer}</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Delivery Information Section */}
              <div className="p-4 border-t">
                <h4 className="text-md font-medium mb-3 flex items-center">
                  <Truck className="mr-2 h-4 w-4 text-green-500" />
                  Delivery Information
                </h4>

                {/* Selected Shipping Method */}
                <div className="mb-3 bg-muted/30 p-2 rounded-md">
                  <h4 className="text-sm text-muted-foreground flex items-center">
                    <Package className="h-3 w-3 mr-1 text-muted-foreground" />
                    Selected Shipping Method
                  </h4>
                  <p className="font-medium">
                    {listing.buyerPaymentInfo?.selectedShipping || listing.selectedShipping || 'N/A'}
                    {(() => {
                      // Find the shipping cost for the selected method
                      if (listing.shippingOptions && listing.shippingOptions.length > 0) {
                        const selectedOption = listing.shippingOptions.find(
                          (option: any) => option.name === (listing.buyerPaymentInfo?.selectedShipping || listing.selectedShipping)
                        );
                        if (selectedOption && (selectedOption.cost || selectedOption.price)) {
                          try {
                            const price = safeParsePrice(selectedOption.cost || selectedOption.price);
                            return ` ($${price.toFixed(2)})`;
                          } catch (e) {
                            console.error('Error formatting shipping price:', e);
                            return '';
                          }
                        }
                      }
                      return '';
                    })()}
                  </p>
                </div>

                {/* Delivery Address */}
                {listing.buyerDeliveryAddress ? (
                  <div>
                    <h4 className="text-sm font-medium mb-1 flex items-center">
                      <MapPin className="h-3 w-3 mr-1 text-muted-foreground" />
                      Delivery Address
                    </h4>
                    <div className="p-2 bg-muted rounded-md text-sm">
                      {listing.buyerDeliveryAddress.name && <p className="font-medium">{listing.buyerDeliveryAddress.name}</p>}
                      {listing.buyerDeliveryAddress.phoneNumber && <p className="text-xs text-muted-foreground">Phone: {listing.buyerDeliveryAddress.phoneNumber}</p>}
                      {listing.buyerDeliveryAddress.address1 && <p>{listing.buyerDeliveryAddress.address1}</p>}
                      {listing.buyerDeliveryAddress.address2 && <p>{listing.buyerDeliveryAddress.address2}</p>}
                      <p>
                        {listing.buyerDeliveryAddress.suburb && `${listing.buyerDeliveryAddress.suburb}, `}
                        {listing.buyerDeliveryAddress.city && `${listing.buyerDeliveryAddress.city} `}
                        {listing.buyerDeliveryAddress.postcode && listing.buyerDeliveryAddress.postcode}
                      </p>
                      {listing.buyerDeliveryAddress.country && <p>{listing.buyerDeliveryAddress.country}</p>}
                    </div>
                  </div>
                ) : (
                  <div className="p-2 border border-yellow-300 bg-yellow-50 dark:bg-yellow-900/20 dark:border-yellow-800 rounded-md text-sm">
                    <div className="flex items-center gap-2 mb-1">
                      <AlertTriangle className="h-4 w-4 text-yellow-500" />
                      <p className="font-medium">No delivery address provided</p>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      This may be because the item was picked up in person or the address information was not imported.
                    </p>
                  </div>
                )}
              </div>

              {/* Payment Information Section */}
              <div className="p-4 border-t md:border-l">
                <h4 className="text-md font-medium mb-3 flex items-center">
                  <CreditCard className="mr-2 h-4 w-4 text-green-500" />
                  Payment Information
                </h4>

                <div className="grid grid-cols-1 gap-3">
                  <div className="flex items-center gap-2">
                    {/* Payment Type */}
                    <div className="flex-1">
                      <h4 className="text-sm text-muted-foreground">Payment Method</h4>
                      <p className="font-medium">
                        {listing.buyerPaymentInfo?.paymentType ||
                         (Array.isArray(listing.paymentMethods) && listing.paymentMethods.length > 0 ?
                          listing.paymentMethods.join(', ') : 'N/A')}
                      </p>
                    </div>

                    {/* Payment Status */}
                    <div className="flex-1">
                      <h4 className="text-sm text-muted-foreground">Payment Status</h4>
                      <p className="font-medium">
                        {listing.buyerPaymentInfo?.isPaymentPending ? (
                          <span className="flex items-center text-amber-500">
                            <Clock className="h-4 w-4 mr-1" /> Pending
                          </span>
                        ) : (
                          <span className="flex items-center text-green-500">
                            <CheckCircle className="h-4 w-4 mr-1" /> Completed
                          </span>
                        )}
                      </p>
                    </div>
                  </div>

                  <div className="bg-muted/30 p-2 rounded-md">
                    {/* Credit Card Information */}
                    {listing.buyerPaymentInfo?.hasPaidByCreditCard && (
                      <div className="mb-2">
                        <h4 className="text-sm text-muted-foreground">Credit Card</h4>
                        <p className="font-medium">
                          {listing.buyerPaymentInfo.creditCardType || 'Credit Card'}
                          {listing.buyerPaymentInfo.creditCardLastFourDigits &&
                            ` (xxxx-xxxx-xxxx-${listing.buyerPaymentInfo.creditCardLastFourDigits})`}
                        </p>
                      </div>
                    )}

                    {/* Payment Date */}
                    {listing.buyerPaymentInfo?.creditCardPaymentDate && (
                      <div className="mb-2">
                        <h4 className="text-sm text-muted-foreground">Payment Date</h4>
                        <p className="font-medium">{formatDate(listing.buyerPaymentInfo.creditCardPaymentDate)}</p>
                      </div>
                    )}

                    {/* Clearance Flag */}
                    {listing.buyerPaymentInfo?.isClearance !== undefined && (
                      <div>
                        <h4 className="text-sm text-muted-foreground">Clearance Sale</h4>
                        <p className="font-medium">
                          {listing.buyerPaymentInfo.isClearance ? (
                            <span className="flex items-center text-green-500">
                              <CheckCircle className="h-4 w-4 mr-1" /> Yes
                            </span>
                          ) : (
                            <span className="flex items-center">
                              <X className="h-4 w-4 mr-1" /> No
                            </span>
                          )}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Title and Category Path */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">{listing.title}</h1>
        {listing.categoryPath && (
          <div className="flex items-center text-sm text-muted-foreground mt-1">
            <Tag className="h-4 w-4 mr-1.5" />
            <span className="font-medium">Category:</span>
            <div className="ml-1.5">
              {listing.categoryPath
                .replace(/\//g, ' > ')  // Replace slashes with >
                .replace(/^\/|\/$/g, '') // Remove leading and trailing slashes
                .replace(/>/g, ' > ') // Ensure proper spacing around >
                .split(' > ')
                .map((category: string, index: number, array: string[]) => (
                  <span key={index}>
                    <span className={index === array.length - 1 ? "font-medium" : ""}>
                      {category}
                    </span>
                    {index < array.length - 1 && (
                      <span className="mx-1 text-muted-foreground/60">›</span>
                    )}
                  </span>
                ))
              }
            </div>
          </div>
        )}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Images and Description */}
        <div className="lg:col-span-2 space-y-6">
          {/* Image Gallery */}
          <Card>
            <CardContent className="p-6">
              {listing.images && listing.images.length > 0 ? (
                <div className="space-y-4">
                  <div className="bg-muted rounded-md overflow-hidden flex items-center justify-center h-[400px]">
                    <img
                      src={listing.images[selectedImageIndex]}
                      alt={listing.title}
                      className="w-full h-auto object-contain max-h-[400px]"
                      onError={(e) => {
                        // If the image fails to load, show the placeholder
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                        target.parentElement?.classList.add('flex', 'items-center', 'justify-center');
                        const placeholder = document.createElement('div');
                        placeholder.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-muted-foreground"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="17 8 12 3 7 8"></polyline><line x1="12" y1="3" x2="12" y2="15"></line></svg>';
                        target.parentElement?.appendChild(placeholder);
                      }}
                    />
                  </div>

                  {listing.images.length > 1 && (
                    <Carousel className="w-full">
                      <CarouselContent>
                        {listing.images.map((image: any, index: number) => (
                          <CarouselItem key={index} className="basis-1/5">
                            <div
                              className={`h-20 cursor-pointer rounded-md overflow-hidden ${selectedImageIndex === index ? 'ring-2 ring-primary' : ''}`}
                              onClick={() => setSelectedImageIndex(index)}
                            >
                              <img
                                src={image}
                                alt={`${listing.title} - image ${index + 1}`}
                                className="w-full h-full object-cover"
                                onError={(e) => {
                                  // If the image fails to load, show a placeholder
                                  const target = e.target as HTMLImageElement;
                                  target.style.display = 'none';
                                  target.parentElement?.classList.add('flex', 'items-center', 'justify-center');
                                  const placeholder = document.createElement('div');
                                  placeholder.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-muted-foreground"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="17 8 12 3 7 8"></polyline><line x1="12" y1="3" x2="12" y2="15"></line></svg>';
                                  target.parentElement?.appendChild(placeholder);
                                }}
                              />
                            </div>
                          </CarouselItem>
                        ))}
                      </CarouselContent>
                      <CarouselPrevious />
                      <CarouselNext />
                    </Carousel>
                  )}
                </div>
              ) : (
                <div className="flex items-center justify-center h-[400px] bg-muted rounded-md">
                  <ShoppingBag className="h-16 w-16 text-muted-foreground" />
                </div>
              )}
            </CardContent>
          </Card>

          {/* Tabs for Description and Questions */}
          <Tabs defaultValue="description">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="description">Description</TabsTrigger>
              <TabsTrigger value="questions">Questions & Answers {questions.length > 0 && `(${questions.length})`}</TabsTrigger>
            </TabsList>

            <TabsContent value="description" className="mt-4">
              <Card>
                <CardContent className="p-6">
                  <h2 className="text-xl font-semibold mb-4">Description</h2>
                  <Separator className="mb-4" />
                  <div className="description-content whitespace-pre-line">
                    {listing.description && listing.description !== 'No description provided'
                      ? <div dangerouslySetInnerHTML={{ __html: formatDescription(listing.description) }} />
                      : (
                        <div className="p-4 border border-yellow-300 bg-yellow-50 dark:bg-yellow-900/20 dark:border-yellow-800 rounded-md">
                          <div className="flex items-center gap-2 mb-2">
                            <AlertTriangle className="h-4 w-4 text-yellow-500" />
                            <p className="font-medium">No description provided</p>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            The listing description could not be retrieved from TradeMe. This may be because the listing is no longer available or there was an error fetching the data.
                          </p>
                        </div>
                      )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="questions" className="mt-4">
              <Card>
                <CardContent className="p-6 space-y-4">
                  <h2 className="text-xl font-semibold flex items-center">
                    <MessageSquare className="mr-2 h-5 w-5" />
                    Questions & Answers
                  </h2>
                  <Separator />

                  {loadingQuestions ? (
                    <div className="flex justify-center py-4">
                      <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-primary"></div>
                    </div>
                  ) : questions.length > 0 ? (
                    <div className="space-y-4">
                      {questions.map((question) => (
                        <div key={question._id} className={`border rounded-md p-4 ${question.isAnswered ? 'border-green-200 bg-green-50 dark:bg-green-950/20 dark:border-green-900' : ''}`}>
                          <div className="flex justify-between">
                            <div className="font-medium">{question.askerName}</div>
                            <div className="text-sm text-muted-foreground">{formatDate(question.askDate)}</div>
                          </div>
                          <div className="mt-2">
                            <div className="flex items-start gap-2">
                              <MessageSquare className="h-4 w-4 mt-1 text-muted-foreground" />
                              <p>{question.question}</p>
                            </div>
                          </div>

                          {/* Display answer if question is answered */}
                          {question.isAnswered && question.answer && (
                            <div className="mt-4 bg-muted p-3 rounded-md">
                              <div className="flex justify-between mb-2">
                                <div className="flex items-center">
                                  <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                                  <span className="font-medium">Answer</span>
                                </div>
                                <div className="text-sm text-muted-foreground">
                                  {formatDate(question.answerDate)}
                                </div>
                              </div>
                              <p className="ml-6">{question.answer}</p>
                              {question.answeredBy && (
                                <div className="mt-2 text-sm text-muted-foreground ml-6">
                                  Answered by: {question.answeredBy.username || 'Unknown'}
                                </div>
                              )}
                            </div>
                          )}

                          {/* Show reply button only for unanswered questions */}
                          {!question.isAnswered && replyingTo === question._id ? (
                            <div className="mt-4 space-y-4">
                              <div className="flex gap-2">
                                <Select value={selectedTemplate} onValueChange={handleTemplateChange}>
                                  <SelectTrigger className="w-[200px]">
                                    <SelectValue placeholder="Use template" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="custom">Custom Answer</SelectItem>
                                    {Object.entries(groupedTemplates).map(([category, items]) => (
                                      <SelectGroup key={category}>
                                        <SelectLabel>{category}</SelectLabel>
                                        {(items as any[]).map((template: any) => (
                                          <SelectItem key={template._id} value={template._id}>
                                            {template.title}
                                          </SelectItem>
                                        ))}
                                      </SelectGroup>
                                    ))}
                                  </SelectContent>
                                </Select>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => {
                                    setSelectedTemplate('');
                                    setAnswerText('');
                                  }}
                                >
                                  Clear
                                </Button>
                              </div>

                              <Textarea
                                placeholder="Type your answer here..."
                                value={answerText}
                                onChange={(e) => setAnswerText(e.target.value)}
                                className="min-h-[100px]"
                              />

                              <div className="flex justify-end gap-2">
                                <Button
                                  variant="outline"
                                  onClick={() => {
                                    setReplyingTo(null);
                                    setAnswerText("");
                                  }}
                                >
                                  Cancel
                                </Button>
                                <Button
                                  onClick={() => handleSubmitAnswer(question._id)}
                                  disabled={submitting || !answerText.trim()}
                                >
                                  {submitting ? (
                                    <div className="flex items-center">
                                      <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                                      Submitting...
                                    </div>
                                  ) : (
                                    <div className="flex items-center">
                                      <Send className="mr-2 h-4 w-4" />
                                      Submit Answer
                                    </div>
                                  )}
                                </Button>
                              </div>
                            </div>
                          ) : !question.isAnswered && (
                            <Button
                              variant="outline"
                              size="sm"
                              className="mt-3"
                              onClick={() => setReplyingTo(question._id)}
                            >
                              <MessageSquare className="mr-2 h-4 w-4" />
                              Reply
                            </Button>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-muted-foreground">No questions have been asked about this item.</p>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Right Column - Listing Information */}
        <div className="space-y-6">
          {/* Listing Header */}
          <Card>
            <CardContent className="p-6 space-y-4">
              <div className="flex justify-between items-start">
                <div>
                  <h2 className="text-xl font-bold">Listing Details</h2>
                </div>
                {getStatusBadge(listing.status)}
              </div>

              <div className="text-sm text-muted-foreground">
                <div className="flex items-center gap-2">
                  <span>Listing ID: {listing.currentListingId || listing.trademeListingId || 'N/A'}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Price Information */}
          <Card>
            <CardContent className="p-6 space-y-4">
              <h2 className="text-lg font-semibold flex items-center">
                <CreditCard className="mr-2 h-5 w-5" />
                Price Information
              </h2>
              <Separator />

              <div className="space-y-3">
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <span className="text-sm text-muted-foreground">Current price:</span>
                    <div className="text-lg font-bold">
                      ${listing.currentPrice ? listing.currentPrice.toFixed(2) : 'N/A'}
                    </div>
                  </div>

                  {listing.reservePrice && listing.reservePrice > 0 && (
                    <div>
                      <span className="text-sm text-muted-foreground">Reserve price:</span>
                      <div className="text-lg">${listing.reservePrice.toFixed(2)}</div>
                    </div>
                  )}

                  {listing.buyNowPrice > 0 && (
                    <div>
                      <span className="text-sm text-muted-foreground">Buy now price:</span>
                      <div className="text-lg">${listing.buyNowPrice.toFixed(2)}</div>
                    </div>
                  )}

                  {listing.status === 'sold' && (
                    <div>
                      <span className="text-sm text-muted-foreground">Closing bid:</span>
                      <div className="text-lg font-bold text-green-500">
                        ${(listing.closingBid || (listing.currentPrice) || 0).toFixed(2)}
                      </div>
                    </div>
                  )}

                  {listing.status === 'sold' && listing.totalSalePrice && (
                    <div>
                      <span className="text-sm text-muted-foreground">Total sale price:</span>
                      <div className="text-lg font-bold">${listing.totalSalePrice.toFixed(2)}</div>
                    </div>
                  )}
                </div>

                <div className="grid grid-cols-3 gap-2 pt-2">
                  {/* Get the current listing data from history */}
                  {(() => {
                    const currentListing = listing.listingHistory && listing.listingHistory.length > 0
                      ? listing.listingHistory[0]
                      : null;

                    return (
                      <>
                        <div>
                          <span className="text-sm text-muted-foreground">Views:</span>
                          <div className="flex items-center">
                            <Eye className="h-4 w-4 mr-1 text-muted-foreground" />
                            <span>{currentListing?.views || 0}</span>
                          </div>
                        </div>

                        <div>
                          <span className="text-sm text-muted-foreground">Watchers:</span>
                          <div className="flex items-center">
                            <TradeMeWatchlistIcon className="text-muted-foreground mr-1" style={{ fontSize: '14px', display: 'flex', alignItems: 'center' }} />
                            <span>{currentListing?.watchers || 0}</span>
                          </div>
                        </div>

                        <div>
                          <span className="text-sm text-muted-foreground">Bids:</span>
                          <div className="flex items-center">
                            <Flag className="h-4 w-4 mr-1 text-muted-foreground" />
                            <span>{currentListing?.bids || 0}</span>
                          </div>
                        </div>
                      </>
                    );
                  })()}

                  <div className="col-span-3">
                    <span className="text-sm text-muted-foreground">Questions:</span>
                    <div className="flex items-center">
                      <MessageSquare className="h-4 w-4 mr-1 text-muted-foreground" />
                      <span>{listing.questions?.length || 0}</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Auction Information */}
          <Card>
            <CardContent className="p-6 space-y-4">
              <h2 className="text-lg font-semibold flex items-center">
                <Clock className="mr-2 h-5 w-5" />
                Auction Information
              </h2>
              <Separator />

              <div className="space-y-3">
                <div className="grid grid-cols-1 gap-2">
                  {/* Get the current listing data from history */}
                  {(() => {
                    const currentListing = listing.listingHistory && listing.listingHistory.length > 0
                      ? listing.listingHistory[0]
                      : null;

                    return (
                      <>
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <span className="text-sm text-muted-foreground">Start date:</span>
                            <div>{formatDate(currentListing?.startDate || listing.createdAt)}</div>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <span className="text-sm text-muted-foreground">End date:</span>
                            <div>{formatDate(currentListing?.endDate || listing.endDate)}</div>
                          </div>
                        </div>

                        {listing.status === 'active' && (currentListing?.endDate || listing.endDate) && (
                          <div className="flex items-center gap-2">
                            <Clock className="h-4 w-4 text-muted-foreground" />
                            <div>
                              <span className="text-sm text-muted-foreground">Time remaining:</span>
                              <div>{getTimeRemaining(currentListing?.endDate || listing.endDate)}</div>
                            </div>
                          </div>
                        )}
                      </>
                    );
                  })()}

                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <span className="text-sm text-muted-foreground">Listed by:</span>
                      <div>{listing.createdBy?.fullName || listing.createdBy?.username || 'Unknown'}</div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Item Information */}
          <Card>
            <CardContent className="p-6 space-y-4">
              <h2 className="text-lg font-semibold flex items-center">
                <Package className="mr-2 h-5 w-5" />
                Item Information
              </h2>
              <Separator />

              <div className="space-y-3">
                <div className="grid grid-cols-1 gap-2">
                  {listing.stockCode && (
                    <div className="flex items-center gap-2">
                      <Tag className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <span className="text-sm text-muted-foreground">Stock Code:</span>
                        <div>{listing.stockCode}</div>
                      </div>
                    </div>
                  )}

                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <span className="text-sm text-muted-foreground">Item Location:</span>
                      <div>{listing.location || listing.physicalLocation || 'Not specified'}</div>
                    </div>
                  </div>

                  {(listing as any).cost !== undefined && (
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <span className="text-sm text-muted-foreground">Cost:</span>
                        <div>${((listing as any).cost || 0).toFixed(2)}</div>
                      </div>
                    </div>
                  )}

                  {(listing as any).buyer && (
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <span className="text-sm text-muted-foreground">Buyer:</span>
                        <div>{(listing as any).buyer.fullName || (listing as any).buyer.username || 'Unknown'}</div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Shipping Information */}
          <Card>
            <CardContent className="p-6 space-y-4">
              <h2 className="text-lg font-semibold flex items-center">
                <Truck className="mr-2 h-5 w-5" />
                Shipping Information
              </h2>
              <Separator />

              {listing.shippingOptions && listing.shippingOptions.length > 0 ? (
                <div className="space-y-2">
                  {listing.shippingOptions.map((option: any, index: number) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Truck className="h-4 w-4 text-muted-foreground" />
                        <span>{option.name}</span>
                      </div>
                      <div className="font-medium">
                        ${safeParsePrice(option.cost || option.price).toFixed(2)}
                      </div>
                    </div>
                  ))}
                </div>
              ) : listing.selectedShipping ? (
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Truck className="h-4 w-4 text-muted-foreground" />
                    <span>{listing.selectedShipping}</span>
                  </div>
                </div>
              ) : (
                <p className="text-muted-foreground">No shipping information available.</p>
              )}
            </CardContent>
          </Card>

          {/* Payment Methods */}
          <Card>
            <CardContent className="p-6 space-y-4">
              <h2 className="text-lg font-semibold flex items-center">
                <CreditCard className="mr-2 h-5 w-5" />
                Payment Methods
              </h2>
              <Separator />

              {/* Handle array format of payment methods */}
              {Array.isArray(listing.paymentMethods) && listing.paymentMethods.length > 0 ? (
                <div className="space-y-3">
                  {listing.paymentMethods.map((method: string, index: number) => {
                    // Determine which icon to show based on the payment method
                    switch(method) {
                      case 'Ping':
                        return (
                          <div key={index} className="flex items-center gap-2">
                            <img
                              src="/img/trademe/ping.svg"
                              alt="Ping"
                              className="h-5 w-5"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.style.display = 'none';
                                const parent = target.parentElement;
                                if (parent) {
                                  const icon = document.createElement('span');
                                  icon.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-muted-foreground"><rect x="2" y="5" width="20" height="14" rx="2"></rect><line x1="2" y1="10" x2="22" y2="10"></line></svg>';
                                  parent.insertBefore(icon, parent.firstChild);
                                }
                              }}
                            />
                            <span>Ping</span>
                          </div>
                        );
                      case 'Afterpay':
                        return (
                          <div key={index} className="flex items-center gap-2">
                            <img
                              src="/img/trademe/afterpay.svg"
                              alt="Afterpay"
                              className="h-5 w-5"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.style.display = 'none';
                                const parent = target.parentElement;
                                if (parent) {
                                  const icon = document.createElement('span');
                                  icon.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-muted-foreground"><rect x="2" y="5" width="20" height="14" rx="2"></rect><line x1="2" y1="10" x2="22" y2="10"></line></svg>';
                                  parent.insertBefore(icon, parent.firstChild);
                                }
                              }}
                            />
                            <span>Afterpay</span>
                          </div>
                        );
                      case 'BankDeposit':
                        return (
                          <div key={index} className="flex items-center gap-2">
                            <CreditCard className="h-4 w-4 text-muted-foreground" />
                            <span>Bank Deposit</span>
                          </div>
                        );
                      case 'Cash':
                        return (
                          <div key={index} className="flex items-center gap-2">
                            <DollarSign className="h-4 w-4 text-muted-foreground" />
                            <span>Cash</span>
                          </div>
                        );
                      default:
                        return (
                          <div key={index} className="flex items-center gap-2">
                            <CreditCard className="h-4 w-4 text-muted-foreground" />
                            <span>{method}</span>
                          </div>
                        );
                    }
                  })}
                </div>
              ) : listing.paymentMethods ? (
                // Handle object format of payment methods (legacy format)
                <div className="space-y-2">
                  {listing.paymentMethods.ping && (
                    <div className="flex items-center gap-2">
                      <img
                        src="/img/trademe/ping.svg"
                        alt="Ping"
                        className="h-5 w-5"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                          const parent = target.parentElement;
                          if (parent) {
                            const icon = document.createElement('span');
                            icon.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-muted-foreground"><rect x="2" y="5" width="20" height="14" rx="2"></rect><line x1="2" y1="10" x2="22" y2="10"></line></svg>';
                            parent.insertBefore(icon, parent.firstChild);
                          }
                        }}
                      />
                      <span>Ping</span>
                    </div>
                  )}
                  {listing.paymentMethods.afterpay && (
                    <div className="flex items-center gap-2">
                      <img
                        src="/img/trademe/afterpay.svg"
                        alt="Afterpay"
                        className="h-5 w-5"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                          const parent = target.parentElement;
                          if (parent) {
                            const icon = document.createElement('span');
                            icon.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-muted-foreground"><rect x="2" y="5" width="20" height="14" rx="2"></rect><line x1="2" y1="10" x2="22" y2="10"></line></svg>';
                            parent.insertBefore(icon, parent.firstChild);
                          }
                        }}
                      />
                      <span>Afterpay</span>
                    </div>
                  )}
                  {listing.paymentMethods.bankDeposit && (
                    <div className="flex items-center gap-2">
                      <CreditCard className="h-4 w-4 text-muted-foreground" />
                      <span>Bank Deposit</span>
                    </div>
                  )}
                  {listing.paymentMethods.cash && (
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4 text-muted-foreground" />
                      <span>Cash</span>
                    </div>
                  )}
                  {listing.paymentMethods.other && (
                    <div className="flex items-center gap-2">
                      <CreditCard className="h-4 w-4 text-muted-foreground" />
                      <span>Other: {listing.paymentMethods.otherDetails || 'Not specified'}</span>
                    </div>
                  )}
                </div>
              ) : listing.paymentMethod ? (
                // Handle string format (legacy format)
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <CreditCard className="h-4 w-4 text-muted-foreground" />
                    <span>{listing.paymentMethod}</span>
                  </div>
                </div>
              ) : (
                <p className="text-muted-foreground">Bank transfer or cash on pickup</p>
              )}
            </CardContent>
          </Card>


        </div>
      </div>
    </div>
  );
}
