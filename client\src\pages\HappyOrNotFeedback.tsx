import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, RefreshCw, Search } from 'lucide-react';
import { useToast } from '@/hooks/useToast';
import { Link } from 'react-router-dom';
import { DateRange } from '@/components/ui/date-range-presets';
import { FollowUpResponseChart } from '@/components/FollowUpResponseChart';
import { EnhancedDateRange } from '@/components/ui/enhanced-date-range';
import { useAuth } from '@/contexts/AuthContext';
import {
  getHappyOrNotFeedbackData,
  syncHappyOrNotFeedback,
  getHappyOrNotFollowUpResponses,
  getHappyOrNotGoalSettings,
  HappyOrNotFeedbackData,
  FollowUpResponseData,
  HappyOrNotGoalSettings
} from '@/api/happyOrNot';
import {
  ResponsiveContainer,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  AreaChart as RechartsAreaChart,
  Area,
  PieChart as RechartsPieChart,
  Pie,
  Cell
} from 'recharts';

// Define colors for different happiness levels
const COLORS = ['#e24e67', '#ef9ea1', '#99cd9b', '#0daa5d'];

export function HappyOrNotFeedback() {
  const [loading, setLoading] = useState(true);
  const [syncing, setSyncing] = useState(false);
  const [feedbackData, setFeedbackData] = useState<HappyOrNotFeedbackData | null>(null);
  const [positiveResponsesData, setPositiveResponsesData] = useState<FollowUpResponseData | null>(null);
  const [negativeResponsesData, setNegativeResponsesData] = useState<FollowUpResponseData | null>(null);
  const [loadingResponses, setLoadingResponses] = useState(false);
  const [settings, setSettings] = useState<HappyOrNotGoalSettings | null>(null);
  const [loadingSettings, setLoadingSettings] = useState(true);
  const [dateRange, setDateRange] = useState<DateRange>(() => {
    // Set default to Week to Date (Monday to current day)
    const today = new Date();
    const dayOfWeek = today.getDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
    const daysToSubtract = dayOfWeek === 0 ? 6 : dayOfWeek - 1;

    // Calculate Monday of current week
    const startDate = new Date(today);
    startDate.setDate(today.getDate() - daysToSubtract);
    startDate.setHours(0, 0, 0, 0);

    // Set end date to today at 23:59:59
    const endDate = new Date();
    endDate.setHours(23, 59, 59, 999);

    return {
      startDate,
      endDate,
      label: 'This Week'
    };
  });
  const [currentPreset, setCurrentPreset] = useState<string>('This Week');
  const [groupBy, setGroupBy] = useState<'day' | 'week' | 'month'>('day');

  const { toast } = useToast();
  const { user } = useAuth();

  // Check if user can manage goals (admin or manager)
  const canManageGoals = user?.role === 'admin' || user?.role === 'manager';

  // Load feedback data on initial render and when date range or grouping changes
  useEffect(() => {
    fetchFeedbackData();
    fetchFollowUpResponseData();
  }, [dateRange, groupBy]);

  // Load settings on initial render
  useEffect(() => {
    fetchSettings();
  }, []);

  // Fetch Happy or Not goal settings
  const fetchSettings = async () => {
    setLoadingSettings(true);
    try {
      const response = await getHappyOrNotGoalSettings();
      if (response.success) {
        setSettings(response.data);
      } else {
        toast({
          title: 'Error',
          description: 'Failed to load Happy or Not goal settings',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error fetching Happy or Not goal settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to load Happy or Not goal settings',
        variant: 'destructive',
      });
    } finally {
      setLoadingSettings(false);
    }
  };

  // Handle preset selection
  const handlePresetSelect = (range: DateRange) => {
    setDateRange(range);
    setCurrentPreset(range.label || 'Custom Range');
  };

  // Fetch follow-up response data
  const fetchFollowUpResponseData = async () => {
    if (!dateRange.startDate || !dateRange.endDate) return;

    setLoadingResponses(true);
    try {
      // Ensure we're using the full day for the date range
      const startDateStr = dateRange.startDate.toISOString().split('T')[0];
      const endDateStr = dateRange.endDate.toISOString().split('T')[0];

      // Fetch positive responses (Happy and Very Happy)
      const positiveResponse = await getHappyOrNotFollowUpResponses({
        startDate: startDateStr,
        endDate: endDateStr,
        feedbackType: 'positive'
      });

      // Fetch negative responses (Unhappy and Very Unhappy)
      const negativeResponse = await getHappyOrNotFollowUpResponses({
        startDate: startDateStr,
        endDate: endDateStr,
        feedbackType: 'negative'
      });

      if (positiveResponse.success) {
        setPositiveResponsesData(positiveResponse.data);
      }

      if (negativeResponse.success) {
        setNegativeResponsesData(negativeResponse.data);
      }
    } catch (error) {
      console.error('Error fetching follow-up response data:', error);
      toast({
        title: 'Error',
        description: 'Failed to load follow-up response data',
        variant: 'destructive',
      });
    } finally {
      setLoadingResponses(false);
    }
  };

  // Fetch feedback data
  const fetchFeedbackData = async () => {
    if (!dateRange.startDate || !dateRange.endDate) return;

    setLoading(true);
    try {
      // Ensure we're using the full day for the date range
      const startDateStr = dateRange.startDate.toISOString().split('T')[0];
      const endDateStr = dateRange.endDate.toISOString().split('T')[0];

      console.log(`Fetching data from ${startDateStr} to ${endDateStr}`);

      const response = await getHappyOrNotFeedbackData({
        startDate: startDateStr,
        endDate: endDateStr,
        groupBy
      });

      if (response.success) {
        setFeedbackData(response.data);
      } else {
        toast({
          title: 'Error',
          description: 'Failed to load feedback data',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error fetching feedback data:', error);
      toast({
        title: 'Error',
        description: 'Failed to load feedback data',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Sync feedback data
  const syncFeedbackData = async () => {
    setSyncing(true);
    try {
      const response = await syncHappyOrNotFeedback();

      if (response.success) {
        toast({
          title: 'Success',
          description: 'Feedback data synced successfully',
        });

        // Refresh data after sync
        fetchFeedbackData();
      } else {
        toast({
          title: 'Error',
          description: 'Failed to sync feedback data',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error syncing feedback data:', error);
      toast({
        title: 'Error',
        description: 'Failed to sync feedback data',
        variant: 'destructive',
      });
    } finally {
      setSyncing(false);
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    // Check if the date is in YYYY-MM format (month grouping)
    if (dateString.match(/^\d{4}-\d{2}$/)) {
      const [year, month] = dateString.split('-');
      // Convert month number to month name
      const monthNames = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
      ];
      return `${monthNames[parseInt(month) - 1]} ${year}`;
    }
    // Check if the date is in YYYY-WXX format (week grouping)
    else if (dateString.match(/^\d{4}-W\d{2}$/)) {
      const [year, week] = dateString.split('-W');
      return `Week ${week}, ${year}`;
    }
    // Regular date format (day grouping)
    else {
      const date = new Date(dateString);
      return date.toLocaleDateString();
    }
  };

  // Format percentage change
  const formatChange = (change: any) => {
    // Handle undefined, null, or non-numeric values
    if (change === undefined || change === null || isNaN(Number(change))) {
      return '0.0%';
    }

    // Convert to number if it's a string
    const numericChange = typeof change === 'string' ? parseFloat(change) : change;
    const sign = numericChange >= 0 ? '+' : '';
    return `${sign}${numericChange.toFixed(1)}%`;
  };

  // Get color based on change value
  const getChangeColor = (change: any) => {
    // Handle undefined, null, or non-numeric values
    if (change === undefined || change === null || isNaN(Number(change))) {
      return 'text-muted-foreground'; // Neutral color for invalid values
    }

    // Convert to number if it's a string
    const numericChange = typeof change === 'string' ? parseFloat(change) : change;
    return numericChange >= 0 ? 'text-green-500' : 'text-red-500';
  };

  // Prepare data for pie chart
  const preparePieChartData = () => {
    if (!feedbackData) return [];

    return feedbackData.buttonCounts.map(item => ({
      name: getButtonLabel(item._id),
      value: item.count
    }));
  };

  // Get button label based on index
  const getButtonLabel = (index: number) => {
    if (index <= 3) {
      // 4-button scale
      switch (index) {
        case 0: return 'Very Unhappy';
        case 1: return 'Unhappy';
        case 2: return 'Happy';
        case 3: return 'Very Happy';
        default: return 'Unknown';
      }
    } else {
      // NPS scale (0-10)
      return `Rating ${index}`;
    }
  };

  // Get button color based on index
  const getButtonColor = (index: number) => {
    if (index <= 3) {
      // 4-button scale
      // Map the index to the correct color:
      // 0 (Very Unhappy) -> #e24e67
      // 1 (Unhappy) -> #ef9ea1
      // 2 (Happy) -> #99cd9b
      // 3 (Very Happy) -> #0daa5d
      return COLORS[index];
    } else {
      // NPS scale (0-10)
      // Map 0-10 to our 4 colors
      if (index <= 2) return COLORS[0]; // Very Unhappy
      if (index <= 4) return COLORS[1]; // Unhappy
      if (index <= 7) return COLORS[2]; // Happy
      return COLORS[3]; // Very Happy
    }
  };

  // Get face image based on index
  const getFaceImage = (index: number) => {
    if (index <= 3) {
      // 4-button scale
      switch (index) {
        case 0: return '/img/happyornot/veryangry.svg';
        case 1: return '/img/happyornot/angry.svg';
        case 2: return '/img/happyornot/happy.svg';
        case 3: return '/img/happyornot/veryhappy.svg';
        default: return '/img/happyornot/happy.svg';
      }
    } else {
      // NPS scale (0-10)
      // Map 0-10 to 0-4 for face images
      if (index <= 2) return '/img/happyornot/veryangry.svg';
      if (index <= 4) return '/img/happyornot/angry.svg';
      if (index <= 7) return '/img/happyornot/happy.svg';
      return '/img/happyornot/veryhappy.svg';
    }
  };

  // Prepare data for time series chart
  const prepareTimeSeriesData = () => {
    if (!feedbackData) return [];

    // Create a map to store all possible months/dates
    const allDatesMap = new Map();

    // If grouping by month and we have a full year selected, ensure all months are included
    if (groupBy === 'month' && dateRange.startDate && dateRange.endDate) {
      const startYear = dateRange.startDate.getFullYear();
      const endYear = dateRange.endDate.getFullYear();

      // If we're looking at a single year or range that spans a year
      if (endYear - startYear <= 1) {
        // Create entries for all months
        for (let month = 1; month <= 12; month++) {
          const monthStr = month.toString().padStart(2, '0');
          const dateKey = `${startYear}-${monthStr}`;
          const monthNames = [
            'January', 'February', 'March', 'April', 'May', 'June',
            'July', 'August', 'September', 'October', 'November', 'December'
          ];

          // Create an empty data point for this month
          allDatesMap.set(dateKey, {
            date: `${monthNames[month - 1]} ${startYear}`,
            originalDate: dateKey,
            // Initialize all button indexes with 0
            'Very Unhappy': 0,
            'Unhappy': 0,
            'Happy': 0,
            'Very Happy': 0
          });
        }
      }
    }

    // Process the actual data
    feedbackData.timeSeriesData.forEach(item => {
      const formattedDate = formatDate(item.date);

      // Create or update the entry in our map
      if (!allDatesMap.has(item.date)) {
        // Create a new object with formatted date
        const newItem: any = {
          date: formattedDate,
          originalDate: item.date,
          // Initialize all button indexes with 0
          'Very Unhappy': 0,
          'Unhappy': 0,
          'Happy': 0,
          'Very Happy': 0
        };
        allDatesMap.set(item.date, newItem);
      }

      const dateEntry = allDatesMap.get(item.date);

      // Add counts for each button index
      for (let i = 0; i <= 10; i++) {
        if (item[i] !== undefined) {
          dateEntry[getButtonLabel(i)] = item[i];
        }
      }
    });

    // Convert map to array and sort
    const result = Array.from(allDatesMap.values());

    // Sort by original date
    result.sort((a, b) => {
      return a.originalDate.localeCompare(b.originalDate);
    });

    return result;
  };

  // Custom tooltip for charts
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      // Calculate total for this date point
      const total = payload.reduce((sum: number, entry: any) => {
        return sum + (entry.value || 0);
      }, 0);

      return (
        <div className="bg-background border rounded-md shadow-md p-2 text-sm">
          <p className="font-medium">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }}>
              {entry.name}: {entry.value} {entry.value > 0 && total > 0 ? `(${((entry.value / total) * 100).toFixed(1)}%)` : ''}
            </p>
          ))}
          <div className="mt-1 pt-1 border-t border-border">
            <p className="font-medium">Total: {total}</p>
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Happy or Not Feedback</h1>
          <p className="text-muted-foreground">
            Track and analyze customer feedback from the Happy or Not kiosk
          </p>
        </div>

        {canManageGoals && (
          <div className="flex flex-col sm:flex-row gap-2">
            <Button
              variant="outline"
              onClick={syncFeedbackData}
              disabled={syncing}
            >
              {syncing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Syncing...
                </>
              ) : (
                <>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Sync Data
                </>
              )}
            </Button>
          </div>
        )}
      </div>

      {/* Goals Section - Only visible to admin/manager */}
      {settings && !loadingSettings && canManageGoals && (
        <Card className="bg-muted/30 border-dashed">
          <CardContent className="p-4">
            <div className="flex flex-col md:flex-row justify-between items-center gap-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-background rounded-full">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                    <path d="M12 2v4M12 18v4M4.93 4.93l2.83 2.83M16.24 16.24l2.83 2.83M2 12h4M18 12h4M4.93 19.07l2.83-2.83M16.24 7.76l2.83-2.83"/>
                  </svg>
                </div>
                <div>
                  <p className="text-sm font-medium">Weekly Feedback Goal</p>
                  <p className="text-2xl font-bold">{settings.weeklyFeedbackGoal}</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div className="p-2 bg-background rounded-full">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                    <path d="M14 9a2 2 0 0 1-2 2H6l-4 4V4c0-1.1.9-2 2-2h8a2 2 0 0 1 2 2v5Z"/>
                    <path d="M18 9h2a2 2 0 0 1 2 2v11l-4-4h-6a2 2 0 0 1-2-2v-1"/>
                  </svg>
                </div>
                <div>
                  <p className="text-sm font-medium">Happiness Score Goal</p>
                  <p className="text-2xl font-bold">{settings.happinessScoreGoal}%</p>
                </div>
              </div>

              <div>
                <Button variant="outline" size="sm" asChild>
                  <Link to="/settings?tab=happyOrNot">
                    Edit Goals
                  </Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Feedback Type Icons Row */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
        {loading ? (
          Array(4).fill(0).map((_, i) => (
            <Card key={i}>
              <CardContent className="p-4 flex justify-center items-center h-24">
                <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
              </CardContent>
            </Card>
          ))
        ) : feedbackData ? (
          <>
            {/* Very Happy */}
            <Card className="border-l-4" style={{ borderLeftColor: getButtonColor(3) }}>
              <CardContent className="p-4 flex items-center gap-3">
                <img src="/img/happyornot/veryhappy.svg" alt="Very Happy" className="w-12 h-12" />
                <div>
                  <p className="font-medium">Very Happy</p>
                  <div className="flex items-baseline gap-2">
                    <span className="text-2xl font-bold">
                      {feedbackData.buttonCounts.find(item => item._id === 3)?.count || 0}
                    </span>
                    <span className="text-xs text-muted-foreground">
                      {feedbackData.totalCount > 0 ?
                        `${(((feedbackData.buttonCounts.find(item => item._id === 3)?.count || 0) / feedbackData.totalCount) * 100).toFixed(1)}%`
                        : '0%'}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Happy */}
            <Card className="border-l-4" style={{ borderLeftColor: getButtonColor(2) }}>
              <CardContent className="p-4 flex items-center gap-3">
                <img src="/img/happyornot/happy.svg" alt="Happy" className="w-12 h-12" />
                <div>
                  <p className="font-medium">Happy</p>
                  <div className="flex items-baseline gap-2">
                    <span className="text-2xl font-bold">
                      {feedbackData.buttonCounts.find(item => item._id === 2)?.count || 0}
                    </span>
                    <span className="text-xs text-muted-foreground">
                      {feedbackData.totalCount > 0 ?
                        `${(((feedbackData.buttonCounts.find(item => item._id === 2)?.count || 0) / feedbackData.totalCount) * 100).toFixed(1)}%`
                        : '0%'}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Unhappy */}
            <Card className="border-l-4" style={{ borderLeftColor: getButtonColor(1) }}>
              <CardContent className="p-4 flex items-center gap-3">
                <img src="/img/happyornot/angry.svg" alt="Unhappy" className="w-12 h-12" />
                <div>
                  <p className="font-medium">Unhappy</p>
                  <div className="flex items-baseline gap-2">
                    <span className="text-2xl font-bold">
                      {feedbackData.buttonCounts.find(item => item._id === 1)?.count || 0}
                    </span>
                    <span className="text-xs text-muted-foreground">
                      {feedbackData.totalCount > 0 ?
                        `${(((feedbackData.buttonCounts.find(item => item._id === 1)?.count || 0) / feedbackData.totalCount) * 100).toFixed(1)}%`
                        : '0%'}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Very Unhappy */}
            <Card className="border-l-4" style={{ borderLeftColor: getButtonColor(0) }}>
              <CardContent className="p-4 flex items-center gap-3">
                <img src="/img/happyornot/veryangry.svg" alt="Very Unhappy" className="w-12 h-12" />
                <div>
                  <p className="font-medium">Very Unhappy</p>
                  <div className="flex items-baseline gap-2">
                    <span className="text-2xl font-bold">
                      {feedbackData.buttonCounts.find(item => item._id === 0)?.count || 0}
                    </span>
                    <span className="text-xs text-muted-foreground">
                      {feedbackData.totalCount > 0 ?
                        `${(((feedbackData.buttonCounts.find(item => item._id === 0)?.count || 0) / feedbackData.totalCount) * 100).toFixed(1)}%`
                        : '0%'}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </>
        ) : (
          Array(4).fill(0).map((_, i) => (
            <Card key={i}>
              <CardContent className="p-4 flex justify-center items-center h-24">
                <div className="text-center text-muted-foreground">No data available</div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base font-medium">Date Range</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <EnhancedDateRange
              onSelectDateRange={handlePresetSelect}
              currentDateRange={dateRange}
              currentPreset={currentPreset}
            />

            <div>
              <p className="text-sm text-muted-foreground mb-2">Group By</p>
              <Select
                value={groupBy}
                onValueChange={(value) => setGroupBy(value as 'day' | 'week' | 'month')}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select grouping" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="day">Day</SelectItem>
                  <SelectItem value="week">Week</SelectItem>
                  <SelectItem value="month">Month</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base font-medium">Total Feedback</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex justify-center items-center h-16">
                <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
              </div>
            ) : feedbackData ? (
              <div className="text-center">
                <div className="text-4xl font-bold">{feedbackData.totalCount}</div>
                <div className="flex justify-center gap-4 mt-2">
                  <div>
                    <p className="text-xs text-muted-foreground">vs Previous Period</p>
                    <p className={getChangeColor(feedbackData.comparison.previousPeriod.change)}>
                      {formatChange(feedbackData.comparison.previousPeriod.change)}
                    </p>
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground">vs Last Year</p>
                    <p className={getChangeColor(feedbackData.comparison.lastYear.change)}>
                      {formatChange(feedbackData.comparison.lastYear.change)}
                    </p>
                  </div>
                </div>

                {/* Weekly Feedback Goal Progress */}
                {settings && feedbackData && (
                  <div className="mt-4 pt-4 border-t border-border">
                    <p className="text-xs text-muted-foreground mb-1">Weekly Goal Progress</p>
                    <div className="flex items-center gap-2">
                      <div className="w-full h-2 bg-muted rounded-full overflow-hidden">
                        <div
                          className="h-full rounded-full transition-all duration-500 ease-out"
                          style={{
                            width: `${Math.min(100, (feedbackData.totalCount / settings.weeklyFeedbackGoal) * 100)}%`,
                            backgroundColor: feedbackData.totalCount >= settings.weeklyFeedbackGoal ? '#0daa5d' : '#3b82f6'
                          }}
                        />
                      </div>
                      <span className="text-xs font-medium whitespace-nowrap">
                        {feedbackData.totalCount}/{settings.weeklyFeedbackGoal}
                      </span>
                    </div>
                    {feedbackData.totalCount >= settings.weeklyFeedbackGoal && (
                      <p className="text-xs text-green-600 dark:text-green-400 font-medium mt-1">
                        🎉 Goal achieved!
                      </p>
                    )}
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center text-muted-foreground">No data available</div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base font-medium">Happiness Score</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex justify-center items-center h-16">
                <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
              </div>
            ) : feedbackData ? (
              <div className="text-center">
                <div className="text-4xl font-bold">{feedbackData.happinessScore}%</div>
                <div className="flex justify-center gap-4 mt-2">
                  <div>
                    <p className="text-xs text-muted-foreground">vs Previous Period</p>
                    <p className={getChangeColor(feedbackData.comparison.previousPeriod.scoreChange)}>
                      {formatChange(feedbackData.comparison.previousPeriod.scoreChange)}
                    </p>
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground">vs Last Year</p>
                    <p className={getChangeColor(feedbackData.comparison.lastYear.scoreChange)}>
                      {formatChange(feedbackData.comparison.lastYear.scoreChange)}
                    </p>
                  </div>
                </div>

                {/* Happiness Score Goal Progress */}
                {settings && feedbackData && (
                  <div className="mt-4 pt-4 border-t border-border">
                    <p className="text-xs text-muted-foreground mb-1">Score Goal Progress</p>
                    <div className="flex items-center gap-2">
                      <div className="w-full h-2 bg-muted rounded-full overflow-hidden">
                        <div
                          className="h-full rounded-full transition-all duration-500 ease-out"
                          style={{
                            width: `${Math.min(100, (feedbackData.happinessScore / settings.happinessScoreGoal) * 100)}%`,
                            backgroundColor: feedbackData.happinessScore >= settings.happinessScoreGoal ? '#0daa5d' : '#99cd9b'
                          }}
                        />
                      </div>
                      <span className="text-xs font-medium whitespace-nowrap">
                        {feedbackData.happinessScore}%/{settings.happinessScoreGoal}%
                      </span>
                    </div>
                    {feedbackData.happinessScore >= settings.happinessScoreGoal && (
                      <p className="text-xs text-green-600 dark:text-green-400 font-medium mt-1">
                        🎉 Goal achieved!
                      </p>
                    )}
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center text-muted-foreground">No data available</div>
            )}
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Feedback Over Time</CardTitle>
            <CardDescription>
              Feedback distribution by {groupBy === 'day' ? 'day' : groupBy === 'week' ? 'week' : 'month'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex justify-center items-center h-80">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
            ) : feedbackData && feedbackData.timeSeriesData.length > 0 ? (
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={prepareTimeSeriesData()}
                    margin={{ top: 20, right: 30, left: 20, bottom: 30 }}
                    stackOffset="expand"
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="date"
                      tick={{ fontSize: 11 }}
                      interval={groupBy === 'month' ? 0 : 'preserveEnd'}
                      angle={groupBy === 'month' ? -45 : 0}
                      textAnchor={groupBy === 'month' ? 'end' : 'middle'}
                      height={60}
                    />
                    <YAxis />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend />
                    {feedbackData.buttonCounts.map((item) => (
                      <Bar
                        key={item._id}
                        dataKey={getButtonLabel(item._id)}
                        stackId="a"
                        fill={getButtonColor(item._id)}
                      />
                    ))}
                  </BarChart>
                </ResponsiveContainer>
              </div>
            ) : (
              <div className="flex justify-center items-center h-80 text-muted-foreground">
                No data available for the selected period
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Feedback Distribution</CardTitle>
            <CardDescription>
              Distribution of feedback by rating
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex justify-center items-center h-96">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
            ) : feedbackData && feedbackData.buttonCounts.length > 0 ? (
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <RechartsPieChart>
                    <Pie
                      data={preparePieChartData()}
                      cx="50%"
                      cy="45%"
                      labelLine={false}
                      outerRadius={110}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {preparePieChartData().map((_, index) => (
                        <Cell
                          key={`cell-${index}`}
                          fill={getButtonColor(feedbackData.buttonCounts[index]?._id || 0)}
                        />
                      ))}
                    </Pie>
                    <Tooltip />
                    <Legend
                      layout="horizontal"
                      verticalAlign="bottom"
                      align="center"
                      wrapperStyle={{ paddingTop: '20px' }}
                    />
                  </RechartsPieChart>
                </ResponsiveContainer>
              </div>
            ) : (
              <div className="flex justify-center items-center h-96 text-muted-foreground">
                No data available for the selected period
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="md:col-span-3">
          <CardHeader>
            <CardTitle>Feedback Trends</CardTitle>
            <CardDescription>
              Trends in feedback over time
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex justify-center items-center h-80">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
            ) : feedbackData && feedbackData.timeSeriesData.length > 0 ? (
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <RechartsAreaChart
                    data={prepareTimeSeriesData()}
                    margin={{ top: 20, right: 30, left: 20, bottom: 30 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="date"
                      tick={{ fontSize: 11 }}
                      interval={groupBy === 'month' ? 0 : 'preserveEnd'}
                      angle={groupBy === 'month' ? -45 : 0}
                      textAnchor={groupBy === 'month' ? 'end' : 'middle'}
                      height={60}
                    />
                    <YAxis />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend />
                    {feedbackData.buttonCounts.map((item) => (
                      <Area
                        key={item._id}
                        type="monotone"
                        dataKey={getButtonLabel(item._id)}
                        stroke={getButtonColor(item._id)}
                        fill={getButtonColor(item._id)}
                        fillOpacity={0.3}
                        stackId="1"
                      />
                    ))}
                  </RechartsAreaChart>
                </ResponsiveContainer>
              </div>
            ) : (
              <div className="flex justify-center items-center h-80 text-muted-foreground">
                No data available for the selected period
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="md:col-span-1">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div>
              <CardTitle>Recent Feedback</CardTitle>
              <CardDescription>
                Last 5 feedback items with comments
              </CardDescription>
            </div>
            <Button variant="outline" asChild>
              <Link to="/feedback-details">
                <Search className="mr-2 h-4 w-4" />
                View All
              </Link>
            </Button>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex justify-center items-center h-40">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
            ) : feedbackData && feedbackData.recentFeedbacks.length > 0 ? (
              <div className="space-y-4">
                {feedbackData.recentFeedbacks.slice(0, 5).map((feedback) => (
                  <div
                    key={feedback.feedbackId}
                    className="p-4 border rounded-lg"
                    style={{ borderLeftColor: getButtonColor(feedback.buttonIndex), borderLeftWidth: '4px' }}
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex items-center gap-3">
                        <img
                          src={getFaceImage(feedback.buttonIndex)}
                          alt={getButtonLabel(feedback.buttonIndex)}
                          className="w-10 h-10"
                        />
                        <div>
                          <p className="font-medium">{getButtonLabel(feedback.buttonIndex)}</p>
                          <p className="text-sm text-muted-foreground">
                            {new Date(feedback.localTime).toLocaleString()}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm">{feedback.experiencePointName}</p>
                      </div>
                    </div>
                    {(feedback.text || feedback.followupOptionText) && (
                      <div className="mt-2 pt-2 border-t">
                        {feedback.text && (
                          <p className="text-sm italic">"{feedback.text}"</p>
                        )}
                        {feedback.followupOptionText && (
                          <p className="text-sm mt-1">
                            <span className="font-medium">Follow-up:</span> {feedback.followupOptionText}
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex justify-center items-center h-40 text-muted-foreground">
                No feedback with comments available for the selected period
              </div>
            )}
          </CardContent>
        </Card>

        <div className="md:col-span-2">
          <div className="grid grid-cols-1 gap-4">
            <FollowUpResponseChart
              data={negativeResponsesData}
              loading={loadingResponses}
              title="What could be improved?"
              description="Follow-up responses from unhappy customers"
              color="#e24e67"
              lightColor="#ef9ea1"
            />

            <FollowUpResponseChart
              data={positiveResponsesData}
              loading={loadingResponses}
              title="What was good?"
              description="Follow-up responses from happy customers"
              color="#0daa5d"
              lightColor="#99cd9b"
            />

          </div>
        </div>
      </div>


    </div>
  );
}

export default HappyOrNotFeedback;
