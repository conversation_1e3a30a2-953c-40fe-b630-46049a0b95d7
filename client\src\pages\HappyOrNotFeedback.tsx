import { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, Settings, TrendingUp, Users, MessageSquare, BarChart3, Calendar, CalendarDays, CalendarRange } from 'lucide-react';
import { useToast } from '@/hooks/useToast';
import { Link, useNavigate } from 'react-router-dom';
import { DateRange } from '@/components/ui/date-range-presets';
import { FollowUpResponseChart } from '@/components/FollowUpResponseChart';
import { EnhancedDateRange } from '@/components/ui/enhanced-date-range';
import { useAuth } from '@/contexts/AuthContext';
import {
  getHappyOrNotFeedbackData,
  getPaginatedHappyOrNotFeedback,
  getHappyOrNotFollowUpResponses,
  getHappyOrNotGoalSettings,
  HappyOrNotFeedbackData,
  FollowUpResponseData,
  HappyOrNotGoalSettings
} from '@/api/happyOrNot';
import {
  CartesianGrid,
  Line,
  LineChart,
  XAxis,
  YAxis,
  Pie,
  PieChart,
  Bar,
  BarChart,
  LabelList
} from 'recharts';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";

// Define colors for different happiness levels
const COLORS = ['#e24e67', '#ef9ea1', '#99cd9b', '#0daa5d'];

export function HappyOrNotFeedback() {
  const [loading, setLoading] = useState(true);
  const [feedbackData, setFeedbackData] = useState<HappyOrNotFeedbackData | null>(null);
  const [currentWeekData, setCurrentWeekData] = useState<HappyOrNotFeedbackData | null>(null);
  const [recentFeedback, setRecentFeedback] = useState<any[]>([]);
  const [positiveResponsesData, setPositiveResponsesData] = useState<FollowUpResponseData | null>(null);
  const [negativeResponsesData, setNegativeResponsesData] = useState<FollowUpResponseData | null>(null);
  const [loadingResponses, setLoadingResponses] = useState(false);
  const [positiveFollowUpData, setPositiveFollowUpData] = useState<any[]>([]);
  const [negativeFollowUpData, setNegativeFollowUpData] = useState<any[]>([]);
  const [loadingFollowUpData, setLoadingFollowUpData] = useState(false);
  const [settings, setSettings] = useState<HappyOrNotGoalSettings | null>(null);
  const [loadingSettings, setLoadingSettings] = useState(true);
  const [loadingRecentFeedback, setLoadingRecentFeedback] = useState(false);
  const [isScrollPaused, setIsScrollPaused] = useState(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [dateRange, setDateRange] = useState<DateRange>(() => {
    // Set default to Week to Date (Monday to current day)
    const today = new Date();
    const dayOfWeek = today.getDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
    const daysToSubtract = dayOfWeek === 0 ? 6 : dayOfWeek - 1;

    // Calculate Monday of current week
    const startDate = new Date(today);
    startDate.setDate(today.getDate() - daysToSubtract);
    startDate.setHours(0, 0, 0, 0);

    // Set end date to today at 23:59:59
    const endDate = new Date();
    endDate.setHours(23, 59, 59, 999);

    return {
      startDate,
      endDate,
      label: 'This Week'
    };
  });
  const [currentPreset, setCurrentPreset] = useState<string>('This Week');
  const [groupBy, setGroupBy] = useState<'day' | 'week' | 'month'>('day');

  const { toast } = useToast();
  const { user } = useAuth();
  const navigate = useNavigate();

  // Check if user can manage goals (admin or manager)
  const canManageGoals = user?.role === 'admin' || user?.role === 'manager';

  // Calculate current week range (Monday to current day)
  const getCurrentWeekRange = () => {
    const today = new Date();
    const dayOfWeek = today.getDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
    const daysToSubtract = dayOfWeek === 0 ? 6 : dayOfWeek - 1;

    const weekStart = new Date(today);
    weekStart.setDate(today.getDate() - daysToSubtract);
    weekStart.setHours(0, 0, 0, 0);

    const weekEnd = new Date(today);
    weekEnd.setHours(23, 59, 59, 999);

    return { weekStart, weekEnd };
  };

  // Load feedback data on initial render and when date range or grouping changes
  useEffect(() => {
    fetchFeedbackData();
    fetchFollowUpResponseData();
    fetchFollowUpData();
  }, [dateRange, groupBy]);

  // Load settings on initial render
  useEffect(() => {
    fetchSettings();
    fetchCurrentWeekData();
    fetchRecentFeedback();
  }, []);

  // Fetch current week data
  const fetchCurrentWeekData = async () => {
    try {
      const { weekStart, weekEnd } = getCurrentWeekRange();
      const startDateStr = weekStart.toISOString().split('T')[0];
      const endDateStr = weekEnd.toISOString().split('T')[0];

      const response = await getHappyOrNotFeedbackData({
        startDate: startDateStr,
        endDate: endDateStr,
        groupBy: 'day'
      });

      if (response.success) {
        setCurrentWeekData(response.data);
      }
    } catch (error) {
      console.error('Error fetching current week data:', error);
    }
  };

  // Fetch recent feedback for the live stream (this week only)
  const fetchRecentFeedback = async () => {
    setLoadingRecentFeedback(true);
    try {
      const { weekStart, weekEnd } = getCurrentWeekRange();
      const startDateStr = weekStart.toISOString().split('T')[0];
      const endDateStr = weekEnd.toISOString().split('T')[0];

      const response = await getPaginatedHappyOrNotFeedback({
        limit: 100,
        page: 1,
        sortBy: 'localTime',
        sortOrder: 'desc',
        startDate: startDateStr,
        endDate: endDateStr
      });

      if (response.success) {
        setRecentFeedback(response.data.feedbacks);
      }
    } catch (error) {
      console.error('Error fetching recent feedback:', error);
    } finally {
      setLoadingRecentFeedback(false);
    }
  };

  // Fetch Happy or Not goal settings
  const fetchSettings = async () => {
    setLoadingSettings(true);
    try {
      const response = await getHappyOrNotGoalSettings();
      if (response.success) {
        setSettings(response.data);
      } else {
        toast({
          title: 'Error',
          description: 'Failed to load Happy or Not goal settings',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error fetching Happy or Not goal settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to load Happy or Not goal settings',
        variant: 'destructive',
      });
    } finally {
      setLoadingSettings(false);
    }
  };

  // Handle preset selection
  const handlePresetSelect = (range: DateRange) => {
    setDateRange(range);
    setCurrentPreset(range.label || 'Custom Range');
  };

  // Fetch follow-up response data
  const fetchFollowUpResponseData = async () => {
    if (!dateRange.startDate || !dateRange.endDate) return;

    setLoadingResponses(true);
    try {
      // Ensure we're using the full day for the date range
      const startDateStr = dateRange.startDate.toISOString().split('T')[0];
      const endDateStr = dateRange.endDate.toISOString().split('T')[0];

      // Fetch positive responses (Happy and Very Happy)
      const positiveResponse = await getHappyOrNotFollowUpResponses({
        startDate: startDateStr,
        endDate: endDateStr,
        feedbackType: 'positive'
      });

      // Fetch negative responses (Unhappy and Very Unhappy)
      const negativeResponse = await getHappyOrNotFollowUpResponses({
        startDate: startDateStr,
        endDate: endDateStr,
        feedbackType: 'negative'
      });

      if (positiveResponse.success) {
        setPositiveResponsesData(positiveResponse.data);
      }

      if (negativeResponse.success) {
        setNegativeResponsesData(negativeResponse.data);
      }
    } catch (error) {
      console.error('Error fetching follow-up response data:', error);
      toast({
        title: 'Error',
        description: 'Failed to load follow-up response data',
        variant: 'destructive',
      });
    } finally {
      setLoadingResponses(false);
    }
  };

  // Fetch follow-up data for bar charts
  const fetchFollowUpData = async () => {
    if (!dateRange.startDate || !dateRange.endDate) return;

    setLoadingFollowUpData(true);
    try {
      const startDateStr = dateRange.startDate.toISOString().split('T')[0];
      const endDateStr = dateRange.endDate.toISOString().split('T')[0];

      // Fetch positive follow-up responses (happy + very happy)
      const positiveResponse = await getHappyOrNotFollowUpResponses({
        startDate: startDateStr,
        endDate: endDateStr,
        buttonIds: [2, 3], // Happy and Very Happy
        limit: 10
      });

      // Fetch negative follow-up responses (unhappy + very unhappy)
      const negativeResponse = await getHappyOrNotFollowUpResponses({
        startDate: startDateStr,
        endDate: endDateStr,
        buttonIds: [0, 1], // Very Unhappy and Unhappy
        limit: 10
      });

      setPositiveFollowUpData(positiveResponse.data || []);
      setNegativeFollowUpData(negativeResponse.data || []);
    } catch (error) {
      console.error('Error fetching follow-up data:', error);
      setPositiveFollowUpData([]);
      setNegativeFollowUpData([]);
    } finally {
      setLoadingFollowUpData(false);
    }
  };

  // Fetch feedback data
  const fetchFeedbackData = async () => {
    if (!dateRange.startDate || !dateRange.endDate) return;

    setLoading(true);
    try {
      // Ensure we're using the full day for the date range
      const startDateStr = dateRange.startDate.toISOString().split('T')[0];
      const endDateStr = dateRange.endDate.toISOString().split('T')[0];

      console.log(`Fetching data from ${startDateStr} to ${endDateStr}`);

      const response = await getHappyOrNotFeedbackData({
        startDate: startDateStr,
        endDate: endDateStr,
        groupBy
      });

      if (response.success) {
        setFeedbackData(response.data);
      } else {
        toast({
          title: 'Error',
          description: 'Failed to load feedback data',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error fetching feedback data:', error);
      toast({
        title: 'Error',
        description: 'Failed to load feedback data',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Auto-scroll functionality for feedback stream
  useEffect(() => {
    if (!recentFeedback.length) return;

    let animationId: number;
    let isRunning = true;

    const scroll = () => {
      if (isRunning && !isScrollPaused && scrollContainerRef.current) {
        const container = scrollContainerRef.current;
        const scrollSpeed = 1; // Increased speed to make it more visible
        const currentScroll = container.scrollTop;
        const maxScroll = container.scrollHeight - container.clientHeight;

        // Only scroll if there's content to scroll
        if (maxScroll > 0) {
          const newScrollPosition = currentScroll + scrollSpeed;

          // Reset to top when reaching the end
          if (newScrollPosition >= maxScroll) {
            container.scrollTop = 0;
          } else {
            container.scrollTop = newScrollPosition;
          }
        }
      }

      // Continue animation loop
      if (isRunning) {
        animationId = requestAnimationFrame(scroll);
      }
    };

    // Start the animation loop
    animationId = requestAnimationFrame(scroll);

    return () => {
      isRunning = false;
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [recentFeedback, isScrollPaused]);

  // Handle navigation to settings
  const handleNavigateToSettings = () => {
    navigate('/settings?tab=happyOrNot');
  };

  // Chart configurations
  const lineChartConfig = {
    veryUnhappy: {
      label: "Very Unhappy",
      color: "#e24e67",
    },
    unhappy: {
      label: "Unhappy",
      color: "#ef9ea1",
    },
    happy: {
      label: "Happy",
      color: "#99cd9b",
    },
    veryHappy: {
      label: "Very Happy",
      color: "#0daa5d",
    },
  } satisfies ChartConfig;

  const pieChartConfig = {
    count: {
      label: "Count",
    },
    veryUnhappy: {
      label: "Very Unhappy",
      color: "#e24e67",
    },
    unhappy: {
      label: "Unhappy",
      color: "#ef9ea1",
    },
    happy: {
      label: "Happy",
      color: "#99cd9b",
    },
    veryHappy: {
      label: "Very Happy",
      color: "#0daa5d",
    },
  } satisfies ChartConfig;

  // Bar chart configurations
  const positiveBarChartConfig = {
    count: {
      label: "Count",
      color: "#0daa5d",
    },
    label: {
      color: "var(--background)",
    },
  } satisfies ChartConfig;

  const negativeBarChartConfig = {
    count: {
      label: "Count",
      color: "#e24e67",
    },
    label: {
      color: "var(--background)",
    },
  } satisfies ChartConfig;

  // Prepare data for line chart
  const prepareLineChartData = () => {
    if (!feedbackData || !feedbackData.timeSeriesData) return [];

    return feedbackData.timeSeriesData.map(item => {
      return {
        date: formatDateForChart(item.date),
        veryUnhappy: item[0] || 0,  // Button index 0 = Very Unhappy
        unhappy: item[1] || 0,      // Button index 1 = Unhappy
        happy: item[2] || 0,        // Button index 2 = Happy
        veryHappy: item[3] || 0,    // Button index 3 = Very Happy
      };
    });
  };

  // Prepare data for pie chart
  const preparePieChartData = () => {
    if (!feedbackData || !feedbackData.buttonCounts) return [];

    const buttonKeys = ['veryUnhappy', 'unhappy', 'happy', 'veryHappy'];
    const buttonColors = ['#e24e67', '#ef9ea1', '#99cd9b', '#0daa5d'];

    return feedbackData.buttonCounts.map(item => ({
      feedback: getButtonLabel(item._id),
      count: item.count || 0,
      fill: buttonColors[item._id] || buttonColors[0],
    }));
  };

  // Prepare data for positive follow-up responses bar chart
  const preparePositiveBarChartData = () => {
    if (!positiveFollowUpData || !positiveFollowUpData.length) return [];

    // Take top 10 responses and format for horizontal bar chart
    return positiveFollowUpData
      .slice(0, 10)
      .map(item => ({
        response: item._id || 'No response',
        count: item.count || 0,
      }))
      .reverse(); // Reverse to show highest at top
  };

  // Prepare data for negative follow-up responses bar chart
  const prepareNegativeBarChartData = () => {
    if (!negativeFollowUpData || !negativeFollowUpData.length) return [];

    // Take top 10 responses and format for horizontal bar chart
    return negativeFollowUpData
      .slice(0, 10)
      .map(item => ({
        response: item._id || 'No response',
        count: item.count || 0,
      }))
      .reverse(); // Reverse to show highest at top
  };

  // Format date for chart display
  const formatDateForChart = (dateStr: string) => {
    if (!dateStr) return 'Invalid Date';

    try {
      if (groupBy === 'month') {
        const date = new Date(dateStr);
        return date.toLocaleDateString('en-US', { month: 'short', year: '2-digit' });
      } else if (groupBy === 'week') {
        // Handle week format - could be "2024-W52" or just a date
        if (dateStr.includes('W')) {
          return `Week ${dateStr.split('W')[1]}`;
        }
        const date = new Date(dateStr);
        return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      } else {
        const date = new Date(dateStr);
        return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      }
    } catch (error) {
      console.warn('Error formatting date:', dateStr, error);
      return dateStr;
    }
  };

  // Get button label based on index
  const getButtonLabel = (index: number) => {
    if (index <= 3) {
      // 4-button scale
      switch (index) {
        case 0: return 'Very Unhappy';
        case 1: return 'Unhappy';
        case 2: return 'Happy';
        case 3: return 'Very Happy';
        default: return 'Unknown';
      }
    } else {
      // NPS scale (0-10)
      return `Rating ${index}`;
    }
  };

  // Get button color based on index
  const getButtonColor = (index: number) => {
    if (index <= 3) {
      // 4-button scale
      // Map the index to the correct color:
      // 0 (Very Unhappy) -> #e24e67
      // 1 (Unhappy) -> #ef9ea1
      // 2 (Happy) -> #99cd9b
      // 3 (Very Happy) -> #0daa5d
      return COLORS[index];
    } else {
      // NPS scale (0-10)
      // Map 0-10 to our 4 colors
      if (index <= 2) return COLORS[0]; // Very Unhappy
      if (index <= 4) return COLORS[1]; // Unhappy
      if (index <= 7) return COLORS[2]; // Happy
      return COLORS[3]; // Very Happy
    }
  };

  // Get face image based on index
  const getFaceImage = (index: number) => {
    if (index <= 3) {
      // 4-button scale
      switch (index) {
        case 0: return '/img/happyornot/veryangry.svg';
        case 1: return '/img/happyornot/angry.svg';
        case 2: return '/img/happyornot/happy.svg';
        case 3: return '/img/happyornot/veryhappy.svg';
        default: return '/img/happyornot/happy.svg';
      }
    } else {
      // NPS scale (0-10)
      // Map 0-10 to 0-4 for face images
      if (index <= 2) return '/img/happyornot/veryangry.svg';
      if (index <= 4) return '/img/happyornot/angry.svg';
      if (index <= 7) return '/img/happyornot/happy.svg';
      return '/img/happyornot/veryhappy.svg';
    }
  };

  // Prepare data for time series chart
  const prepareTimeSeriesData = () => {
    if (!feedbackData || !feedbackData.timeSeriesData) return [];

    // Create a map to store all possible months/dates
    const allDatesMap = new Map();

    // If grouping by month and we have a full year selected, ensure all months are included
    if (groupBy === 'month' && dateRange.startDate && dateRange.endDate) {
      const startYear = dateRange.startDate.getFullYear();
      const endYear = dateRange.endDate.getFullYear();

      // If we're looking at a single year or range that spans a year
      if (endYear - startYear <= 1) {
        // Create entries for all months
        for (let month = 1; month <= 12; month++) {
          const monthStr = month.toString().padStart(2, '0');
          const dateKey = `${startYear}-${monthStr}`;
          const monthNames = [
            'January', 'February', 'March', 'April', 'May', 'June',
            'July', 'August', 'September', 'October', 'November', 'December'
          ];

          // Create an empty data point for this month
          allDatesMap.set(dateKey, {
            date: `${monthNames[month - 1]} ${startYear}`,
            originalDate: dateKey,
            // Initialize all button indexes with 0
            'Very Unhappy': 0,
            'Unhappy': 0,
            'Happy': 0,
            'Very Happy': 0
          });
        }
      }
    }

    // Sort by original date
    result.sort((a, b) => {
      return a.originalDate.localeCompare(b.originalDate);
    });

    return result;
  };



  return (
    <div className="container mx-auto py-6 space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">Happy or Not Feedback</h1>
          <p className="text-muted-foreground">
            Monitor customer satisfaction and feedback trends
          </p>
        </div>

        {canManageGoals && (
          <Button
            variant="outline"
            onClick={handleNavigateToSettings}
            className="flex items-center gap-2"
          >
            <Settings className="h-4 w-4" />
            Feedback Settings
          </Button>
        )}
      </div>

      {/* Current Week Summary Section */}
      <div className="space-y-4">
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
          {/* Summary Cards - Left Side */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Calendar className="h-5 w-5 text-muted-foreground" />
              <h2 className="text-xl font-semibold">This Week's Summary</h2>
              <span className="text-sm text-muted-foreground">
                ({getCurrentWeekRange().weekStart.toLocaleDateString()} - {getCurrentWeekRange().weekEnd.toLocaleDateString()})
              </span>
            </div>

            {/* Top Row - Main Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Total Feedback */}
              <Card className="bg-[#171717] border-[#2a2a2a] hover:bg-[#1a1a1a] transition-colors">
                <CardContent className="p-6">
                  <div className="flex items-center gap-4">
                    <div className="p-3 bg-blue-500/20 rounded-xl border border-blue-500/20">
                      <Users className="h-6 w-6 text-blue-400" />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm text-muted-foreground font-medium">Total Feedback</p>
                      <div className="flex items-baseline gap-2">
                        <p className="text-3xl font-bold text-white">
                          {currentWeekData?.totalCount || 0}
                        </p>
                        {settings && (
                          <span className="text-sm text-muted-foreground">
                            / {settings.weeklyFeedbackGoal}
                          </span>
                        )}
                      </div>
                      {settings && (
                        <div className="mt-2">
                          <div className="flex items-center justify-between text-xs text-muted-foreground mb-1">
                            <span>Goal Progress</span>
                            <span>
                              {Math.min(100, Math.round(((currentWeekData?.totalCount || 0) / settings.weeklyFeedbackGoal) * 100))}%
                            </span>
                          </div>
                          <div className="w-full bg-gray-700 rounded-full h-1.5">
                            <div
                              className="bg-blue-400 h-1.5 rounded-full transition-all duration-300"
                              style={{
                                width: `${Math.min(100, ((currentWeekData?.totalCount || 0) / settings.weeklyFeedbackGoal) * 100)}%`
                              }}
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Happiness Score */}
              <Card className="bg-[#171717] border-[#2a2a2a] hover:bg-[#1a1a1a] transition-colors">
                <CardContent className="p-6">
                  <div className="flex items-center gap-4">
                    <div className="p-3 bg-green-500/20 rounded-xl border border-green-500/20">
                      <TrendingUp className="h-6 w-6 text-green-400" />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm text-muted-foreground font-medium">Happiness Score</p>
                      <div className="flex items-baseline gap-2">
                        <p className="text-3xl font-bold text-white">
                          {currentWeekData?.happinessScore ? `${currentWeekData.happinessScore.toFixed(1)}%` : '0%'}
                        </p>
                        {settings && (
                          <span className="text-sm text-muted-foreground">
                            / {settings.happinessScoreGoal}%
                          </span>
                        )}
                      </div>
                      {settings && (
                        <div className="mt-2">
                          <div className="flex items-center justify-between text-xs text-muted-foreground mb-1">
                            <span>Goal Progress</span>
                            <span>
                              {Math.min(100, Math.round(((currentWeekData?.happinessScore || 0) / settings.happinessScoreGoal) * 100))}%
                            </span>
                          </div>
                          <div className="w-full bg-gray-700 rounded-full h-1.5">
                            <div
                              className="bg-green-400 h-1.5 rounded-full transition-all duration-300"
                              style={{
                                width: `${Math.min(100, ((currentWeekData?.happinessScore || 0) / settings.happinessScoreGoal) * 100)}%`
                              }}
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Feedback Types - 2x2 Grid */}
            <div className="grid grid-cols-2 gap-4">
              {/* Very Happy */}
              <Card className="bg-[#171717] border-[#2a2a2a] border-l-4 border-l-green-500 hover:bg-[#1a1a1a] transition-colors">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-green-500/20 rounded-lg border border-green-500/20">
                      <img src="/img/happyornot/veryhappy.svg" alt="Very Happy" className="w-8 h-8" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Very Happy</p>
                      <p className="text-2xl font-bold text-white">
                        {currentWeekData?.buttonCounts.find(item => item._id === 3)?.count || 0}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Happy */}
              <Card className="bg-[#171717] border-[#2a2a2a] border-l-4 border-l-green-400 hover:bg-[#1a1a1a] transition-colors">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-green-400/20 rounded-lg border border-green-400/20">
                      <img src="/img/happyornot/happy.svg" alt="Happy" className="w-8 h-8" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Happy</p>
                      <p className="text-2xl font-bold text-white">
                        {currentWeekData?.buttonCounts.find(item => item._id === 2)?.count || 0}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Unhappy */}
              <Card className="bg-[#171717] border-[#2a2a2a] border-l-4 border-l-orange-400 hover:bg-[#1a1a1a] transition-colors">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-orange-400/20 rounded-lg border border-orange-400/20">
                      <img src="/img/happyornot/angry.svg" alt="Unhappy" className="w-8 h-8" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Unhappy</p>
                      <p className="text-2xl font-bold text-white">
                        {currentWeekData?.buttonCounts.find(item => item._id === 1)?.count || 0}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Very Unhappy */}
              <Card className="bg-[#171717] border-[#2a2a2a] border-l-4 border-l-red-500 hover:bg-[#1a1a1a] transition-colors">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-red-500/20 rounded-lg border border-red-500/20">
                      <img src="/img/happyornot/veryangry.svg" alt="Very Unhappy" className="w-8 h-8" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Very Unhappy</p>
                      <p className="text-2xl font-bold text-white">
                        {currentWeekData?.buttonCounts.find(item => item._id === 0)?.count || 0}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Feedback Stream - Right Side */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5 text-muted-foreground" />
              <h2 className="text-xl font-semibold">Recent Feedback</h2>
            </div>

            <Card className="bg-[#171717] border-[#2a2a2a] relative">
              <div className="absolute top-4 right-4 z-10">
                <Link to="/feedback-details">
                  <Button variant="default" size="sm">
                    <MessageSquare className="h-4 w-4 mr-2" />
                    View All Feedback
                  </Button>
                </Link>
              </div>
              <div
                ref={scrollContainerRef}
                className="h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent"
                onMouseEnter={() => setIsScrollPaused(true)}
                onMouseLeave={() => setIsScrollPaused(false)}
                onWheel={() => setIsScrollPaused(true)}
                onTouchStart={() => setIsScrollPaused(true)}
              >
                  {loadingRecentFeedback ? (
                    <div className="flex justify-center items-center h-full">
                      <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                    </div>
                  ) : recentFeedback.length > 0 ? (
                    <div className="space-y-1 pt-16 px-4 pb-4">
                      {recentFeedback.map((feedback) => (
                        <div
                          key={feedback._id}
                          className="flex items-start gap-3 p-3 rounded-lg hover:bg-[#1a1a1a] transition-colors"
                        >
                          <img
                            src={getFaceImage(feedback.buttonIndex)}
                            alt={getButtonLabel(feedback.buttonIndex)}
                            className="w-8 h-8 flex-shrink-0"
                          />
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-1">
                              <span className="font-medium text-sm">
                                {getButtonLabel(feedback.buttonIndex)}
                              </span>
                              <span className="text-xs text-muted-foreground">
                                {new Date(feedback.localTime).toLocaleString()}
                              </span>
                            </div>
                            {feedback.text && (
                              <p className="text-sm text-muted-foreground line-clamp-2">
                                {feedback.text}
                              </p>
                            )}
                            {feedback.followupOptionText && (
                              <p className="text-sm text-blue-400 line-clamp-1 mt-1">
                                Follow-up: {feedback.followupOptionText}
                              </p>
                            )}
                            {feedback.experiencePointName && (
                              <p className="text-xs text-muted-foreground mt-1">
                                {feedback.experiencePointName}
                              </p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="flex justify-center items-center h-full text-muted-foreground pt-16">
                      <div className="text-center">
                        <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50" />
                        <p className="text-sm">No feedback this week</p>
                      </div>
                    </div>
                  )}
              </div>
              {recentFeedback.length > 0 && (
                <div className="px-4 py-2 bg-[#1a1a1a] border-t border-[#2a2a2a] flex items-center justify-center gap-2">
                  {!isScrollPaused ? (
                    <p className="text-xs text-muted-foreground text-center">
                      Auto-scrolling • Hover to pause
                    </p>
                  ) : (
                    <div className="flex items-center gap-2">
                      <p className="text-xs text-muted-foreground">
                        Scrolling paused
                      </p>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 px-2 text-xs"
                        onClick={() => setIsScrollPaused(false)}
                      >
                        Resume
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </Card>
          </div>
        </div>
      </div>

      {/* Detailed Analytics Section */}
      <div className="space-y-8">
        {/* Section Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-primary/10">
              <BarChart3 className="h-5 w-5 text-primary" />
            </div>
            <div>
              <h2 className="text-2xl font-bold tracking-tight">Detailed Analytics</h2>
              <p className="text-sm text-muted-foreground">
                Comprehensive feedback analysis and trends
              </p>
            </div>
          </div>
        </div>

        {/* Enhanced Controls Row */}
        <Card className="border-2 border-dashed border-muted-foreground/20">
          <CardHeader className="pb-4">
            <div className="flex items-center gap-2">
              <Settings className="h-4 w-4 text-muted-foreground" />
              <CardTitle className="text-lg">Analysis Controls</CardTitle>
            </div>
            <CardDescription>
              Configure your analytics view and time range
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-3">
                <label className="text-sm font-medium text-foreground">Date Range</label>
                <EnhancedDateRange
                  onSelectDateRange={handlePresetSelect}
                  currentDateRange={dateRange}
                  currentPreset={currentPreset}
                />
              </div>

              <div className="space-y-3">
                <label className="text-sm font-medium text-foreground">Group By</label>
                <Select
                  value={groupBy}
                  onValueChange={(value) => setGroupBy(value as 'day' | 'week' | 'month')}
                >
                  <SelectTrigger className="h-10">
                    <SelectValue placeholder="Select grouping" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="day">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        Daily View
                      </div>
                    </SelectItem>
                    <SelectItem value="week">
                      <div className="flex items-center gap-2">
                        <CalendarDays className="h-4 w-4" />
                        Weekly View
                      </div>
                    </SelectItem>
                    <SelectItem value="month">
                      <div className="flex items-center gap-2">
                        <CalendarRange className="h-4 w-4" />
                        Monthly View
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Enhanced Summary Statistics */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
            <h3 className="text-lg font-semibold">Summary Statistics</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
            {/* Total Feedback Card */}
            <Card className="lg:col-span-2 border-l-4 border-l-primary">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-muted-foreground">Total Feedback</p>
                    <div className="text-3xl font-bold tracking-tight">
                      {loading ? (
                        <Loader2 className="h-6 w-6 animate-spin" />
                      ) : (
                        feedbackData?.totalCount?.toLocaleString() || '0'
                      )}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      for selected period
                    </p>
                  </div>
                  <div className="p-3 rounded-full bg-primary/10">
                    <MessageSquare className="h-6 w-6 text-primary" />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Individual Feedback Type Cards */}
            {loading ? (
              Array.from({ length: 4 }).map((_, index) => (
                <Card key={index} className="relative overflow-hidden">
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-4 h-4 rounded-full bg-muted animate-pulse" />
                      <div className="h-3 w-20 bg-muted animate-pulse rounded" />
                    </div>
                    <div className="mt-3 space-y-2">
                      <div className="h-7 w-16 bg-muted animate-pulse rounded" />
                      <div className="h-3 w-12 bg-muted animate-pulse rounded" />
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              feedbackData?.buttonCounts.map((item) => {
                const percentage = feedbackData.totalCount > 0
                  ? (item.count / feedbackData.totalCount) * 100
                  : 0;

                return (
                  <Card key={item._id} className="relative overflow-hidden group hover:shadow-md transition-shadow">
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-3 mb-3">
                        <div
                          className="w-4 h-4 rounded-full ring-2 ring-background shadow-sm"
                          style={{ backgroundColor: getButtonColor(item._id) }}
                        />
                        <span className="text-sm font-medium text-foreground">
                          {getButtonLabel(item._id)}
                        </span>
                      </div>
                      <div className="space-y-1">
                        <div className="text-2xl font-bold tracking-tight">
                          {item.count.toLocaleString()}
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="text-sm font-medium text-muted-foreground">
                            {percentage.toFixed(1)}%
                          </div>
                          <div className="flex-1 h-1.5 bg-muted rounded-full overflow-hidden">
                            <div
                              className="h-full rounded-full transition-all duration-500"
                              style={{
                                backgroundColor: getButtonColor(item._id),
                                width: `${Math.min(percentage, 100)}%`
                              }}
                            />
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })
            )}
          </div>
        </div>

        {/* Enhanced Charts Section */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
            <h3 className="text-lg font-semibold">Visual Analytics</h3>
          </div>

          <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
            {/* Feedback Over Time Chart */}
            <Card className="border-2">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-lg">Feedback Over Time</CardTitle>
                    <CardDescription className="mt-1">
                      Trends and patterns by {groupBy === 'day' ? 'day' : groupBy === 'week' ? 'week' : 'month'}
                    </CardDescription>
                  </div>
                  <div className="p-2 rounded-lg bg-muted/50">
                    <TrendingUp className="h-4 w-4 text-muted-foreground" />
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                {loading ? (
                  <div className="flex flex-col justify-center items-center h-80 space-y-3">
                    <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                    <p className="text-sm text-muted-foreground">Loading chart data...</p>
                  </div>
                ) : feedbackData && feedbackData.timeSeriesData && feedbackData.timeSeriesData.length > 0 ? (
                  <ChartContainer config={lineChartConfig} className="h-80 w-full">
                    <LineChart
                      accessibilityLayer
                      data={prepareLineChartData()}
                      margin={{
                        left: 12,
                        right: 12,
                        top: 12,
                        bottom: 12,
                      }}
                    >
                      <CartesianGrid
                        vertical={false}
                        strokeDasharray="3 3"
                        className="stroke-muted/30"
                      />
                      <XAxis
                        dataKey="date"
                        tickLine={false}
                        axisLine={false}
                        tickMargin={8}
                        className="text-xs"
                        tickFormatter={(value) => {
                          if (groupBy === 'month') return value.slice(0, 3);
                          return value;
                        }}
                      />
                      <ChartTooltip
                        cursor={{ strokeDasharray: "3 3", stroke: "hsl(var(--muted-foreground))" }}
                        content={<ChartTooltipContent />}
                      />
                      <Line
                        dataKey="veryHappy"
                        type="monotone"
                        stroke="#0daa5d"
                        strokeWidth={3}
                        dot={false}
                        activeDot={{ r: 4, stroke: "#0daa5d", strokeWidth: 2, fill: "#0daa5d" }}
                      />
                      <Line
                        dataKey="happy"
                        type="monotone"
                        stroke="#99cd9b"
                        strokeWidth={3}
                        dot={false}
                        activeDot={{ r: 4, stroke: "#99cd9b", strokeWidth: 2, fill: "#99cd9b" }}
                      />
                      <Line
                        dataKey="unhappy"
                        type="monotone"
                        stroke="#ef9ea1"
                        strokeWidth={3}
                        dot={false}
                        activeDot={{ r: 4, stroke: "#ef9ea1", strokeWidth: 2, fill: "#ef9ea1" }}
                      />
                      <Line
                        dataKey="veryUnhappy"
                        type="monotone"
                        stroke="#e24e67"
                        strokeWidth={3}
                        dot={false}
                        activeDot={{ r: 4, stroke: "#e24e67", strokeWidth: 2, fill: "#e24e67" }}
                      />
                    </LineChart>
                  </ChartContainer>
                ) : (
                  <div className="flex flex-col justify-center items-center h-80 space-y-3 text-muted-foreground">
                    <BarChart3 className="h-12 w-12 opacity-50" />
                    <div className="text-center">
                      <p className="font-medium">No data available</p>
                      <p className="text-sm">Try adjusting your date range or filters</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Feedback Distribution Chart */}
            <Card className="border-2">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-lg">Feedback Distribution</CardTitle>
                    <CardDescription className="mt-1">
                      Overall breakdown by rating type
                    </CardDescription>
                  </div>
                  <div className="p-2 rounded-lg bg-muted/50">
                    <Users className="h-4 w-4 text-muted-foreground" />
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                {loading ? (
                  <div className="flex flex-col justify-center items-center h-80 space-y-3">
                    <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                    <p className="text-sm text-muted-foreground">Loading distribution...</p>
                  </div>
                ) : feedbackData && feedbackData.buttonCounts && feedbackData.buttonCounts.length > 0 ? (
                  <ChartContainer
                    config={pieChartConfig}
                    className="mx-auto aspect-square max-h-[320px]"
                  >
                    <PieChart>
                      <ChartTooltip
                        cursor={false}
                        content={<ChartTooltipContent hideLabel />}
                      />
                      <Pie
                        data={preparePieChartData()}
                        dataKey="count"
                        nameKey="feedback"
                        stroke="2"
                        strokeWidth={2}
                        innerRadius={60}
                        outerRadius={120}
                      />
                    </PieChart>
                  </ChartContainer>
                ) : (
                  <div className="flex flex-col justify-center items-center h-80 space-y-3 text-muted-foreground">
                    <Users className="h-12 w-12 opacity-50" />
                    <div className="text-center">
                      <p className="font-medium">No distribution data</p>
                      <p className="text-sm">Try adjusting your date range or filters</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Follow-up Response Bar Charts */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
            <h3 className="text-lg font-semibold">Follow-up Responses</h3>
          </div>

          <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
            {/* Positive Follow-up Responses Chart */}
            <Card className="border-2">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-lg">What was good?</CardTitle>
                    <CardDescription className="mt-1">
                      Follow-up responses from happy customers
                    </CardDescription>
                  </div>
                  <div className="p-2 rounded-lg bg-green-500/10">
                    <TrendingUp className="h-4 w-4 text-green-600" />
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                {loadingFollowUpData ? (
                  <div className="flex flex-col justify-center items-center h-80 space-y-3">
                    <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                    <p className="text-sm text-muted-foreground">Loading positive responses...</p>
                  </div>
                ) : preparePositiveBarChartData().length > 0 ? (
                  <ChartContainer config={positiveBarChartConfig} className="h-80 w-full">
                    <BarChart
                      accessibilityLayer
                      data={preparePositiveBarChartData()}
                      layout="vertical"
                      margin={{
                        right: 16,
                        left: 8,
                        top: 8,
                        bottom: 8,
                      }}
                    >
                      <CartesianGrid horizontal={false} />
                      <YAxis
                        dataKey="response"
                        type="category"
                        tickLine={false}
                        tickMargin={10}
                        axisLine={false}
                        tickFormatter={(value) => value.length > 20 ? value.slice(0, 20) + '...' : value}
                        hide
                      />
                      <XAxis dataKey="count" type="number" hide />
                      <ChartTooltip
                        cursor={false}
                        content={<ChartTooltipContent indicator="line" />}
                      />
                      <Bar
                        dataKey="count"
                        layout="vertical"
                        fill="#0daa5d"
                        radius={4}
                      >
                        <LabelList
                          dataKey="response"
                          position="insideLeft"
                          offset={8}
                          className="fill-white"
                          fontSize={12}
                          formatter={(value) => value.length > 25 ? value.slice(0, 25) + '...' : value}
                        />
                        <LabelList
                          dataKey="count"
                          position="right"
                          offset={8}
                          className="fill-foreground"
                          fontSize={12}
                        />
                      </Bar>
                    </BarChart>
                  </ChartContainer>
                ) : (
                  <div className="flex flex-col justify-center items-center h-80 space-y-3 text-muted-foreground">
                    <MessageSquare className="h-12 w-12 opacity-50" />
                    <div className="text-center">
                      <p className="font-medium">No positive responses</p>
                      <p className="text-sm">No follow-up responses from happy customers in this period</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Negative Follow-up Responses Chart */}
            <Card className="border-2">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-lg">What could be improved?</CardTitle>
                    <CardDescription className="mt-1">
                      Follow-up responses from unhappy customers
                    </CardDescription>
                  </div>
                  <div className="p-2 rounded-lg bg-red-500/10">
                    <MessageSquare className="h-4 w-4 text-red-600" />
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                {loadingFollowUpData ? (
                  <div className="flex flex-col justify-center items-center h-80 space-y-3">
                    <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                    <p className="text-sm text-muted-foreground">Loading negative responses...</p>
                  </div>
                ) : prepareNegativeBarChartData().length > 0 ? (
                  <ChartContainer config={negativeBarChartConfig} className="h-80 w-full">
                    <BarChart
                      accessibilityLayer
                      data={prepareNegativeBarChartData()}
                      layout="vertical"
                      margin={{
                        right: 16,
                        left: 8,
                        top: 8,
                        bottom: 8,
                      }}
                    >
                      <CartesianGrid horizontal={false} />
                      <YAxis
                        dataKey="response"
                        type="category"
                        tickLine={false}
                        tickMargin={10}
                        axisLine={false}
                        tickFormatter={(value) => value.length > 20 ? value.slice(0, 20) + '...' : value}
                        hide
                      />
                      <XAxis dataKey="count" type="number" hide />
                      <ChartTooltip
                        cursor={false}
                        content={<ChartTooltipContent indicator="line" />}
                      />
                      <Bar
                        dataKey="count"
                        layout="vertical"
                        fill="#e24e67"
                        radius={4}
                      >
                        <LabelList
                          dataKey="response"
                          position="insideLeft"
                          offset={8}
                          className="fill-white"
                          fontSize={12}
                          formatter={(value) => value.length > 25 ? value.slice(0, 25) + '...' : value}
                        />
                        <LabelList
                          dataKey="count"
                          position="right"
                          offset={8}
                          className="fill-foreground"
                          fontSize={12}
                        />
                      </Bar>
                    </BarChart>
                  </ChartContainer>
                ) : (
                  <div className="flex flex-col justify-center items-center h-80 space-y-3 text-muted-foreground">
                    <MessageSquare className="h-12 w-12 opacity-50" />
                    <div className="text-center">
                      <p className="font-medium">No negative responses</p>
                      <p className="text-sm">No follow-up responses from unhappy customers in this period</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}

export default HappyOrNotFeedback;
