import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { DollarSign, CheckCircle, XCircle, AlertCircle } from 'lucide-react';

interface PricingOnlySectionProps {
  control: any;
  disabled?: boolean;
  auditType: 'buy' | 'pawn' | 'price';
}

/**
 * Pricing section component without Authorized Limit Check
 * Shows only pricing assessment for the Item Assessment step
 */
export function PricingOnlySection({ control, disabled = false, auditType }: PricingOnlySectionProps) {
  // Common fail reasons for pricing
  const pricingFailReasons = [
    { id: 'overpaid', label: 'Overpaid for item' },
    { id: 'underpaid', label: 'Underpaid for item' },
    { id: 'no_market_research', label: 'No market research conducted' },
    { id: 'incorrect_valuation', label: 'Incorrect valuation method' },
    { id: 'pricing_policy_violation', label: 'Pricing policy violation' },
  ];

  // Helper function to render status badge
  const renderStatusBadge = (status: string) => {
    switch (status) {
      case 'pass':
        return <Badge variant="success" className="flex items-center gap-1"><CheckCircle className="h-3 w-3" />Pass</Badge>;
      case 'fail':
        return <Badge variant="destructive" className="flex items-center gap-1"><XCircle className="h-3 w-3" />Fail</Badge>;
      default:
        return <Badge variant="secondary" className="flex items-center gap-1"><AlertCircle className="h-3 w-3" />Not Assessed</Badge>;
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <DollarSign className="h-5 w-5 text-primary" />
            <CardTitle>Pricing Assessment</CardTitle>
          </div>
          <FormField
            control={control}
            name="pricing.status"
            render={({ field }) => renderStatusBadge(field.value)}
          />
        </div>
        <CardDescription>
          Assess whether the pricing was appropriate and followed company guidelines
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <FormField
          control={control}
          name="pricing.status"
          render={({ field }) => (
            <FormItem className="space-y-3">
              <FormLabel>Assessment</FormLabel>
              <FormControl>
                <RadioGroup
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  className="flex flex-col space-y-2"
                  disabled={disabled}
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="pass" id="pricing-pass" />
                    <FormLabel htmlFor="pricing-pass" className="cursor-pointer">Pass</FormLabel>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="fail" id="pricing-fail" />
                    <FormLabel htmlFor="pricing-fail" className="cursor-pointer">Fail</FormLabel>
                  </div>
                </RadioGroup>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Show fail reasons if status is "fail" */}
        <FormField
          control={control}
          name="pricing.status"
          render={({ field }) => (
            <div>
              {field.value === 'fail' && (
                <FormField
                  control={control}
                  name="pricing.failReasons"
                  render={({ field: failReasonsField }) => (
                    <FormItem>
                      <FormLabel>Fail Reasons</FormLabel>
                      <FormDescription>Select all that apply</FormDescription>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 border rounded-md p-3">
                        {pricingFailReasons.map((reason) => (
                          <FormItem
                            key={reason.id}
                            className="flex flex-row items-start space-x-3 space-y-0 p-2 hover:bg-accent rounded-md"
                          >
                            <FormControl>
                              <Checkbox
                                checked={failReasonsField.value?.includes(reason.id)}
                                onCheckedChange={(checked) => {
                                  const currentReasons = failReasonsField.value || [];
                                  if (checked) {
                                    failReasonsField.onChange([...currentReasons, reason.id]);
                                  } else {
                                    failReasonsField.onChange(currentReasons.filter((r: string) => r !== reason.id));
                                  }
                                }}
                                disabled={disabled}
                              />
                            </FormControl>
                            <FormLabel className="text-sm font-normal cursor-pointer">
                              {reason.label}
                            </FormLabel>
                          </FormItem>
                        ))}
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
            </div>
          )}
        />

        {/* Additional pricing fields for detailed assessment */}
        <FormField
          control={control}
          name="pricing.status"
          render={({ field }) => (
            <div>
              {(field.value === 'pass' || field.value === 'fail') && (
                <div className="space-y-4 border rounded-md p-4 bg-muted/30">
                  <h4 className="font-medium">Pricing Details</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <FormField
                      control={control}
                      name="pricing.suggestedPrice"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Suggested Price</FormLabel>
                          <FormControl>
                            <Input
                              type="text"
                              placeholder="Enter suggested price"
                              {...field}
                              disabled={disabled}
                            />
                          </FormControl>
                          <FormDescription>
                            Recommended market price
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={control}
                      name="pricing.costPrice"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Cost Price</FormLabel>
                          <FormControl>
                            <Input
                              type="text"
                              placeholder="Enter cost price"
                              {...field}
                              disabled={disabled}
                            />
                          </FormControl>
                          <FormDescription>
                            Actual cost paid
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={control}
                      name="pricing.ticketPrice"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Ticket Price</FormLabel>
                          <FormControl>
                            <Input
                              type="text"
                              placeholder="Enter ticket price"
                              {...field}
                              disabled={disabled}
                            />
                          </FormControl>
                          <FormDescription>
                            Intended selling price
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              )}
            </div>
          )}
        />

        <FormField
          control={control}
          name="pricing.notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Notes</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Enter any additional notes about pricing"
                  className="min-h-[100px]"
                  {...field}
                  disabled={disabled}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </CardContent>
    </Card>
  );
}
