#!/usr/bin/env node

/**
 * Debug <PERSON>ript for Screenshot Service Issues
 * 
 * This script helps debug the "Navigating frame was detached" error
 * by testing different configurations and providing detailed logging.
 */

const os = require('os');
const puppeteer = require('puppeteer');

// Mock the updateStatus function for testing
let debugLogs = [];
function updateStatus(update) {
  if (update.message) {
    const logEntry = {
      time: new Date(),
      message: update.message
    };
    debugLogs.push(logEntry);
    console.log(`[${logEntry.time.toISOString()}] ${update.message}`);
  }
}

// Test different Puppeteer configurations
const configurations = {
  original: {
    headless: 'new',
    args: ['--no-sandbox', '--disable-setuid-sandbox', '--window-size=1920,1080']
  },
  
  enhanced: {
    headless: 'new',
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-gpu',
      '--disable-gpu-sandbox',
      '--disable-software-rasterizer',
      '--disable-background-timer-throttling',
      '--disable-backgrounding-occluded-windows',
      '--disable-renderer-backgrounding',
      '--disable-features=TranslateUI,VizDisplayCompositor',
      '--disable-ipc-flooding-protection',
      '--window-size=1920,1080',
      '--no-first-run',
      '--disable-default-apps',
      '--disable-popup-blocking',
      '--disable-translate',
      '--disable-extensions',
      '--disable-web-security',
      '--disable-features=site-per-process',
      '--disable-hang-monitor',
      '--disable-prompt-on-repost',
      '--disable-background-networking',
      '--disable-sync',
      '--metrics-recording-only',
      '--no-report-upload',
      '--safebrowsing-disable-auto-update',
      '--enable-automation',
      '--password-store=basic',
      '--use-mock-keychain'
    ]
  },

  minimal: {
    headless: 'new',
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--window-size=1920,1080'
    ]
  }
};

async function testConfiguration(configName, config, testUrl) {
  console.log(`\n🧪 Testing configuration: ${configName}`);
  console.log('='.repeat(50));
  
  let browser = null;
  try {
    updateStatus({ message: `Launching browser with ${configName} configuration...` });
    browser = await puppeteer.launch(config);
    
    const page = await browser.newPage();
    
    // Configure page settings
    page.setDefaultNavigationTimeout(30000);
    page.setDefaultTimeout(30000);
    
    await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
    
    await page.setViewport({
      width: 1920,
      height: 1080,
      deviceScaleFactor: 1
    });

    // Add event listeners for debugging
    page.on('error', (error) => {
      updateStatus({ message: `Page error: ${error.message}` });
    });

    page.on('pageerror', (error) => {
      updateStatus({ message: `Page script error: ${error.message}` });
    });

    page.on('framedetached', (frame) => {
      updateStatus({ message: `Frame detached: ${frame.url()}` });
    });

    page.on('framenavigated', (frame) => {
      updateStatus({ message: `Frame navigated: ${frame.url()}` });
    });

    // Test navigation
    updateStatus({ message: `Navigating to: ${testUrl}` });
    
    const startTime = Date.now();
    await page.goto(testUrl, {
      waitUntil: ['networkidle0', 'domcontentloaded'],
      timeout: 30000
    });
    const navigationTime = Date.now() - startTime;
    
    updateStatus({ message: `Navigation completed in ${navigationTime}ms` });
    
    // Wait for content
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Test page responsiveness
    const readyState = await page.evaluate(() => document.readyState);
    updateStatus({ message: `Document ready state: ${readyState}` });
    
    // Take screenshot
    updateStatus({ message: 'Taking screenshot...' });
    const screenshotStartTime = Date.now();
    
    const screenshot = await page.screenshot({
      encoding: null,
      fullPage: false,
      type: 'png',
      clip: {
        x: 0,
        y: 0,
        width: 1920,
        height: 1080
      }
    });
    
    const screenshotTime = Date.now() - screenshotStartTime;
    updateStatus({ message: `Screenshot completed in ${screenshotTime}ms (${screenshot.length} bytes)` });
    
    console.log(`✅ ${configName} configuration: SUCCESS`);
    console.log(`   Navigation time: ${navigationTime}ms`);
    console.log(`   Screenshot time: ${screenshotTime}ms`);
    console.log(`   Screenshot size: ${screenshot.length} bytes`);
    
    return { success: true, navigationTime, screenshotTime, size: screenshot.length };
    
  } catch (error) {
    console.log(`❌ ${configName} configuration: FAILED`);
    console.log(`   Error: ${error.message}`);
    updateStatus({ message: `Configuration ${configName} failed: ${error.message}` });
    
    return { success: false, error: error.message };
  } finally {
    if (browser) {
      try {
        await browser.close();
        updateStatus({ message: `Browser closed for ${configName}` });
      } catch (closeError) {
        updateStatus({ message: `Browser close error for ${configName}: ${closeError.message}` });
      }
    }
  }
}

async function main() {
  console.log('🔍 Screenshot Service Debug Tool');
  console.log('='.repeat(60));
  console.log(`Platform: ${os.platform()}`);
  console.log(`Architecture: ${os.arch()}`);
  console.log(`Node.js Version: ${process.version}`);
  
  // Test URLs - start with simple ones
  const testUrls = [
    'data:text/html,<html><head><title>Simple Test</title></head><body><h1>Test Page</h1><p>This is a simple test page.</p></body></html>',
    'https://httpbin.org/html',
    'https://example.com'
  ];
  
  for (const testUrl of testUrls) {
    console.log(`\n🌐 Testing URL: ${testUrl.substring(0, 80)}${testUrl.length > 80 ? '...' : ''}`);
    console.log('='.repeat(60));
    
    const results = {};
    
    for (const [configName, config] of Object.entries(configurations)) {
      const result = await testConfiguration(configName, config, testUrl);
      results[configName] = result;
      
      // Wait between tests to avoid resource conflicts
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    // Summary for this URL
    console.log(`\n📊 Summary for ${testUrl.substring(0, 50)}${testUrl.length > 50 ? '...' : ''}:`);
    for (const [configName, result] of Object.entries(results)) {
      if (result.success) {
        console.log(`   ✅ ${configName}: ${result.navigationTime}ms nav, ${result.screenshotTime}ms screenshot`);
      } else {
        console.log(`   ❌ ${configName}: ${result.error}`);
      }
    }
  }
  
  console.log('\n🎯 Debug session completed!');
  console.log('\n📝 All debug logs:');
  debugLogs.forEach((log, index) => {
    console.log(`   ${index + 1}. [${log.time.toISOString()}] ${log.message}`);
  });
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testConfiguration, configurations };
