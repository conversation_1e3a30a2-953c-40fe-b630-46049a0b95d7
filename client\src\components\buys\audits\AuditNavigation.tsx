import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { cn } from '@/lib/utils';
import { BarChart3, Flag, Plus, FileText, Users, PieChart, Download } from 'lucide-react';

/**
 * Navigation component for the Buy/Pawn Audit feature
 */
export function AuditNavigation() {
  const location = useLocation();
  const { user } = useAuth();
  const canAccessAdminRoutes = user?.role === 'admin' || user?.role === 'manager';

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  return (
    <div className="flex flex-wrap gap-2 mb-6">
      <Link
        to="/buys/audits"
        className={cn(
          "flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors",
          isActive('/buys/audits')
            ? "bg-primary text-primary-foreground"
            : "bg-muted hover:bg-muted/80"
        )}
      >
        <BarChart3 className="mr-2 h-4 w-4" />
        Dashboard
      </Link>

      <Link
        to="/buys/audits/flagged"
        className={cn(
          "flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors",
          isActive('/buys/audits/flagged')
            ? "bg-primary text-primary-foreground"
            : "bg-muted hover:bg-muted/80"
        )}
      >
        <Flag className="mr-2 h-4 w-4" />
        Flagged Audits
      </Link>

      {canAccessAdminRoutes && (
        <>
          <Link
            to="/buys/audits/reporting"
            className={cn(
              "flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors",
              isActive('/buys/audits/reporting')
                ? "bg-primary text-primary-foreground"
                : "bg-muted hover:bg-muted/80"
            )}
          >
            <FileText className="mr-2 h-4 w-4" />
            Reporting
          </Link>

          <Link
            to="/buys/audits/staff"
            className={cn(
              "flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors",
              isActive('/buys/audits/staff')
                ? "bg-primary text-primary-foreground"
                : "bg-muted hover:bg-muted/80"
            )}
          >
            <Users className="mr-2 h-4 w-4" />
            Staff Performance
          </Link>

          <Link
            to="/buys/audits/metrics"
            className={cn(
              "flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors",
              isActive('/buys/audits/metrics')
                ? "bg-primary text-primary-foreground"
                : "bg-muted hover:bg-muted/80"
            )}
          >
            <PieChart className="mr-2 h-4 w-4" />
            Metrics
          </Link>

          <Link
            to="/buys/audits/exports"
            className={cn(
              "flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors",
              isActive('/buys/audits/exports')
                ? "bg-primary text-primary-foreground"
                : "bg-muted hover:bg-muted/80"
            )}
          >
            <Download className="mr-2 h-4 w-4" />
            Exports
          </Link>
        </>
      )}
    </div>
  );
}
