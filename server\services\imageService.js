const fs = require('fs');
const path = require('path');
const axios = require('axios');

// Try to load optional dependencies
let uuidv4;
let sharp;
try {
  const uuid = require('uuid');
  uuidv4 = uuid.v4;
} catch (error) {
  console.warn('uuid package not found. Using fallback for unique IDs.');
  // Fallback implementation for uuidv4
  uuidv4 = () => Math.random().toString(36).substring(2, 15) +
             Math.random().toString(36).substring(2, 15) +
             Date.now().toString(36);
}

try {
  sharp = require('sharp');
} catch (error) {
  console.warn('sharp package not found. Image processing will be limited.');
}

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(__dirname, '../uploads');
const trademeImagesDir = path.join(uploadsDir, 'trademe');

// Ensure directories exist
try {
  if (!fs.existsSync(uploadsDir)) {
    fs.mkdirSync(uploadsDir, { recursive: true });
  }

  if (!fs.existsSync(trademeImagesDir)) {
    fs.mkdirSync(trademeImagesDir, { recursive: true });
  }
} catch (error) {
  console.error('Error creating directories:', error);
}

/**
 * Download an image from a URL and save it locally
 * @param {string} imageUrl - The URL of the image to download
 * @param {string} stockCode - The stock code (for organizing images)
 * @param {string} listingId - The TradeMe listing ID (as fallback if stockCode is not provided)
 * @returns {Promise<Object>} - Object containing paths to the saved images
 */
async function downloadAndSaveImage(imageUrl, stockCode, listingId) {
  try {
    if (!imageUrl) {
      throw new Error('No image URL provided');
    }

    // Use stockCode if provided, otherwise fall back to listingId
    // If stockCode is empty or just whitespace, use "listing-" + listingId as the folder name
    let folderName;
    if (stockCode && stockCode.trim() !== '') {
      folderName = stockCode.trim();
    } else if (listingId) {
      folderName = `listing-${listingId}`;
    } else {
      throw new Error('No stockCode or listingId provided for image organization');
    }

    // Create a directory for this item if it doesn't exist
    const itemDir = path.join(trademeImagesDir, folderName);
    if (!fs.existsSync(itemDir)) {
      fs.mkdirSync(itemDir, { recursive: true });
    }

    // Generate a unique filename
    const filename = `${uuidv4()}${path.extname(imageUrl) || '.jpg'}`;
    const originalPath = path.join(itemDir, filename);
    const compressedPath = path.join(itemDir, `compressed_${filename}`);
    const thumbnailPath = path.join(itemDir, `thumbnail_${filename}`);

    // Check if this image URL has already been downloaded to avoid duplicates
    const existingFiles = fs.readdirSync(itemDir);
    for (const file of existingFiles) {
      if (file.endsWith('.jpg') || file.endsWith('.jpeg') || file.endsWith('.png')) {
        // Skip downloading if we already have this image
        if (file.includes(path.basename(imageUrl, path.extname(imageUrl)))) {
          return {
            original: `/uploads/trademe/${folderName}/${file}`,
            compressed: `/uploads/trademe/${folderName}/compressed_${file}`,
            thumbnail: `/uploads/trademe/${folderName}/thumbnail_${file}`
          };
        }
      }
    }

    // Download the image
    const response = await axios({
      method: 'get',
      url: imageUrl,
      responseType: 'arraybuffer'
    });

    // Save the original image
    fs.writeFileSync(originalPath, response.data);

    // Create compressed and thumbnail versions if sharp is available
    if (sharp) {
      // Create a compressed version
      await sharp(response.data)
        .resize(800) // Resize to max width of 800px
        .jpeg({ quality: 80 })
        .toFile(compressedPath);

      // Create a thumbnail
      await sharp(response.data)
        .resize(200) // Resize to max width of 200px
        .jpeg({ quality: 70 })
        .toFile(thumbnailPath);
    } else {
      // If sharp is not available, just copy the original
      fs.copyFileSync(originalPath, compressedPath);
      fs.copyFileSync(originalPath, thumbnailPath);
    }

    // Return the paths relative to the server root
    return {
      original: `/uploads/trademe/${folderName}/${filename}`,
      compressed: `/uploads/trademe/${folderName}/compressed_${filename}`,
      thumbnail: `/uploads/trademe/${folderName}/thumbnail_${filename}`
    };
  } catch (error) {
    console.error('Error downloading and saving image:', error);
    // Return the original URL if we couldn't save it locally
    return {
      original: imageUrl,
      compressed: imageUrl,
      thumbnail: imageUrl
    };
  }
}

/**
 * Check if an image URL is a local path or an external URL
 * @param {string} imageUrl - The image URL to check
 * @returns {boolean} - True if the image is local, false if it's external
 */
function isLocalImage(imageUrl) {
  if (!imageUrl) return false;
  return imageUrl.startsWith('/uploads/');
}

module.exports = {
  downloadAndSaveImage,
  isLocalImage
};
