const Contact = require('../models/Contact');

class ContactService {
  // Get all contacts with pagination
  static async getAll(page = 1, limit = 10, searchTerm = '', type = null) {
    try {
      console.log(`Fetching contacts - page: ${page}, limit: ${limit}, search: ${searchTerm}, type: ${type || 'all'}`);

      const skip = (page - 1) * limit;

      let query = {};

      // Filter by type if provided
      if (type) {
        query.type = type;
      }

      // Add search functionality if searchTerm is provided
      if (searchTerm) {
        query = {
          ...query,
          $or: [
            { name: { $regex: searchTerm, $options: 'i' } },
            { email: { $regex: searchTerm, $options: 'i' } },
            { phone: { $regex: searchTerm, $options: 'i' } }
          ]
        };
      }

      // Execute query with pagination
      const contacts = await Contact.find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit);

      // Get total count for pagination
      const total = await Contact.countDocuments(query);

      return {
        contacts,
        pagination: {
          total,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      console.error(`Error fetching contacts: ${error.stack}`);
      throw new Error(`Error fetching contacts: ${error.message}`);
    }
  }

  // Get contact by ID
  static async getById(id) {
    try {
      console.log(`Fetching contact with ID: ${id}`);
      const contact = await Contact.findById(id);
      if (!contact) {
        console.log(`Contact with ID ${id} not found`);
        throw new Error('Contact not found');
      }
      return contact;
    } catch (error) {
      console.error(`Error fetching contact: ${error.stack}`);
      throw new Error(`Error fetching contact: ${error.message}`);
    }
  }

  // Create a new contact
  static async create(contactData) {
    try {
      console.log('Creating new contact:', contactData.name);

      // Only check email uniqueness if an email is provided and not empty
      if (contactData.email && contactData.email.trim() !== '') {
        const existingContact = await Contact.findOne({ email: contactData.email.toLowerCase() });
        if (existingContact) {
          console.log(`Contact with email ${contactData.email} already exists`);
          throw new Error('A contact with this email already exists');
        }
      } else {
        // Set email to undefined when empty to avoid uniqueness issues
        // This avoids both empty strings and null values
        contactData.email = undefined;
      }

      const contact = new Contact(contactData);
      await contact.save();
      console.log(`Contact created successfully with ID: ${contact._id}`);
      return contact;
    } catch (error) {
      console.error(`Error creating contact: ${error.stack}`);
      throw new Error(`Error creating contact: ${error.message}`);
    }
  }

  // Update an existing contact
  static async update(id, contactData) {
    try {
      console.log(`Updating contact with ID: ${id}`);

      // Only check email uniqueness if an email is provided and not empty
      if (contactData.email && contactData.email.trim() !== '') {
        const existingContact = await Contact.findOne({
          email: contactData.email.toLowerCase(),
          _id: { $ne: id } // Exclude current contact from check
        });

        if (existingContact) {
          console.log(`Another contact with email ${contactData.email} already exists`);
          throw new Error('A contact with this email already exists');
        }
      }

      const contact = await Contact.findByIdAndUpdate(
        id,
        { ...contactData, updatedAt: Date.now() },
        { new: true, runValidators: true }
      );

      if (!contact) {
        console.log(`Contact with ID ${id} not found for update`);
        throw new Error('Contact not found');
      }

      console.log(`Contact with ID ${id} updated successfully`);
      return contact;
    } catch (error) {
      console.error(`Error updating contact: ${error.stack}`);
      throw new Error(`Error updating contact: ${error.message}`);
    }
  }

  // Delete a contact
  static async delete(id) {
    try {
      console.log(`Deleting contact with ID: ${id}`);
      const contact = await Contact.findByIdAndDelete(id);
      if (!contact) {
        console.log(`Contact with ID ${id} not found for deletion`);
        throw new Error('Contact not found');
      }
      console.log(`Contact with ID ${id} deleted successfully`);
      return contact;
    } catch (error) {
      console.error(`Error deleting contact: ${error.stack}`);
      throw new Error(`Error deleting contact: ${error.message}`);
    }
  }
}

module.exports = ContactService;