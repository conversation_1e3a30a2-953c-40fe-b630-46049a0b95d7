const User = require("../models/User");
const { generatePasswordHash } = require("../utils/password");

/**
 * Service for seeding initial data into the database
 */
class SeedService {
  /**
   * Seeds an admin user with predefined credentials
   * @returns {Object} Result of the operation with success status and message
   */
  async seedAdminUser() {
    try {
      // Check if admin user already exists
      const existingAdmin = await User.findOne({ email: "<EMAIL>" });

      if (existingAdmin) {
        console.log("Admin user already exists, skipping creation");
        return {
          success: true,
          message: "Admin user already exists",
          user: existingAdmin,
        };
      }

      // Generate password hash
      const passwordHash = await generatePasswordHash("admin123");

      // Create the admin user
      const adminUser = new User({
        email: "<EMAIL>",
        username: "admin",
        password: passwordHash,
        role: "admin",
        isActive: true,
      });

      await adminUser.save();
      console.log("Admin user created successfully");

      return {
        success: true,
        message: "Admin user created successfully",
        user: adminUser,
      };
    } catch (error) {
      console.error("Error seeding admin user:", error);
      return {
        success: false,
        message: error.message,
        error,
      };
    }
  }
}

module.exports = new SeedService();
