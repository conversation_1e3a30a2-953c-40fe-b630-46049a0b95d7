const express = require('express');
const router = express.Router();
const deviceCheckerService = require('../services/deviceCheckerService');
const { requireUser } = require('./middleware/auth');

// Get device checker settings
router.get('/settings', requireUser, async (req, res) => {
  try {
    // Only allow admin and managers to access settings
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        error: 'Only administrators and managers can access device checker settings'
      });
    }

    const result = await deviceCheckerService.getSettings();
    return res.json(result);
  } catch (error) {
    console.error('Error getting device checker settings:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Update device checker settings
router.put('/settings', requireUser, async (req, res) => {
  try {
    // Only allow admin and managers to update settings
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        error: 'Only administrators and managers can update device checker settings'
      });
    }

    const result = await deviceCheckerService.updateSettings(req.body);
    return res.json(result);
  } catch (error) {
    console.error('Error updating device checker settings:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Verify connection to the API
router.post('/verify-connection', requireUser, async (req, res) => {
  try {
    // Only allow admin and managers to verify connection
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        error: 'Only administrators and managers can verify API connection'
      });
    }

    const result = await deviceCheckerService.verifyConnection();
    return res.json(result);
  } catch (error) {
    console.error('Error verifying API connection:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get account balance
router.get('/balance', requireUser, async (req, res) => {
  try {
    const result = await deviceCheckerService.getAccountBalance();
    return res.json(result);
  } catch (error) {
    console.error('Error getting account balance:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get available services from local database
router.get('/services', requireUser, async (req, res) => {
  try {
    const result = await deviceCheckerService.getLocalServices();
    return res.json(result);
  } catch (error) {
    console.error('Error getting available services:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Refresh available services from external API
router.post('/refresh-services', requireUser, async (req, res) => {
  try {
    // Only allow admin and managers to refresh services
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        error: 'Only administrators and managers can refresh services'
      });
    }

    const result = await deviceCheckerService.refreshAvailableServices();
    return res.json(result);
  } catch (error) {
    console.error('Error refreshing available services:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Lookup device information
router.post('/lookup', requireUser, async (req, res) => {
  try {
    // Allow only employee, manager, and admin roles
    if (!['employee', 'manager', 'admin'].includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to use the device checker'
      });
    }

    const result = await deviceCheckerService.lookupDevice(req.body, req.user);
    return res.json(result);
  } catch (error) {
    console.error('Error looking up device:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get lookup history
router.get('/history', requireUser, async (req, res) => {
  try {
    // Share lookup history between all authenticated users
    const query = { ...req.query };

    // If a specific userId is requested (e.g., for filtering), honor that request
    // Otherwise, show all lookups for all users

    const result = await deviceCheckerService.getLookupHistory(query);
    return res.json(result);
  } catch (error) {
    console.error('Error getting lookup history:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = router;
