import { useEffect, useState } from 'react';
import { getCustomers, Customer, PaginationData } from '@/api/customers';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Search, UserPlus, Mail, Phone } from 'lucide-react';
import { CustomerForm } from '@/components/CustomerForm';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogDescription } from '@/components/ui/dialog';
import { useToast } from '@/hooks/useToast';
import { PaginationControls } from '@/components/ui/pagination-controls';
import { useSearchParams, useNavigate } from 'react-router-dom';

export function Customers() {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearch, setDebouncedSearch] = useState('');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [pagination, setPagination] = useState<PaginationData>({
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 0
  });

  const { toast } = useToast();
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();

  // Initialize state from URL parameters
  useEffect(() => {
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';

    setPagination(prev => ({
      ...prev,
      page,
      limit
    }));

    setSearchTerm(search);
    setDebouncedSearch(search);
  }, [searchParams]);

  // Debounce search input
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(searchTerm);
      // Update URL with search term
      handleUrlUpdate(pagination.page, pagination.limit, searchTerm);
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Fetch customers when page, limit, or search changes
  useEffect(() => {
    fetchCustomers(pagination.page, pagination.limit, debouncedSearch);
  }, [pagination.page, pagination.limit, debouncedSearch]);

  const handleUrlUpdate = (page: number, limit: number, search: string) => {
    const params = new URLSearchParams();

    if (page !== 1) params.set('page', page.toString());
    if (limit !== 10) params.set('limit', limit.toString());
    if (search) params.set('search', search);

    setSearchParams(params);
  };

  const fetchCustomers = async (page: number, limit: number, search: string) => {
    setLoading(true);
    try {
      const data = await getCustomers(page, limit, search);
      setCustomers(data.customers);
      setPagination(data.pagination);
    } catch (error) {
      console.error('Failed to fetch customers:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.message || 'Failed to fetch customers',
      });
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
    handleUrlUpdate(page, pagination.limit, searchTerm);
  };

  const handleLimitChange = (limit: number) => {
    // When changing limit, reset to page 1
    setPagination(prev => ({ ...prev, limit, page: 1 }));
    handleUrlUpdate(1, limit, searchTerm);
  };

  const handleCustomerCreated = () => {
    setIsDialogOpen(false);
    fetchCustomers(pagination.page, pagination.limit, debouncedSearch);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleViewCustomer = (id: string) => {
    navigate(`/customers/${id}`);
  };

  if (loading && customers.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Loading customers...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Customers</h1>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button className="gap-2">
              <UserPlus className="h-4 w-4" />
              Add Customer
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add New Customer</DialogTitle>
              <DialogDescription>
                Create a new customer record. At least one contact method (email or phone) is required.
              </DialogDescription>
            </DialogHeader>
            <CustomerForm onSuccess={handleCustomerCreated} />
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Search & Filter</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search customers..."
              value={searchTerm}
              onChange={handleSearchChange}
              className="pl-9"
            />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Phone</TableHead>
                <TableHead>Address</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {customers.length > 0 ? (
                customers.map((customer) => (
                  <TableRow
                    key={customer._id}
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleViewCustomer(customer._id)}
                  >
                    <TableCell className="font-medium">{customer.name}</TableCell>
                    <TableCell>
                      {customer.email ? (
                        <a
                          href={`mailto:${customer.email}`}
                          className="text-blue-500 hover:underline flex items-center gap-1"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <Mail className="h-4 w-4" />
                          {customer.email}
                        </a>
                      ) : (
                        <span className="text-muted-foreground">—</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {customer.phone ? (
                        <a
                          href={`tel:${customer.phone}`}
                          className="text-blue-500 hover:underline flex items-center gap-1"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <Phone className="h-4 w-4" />
                          {customer.phone}
                        </a>
                      ) : (
                        <span className="text-muted-foreground">—</span>
                      )}
                    </TableCell>
                    <TableCell>{customer.address || <span className="text-muted-foreground">—</span>}</TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={4} className="text-center py-4">
                    {loading ? 'Loading customers...' : 'No customers found'}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>

          {pagination.totalPages > 0 && (
            <div className="p-4 border-t">
              <PaginationControls
                currentPage={pagination.page}
                totalPages={pagination.totalPages}
                onPageChange={handlePageChange}
                onLimitChange={handleLimitChange}
                limit={pagination.limit}
                totalItems={pagination.total}
              />
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}