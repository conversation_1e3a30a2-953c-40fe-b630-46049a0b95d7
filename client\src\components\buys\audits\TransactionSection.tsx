import { useState, useEffect } from 'react';
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { FileText, ClipboardCheck } from 'lucide-react';

interface TransactionSectionProps {
  control: any;
  disabled?: boolean;
}

/**
 * Component for the Transaction section of the audit form
 * This section is shown for Buy and Pawn audits
 */
export function TransactionSection({ control, disabled = false }: TransactionSectionProps) {
  // State to track the status of each assessment
  const [dataEntryStatus, setDataEntryStatus] = useState('not_assessed');
  const [itemConditionStatus, setItemConditionStatus] = useState('not_assessed');

  // Watch for changes in the form values
  useEffect(() => {
    const subscription = control._formState.submitCount;
    const dataEntryValue = control._getWatch('dataEntryQuality.status');
    const itemConditionValue = control._getWatch('itemConditionCheck.status');

    if (dataEntryValue) {
      setDataEntryStatus(dataEntryValue);
    }

    if (itemConditionValue) {
      setItemConditionStatus(itemConditionValue);
    }

    return () => {};
  }, [control]);

  // Common fail reasons for data entry quality
  const dataEntryFailReasons = [
    { id: 'missing_info', label: 'Missing information' },
    { id: 'incorrect_info', label: 'Incorrect information' },
    { id: 'incomplete_info', label: 'Incomplete information' },
    { id: 'wrong_category', label: 'Wrong category' },
    { id: 'wrong_description', label: 'Wrong description' },
  ];

  // Common fail reasons for item condition check
  const itemConditionFailReasons = [
    { id: 'condition_not_checked', label: 'Condition not checked' },
    { id: 'condition_misrepresented', label: 'Condition misrepresented' },
    { id: 'damage_not_noted', label: 'Damage not noted' },
    { id: 'missing_parts_not_noted', label: 'Missing parts not noted' },
    { id: 'functionality_not_tested', label: 'Functionality not tested' },
  ];

  // Function to render status badge
  const renderStatusBadge = (status: string) => {
    if (status === 'pass') {
      return <Badge className="bg-green-500 hover:bg-green-600">Pass</Badge>;
    } else if (status === 'fail') {
      return <Badge className="bg-red-500 hover:bg-red-600">Fail</Badge>;
    } else {
      return <Badge variant="outline">Not Assessed</Badge>;
    }
  };

  return (
    <Card className="mb-6">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div className="space-y-1">
          <CardTitle className="flex items-center">
            <ClipboardCheck className="mr-2 h-5 w-5" />
            Transaction Assessment
          </CardTitle>
          <CardDescription>
            Assess the quality of data entry and item condition check
          </CardDescription>
        </div>
        <div className="flex items-center space-x-2">
          <div className="text-sm text-muted-foreground">Data Entry:</div>
          {renderStatusBadge(dataEntryStatus)}
          <div className="text-sm text-muted-foreground ml-4">Item Condition:</div>
          {renderStatusBadge(itemConditionStatus)}
        </div>
      </CardHeader>

      <CardContent>
        <Tabs defaultValue="data-entry" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="data-entry" className="flex items-center">
              <FileText className="mr-2 h-4 w-4" />
              Data Entry Quality
            </TabsTrigger>
            <TabsTrigger value="item-condition" className="flex items-center">
              <ClipboardCheck className="mr-2 h-4 w-4" />
              Item Condition Check
            </TabsTrigger>
          </TabsList>

          {/* Data Entry Quality Tab */}
          <TabsContent value="data-entry" className="space-y-4 mt-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="md:col-span-1">
                <FormField
                  control={control}
                  name="dataEntryQuality.status"
                  render={({ field }) => (
                    <FormItem className="space-y-3">
                      <FormLabel>Assessment</FormLabel>
                      <FormControl>
                        <RadioGroup
                          onValueChange={(value) => {
                            field.onChange(value);
                            setDataEntryStatus(value);
                          }}
                          value={field.value}
                          className="flex flex-col space-y-1"
                          disabled={disabled}
                        >
                          <FormItem className="flex items-center space-x-3 space-y-0 p-2 rounded-md hover:bg-accent">
                            <FormControl>
                              <RadioGroupItem value="pass" id="dataEntryPass" />
                            </FormControl>
                            <FormLabel className="font-normal cursor-pointer w-full" htmlFor="dataEntryPass">
                              Pass
                            </FormLabel>
                          </FormItem>
                          <FormItem className="flex items-center space-x-3 space-y-0 p-2 rounded-md hover:bg-accent">
                            <FormControl>
                              <RadioGroupItem value="fail" id="dataEntryFail" />
                            </FormControl>
                            <FormLabel className="font-normal cursor-pointer w-full" htmlFor="dataEntryFail">
                              Fail
                            </FormLabel>
                          </FormItem>
                          <FormItem className="flex items-center space-x-3 space-y-0 p-2 rounded-md hover:bg-accent">
                            <FormControl>
                              <RadioGroupItem value="not_assessed" id="dataEntryNotAssessed" />
                            </FormControl>
                            <FormLabel className="font-normal cursor-pointer w-full" htmlFor="dataEntryNotAssessed">
                              Not Assessed
                            </FormLabel>
                          </FormItem>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="md:col-span-2">
                {/* Only show fail reasons if status is "fail" */}
                {dataEntryStatus === 'fail' ? (
                  <FormField
                    control={control}
                    name="dataEntryQuality.failReasons"
                    render={({ field }) => (
                      <FormItem>
                        <div className="mb-2">
                          <FormLabel>Fail Reasons</FormLabel>
                          <FormDescription>
                            Select all that apply
                          </FormDescription>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 border rounded-md p-3">
                          {dataEntryFailReasons.map((reason) => (
                            <FormItem
                              key={reason.id}
                              className="flex flex-row items-start space-x-3 space-y-0 p-2 hover:bg-accent rounded-md"
                            >
                              <FormControl>
                                <Checkbox
                                  checked={field.value?.includes(reason.id)}
                                  onCheckedChange={(checked) => {
                                    const currentValue = field.value || [];
                                    if (checked) {
                                      field.onChange([...currentValue, reason.id]);
                                    } else {
                                      field.onChange(
                                        currentValue.filter((value: string) => value !== reason.id)
                                      );
                                    }
                                  }}
                                  disabled={disabled}
                                />
                              </FormControl>
                              <FormLabel className="font-normal cursor-pointer">
                                {reason.label}
                              </FormLabel>
                            </FormItem>
                          ))}
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                ) : (
                  <div className="flex items-center justify-center h-full border rounded-md p-6">
                    <p className="text-muted-foreground text-center">
                      {dataEntryStatus === 'pass'
                        ? "Data entry quality has been marked as passing. No issues were identified."
                        : "Select an assessment status to continue."}
                    </p>
                  </div>
                )}
              </div>
            </div>

            <Separator className="my-4" />

            <FormField
              control={control}
              name="dataEntryQuality.notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter any additional notes about data entry quality"
                      className="min-h-[100px]"
                      {...field}
                      disabled={disabled}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </TabsContent>

          {/* Item Condition Check Tab */}
          <TabsContent value="item-condition" className="space-y-4 mt-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="md:col-span-1">
                <FormField
                  control={control}
                  name="itemConditionCheck.status"
                  render={({ field }) => (
                    <FormItem className="space-y-3">
                      <FormLabel>Assessment</FormLabel>
                      <FormControl>
                        <RadioGroup
                          onValueChange={(value) => {
                            field.onChange(value);
                            setItemConditionStatus(value);
                          }}
                          value={field.value}
                          className="flex flex-col space-y-1"
                          disabled={disabled}
                        >
                          <FormItem className="flex items-center space-x-3 space-y-0 p-2 rounded-md hover:bg-accent">
                            <FormControl>
                              <RadioGroupItem value="pass" id="itemConditionPass" />
                            </FormControl>
                            <FormLabel className="font-normal cursor-pointer w-full" htmlFor="itemConditionPass">
                              Pass
                            </FormLabel>
                          </FormItem>
                          <FormItem className="flex items-center space-x-3 space-y-0 p-2 rounded-md hover:bg-accent">
                            <FormControl>
                              <RadioGroupItem value="fail" id="itemConditionFail" />
                            </FormControl>
                            <FormLabel className="font-normal cursor-pointer w-full" htmlFor="itemConditionFail">
                              Fail
                            </FormLabel>
                          </FormItem>
                          <FormItem className="flex items-center space-x-3 space-y-0 p-2 rounded-md hover:bg-accent">
                            <FormControl>
                              <RadioGroupItem value="not_assessed" id="itemConditionNotAssessed" />
                            </FormControl>
                            <FormLabel className="font-normal cursor-pointer w-full" htmlFor="itemConditionNotAssessed">
                              Not Assessed
                            </FormLabel>
                          </FormItem>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="md:col-span-2">
                {/* Only show fail reasons if status is "fail" */}
                {itemConditionStatus === 'fail' ? (
                  <FormField
                    control={control}
                    name="itemConditionCheck.failReasons"
                    render={({ field }) => (
                      <FormItem>
                        <div className="mb-2">
                          <FormLabel>Fail Reasons</FormLabel>
                          <FormDescription>
                            Select all that apply
                          </FormDescription>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 border rounded-md p-3">
                          {itemConditionFailReasons.map((reason) => (
                            <FormItem
                              key={reason.id}
                              className="flex flex-row items-start space-x-3 space-y-0 p-2 hover:bg-accent rounded-md"
                            >
                              <FormControl>
                                <Checkbox
                                  checked={field.value?.includes(reason.id)}
                                  onCheckedChange={(checked) => {
                                    const currentValue = field.value || [];
                                    if (checked) {
                                      field.onChange([...currentValue, reason.id]);
                                    } else {
                                      field.onChange(
                                        currentValue.filter((value: string) => value !== reason.id)
                                      );
                                    }
                                  }}
                                  disabled={disabled}
                                />
                              </FormControl>
                              <FormLabel className="font-normal cursor-pointer">
                                {reason.label}
                              </FormLabel>
                            </FormItem>
                          ))}
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                ) : (
                  <div className="flex items-center justify-center h-full border rounded-md p-6">
                    <p className="text-muted-foreground text-center">
                      {itemConditionStatus === 'pass'
                        ? "Item condition check has been marked as passing. No issues were identified."
                        : "Select an assessment status to continue."}
                    </p>
                  </div>
                )}
              </div>
            </div>

            <Separator className="my-4" />

            <FormField
              control={control}
              name="itemConditionCheck.notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter any additional notes about item condition"
                      className="min-h-[100px]"
                      {...field}
                      disabled={disabled}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
