import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { AlertCircle, Check, X } from "lucide-react";
import api from "@/api/api";

type PinSetupModalProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
};

type PinSetupForm = {
  pin: string;
  confirmPin: string;
};

export function PinSetupModal({ open, onOpenChange }: PinSetupModalProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasPin, setHasPin] = useState(false);
  const [pinExpiry, setPinExpiry] = useState<Date | null>(null);

  const {
    register,
    handleSubmit,
    watch,
    reset,
    formState: { errors },
  } = useForm<PinSetupForm>();

  const pin = watch("pin", "");

  // Check if user has a PIN already
  useEffect(() => {
    if (open) {
      const checkPinStatus = async () => {
        try {
          const response = await api.get("/auth/pin-status");
          setHasPin(response.data.hasPin);
          if (response.data.pinExpiresAt) {
            setPinExpiry(new Date(response.data.pinExpiresAt));
          }
        } catch (error) {
          console.error("Error checking PIN status:", error);
        }
      };

      checkPinStatus();
      reset();
      setError(null);
    }
  }, [open, reset]);

  // PIN validation rules
  const validatePin = (value: string) => {
    if (!value) return "PIN is required";
    if (value.length < 6) return "PIN must be at least 6 digits";
    if (!/^\d+$/.test(value)) return "PIN must contain only numbers";
    return true;
  };

  const onSubmit = async (data: PinSetupForm) => {
    try {
      setLoading(true);
      setError(null);

      if (data.pin !== data.confirmPin) {
        setError("PINs do not match");
        return;
      }

      const response = await api.post("/auth/setup-pin", {
        pin: data.pin,
      });

      if (response.data.success) {
        toast.success("PIN setup successfully");
        reset();
        onOpenChange(false);
      } else {
        setError(response.data.message || "Failed to set up PIN");
      }
    } catch (error: any) {
      setError(error.response?.data?.message || "An error occurred while setting up PIN");
    } finally {
      setLoading(false);
    }
  };

  const handleRemovePin = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await api.post("/auth/remove-pin");

      if (response.data.success) {
        toast.success("PIN removed successfully");
        setHasPin(false);
        reset();
      } else {
        setError(response.data.message || "Failed to remove PIN");
      }
    } catch (error: any) {
      setError(error.response?.data?.message || "An error occurred while removing PIN");
    } finally {
      setLoading(false);
    }
  };

  const formatExpiryDate = (date: Date) => {
    return date.toLocaleDateString("en-NZ", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{hasPin ? "Manage PIN" : "Set Up PIN"}</DialogTitle>
          <DialogDescription>
            {hasPin
              ? "You can update your PIN or remove it completely."
              : "Set up a PIN for quick login to your account."}
          </DialogDescription>
        </DialogHeader>

        {error && (
          <div className="bg-red-900/30 border border-red-800 rounded-md p-3 flex items-start">
            <AlertCircle className="h-5 w-5 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
            <span className="text-red-200 text-sm">{error}</span>
          </div>
        )}

        {hasPin && pinExpiry && (
          <div className="bg-yellow-900/30 border border-yellow-800 rounded-md p-3 mb-4">
            <p className="text-yellow-200 text-sm">
              Your PIN will expire on {formatExpiryDate(pinExpiry)}. You'll need to set a new PIN after this date.
            </p>
          </div>
        )}

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="pin">
              {hasPin ? "New PIN" : "PIN"} <span className="text-red-500">*</span>
            </Label>
            <Input
              id="pin"
              type="password"
              inputMode="numeric"
              placeholder="Enter 6+ digit PIN"
              {...register("pin", { validate: validatePin })}
              className={errors.pin ? "border-red-500" : ""}
            />
            {errors.pin && (
              <p className="text-red-500 text-xs mt-1">{errors.pin.message}</p>
            )}
            <div className="text-xs text-muted-foreground mt-1">
              <ul className="space-y-1">
                <li className="flex items-center">
                  {pin.length >= 6 ? (
                    <Check className="h-3 w-3 text-green-500 mr-1" />
                  ) : (
                    <X className="h-3 w-3 text-red-500 mr-1" />
                  )}
                  At least 6 digits
                </li>
                <li className="flex items-center">
                  {/^\d+$/.test(pin) || !pin ? (
                    <Check className="h-3 w-3 text-green-500 mr-1" />
                  ) : (
                    <X className="h-3 w-3 text-red-500 mr-1" />
                  )}
                  Numbers only
                </li>
              </ul>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="confirmPin">
              Confirm PIN <span className="text-red-500">*</span>
            </Label>
            <Input
              id="confirmPin"
              type="password"
              inputMode="numeric"
              placeholder="Confirm your PIN"
              {...register("confirmPin", {
                validate: (value) =>
                  value === pin || "PINs do not match",
              })}
              className={errors.confirmPin ? "border-red-500" : ""}
            />
            {errors.confirmPin && (
              <p className="text-red-500 text-xs mt-1">
                {errors.confirmPin.message}
              </p>
            )}
          </div>

          <DialogFooter className="flex flex-col-reverse sm:flex-row sm:justify-between sm:space-x-2">
            {hasPin && (
              <Button
                type="button"
                variant="destructive"
                onClick={handleRemovePin}
                disabled={loading}
              >
                Remove PIN
              </Button>
            )}
            <div className="flex flex-col-reverse sm:flex-row sm:space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? "Saving..." : hasPin ? "Update PIN" : "Set PIN"}
              </Button>
            </div>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
