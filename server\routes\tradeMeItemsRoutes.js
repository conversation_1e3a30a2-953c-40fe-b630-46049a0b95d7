const express = require('express');
const router = express.Router();
const { requireUser } = require('./middleware/auth');
const tradeMeItemsService = require('../services/tradeMeItemsService');
const tradeMeService = require('../services/tradeMeService');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    // Create directory if it doesn't exist
    const dir = path.join(__dirname, '../uploads/trademe/temp');
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    cb(null, dir);
  },
  filename: function (req, file, cb) {
    // Generate unique filename
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: function (req, file, cb) {
    // Accept images only
    if (!file.originalname.match(/\.(jpg|jpeg|png|gif)$/)) {
      return cb(new Error('Only image files are allowed!'), false);
    }
    cb(null, true);
  }
});

/**
 * @route GET /api/trademe/items
 * @description Get TradeMe items with filtering and pagination
 * @access Private
 */
router.get('/', requireUser, async (req, res) => {
  try {
    const {
      status,
      soldStatus,
      search,
      page = 1,
      limit = 20,
      sort = 'createdAt',
      sortDirection = 'desc',
      buyer,
      isFaulty,
      days,
      locationId
    } = req.query;

    const result = await tradeMeItemsService.getItems({
      status,
      soldStatus,
      search,
      page: parseInt(page),
      limit: parseInt(limit),
      sort,
      sortDirection,
      buyer,
      isFaulty: isFaulty === 'true',
      days,
      locationId
    });

    if (result.success) {
      return res.status(200).json(result);
    } else {
      return res.status(400).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    console.error('Error getting TradeMe items:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/trademe/items/:id
 * @description Get a single TradeMe item by ID
 * @access Private
 */
router.get('/:id', requireUser, async (req, res) => {
  try {
    const { id } = req.params;

    const result = await tradeMeItemsService.getItemById(id);

    if (result.success) {
      return res.status(200).json(result);
    } else {
      return res.status(404).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    console.error('Error getting TradeMe item:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/trademe/items/listing/:listingId
 * @description Get a single TradeMe item by TradeMe listing ID
 * @access Private
 */
router.get('/listing/:listingId', requireUser, async (req, res) => {
  try {
    const { listingId } = req.params;

    const result = await tradeMeItemsService.getItemByListingId(listingId);

    if (result.success) {
      return res.status(200).json(result);
    } else {
      return res.status(404).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    console.error('Error getting TradeMe item by listing ID:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route POST /api/trademe/items
 * @description Create a new TradeMe item
 * @access Private
 */
router.post('/', requireUser, upload.array('images', 10), async (req, res) => {
  try {
    // Get item data from request body
    const itemData = JSON.parse(req.body.itemData || '{}');

    // Process uploaded images
    if (req.files && req.files.length > 0) {
      const images = [];

      // Use stockCode for folder name if available
      const folderName = itemData.stockCode && itemData.stockCode.trim() !== ''
        ? itemData.stockCode.trim()
        : `temp_${Date.now()}`;

      // Create directory for this item if it doesn't exist
      const itemDir = path.join(__dirname, `../uploads/trademe/${folderName}`);
      if (!fs.existsSync(itemDir)) {
        fs.mkdirSync(itemDir, { recursive: true });
      }

      // Move files from temp directory to permanent location
      for (const file of req.files) {
        // Move file to item directory
        const newPath = path.join(itemDir, file.filename);
        fs.renameSync(file.path, newPath);

        // Add to images array
        images.push(`/uploads/trademe/${folderName}/${file.filename}`);
      }

      // Add images to item data
      itemData.images = [...(itemData.images || []), ...images];
    }

    // Create the item
    const result = await tradeMeItemsService.createItem(itemData, req.user);

    if (result.success) {
      // If item was created successfully and has a stockCode, ensure images are in the correct folder
      if (itemData.images && itemData.images.length > 0 && result.item.stockCode) {
        const stockCodeDir = path.join(__dirname, `../uploads/trademe/${result.item.stockCode}`);
        if (!fs.existsSync(stockCodeDir)) {
          fs.mkdirSync(stockCodeDir, { recursive: true });
        }

        const updatedImages = [];
        let needsUpdate = false;

        for (const image of itemData.images) {
          const oldPath = path.join(__dirname, '..', image);
          const filename = path.basename(image);

          // Check if the image is already in the correct folder
          if (image.includes(`/uploads/trademe/${result.item.stockCode}/`)) {
            // Image is already in the correct folder
            updatedImages.push(image);
          } else {
            // Image needs to be moved to the stockCode folder
            const newPath = path.join(stockCodeDir, filename);

            // Move file to stockCode location if it exists
            if (fs.existsSync(oldPath)) {
              fs.renameSync(oldPath, newPath);
            }

            // Update image path
            updatedImages.push(`/uploads/trademe/${result.item.stockCode}/${filename}`);
            needsUpdate = true;
          }
        }

        // Only update the item if image paths have changed
        if (needsUpdate) {
          // Update item with permanent image paths
          await tradeMeItemsService.updateItem(
            result.item._id,
            { images: updatedImages },
            req.user
          );

          // Update result with new image paths
          result.item.images = updatedImages;
        }
      }

      return res.status(201).json(result);
    } else {
      // If item creation failed, clean up uploaded files
      if (req.files && req.files.length > 0) {
        for (const file of req.files) {
          if (fs.existsSync(file.path)) {
            fs.unlinkSync(file.path);
          }
        }
      }

      return res.status(400).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    console.error('Error creating TradeMe item:', error);

    // Clean up uploaded files on error
    if (req.files && req.files.length > 0) {
      for (const file of req.files) {
        if (fs.existsSync(file.path)) {
          fs.unlinkSync(file.path);
        }
      }
    }

    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route PUT /api/trademe/items/:id
 * @description Update an existing TradeMe item
 * @access Private
 */
router.put('/:id', requireUser, upload.array('images', 10), async (req, res) => {
  try {
    const { id } = req.params;

    // Get item data from request body
    const updateData = JSON.parse(req.body.itemData || '{}');

    // Process uploaded images
    if (req.files && req.files.length > 0) {
      // Get existing item to access current images
      const existingItem = await tradeMeItemsService.getItemById(id);

      if (!existingItem.success) {
        return res.status(404).json({
          success: false,
          error: existingItem.error
        });
      }

      // Determine folder name - use stockCode from update data or existing item
      const stockCode = updateData.stockCode || existingItem.item.stockCode;
      const folderName = stockCode && stockCode.trim() !== ''
        ? stockCode.trim()
        : id;

      // Create directory for this item if it doesn't exist
      const itemDir = path.join(__dirname, `../uploads/trademe/${folderName}`);
      if (!fs.existsSync(itemDir)) {
        fs.mkdirSync(itemDir, { recursive: true });
      }

      const newImages = [];

      // Move files from temp directory to permanent location
      for (const file of req.files) {
        const newPath = path.join(itemDir, file.filename);
        fs.renameSync(file.path, newPath);

        // Add to images array
        newImages.push(`/uploads/trademe/${folderName}/${file.filename}`);
      }

      // Combine with existing images if keepExistingImages is true
      if (updateData.keepExistingImages !== false) {
        // If stockCode has changed, move existing images to the new folder
        if (updateData.stockCode &&
            existingItem.item.stockCode &&
            updateData.stockCode !== existingItem.item.stockCode) {

          const updatedExistingImages = [];

          // Move existing images to the new stockCode folder
          for (const image of existingItem.item.images || []) {
            const oldPath = path.join(__dirname, '..', image);
            const filename = path.basename(image);
            const newPath = path.join(itemDir, filename);

            // Move file if it exists
            if (fs.existsSync(oldPath)) {
              fs.renameSync(oldPath, newPath);
            }

            // Update image path
            updatedExistingImages.push(`/uploads/trademe/${folderName}/${filename}`);
          }

          updateData.images = [...updatedExistingImages, ...newImages];
        } else {
          // No stockCode change, just combine existing and new images
          updateData.images = [...(existingItem.item.images || []), ...newImages];
        }
      } else {
        updateData.images = newImages;

        // Delete old images if not keeping them
        if (existingItem.item.images && existingItem.item.images.length > 0) {
          for (const image of existingItem.item.images) {
            const imagePath = path.join(__dirname, '..', image);
            if (fs.existsSync(imagePath)) {
              fs.unlinkSync(imagePath);
            }
          }
        }
      }

      // Remove keepExistingImages from updateData
      delete updateData.keepExistingImages;
    }

    // Update the item
    const result = await tradeMeItemsService.updateItem(id, updateData, req.user);

    if (result.success) {
      return res.status(200).json(result);
    } else {
      return res.status(400).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    console.error('Error updating TradeMe item:', error);

    // Clean up uploaded files on error
    if (req.files && req.files.length > 0) {
      for (const file of req.files) {
        if (fs.existsSync(file.path)) {
          fs.unlinkSync(file.path);
        }
      }
    }

    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route POST /api/trademe/items/:id/list
 * @description List an item on TradeMe
 * @access Private
 */
router.post('/:id/list', requireUser, async (req, res) => {
  try {
    const { id } = req.params;

    const result = await tradeMeItemsService.listItemOnTradeMe(id, req.user);

    if (result.success) {
      return res.status(200).json(result);
    } else {
      return res.status(400).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    console.error('Error listing TradeMe item:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route POST /api/trademe/items/:id/relist
 * @description Relist an item on TradeMe
 * @access Private
 */
router.post('/:id/relist', requireUser, async (req, res) => {
  try {
    const { id } = req.params;
    const options = req.body;

    const result = await tradeMeItemsService.relistItemOnTradeMe(id, req.user, options);

    if (result.success) {
      return res.status(200).json(result);
    } else {
      return res.status(400).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    console.error('Error relisting TradeMe item:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route POST /api/trademe/items/:id/archive
 * @description Archive an item
 * @access Private
 */
router.post('/:id/archive', requireUser, async (req, res) => {
  try {
    const { id } = req.params;

    const result = await tradeMeItemsService.archiveItem(id, req.user);

    if (result.success) {
      return res.status(200).json(result);
    } else {
      return res.status(400).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    console.error('Error archiving TradeMe item:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/trademe/items/:id/audit
 * @description Get audit history for an item
 * @access Private
 */
router.get('/:id/audit', requireUser, async (req, res) => {
  try {
    const { id } = req.params;
    const { page = 1, limit = 20, action } = req.query;

    const result = await tradeMeItemsService.getItemAuditHistory(id, {
      page: parseInt(page),
      limit: parseInt(limit),
      action
    });

    if (result.success) {
      return res.status(200).json(result);
    } else {
      return res.status(400).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    console.error('Error getting TradeMe item audit history:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route POST /api/trademe/items/:id/questions/:questionId/answer
 * @description Answer a question on a TradeMe item
 * @access Private
 */
router.post('/:id/questions/:questionId/answer', requireUser, async (req, res) => {
  try {
    const { id, questionId } = req.params;
    const { answer } = req.body;

    if (!answer) {
      return res.status(400).json({
        success: false,
        error: 'Answer is required'
      });
    }

    // Get the item
    const itemResult = await tradeMeItemsService.getItemById(id);

    if (!itemResult.success) {
      return res.status(404).json({
        success: false,
        error: itemResult.error
      });
    }

    const item = itemResult.item;

    // Find the question
    const question = item.questions.find(q => q.trademeQuestionId === questionId);

    if (!question) {
      return res.status(404).json({
        success: false,
        error: 'Question not found'
      });
    }

    // Answer the question in our database
    item.answerQuestion(questionId, answer, req.user);
    await item.save();

    // Create audit entry
    await tradeMeItemsService.createAuditEntry({
      itemId: item._id,
      trademeListingId: item.currentListingId,
      action: 'question_answered',
      details: `Question answered: "${question.question.substring(0, 50)}..."`,
      newValues: { answer },
      performedBy: req.user._id
    });

    // Answer the question on TradeMe if the item is active
    if (item.status === 'active' && item.currentListingId) {
      try {
        const tradeMeResult = await tradeMeService.answerQuestion(
          item.currentListingId,
          questionId,
          answer
        );

        if (!tradeMeResult.success) {
          return res.status(200).json({
            success: true,
            item,
            warning: `Question answered in local database but failed to answer on TradeMe: ${tradeMeResult.error}`
          });
        }
      } catch (tradeMeError) {
        return res.status(200).json({
          success: true,
          item,
          warning: `Question answered in local database but failed to answer on TradeMe: ${tradeMeError.message}`
        });
      }
    }

    return res.status(200).json({
      success: true,
      item,
      message: 'Question answered successfully'
    });
  } catch (error) {
    console.error('Error answering TradeMe question:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route POST /api/trademe/items/:id/notes
 * @description Add a note to a TradeMe item
 * @access Private
 */
router.post('/:id/notes', requireUser, async (req, res) => {
  try {
    const { id } = req.params;
    const { text } = req.body;

    if (!text) {
      return res.status(400).json({
        success: false,
        error: 'Note text is required'
      });
    }

    const result = await tradeMeItemsService.addNote(id, text, req.user);

    if (result.success) {
      return res.status(200).json(result);
    } else {
      return res.status(400).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    console.error('Error adding note to TradeMe item:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/trademe/items/:id/notes
 * @description Get notes for a TradeMe item
 * @access Private
 */
router.get('/:id/notes', requireUser, async (req, res) => {
  try {
    const { id } = req.params;

    const result = await tradeMeItemsService.getNotes(id);

    if (result.success) {
      return res.status(200).json(result);
    } else {
      return res.status(400).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    console.error('Error getting notes for TradeMe item:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route DELETE /api/trademe/items/:id/notes/:noteId
 * @description Delete a note from a TradeMe item
 * @access Private
 */
router.delete('/:id/notes/:noteId', requireUser, async (req, res) => {
  try {
    const { id, noteId } = req.params;

    const result = await tradeMeItemsService.deleteNote(id, noteId, req.user);

    if (result.success) {
      return res.status(200).json(result);
    } else {
      return res.status(400).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    console.error('Error deleting note from TradeMe item:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = router;
/**
 * @route POST /api/trademe/items/:id/notes
 * @description Add a note to a TradeMe item
 * @access Private
 */
router.post('/:id/notes', requireUser, async (req, res) => {
  try {
    const { id } = req.params;
    const { text } = req.body;

    if (!text) {
      return res.status(400).json({
        success: false,
        error: 'Note text is required'
      });
    }

    const result = await tradeMeItemsService.addNote(id, text, req.user);

    if (result.success) {
      return res.status(200).json(result);
    } else {
      return res.status(400).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    console.error('Error adding note to TradeMe item:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/trademe/items/:id/notes
 * @description Get notes for a TradeMe item
 * @access Private
 */
router.get('/:id/notes', requireUser, async (req, res) => {
  try {
    const { id } = req.params;

    const result = await tradeMeItemsService.getNotes(id);

    if (result.success) {
      return res.status(200).json(result);
    } else {
      return res.status(400).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    console.error('Error getting notes for TradeMe item:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route DELETE /api/trademe/items/:id/notes/:noteId
 * @description Delete a note from a TradeMe item
 * @access Private
 */
router.delete('/:id/notes/:noteId', requireUser, async (req, res) => {
  try {
    const { id, noteId } = req.params;

    const result = await tradeMeItemsService.deleteNote(id, noteId, req.user);

    if (result.success) {
      return res.status(200).json(result);
    } else {
      return res.status(400).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    console.error('Error deleting note from TradeMe item:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});
