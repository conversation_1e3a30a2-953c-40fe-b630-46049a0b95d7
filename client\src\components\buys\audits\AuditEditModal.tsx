import { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Save, 
  X, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  DollarSign,
  Package,
  ClipboardCheck
} from 'lucide-react';
import { useToast } from '@/hooks/useToast';
import { format } from 'date-fns';

interface AuditEditModalProps {
  audit: any;
  isOpen: boolean;
  onClose: () => void;
  onSave: (updatedAudit: any) => void;
}

export function AuditEditModal({ audit, isOpen, onClose, onSave }: AuditEditModalProps) {
  const { toast } = useToast();
  const [editedAudit, setEditedAudit] = useState(audit);
  const [isSaving, setIsSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('basic');

  // Reset form when audit changes
  useEffect(() => {
    setEditedAudit(audit);
  }, [audit]);

  const handleSave = async () => {
    try {
      setIsSaving(true);
      
      // TODO: Implement API call to update audit
      // await updateAudit(editedAudit._id, editedAudit);
      
      toast({
        title: 'Audit Updated',
        description: 'The audit has been updated successfully.',
      });
      
      onSave(editedAudit);
      onClose();
    } catch (error: any) {
      toast({
        title: 'Error',
        description: 'Failed to update audit.',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const updateField = (path: string, value: any) => {
    const keys = path.split('.');
    const newAudit = { ...editedAudit };
    let current = newAudit;
    
    for (let i = 0; i < keys.length - 1; i++) {
      if (!current[keys[i]]) current[keys[i]] = {};
      current = current[keys[i]];
    }
    
    current[keys[keys.length - 1]] = value;
    setEditedAudit(newAudit);
  };

  const renderStatusRadioGroup = (path: string, currentValue: string) => (
    <RadioGroup
      value={currentValue}
      onValueChange={(value) => updateField(path, value)}
      className="flex flex-col space-y-2"
    >
      <div className="flex items-center space-x-2">
        <RadioGroupItem value="pass" id={`${path}-pass`} />
        <Label htmlFor={`${path}-pass`} className="cursor-pointer">Pass</Label>
      </div>
      <div className="flex items-center space-x-2">
        <RadioGroupItem value="fail" id={`${path}-fail`} />
        <Label htmlFor={`${path}-fail`} className="cursor-pointer">Fail</Label>
      </div>
      <div className="flex items-center space-x-2">
        <RadioGroupItem value="not_assessed" id={`${path}-na`} />
        <Label htmlFor={`${path}-na`} className="cursor-pointer">Not Assessed</Label>
      </div>
    </RadioGroup>
  );

  const failReasonOptions = [
    { id: 'missing_information', label: 'Missing Information' },
    { id: 'incorrect_data', label: 'Incorrect Data' },
    { id: 'policy_violation', label: 'Policy Violation' },
    { id: 'documentation_error', label: 'Documentation Error' },
    { id: 'other', label: 'Other' },
  ];

  const renderFailReasons = (path: string, currentReasons: string[] = []) => (
    <div className="space-y-2">
      <Label>Fail Reasons</Label>
      <div className="grid grid-cols-2 gap-2">
        {failReasonOptions.map((reason) => (
          <div key={reason.id} className="flex items-center space-x-2">
            <Checkbox
              id={`${path}-${reason.id}`}
              checked={currentReasons.includes(reason.id)}
              onCheckedChange={(checked) => {
                const newReasons = checked
                  ? [...currentReasons, reason.id]
                  : currentReasons.filter(r => r !== reason.id);
                updateField(`${path}.failReasons`, newReasons);
              }}
            />
            <Label htmlFor={`${path}-${reason.id}`} className="text-sm">
              {reason.label}
            </Label>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Audit</DialogTitle>
          <DialogDescription>
            Make changes to the audit details and assessments. Only managers and admins can edit audits.
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="basic">Basic Info</TabsTrigger>
            <TabsTrigger value="assessments">Assessments</TabsTrigger>
            <TabsTrigger value="pricing">Pricing</TabsTrigger>
            <TabsTrigger value="notes">Notes & Status</TabsTrigger>
          </TabsList>

          {/* Basic Information Tab */}
          <TabsContent value="basic" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Basic Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="transactionId">Transaction ID</Label>
                    <Input
                      id="transactionId"
                      value={editedAudit.transactionId || ''}
                      onChange={(e) => updateField('transactionId', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="amount">Amount</Label>
                    <Input
                      id="amount"
                      type="number"
                      step="0.01"
                      value={editedAudit.amount || ''}
                      onChange={(e) => updateField('amount', parseFloat(e.target.value))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="transactionDate">Transaction Date</Label>
                    <Input
                      id="transactionDate"
                      type="date"
                      value={editedAudit.transactionDate ? format(new Date(editedAudit.transactionDate), 'yyyy-MM-dd') : ''}
                      onChange={(e) => updateField('transactionDate', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="auditDate">Audit Date</Label>
                    <Input
                      id="auditDate"
                      type="date"
                      value={editedAudit.auditDate ? format(new Date(editedAudit.auditDate), 'yyyy-MM-dd') : ''}
                      onChange={(e) => updateField('auditDate', e.target.value)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Assessments Tab */}
          <TabsContent value="assessments" className="space-y-4">
            {/* Data Entry Quality */}
            {editedAudit.auditType !== 'price' && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <ClipboardCheck className="h-5 w-5" />
                    Data Entry Quality
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {renderStatusRadioGroup('dataEntryQuality.status', editedAudit.dataEntryQuality?.status || 'not_assessed')}
                  
                  {editedAudit.dataEntryQuality?.status === 'fail' && 
                    renderFailReasons('dataEntryQuality', editedAudit.dataEntryQuality?.failReasons)
                  }
                  
                  <div>
                    <Label htmlFor="dataEntryNotes">Notes</Label>
                    <Textarea
                      id="dataEntryNotes"
                      value={editedAudit.dataEntryQuality?.notes || ''}
                      onChange={(e) => updateField('dataEntryQuality.notes', e.target.value)}
                      placeholder="Enter notes about data entry quality..."
                    />
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Item Condition Check */}
            {editedAudit.auditType !== 'price' && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Package className="h-5 w-5" />
                    Item Condition Check
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {renderStatusRadioGroup('itemConditionCheck.status', editedAudit.itemConditionCheck?.status || 'not_assessed')}
                  
                  {editedAudit.itemConditionCheck?.status === 'fail' && 
                    renderFailReasons('itemConditionCheck', editedAudit.itemConditionCheck?.failReasons)
                  }
                  
                  <div>
                    <Label htmlFor="itemConditionNotes">Notes</Label>
                    <Textarea
                      id="itemConditionNotes"
                      value={editedAudit.itemConditionCheck?.notes || ''}
                      onChange={(e) => updateField('itemConditionCheck.notes', e.target.value)}
                      placeholder="Enter notes about item condition..."
                    />
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Pricing Tab */}
          <TabsContent value="pricing" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5" />
                  Pricing Assessment
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {editedAudit.auditType !== 'price' && 
                  renderStatusRadioGroup('pricing.status', editedAudit.pricing?.status || 'not_assessed')
                }
                
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="suggestedPrice">Suggested Price</Label>
                    <Input
                      id="suggestedPrice"
                      type="number"
                      step="0.01"
                      value={editedAudit.pricing?.suggestedPrice || ''}
                      onChange={(e) => updateField('pricing.suggestedPrice', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="costPrice">Cost Price</Label>
                    <Input
                      id="costPrice"
                      type="number"
                      step="0.01"
                      value={editedAudit.pricing?.costPrice || ''}
                      onChange={(e) => updateField('pricing.costPrice', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="ticketPrice">Ticket Price</Label>
                    <Input
                      id="ticketPrice"
                      type="number"
                      step="0.01"
                      value={editedAudit.pricing?.ticketPrice || ''}
                      onChange={(e) => updateField('pricing.ticketPrice', e.target.value)}
                    />
                  </div>
                </div>

                {/* Price Audit Specific Fields */}
                {editedAudit.auditType === 'price' && (
                  <>
                    <div>
                      <Label>Overpayment Reason</Label>
                      <RadioGroup
                        value={editedAudit.pricing?.overpaymentReason || ''}
                        onValueChange={(value) => updateField('pricing.overpaymentReason', value)}
                        className="mt-2"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="paid_over_ghost_price" id="reason-ghost" />
                          <Label htmlFor="reason-ghost">Paid over Ghost recorded price</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="paid_over_gold_calculator" id="reason-gold" />
                          <Label htmlFor="reason-gold">Paid over gold calculator value</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="insufficient_research" id="reason-research" />
                          <Label htmlFor="reason-research">Insufficient market research</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="other" id="reason-other" />
                          <Label htmlFor="reason-other">Other reason</Label>
                        </div>
                      </RadioGroup>
                    </div>

                    {editedAudit.pricing?.overpaymentReason === 'other' && (
                      <div>
                        <Label htmlFor="customReason">Custom Reason</Label>
                        <Input
                          id="customReason"
                          value={editedAudit.pricing?.customOverpaymentReason || ''}
                          onChange={(e) => updateField('pricing.customOverpaymentReason', e.target.value)}
                          placeholder="Please specify the reason..."
                        />
                      </div>
                    )}
                  </>
                )}

                <div>
                  <Label htmlFor="pricingNotes">Pricing Notes</Label>
                  <Textarea
                    id="pricingNotes"
                    value={editedAudit.pricing?.notes || ''}
                    onChange={(e) => updateField('pricing.notes', e.target.value)}
                    placeholder="Enter notes about pricing assessment..."
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Notes & Status Tab */}
          <TabsContent value="notes" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Overall Assessment</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label>Overall Compliance</Label>
                  {renderStatusRadioGroup('overallCompliance', editedAudit.overallCompliance || 'not_assessed')}
                </div>
                
                <div>
                  <Label htmlFor="overallScore">Overall Score (%)</Label>
                  <Input
                    id="overallScore"
                    type="number"
                    min="0"
                    max="100"
                    value={editedAudit.overallScore || ''}
                    onChange={(e) => updateField('overallScore', parseInt(e.target.value))}
                  />
                </div>
                
                <div>
                  <Label htmlFor="auditNotes">Audit Notes</Label>
                  <Textarea
                    id="auditNotes"
                    value={editedAudit.auditNotes || ''}
                    onChange={(e) => updateField('auditNotes', e.target.value)}
                    placeholder="Enter overall audit notes..."
                    className="min-h-[100px]"
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-2 pt-4 border-t">
          <Button variant="outline" onClick={onClose} disabled={isSaving}>
            <X className="h-4 w-4 mr-2" />
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={isSaving}>
            <Save className="h-4 w-4 mr-2" />
            {isSaving ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
