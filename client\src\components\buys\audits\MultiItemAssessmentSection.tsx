import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { FileText, ClipboardCheck, CheckCircle, XCircle, AlertCircle, Package } from 'lucide-react';
import { useFormContext } from 'react-hook-form';

interface MultiItemAssessmentSectionProps {
  control: any;
  disabled?: boolean;
  auditType: 'buy' | 'pawn' | 'price';
}

/**
 * Component for assessing multiple items individually in a transaction
 * Each item gets its own assessment for data entry quality and condition check
 */
export function MultiItemAssessmentSection({ control, disabled = false, auditType }: MultiItemAssessmentSectionProps) {
  const { watch } = useFormContext();
  const items = watch('items') || [];

  // Common fail reasons for data entry quality
  const dataEntryFailReasons = [
    { id: 'missing_info', label: 'Missing information' },
    { id: 'missing_serial_number', label: 'Missing Serial Number' },
    { id: 'incorrect_info', label: 'Incorrect information' },
    { id: 'incomplete_info', label: 'Incomplete information' },
    { id: 'wrong_category', label: 'Wrong category' },
    { id: 'wrong_description', label: 'Wrong description' },
  ];

  // Common fail reasons for item condition check
  const conditionFailReasons = [
    { id: 'poor_condition', label: 'Poor condition' },
    { id: 'damaged', label: 'Damaged item' },
    { id: 'not_working', label: 'Not working' },
    { id: 'missing_parts', label: 'Missing parts' },
    { id: 'condition_mismatch', label: 'Condition does not match description' },
  ];

  // Helper function to render status badge
  const renderStatusBadge = (status: string) => {
    switch (status) {
      case 'pass':
        return <Badge variant="success" className="flex items-center gap-1"><CheckCircle className="h-3 w-3" />Pass</Badge>;
      case 'fail':
        return <Badge variant="destructive" className="flex items-center gap-1"><XCircle className="h-3 w-3" />Fail</Badge>;
      default:
        return <Badge variant="secondary" className="flex items-center gap-1"><AlertCircle className="h-3 w-3" />Not Assessed</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <Package className="h-5 w-5 text-primary" />
            <CardTitle>Item Assessment</CardTitle>
          </div>
          <CardDescription>
            Assess each item individually for data entry quality and condition
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Individual Item Assessments */}
      {items.map((item: any, itemIndex: number) => (
        <Card key={itemIndex} className="border-l-4 border-l-primary">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg">Item {itemIndex + 1}: {item.brand} - {item.description}</CardTitle>
                <CardDescription>Stockcode: {item.stockcode} | Cost: ${item.cost}</CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Data Entry Quality for this item */}
            {auditType !== 'price' && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <FileText className="h-4 w-4 text-primary" />
                    <h4 className="font-medium">Data Entry Quality</h4>
                  </div>
                  <FormField
                    control={control}
                    name={`itemAssessments.${itemIndex}.dataEntryQuality.status`}
                    render={({ field }) => renderStatusBadge(field.value)}
                  />
                </div>

                <FormField
                  control={control}
                  name={`itemAssessments.${itemIndex}.dataEntryQuality.status`}
                  render={({ field }) => (
                    <FormItem className="space-y-3">
                      <FormLabel>Assessment</FormLabel>
                      <FormControl>
                        <RadioGroup
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          className="flex flex-col space-y-2"
                          disabled={disabled}
                        >
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="pass" id={`dataEntry-${itemIndex}-pass`} />
                            <FormLabel htmlFor={`dataEntry-${itemIndex}-pass`} className="cursor-pointer">Pass</FormLabel>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="fail" id={`dataEntry-${itemIndex}-fail`} />
                            <FormLabel htmlFor={`dataEntry-${itemIndex}-fail`} className="cursor-pointer">Fail</FormLabel>
                          </div>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Show fail reasons if status is "fail" */}
                <FormField
                  control={control}
                  name={`itemAssessments.${itemIndex}.dataEntryQuality.status`}
                  render={({ field }) => (
                    <div>
                      {field.value === 'fail' && (
                        <FormField
                          control={control}
                          name={`itemAssessments.${itemIndex}.dataEntryQuality.failReasons`}
                          render={({ field: failReasonsField }) => (
                            <FormItem>
                              <FormLabel>Fail Reasons</FormLabel>
                              <FormDescription>Select all that apply</FormDescription>
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-2 border rounded-md p-3">
                                {dataEntryFailReasons.map((reason) => (
                                  <FormItem
                                    key={reason.id}
                                    className="flex flex-row items-start space-x-3 space-y-0 p-2 hover:bg-accent rounded-md"
                                  >
                                    <FormControl>
                                      <Checkbox
                                        checked={failReasonsField.value?.includes(reason.id)}
                                        onCheckedChange={(checked) => {
                                          const currentReasons = failReasonsField.value || [];
                                          if (checked) {
                                            failReasonsField.onChange([...currentReasons, reason.id]);
                                          } else {
                                            failReasonsField.onChange(currentReasons.filter((r: string) => r !== reason.id));
                                          }
                                        }}
                                        disabled={disabled}
                                      />
                                    </FormControl>
                                    <FormLabel className="text-sm font-normal cursor-pointer">
                                      {reason.label}
                                    </FormLabel>
                                  </FormItem>
                                ))}
                              </div>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      )}
                    </div>
                  )}
                />

                <FormField
                  control={control}
                  name={`itemAssessments.${itemIndex}.dataEntryQuality.notes`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Notes</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter any additional notes about data entry quality"
                          className="min-h-[80px]"
                          {...field}
                          disabled={disabled}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Separator />
              </div>
            )}

            {/* Item Condition Check for this item */}
            {auditType !== 'price' && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <ClipboardCheck className="h-4 w-4 text-primary" />
                    <h4 className="font-medium">Item Condition Check</h4>
                  </div>
                  <FormField
                    control={control}
                    name={`itemAssessments.${itemIndex}.itemConditionCheck.status`}
                    render={({ field }) => renderStatusBadge(field.value)}
                  />
                </div>

                <FormField
                  control={control}
                  name={`itemAssessments.${itemIndex}.itemConditionCheck.status`}
                  render={({ field }) => (
                    <FormItem className="space-y-3">
                      <FormLabel>Assessment</FormLabel>
                      <FormControl>
                        <RadioGroup
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          className="flex flex-col space-y-2"
                          disabled={disabled}
                        >
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="pass" id={`condition-${itemIndex}-pass`} />
                            <FormLabel htmlFor={`condition-${itemIndex}-pass`} className="cursor-pointer">Pass</FormLabel>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="fail" id={`condition-${itemIndex}-fail`} />
                            <FormLabel htmlFor={`condition-${itemIndex}-fail`} className="cursor-pointer">Fail</FormLabel>
                          </div>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Show fail reasons if status is "fail" */}
                <FormField
                  control={control}
                  name={`itemAssessments.${itemIndex}.itemConditionCheck.status`}
                  render={({ field }) => (
                    <div>
                      {field.value === 'fail' && (
                        <FormField
                          control={control}
                          name={`itemAssessments.${itemIndex}.itemConditionCheck.failReasons`}
                          render={({ field: failReasonsField }) => (
                            <FormItem>
                              <FormLabel>Fail Reasons</FormLabel>
                              <FormDescription>Select all that apply</FormDescription>
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-2 border rounded-md p-3">
                                {conditionFailReasons.map((reason) => (
                                  <FormItem
                                    key={reason.id}
                                    className="flex flex-row items-start space-x-3 space-y-0 p-2 hover:bg-accent rounded-md"
                                  >
                                    <FormControl>
                                      <Checkbox
                                        checked={failReasonsField.value?.includes(reason.id)}
                                        onCheckedChange={(checked) => {
                                          const currentReasons = failReasonsField.value || [];
                                          if (checked) {
                                            failReasonsField.onChange([...currentReasons, reason.id]);
                                          } else {
                                            failReasonsField.onChange(currentReasons.filter((r: string) => r !== reason.id));
                                          }
                                        }}
                                        disabled={disabled}
                                      />
                                    </FormControl>
                                    <FormLabel className="text-sm font-normal cursor-pointer">
                                      {reason.label}
                                    </FormLabel>
                                  </FormItem>
                                ))}
                              </div>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      )}
                    </div>
                  )}
                />

                <FormField
                  control={control}
                  name={`itemAssessments.${itemIndex}.itemConditionCheck.notes`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Notes</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter any additional notes about item condition"
                          className="min-h-[80px]"
                          {...field}
                          disabled={disabled}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
