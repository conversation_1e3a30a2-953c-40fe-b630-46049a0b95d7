import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription
} from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/useToast';
import { Plus, Edit, Trash2, Copy, Search, X } from 'lucide-react';
import { TradeMeTemplate, getTemplates, createTemplate, updateTemplate, deleteTemplate, setDefaultTemplate } from '@/api/tradeMeTemplates';
import { useAuth } from '@/contexts/AuthContext';

interface TradeMeTemplateManagerProps {
  onSelectTemplate?: (template: TradeMeTemplate) => void;
  defaultType?: 'question' | 'footer' | 'shipping';
  showSelectButton?: boolean;
}

const TradeMeTemplateManager: React.FC<TradeMeTemplateManagerProps> = ({
  onSelectTemplate,
  defaultType = 'question',
  showSelectButton = false
}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [templates, setTemplates] = useState<TradeMeTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedType, setSelectedType] = useState<'question' | 'footer' | 'shipping' | 'withdraw'>(defaultType);
  const [searchTerm, setSearchTerm] = useState('');

  // Template form state
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<TradeMeTemplate | null>(null);
  const [formData, setFormData] = useState<{
    type: 'question' | 'footer' | 'shipping' | 'withdraw';
    title: string;
    content: string;
    price: number;
    method: string;
  }>({
    type: defaultType,
    title: '',
    content: '',
    price: 0,
    method: ''
  });

  // Load templates
  const loadTemplates = async () => {
    try {
      setLoading(true);
      const result = await getTemplates(selectedType, searchTerm);
      if (result.success) {
        setTemplates(result.templates);
      }
    } catch (error) {
      console.error('Error loading templates:', error);
      toast({
        title: 'Error',
        description: 'Failed to load templates',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadTemplates();
  }, [selectedType, searchTerm]);

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'price' ? parseFloat(value) || 0 : value
    }));
  };

  // Open create template dialog
  const handleCreateTemplate = () => {
    setEditingTemplate(null);
    setFormData({
      type: selectedType,
      title: '',
      content: '',
      price: 0,
      method: ''
    });
    setIsDialogOpen(true);
  };

  // Open edit template dialog
  const handleEditTemplate = (template: TradeMeTemplate) => {
    setEditingTemplate(template);
    setFormData({
      type: template.type,
      title: template.title,
      content: template.content,
      price: template.price || 0,
      method: template.method || ''
    });
    setIsDialogOpen(true);
  };

  // Open delete template dialog
  const handleDeleteClick = (template: TradeMeTemplate) => {
    setEditingTemplate(template);
    setIsDeleteDialogOpen(true);
  };

  // Save template (create or update)
  const handleSaveTemplate = async () => {
    try {
      if (!formData.title || !formData.content) {
        toast({
          title: 'Validation Error',
          description: 'Title and content are required',
          variant: 'destructive'
        });
        return;
      }

      let result;
      if (editingTemplate) {
        // Update existing template
        result = await updateTemplate(editingTemplate._id, formData);
        if (result.success) {
          toast({
            title: 'Success',
            description: 'Template updated successfully'
          });
        }
      } else {
        // Create new template
        result = await createTemplate(formData);
        if (result.success) {
          toast({
            title: 'Success',
            description: 'Template created successfully'
          });
        }
      }

      setIsDialogOpen(false);
      loadTemplates();
    } catch (error) {
      console.error('Error saving template:', error);
      toast({
        title: 'Error',
        description: 'Failed to save template',
        variant: 'destructive'
      });
    }
  };

  // Delete template
  const handleDeleteTemplate = async () => {
    if (!editingTemplate) return;

    try {
      const result = await deleteTemplate(editingTemplate._id);
      if (result.success) {
        toast({
          title: 'Success',
          description: 'Template deleted successfully'
        });
        setIsDeleteDialogOpen(false);
        loadTemplates();
      }
    } catch (error) {
      console.error('Error deleting template:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete template',
        variant: 'destructive'
      });
    }
  };

  // Copy template content to clipboard
  const handleCopyContent = (content: string) => {
    navigator.clipboard.writeText(content);
    toast({
      title: 'Copied',
      description: 'Template content copied to clipboard'
    });
  };

  // Set template as default
  const handleSetAsDefault = async (template: TradeMeTemplate) => {
    try {
      const result = await setDefaultTemplate(template._id);
      if (result.success) {
        toast({
          title: 'Success',
          description: `"${template.title}" set as default template`
        });
        loadTemplates();
      }
    } catch (error) {
      console.error('Error setting default template:', error);
      toast({
        title: 'Error',
        description: 'Failed to set default template',
        variant: 'destructive'
      });
    }
  };

  // Select template (for parent component)
  const handleSelectTemplate = (template: TradeMeTemplate) => {
    if (onSelectTemplate) {
      onSelectTemplate(template);
    }
  };

  // Get category color based on template type
  const getCategoryColor = (type: string) => {
    switch (type) {
      case 'question':
        return 'bg-yellow-500';
      case 'footer':
        return 'bg-blue-500';
      case 'shipping':
        return 'bg-green-500';
      case 'withdraw':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  // Get template type label
  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'question':
        return 'Question Template';
      case 'footer':
        return 'Footer Template';
      case 'shipping':
        return 'Shipping Template';
      case 'withdraw':
        return 'Withdraw Template';
      default:
        return 'Template';
    }
  };

  return (
    <div className="space-y-4">
      <Tabs defaultValue={selectedType} onValueChange={(value) => setSelectedType(value as any)}>
        <div className="flex justify-between items-center mb-4">
          <TabsList>
            <TabsTrigger value="question">Question Answers</TabsTrigger>
            <TabsTrigger value="footer">Listing Footers</TabsTrigger>
            <TabsTrigger value="shipping">Shipping Options</TabsTrigger>
            <TabsTrigger value="withdraw">Withdraw Reasons</TabsTrigger>
          </TabsList>

          <Button onClick={handleCreateTemplate} size="sm">
            <Plus className="h-4 w-4 mr-2" />
            Add Template
          </Button>
        </div>

        <div className="flex items-center mb-4">
          <div className="relative flex-1">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search templates..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            {searchTerm && (
              <button
                className="absolute right-2 top-2.5"
                onClick={() => setSearchTerm('')}
              >
                <X className="h-4 w-4 text-muted-foreground" />
              </button>
            )}
          </div>
        </div>

        <TabsContent value="question" className="space-y-4">
          {loading ? (
            <div className="text-center py-8">Loading templates...</div>
          ) : templates.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No question templates found. Create one to get started.
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-4">
              {templates.map((template) => (
                <TemplateCard
                  key={template._id}
                  template={template}
                  onEdit={handleEditTemplate}
                  onDelete={handleDeleteClick}
                  onCopy={handleCopyContent}
                  onSelect={showSelectButton ? handleSelectTemplate : undefined}
                  getCategoryColor={getCategoryColor}
                  user={user}
                />
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="footer" className="space-y-4">
          {loading ? (
            <div className="text-center py-8">Loading templates...</div>
          ) : templates.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No footer templates found. Create one to get started.
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-4">
              {templates.map((template) => (
                <TemplateCard
                  key={template._id}
                  template={template}
                  onEdit={handleEditTemplate}
                  onDelete={handleDeleteClick}
                  onCopy={handleCopyContent}
                  onSelect={showSelectButton ? handleSelectTemplate : undefined}
                  getCategoryColor={getCategoryColor}
                  user={user}
                />
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="shipping" className="space-y-4">
          {loading ? (
            <div className="text-center py-8">Loading templates...</div>
          ) : templates.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No shipping templates found. Create one to get started.
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-4">
              {templates.map((template) => (
                <TemplateCard
                  key={template._id}
                  template={template}
                  onEdit={handleEditTemplate}
                  onDelete={handleDeleteClick}
                  onCopy={handleCopyContent}
                  onSelect={showSelectButton ? handleSelectTemplate : undefined}
                  getCategoryColor={getCategoryColor}
                  user={user}
                />
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="withdraw" className="space-y-4">
          {loading ? (
            <div className="text-center py-8">Loading templates...</div>
          ) : templates.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No withdraw templates found. Create one to get started.
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-4">
              {templates.map((template) => (
                <TemplateCard
                  key={template._id}
                  template={template}
                  onEdit={handleEditTemplate}
                  onDelete={handleDeleteClick}
                  onCopy={handleCopyContent}
                  onSelect={showSelectButton ? handleSelectTemplate : undefined}
                  onSetDefault={handleSetAsDefault}
                  getCategoryColor={getCategoryColor}
                  user={user}
                />
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Create/Edit Template Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>
              {editingTemplate ? `Edit ${getTypeLabel(formData.type)}` : `Create ${getTypeLabel(formData.type)}`}
            </DialogTitle>
            <DialogDescription>
              {editingTemplate
                ? 'Update the template details below.'
                : 'Fill in the details to create a new template.'}
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="title" className="text-right">
                Title
              </Label>
              <Input
                id="title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                className="col-span-3"
              />
            </div>



            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="content" className="text-right">
                Content
              </Label>
              <Textarea
                id="content"
                name="content"
                value={formData.content}
                onChange={handleInputChange}
                className="col-span-3"
                rows={6}
              />
            </div>

            {formData.type === 'shipping' && (
              <>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="price" className="text-right">
                    Price
                  </Label>
                  <Input
                    id="price"
                    name="price"
                    type="number"
                    value={formData.price}
                    onChange={handleInputChange}
                    className="col-span-3"
                  />
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="method" className="text-right">
                    Method
                  </Label>
                  <Input
                    id="method"
                    name="method"
                    value={formData.method}
                    onChange={handleInputChange}
                    className="col-span-3"
                  />
                </div>
              </>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveTemplate}>Save</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this template? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteTemplate}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

// Template Card Component
interface TemplateCardProps {
  template: TradeMeTemplate;
  onEdit: (template: TradeMeTemplate) => void;
  onDelete: (template: TradeMeTemplate) => void;
  onCopy: (content: string) => void;
  onSelect?: (template: TradeMeTemplate) => void;
  onSetDefault?: (template: TradeMeTemplate) => Promise<void>;
  getCategoryColor: (type: string) => string;
  user: any;
}

const TemplateCard: React.FC<TemplateCardProps> = ({
  template,
  onEdit,
  onDelete,
  onCopy,
  onSelect,
  onSetDefault,
  getCategoryColor,
  user
}) => {
  // Check if user can edit/delete (creator or admin/manager)
  const canModify =
    user?.role === 'admin' ||
    user?.role === 'manager' ||
    template.createdBy._id === user?._id;

  return (
    <Card className="p-4">
      <div className="flex justify-between items-start mb-2">
        <div>
          <h3 className="text-lg font-semibold">{template.title}</h3>
          <div className="flex items-center space-x-2 mt-1">
            <span
              className={`${getCategoryColor(
                template.type
              )} text-white text-xs px-2 py-1 rounded-full`}
            >
              {template.type}
            </span>
            {template.type === 'shipping' && template.price !== undefined && (
              <span className="bg-purple-500 text-white text-xs px-2 py-1 rounded-full">
                ${template.price.toFixed(2)}
              </span>
            )}
          </div>
        </div>

        <div className="flex space-x-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => onCopy(template.content)}
            title="Copy content"
          >
            <Copy className="h-4 w-4" />
          </Button>

          {canModify && (
            <>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => onEdit(template)}
                title="Edit template"
              >
                <Edit className="h-4 w-4" />
              </Button>

              <Button
                variant="ghost"
                size="icon"
                onClick={() => onDelete(template)}
                title="Delete template"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </>
          )}
        </div>
      </div>

      <div className="mt-2">
        <p className="text-sm text-muted-foreground whitespace-pre-wrap">
          {template.content.length > 200
            ? `${template.content.substring(0, 200)}...`
            : template.content}
        </p>
      </div>

      <div className="mt-4 flex justify-between items-center text-xs text-muted-foreground">
        <div>
          Created by {template.createdBy.fullName || template.createdBy.username}
          {template.isDefault && (
            <span className="ml-2 text-green-500 font-medium">• Default</span>
          )}
        </div>

        <div className="flex gap-2">
          {template.type === 'withdraw' && onSetDefault && !template.isDefault && (
            <Button
              size="sm"
              variant="outline"
              onClick={() => onSetDefault(template)}
              title="Set as default template"
            >
              Set as Default
            </Button>
          )}

          {onSelect && (
            <Button size="sm" onClick={() => onSelect(template)}>
              Use Template
            </Button>
          )}
        </div>
      </div>
    </Card>
  );
};

export { TradeMeTemplateManager };
export default TradeMeTemplateManager;
