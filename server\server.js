// Load environment variables
require("dotenv").config();
const mongoose = require("mongoose");
const express = require("express");
const path = require("path");
const fs = require("fs");
const session = require("express-session");
const MongoStore = require('connect-mongo');
const helmet = require('helmet');
const csrf = require('csurf');
const cookieParser = require('cookie-parser');
const basicRoutes = require("./routes/index");
const authRoutes = require("./routes/authRoutes");
const locationRoutes = require('./routes/locationRoutes');
const inventoryRoutes = require('./routes/inventoryRoutes');
const customerRoutes = require('./routes/customerRoutes');
const noteRoutes = require('./routes/noteRoutes');
const auditRoutes = require('./routes/auditRoutes');
const metalPriceRoutes = require('./routes/metalPriceRoutes');
const tradeMeAccountRoutes = require('./routes/tradeMeAccountRoutes');
const tradeMeItemsRoutes = require('./routes/tradeMeItemsRoutes');
const tradeMeTemplatesRoutes = require('./routes/tradeMeTemplatesRoutes');
const tradeMeImportRoutes = require('./routes/tradeMeImportRoutes');
const emailTemplateRoutes = require('./routes/emailTemplateRoutes');
const accessLogRoutes = require('./routes/accessLogRoutes');
const tradeMeSyncRoutes = require('./routes/tradeMeSyncRoutes');
const tradeMeFeedbackRoutes = require('./routes/tradeMeFeedbackRoutes');
const tradeMeCategoryRoutes = require('./routes/tradeMeCategoryRoutes');
const deviceCheckerRoutes = require('./routes/deviceCheckerRoutes');
const loanApplicationRoutes = require('./routes/loanApplicationRoutes');
const bannedItemRoutes = require('./routes/bannedItemRoutes');
const happyOrNotRoutes = require('./routes/happyOrNotRoutes');
const buyPawnAuditRoutes = require('./routes/buyPawnAuditRoutes');
const buyPawnCatchupRoutes = require('./routes/buyPawnCatchupRoutes');
const { connectDB } = require("./config/database");
const cors = require("cors");
const metalPriceService = require('./services/metalPriceService');
const tradeMeSyncOrchestrator = require('./services/tradeMeSyncOrchestrator');
const happyOrNotService = require('./services/happyOrNotService');
const schedule = require('node-schedule');
const accessLogger = require('./routes/middleware/accessLogger');


// Import models
require('./models/Location');
const MetalPriceHistory = require('./models/MetalPriceHistory');

if (!process.env.DATABASE_URL) {
  console.error("Error: DATABASE_URL variables in .env missing.");
  process.exit(-1);
}

const app = express();
const port = process.env.PORT || 3000;

// Trust proxy headers - required when behind a reverse proxy
const trustedProxies = process.env.TRUSTED_PROXY ? process.env.TRUSTED_PROXY.split(',') : ['127.0.0.1'];
app.set('trust proxy', trustedProxies);

// Pretty-print JSON responses
app.enable('json spaces');
// We want to be consistent with URL paths, so we enable strict routing
app.enable('strict routing');

// Configure CORS
const corsOptions = {
  origin: process.env.CORS_ORIGIN || '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-CSRF-Token', 'CSRF-Token'],
  credentials: true
};
app.use(cors(corsOptions));
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));
app.use(cookieParser());

// Apply security headers using helmet
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
      styleSrc: ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com'],
      fontSrc: ["'self'", 'https://fonts.gstatic.com'],
      imgSrc: ["'self'", 'data:', 'blob:'],
      connectSrc: ["'self'", 'https://api.metalpriceapi.com', 'https://api.ifreeicloud.co.uk']
    }
  },
  // Set cross-origin policies
  crossOriginEmbedderPolicy: false,
  crossOriginOpenerPolicy: { policy: 'same-origin-allow-popups' },
  crossOriginResourcePolicy: { policy: 'cross-origin' },
  // Enable XSS protection
  xssFilter: true
}));

// Set up CSRF protection with more permissive settings
const csrfProtection = csrf({
  cookie: {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax', // Changed from 'strict' to 'lax' to allow redirects
    path: '/' // Ensure the cookie is available across the entire site
  },
  ignoreMethods: ['GET', 'HEAD', 'OPTIONS'] // These methods are safe by default
});

// Apply CSRF protection to routes that need it
// We'll exclude certain API endpoints that need to be accessed by external services
app.use((req, res, next) => {
  // Skip CSRF for these paths
  const skipCsrfPaths = [
    '/api/auth/login', // Login route
    '/api/auth/refresh', // Token refresh route
    '/api/auth/setup-pin', // PIN setup route
    '/api/auth/remove-pin', // PIN removal route
    '/api/auth/pin-status', // PIN status route
    '/api/trademe/callback', // TradeMe OAuth callback
    '/api/device-checker/lookup', // Device checker API
    '/api/metal-prices/update', // Metal prices update webhook
    '/api/csrf-token' // CSRF token endpoint itself
  ];

  // Skip CSRF for specific API endpoints
  if (skipCsrfPaths.some(path => req.path.startsWith(path))) {
    next();
  } else {
    // Apply CSRF protection to all other routes
    csrfProtection(req, res, next);
  }
});

// Add debug logging for CSRF errors
app.use((err, req, res, next) => {
  if (err && err.code === 'EBADCSRFTOKEN') {
    console.error(`CSRF Error for ${req.method} ${req.path}:`, {
      headers: req.headers,
      cookies: req.cookies,
      csrfToken: req.cookies['_csrf'] || req.cookies['XSRF-TOKEN'] || 'not found'
    });
    return res.status(403).json({ error: 'invalid csrf token', message: 'CSRF token validation failed' });
  }
  next(err);
});

// Middleware to provide CSRF token to all views
app.use((req, res, next) => {
  // Only set the CSRF token if the csrfToken function exists
  // (it won't exist for routes that skip CSRF)
  if (typeof req.csrfToken === 'function') {
    try {
      const token = req.csrfToken();
      res.cookie('XSRF-TOKEN', token, {
        httpOnly: false, // Client-side JavaScript needs to read this
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax', // Changed from 'strict' to 'lax' to allow redirects
        path: '/' // Ensure the cookie is available across the entire site
      });
    } catch (error) {
      console.warn('Error setting CSRF token cookie:', error);
      // Continue without setting the cookie
    }
  }
  next();
});

// Serve static files from the uploads directory
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Create a specific route for TradeMe images to ensure they're properly served
app.get('/uploads/trademe/:listingId/:filename', (req, res) => {
  const { listingId, filename } = req.params;
  const imagePath = path.join(__dirname, 'uploads', 'trademe', listingId, filename);

  // Check if the file exists
  if (fs.existsSync(imagePath)) {
    res.sendFile(imagePath);
  } else {
    console.error(`Image not found: ${imagePath}`);
    res.status(404).send('Image not found');
  }
});

// Access logger middleware - logs user, IP, and page accessed
app.use(accessLogger);

// Database connection
connectDB();

app.on("error", (error) => {
  console.error(`Server error: ${error.message}`);
  console.error(error.stack);
});

// Basic Routes
app.use(basicRoutes);
// Authentication Routes
app.use('/api/auth', require('./routes/authRoutes'));
// Location Routes
app.use('/api/locations', locationRoutes);
// Inventory Routes
app.use('/api/inventory', inventoryRoutes);
// Address Book Routes (renamed from Customer Routes)
app.use('/api/address-book', customerRoutes);
// Legacy route for backward compatibility
app.use('/api/customers', customerRoutes);
// Notes Routes
app.use('/api/notes', noteRoutes);
// Audit Routes
app.use('/api/audit', auditRoutes);
// Metal Price Routes
app.use('/api/metal-prices', metalPriceRoutes);
// TradeMe Account Routes
app.use('/api/trademe/account', tradeMeAccountRoutes);
// TradeMe Items Routes (New)
app.use('/api/trademe/items', tradeMeItemsRoutes);
// TradeMe Sync Routes
app.use('/api/trademe/sync', tradeMeSyncRoutes);
// TradeMe Import Routes
app.use('/api/trademe/import', tradeMeImportRoutes);
// TradeMe Feedback Routes
app.use('/api/trademe/feedback', tradeMeFeedbackRoutes);
// TradeMe Category Routes
app.use('/api/trademe/categories', tradeMeCategoryRoutes);
// TradeMe Templates Routes (New)
app.use('/api/trademe/templates', tradeMeTemplatesRoutes);
// Email Templates Routes
app.use('/api/email-templates', emailTemplateRoutes);
// Dashboard Settings Routes
app.use('/api/dashboard-settings', require('./routes/dashboardSettings'));
// Access Logs Routes
app.use('/api/access-logs', accessLogRoutes);
// Device Checker Routes
app.use('/api/device-checker', deviceCheckerRoutes);
// Loan Application Routes
app.use('/api/loan-applications', loanApplicationRoutes);
// Banned Items Routes
app.use('/api/banned-items', bannedItemRoutes);
// Happy or Not Routes
app.use('/api/happy-or-not', happyOrNotRoutes);
// Buy/Pawn Audit Routes
app.use('/api/buys/audits', buyPawnAuditRoutes);
// Buy/Pawn Catchup Routes
app.use('/api/buys/catchups', buyPawnCatchupRoutes);

// In production, serve the client build files
if (process.env.NODE_ENV === 'production') {
  // Serve static files from the React app build directory
  app.use(express.static(path.join(__dirname, 'public')));

  // For any request that doesn't match an API route or static file,
  // send the React app's index.html
  app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
  });

  console.log('Running in production mode - serving static files');
} else {
  // If no routes handled the request and we're not in production, it's a 404
  app.use((req, res) => {
    res.status(404).send("Page not found.");
  });
}

const { scheduleScreenshots } = require('./services/screenshotService');
scheduleScreenshots();

// Error handling
app.use((err, req, res, next) => {
  console.error(`Unhandled application error: ${err.message}`);
  console.error(err.stack);
  res.status(500).send("There was an error serving your request.");
});

// Update metal prices on server startup
app.listen(port, '0.0.0.0', async () => {
  console.log(`Server running at http://0.0.0.0:${port}`);

  // Initial price update on server startup
  try {
    console.log('Performing initial metal price update on server startup...');
    const result = await metalPriceService.updatePricesFromApi();
    if (result.success) {
      console.log(`Initial metal price update successful: ${result.message}`);
    } else {
      console.error(`Initial metal price update failed: ${result.error}`);
    }
  } catch (error) {
    console.error('Error during initial metal price update:', error);
    console.error(error.stack);
  }

  // Schedule regular price updates every 15 minutes
  const updateInterval = 15 * 60 * 1000; // 15 minutes in milliseconds
  setInterval(async () => {
    try {
      console.log('Performing scheduled metal price update...');
      const result = await metalPriceService.updatePricesFromApi();
      if (result.success) {
        console.log(`Scheduled metal price update successful: ${result.message}`);
      } else {
        console.error(`Scheduled metal price update failed: ${result.error}`);
      }
    } catch (error) {
      console.error('Error during scheduled metal price update:', error);
      console.error(error.stack);
    }
  }, updateInterval);
});

// Schedule daily update of historical prices at 1 AM
schedule.scheduleJob('0 1 * * *', async function() {
  try {
    console.log('Performing scheduled daily update of historical metal prices...');
    // Update historical data for all metals (limited to 365 days)
    const result = await metalPriceService.updateHistoricalPrices('all', 365);
    if (result.success) {
      console.log(`Scheduled historical price update successful: ${result.message}`);
    } else {
      console.error(`Scheduled historical price update failed: ${result.error}`);
    }
  } catch (error) {
    console.error('Error during scheduled historical price update:', error);
  }
});

// Also update historical prices on server startup
(async function updateHistoricalPricesOnStartup() {
  try {
    console.log('Checking if historical metal prices need updating on startup...');

    // Check when the last update was performed
    const latestRecord = await MetalPriceHistory.findOne({}).sort({ date: -1 });
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (!latestRecord || latestRecord.date < today) {
      console.log('Performing initial historical metal price update on server startup...');
      const result = await metalPriceService.updateHistoricalPrices('all', 365);
      if (result.success) {
        console.log(`Initial historical price update successful: ${result.message}`);
      } else {
        console.error(`Initial historical price update failed: ${result.error}`);
      }
    } else {
      console.log('Historical metal prices already updated today, skipping startup update');
    }
  } catch (error) {
    console.error('Error during startup historical price update:', error);
  }
})();

// Initialize TradeMe sync orchestrator
(async function initializeTradeMeSyncOrchestrator() {
  try {
    console.log('Initializing TradeMe sync orchestrator...');
    await tradeMeSyncOrchestrator.initialize({
      listingsIntervalMinutes: 30, // Sync listings every 30 minutes
      questionsIntervalMinutes: 15, // Sync questions every 15 minutes
      feedbackIntervalMinutes: 60, // Sync feedback every 60 minutes
      categoriesIntervalMinutes: 1440 // Sync categories once a day
    });

    console.log('TradeMe sync orchestrator initialized successfully');
  } catch (error) {
    console.error('Error initializing TradeMe sync orchestrator:', error);
  }
})();

// Schedule Happy or Not sync jobs
(async function initializeHappyOrNotSync() {
  try {
    console.log('Scheduling Happy or Not sync jobs...');

    // Schedule Happy or Not feedback sync every hour
    schedule.scheduleJob('45 * * * *', async () => {
      try {
        console.log('Running scheduled Happy or Not feedback sync...');
        const result = await happyOrNotService.runFullSync();
        if (result.success) {
          console.log('Scheduled Happy or Not feedback sync completed successfully');
        } else {
          console.error('Scheduled Happy or Not feedback sync failed:', result.error);
        }
      } catch (error) {
        console.error('Error in scheduled Happy or Not feedback sync:', error);
      }
    });

    console.log('Happy or Not sync jobs scheduled successfully');
  } catch (error) {
    console.error('Error scheduling Happy or Not sync jobs:', error);
  }
})();
