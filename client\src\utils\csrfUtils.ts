import axios from 'axios';

/**
 * Gets the current CSRF token from cookies
 * @returns The CSRF token or null if not found
 */
export const getCsrfToken = (): string | null => {
  const cookies = document.cookie.split(';');
  for (let i = 0; i < cookies.length; i++) {
    const cookie = cookies[i].trim();
    if (cookie.startsWith('XSRF-TOKEN=')) {
      return cookie.substring('XSRF-TOKEN='.length, cookie.length);
    }
  }
  return null;
};

/**
 * Fetches a new CSRF token from the server
 * This should be called when the application initializes or when a user logs in
 * @returns Promise that resolves when the CSRF token has been fetched
 */
export const fetchCsrfToken = async (): Promise<string | null> => {
  try {
    // Add a cache-busting parameter to avoid cached responses
    const timestamp = new Date().getTime();

    // Use a direct axios call instead of the api instance to avoid circular dependencies
    // and to ensure we don't need a token to get a token
    const response = await axios.get(`/api/csrf-token?_t=${timestamp}`, {
      withCredentials: true // Important: this ensures cookies are sent/received
    });

    if (response.data.success) {

      // Wait a moment for cookies to be set
      await new Promise(resolve => setTimeout(resolve, 100));

      // Check if the token is in cookies
      const token = getCsrfToken();
      if (token) {
        return token;
      } else {
        return null;
      }
    } else {
      return null;
    }
  } catch (error) {
    console.error('Failed to fetch CSRF token:', error);
    // Don't throw the error - we'll try again on the next request
    return null;
  }
};
