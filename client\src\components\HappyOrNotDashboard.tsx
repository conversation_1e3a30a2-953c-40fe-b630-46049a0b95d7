import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, BarChart3, MessageSquare,  Calendar } from 'lucide-react';
import { Link } from 'react-router-dom';
import { formatDateTime } from '@/lib/utils';
import { format } from 'date-fns';
import {
  getHappyOrNotFeedbackData,
  getHappyOrNotGoalSettings,
  HappyOrNotFeedbackData,
  HappyOrNotGoalSettings
} from '@/api/happyOrNot';

export function HappyOrNotDashboard() {
  const [loading, setLoading] = useState(true);
  const [feedbackData, setFeedbackData] = useState<HappyOrNotFeedbackData | null>(null);
  const [settings, setSettings] = useState<HappyOrNotGoalSettings | null>(null);
  const [loadingSettings, setLoadingSettings] = useState(true);
  const [weekRange, setWeekRange] = useState<{ weekStart: Date; weekEnd: Date } | null>(null);



  // Load data on initial render
  useEffect(() => {
    fetchFeedbackData();
    fetchSettings();
  }, []);

  // Fetch feedback data
  const fetchFeedbackData = async () => {
    setLoading(true);
    try {
      // Calculate current week's start (Monday) and end (current day)
      const today = new Date();
      const dayOfWeek = today.getDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
      const daysToSubtract = dayOfWeek === 0 ? 6 : dayOfWeek - 1;

      const weekStart = new Date(today);
      weekStart.setDate(today.getDate() - daysToSubtract);
      weekStart.setHours(0, 0, 0, 0);

      const weekEnd = new Date(today);
      weekEnd.setHours(23, 59, 59, 999);

      // Store week range for title display
      setWeekRange({ weekStart, weekEnd });

      const startDateStr = weekStart.toISOString().split('T')[0];
      const endDateStr = weekEnd.toISOString().split('T')[0];

      const response = await getHappyOrNotFeedbackData({
        startDate: startDateStr,
        endDate: endDateStr,
        groupBy: 'day'
      });

      if (response.success) {
        setFeedbackData(response.data);
      }
    } catch (error) {
      console.error('Error fetching feedback data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Fetch Happy or Not goal settings
  const fetchSettings = async () => {
    setLoadingSettings(true);
    try {
      const response = await getHappyOrNotGoalSettings();
      if (response.success) {
        setSettings(response.data);
      }
    } catch (error) {
      console.error('Error fetching Happy or Not goal settings:', error);
    } finally {
      setLoadingSettings(false);
    }
  };

  // Get button color based on index
  const getButtonColor = (index: number) => {
    const COLORS = ['#e24e67', '#ef9ea1', '#99cd9b', '#0daa5d'];
    if (index <= 3) {
      return COLORS[index];
    } else {
      if (index <= 2) return COLORS[0]; // Very Unhappy
      if (index <= 4) return COLORS[1]; // Unhappy
      if (index <= 7) return COLORS[2]; // Happy
      return COLORS[3]; // Very Happy
    }
  };

  // Get button label based on index
  const getButtonLabel = (index: number) => {
    if (index <= 3) {
      // 4-button scale
      switch (index) {
        case 0: return 'Very Unhappy';
        case 1: return 'Unhappy';
        case 2: return 'Happy';
        case 3: return 'Very Happy';
        default: return 'Unknown';
      }
    } else {
      // NPS scale (0-10)
      return `Rating ${index}`;
    }
  };

  // We'll use the formatDateTime utility from utils.ts instead of this local function

  // Function to get face image based on button index
  const getFaceImage = (index: number) => {
    switch (index) {
      case 0: return '/img/happyornot/veryangry.svg';
      case 1: return '/img/happyornot/angry.svg';
      case 2: return '/img/happyornot/happy.svg';
      case 3: return '/img/happyornot/veryhappy.svg';
      default: return '/img/happyornot/happy.svg';
    }
  };

  // Format percentage change
  const formatChange = (change: any) => {
    if (change === undefined || change === null || isNaN(Number(change))) {
      return '0.0%';
    }
    const numericChange = typeof change === 'string' ? parseFloat(change) : change;
    const sign = numericChange >= 0 ? '+' : '';
    return `${sign}${numericChange.toFixed(1)}%`;
  };

  // Get color for change values
  const getChangeColor = (change: any) => {
    if (change === undefined || change === null || isNaN(Number(change))) {
      return 'text-muted-foreground';
    }
    const numericChange = typeof change === 'string' ? parseFloat(change) : change;
    return numericChange >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400';
  };

  // Generate title with week range
  const getWeekTitle = () => {
    if (!weekRange) return 'Happy or Not Feedback (Week to Date)';

    const startFormatted = format(weekRange.weekStart, 'MMM d');
    const endFormatted = format(weekRange.weekEnd, 'MMM d');

    return `Happy or Not Feedback (This Week: ${startFormatted} - ${endFormatted})`;
  };

  return (
    <Card className="overflow-hidden">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div className="flex items-center space-x-2">
          <div className="p-1.5 bg-gray-100 dark:bg-gray-800 rounded-full">
            <MessageSquare className="h-5 w-5 text-gray-600 dark:text-gray-400" />
          </div>
          <CardTitle className="text-base font-medium">{getWeekTitle()}</CardTitle>
        </div>
        <Button variant="outline" size="sm" asChild>
          <Link to="/feedback" className="flex items-center gap-1">
            <BarChart3 className="h-4 w-4" />
            <span>View Details</span>
          </Link>
        </Button>
      </CardHeader>
      <CardContent>
        {loading || loadingSettings ? (
          <div className="flex flex-col justify-center items-center h-60 bg-gray-50/50 dark:bg-gray-900/20 rounded-lg border border-dashed border-gray-200 dark:border-gray-800">
            <Loader2 className="h-8 w-8 animate-spin text-gray-500 dark:text-gray-400 mb-2" />
            <p className="text-sm text-muted-foreground">Loading feedback data...</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left side - Stats */}
            <div className="space-y-6">

              {/* Feedback Summary Cards */}
              <div className="grid grid-cols-2 gap-3">

                {/* Very Happy */}
                <Card className="border-l-4" style={{ borderLeftColor: getButtonColor(3) }}>
                  <CardContent className="p-3 flex items-center gap-2">
                    <img src="/img/happyornot/veryhappy.svg" alt="Very Happy" className="w-10 h-10" />
                    <div>
                      <p className="font-medium text-sm">Very Happy</p>
                      <div className="flex items-baseline gap-1">
                        <span className="text-xl font-bold">
                          {feedbackData?.buttonCounts.find(item => item._id === 3)?.count || 0}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {feedbackData?.totalCount && feedbackData.totalCount > 0 ?
                            `${(((feedbackData.buttonCounts.find(item => item._id === 3)?.count || 0) / feedbackData.totalCount) * 100).toFixed(1)}%`
                            : '0%'}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Happy */}
                <Card className="border-l-4" style={{ borderLeftColor: getButtonColor(2) }}>
                  <CardContent className="p-3 flex items-center gap-2">
                    <img src="/img/happyornot/happy.svg" alt="Happy" className="w-10 h-10" />
                    <div>
                      <p className="font-medium text-sm">Happy</p>
                      <div className="flex items-baseline gap-1">
                        <span className="text-xl font-bold">
                          {feedbackData?.buttonCounts.find(item => item._id === 2)?.count || 0}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {feedbackData?.totalCount && feedbackData.totalCount > 0 ?
                            `${(((feedbackData.buttonCounts.find(item => item._id === 2)?.count || 0) / feedbackData.totalCount) * 100).toFixed(1)}%`
                            : '0%'}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Unhappy */}
                <Card className="border-l-4" style={{ borderLeftColor: getButtonColor(1) }}>
                  <CardContent className="p-3 flex items-center gap-2">
                    <img src="/img/happyornot/angry.svg" alt="Unhappy" className="w-10 h-10" />
                    <div>
                      <p className="font-medium text-sm">Unhappy</p>
                      <div className="flex items-baseline gap-1">
                        <span className="text-xl font-bold">
                          {feedbackData?.buttonCounts.find(item => item._id === 1)?.count || 0}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {feedbackData?.totalCount && feedbackData.totalCount > 0 ?
                            `${(((feedbackData.buttonCounts.find(item => item._id === 1)?.count || 0) / feedbackData.totalCount) * 100).toFixed(1)}%`
                            : '0%'}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Very Unhappy */}
                <Card className="border-l-4" style={{ borderLeftColor: getButtonColor(0) }}>
                  <CardContent className="p-3 flex items-center gap-2">
                    <img src="/img/happyornot/veryangry.svg" alt="Very Unhappy" className="w-10 h-10" />
                    <div>
                      <p className="font-medium text-sm">Very Unhappy</p>
                      <div className="flex items-baseline gap-1">
                        <span className="text-xl font-bold">
                          {feedbackData?.buttonCounts.find(item => item._id === 0)?.count || 0}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {feedbackData?.totalCount && feedbackData.totalCount > 0 ?
                            `${(((feedbackData.buttonCounts.find(item => item._id === 0)?.count || 0) / feedbackData.totalCount) * 100).toFixed(1)}%`
                            : '0%'}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Stats and Goals - 2x2 Grid */}
              <div className="grid grid-cols-2 gap-4">
                {/* Total Feedback */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base font-medium">Total Feedback</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center">
                      <div className="text-4xl font-bold">{feedbackData?.totalCount || 0}</div>
                      {feedbackData?.comparison && (
                        <div className="flex justify-center gap-4 mt-2">
                          <div>
                            <p className="text-xs text-muted-foreground">vs Previous Period</p>
                            <p className={getChangeColor(feedbackData.comparison.previousPeriod.change)}>
                              {formatChange(feedbackData.comparison.previousPeriod.change)}
                            </p>
                          </div>
                          <div>
                            <p className="text-xs text-muted-foreground">vs Last Year</p>
                            <p className={getChangeColor(feedbackData.comparison.lastYear.change)}>
                              {formatChange(feedbackData.comparison.lastYear.change)}
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Happiness Score */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base font-medium">Happiness Score</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center">
                      <div className="text-4xl font-bold">{feedbackData?.happinessScore || 0}%</div>
                      {feedbackData?.comparison && (
                        <div className="flex justify-center gap-4 mt-2">
                          <div>
                            <p className="text-xs text-muted-foreground">vs Previous Period</p>
                            <p className={getChangeColor(feedbackData.comparison.previousPeriod.scoreChange)}>
                              {formatChange(feedbackData.comparison.previousPeriod.scoreChange)}
                            </p>
                          </div>
                          <div>
                            <p className="text-xs text-muted-foreground">vs Last Year</p>
                            <p className={getChangeColor(feedbackData.comparison.lastYear.scoreChange)}>
                              {formatChange(feedbackData.comparison.lastYear.scoreChange)}
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Feedback Goal */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base font-medium">Feedback Goal</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center">
                      {settings && feedbackData && (
                        <>
                          <div className="flex justify-center items-baseline gap-2">
                            <span className="text-3xl font-bold">{feedbackData.totalCount}</span>
                            <span className="text-lg text-muted-foreground">/ {settings.weeklyFeedbackGoal}</span>
                          </div>
                          <div className="mt-2 text-sm text-center">
                            <span className={`font-medium ${feedbackData.totalCount >= settings.weeklyFeedbackGoal ? 'text-green-600 dark:text-green-400' : 'text-amber-600 dark:text-amber-400'}`}>
                              {feedbackData.totalCount >= settings.weeklyFeedbackGoal ? 'Goal Reached!' : `${Math.round((feedbackData.totalCount / settings.weeklyFeedbackGoal) * 100)}% Complete`}
                            </span>
                          </div>
                          <div className="w-full mt-3">
                            <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                              <div
                                className="h-full rounded-full transition-all duration-500 ease-out"
                                style={{
                                  width: `${Math.min(100, (feedbackData.totalCount / settings.weeklyFeedbackGoal) * 100)}%`,
                                  backgroundColor: feedbackData.totalCount >= settings.weeklyFeedbackGoal ? '#0daa5d' : '#3b82f6'
                                }}
                              />
                            </div>
                          </div>
                        </>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Happiness Score Goal */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base font-medium">Happiness Goal</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center">
                      {settings && feedbackData && (
                        <>
                          <div className="flex justify-center items-baseline gap-2">
                            <span className="text-3xl font-bold">{feedbackData.happinessScore}%</span>
                            <span className="text-lg text-muted-foreground">/ {settings.happinessScoreGoal}%</span>
                          </div>
                          <div className="mt-2 text-sm text-center">
                            <span className={`font-medium ${feedbackData.happinessScore >= settings.happinessScoreGoal ? 'text-green-600 dark:text-green-400' : 'text-amber-600 dark:text-amber-400'}`}>
                              {feedbackData.happinessScore >= settings.happinessScoreGoal ? 'Goal Reached!' : `${Math.round((feedbackData.happinessScore / settings.happinessScoreGoal) * 100)}% Complete`}
                            </span>
                          </div>
                          <div className="w-full mt-3">
                            <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                              <div
                                className="h-full rounded-full transition-all duration-500 ease-out"
                                style={{
                                  width: `${Math.min(100, (feedbackData.happinessScore / settings.happinessScoreGoal) * 100)}%`,
                                  backgroundColor: feedbackData.happinessScore >= settings.happinessScoreGoal ? '#0daa5d' : '#99cd9b'
                                }}
                              />
                            </div>
                          </div>
                        </>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Right side - Recent Feedback */}
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <h3 className="text-base font-medium">Recent Feedback</h3>
                {settings?.lastSynced && (
                  <div className="flex items-center gap-1.5 text-xs text-muted-foreground">
                    <Calendar className="h-3 w-3" />
                    <p>Last updated: {formatDateTime(settings.lastSynced)}</p>
                  </div>
                )}
              </div>

              {feedbackData?.recentFeedbacks && feedbackData.recentFeedbacks.length > 0 ? (
                <div className="space-y-3">
                  {feedbackData.recentFeedbacks.slice(0, 4).map((feedback) => (
                    <div
                      key={feedback.feedbackId}
                      className="p-4 border rounded-lg"
                      style={{ borderLeftColor: getButtonColor(feedback.buttonIndex), borderLeftWidth: '4px' }}
                    >
                      <div className="flex justify-between items-start">
                        <div className="flex items-center gap-3">
                          <img
                            src={getFaceImage(feedback.buttonIndex)}
                            alt={getButtonLabel(feedback.buttonIndex)}
                            className="w-10 h-10"
                          />
                          <div>
                            <p className="font-medium">{getButtonLabel(feedback.buttonIndex)}</p>
                            <p className="text-sm text-muted-foreground">
                              {formatDateTime(feedback.localTime)}
                            </p>
                          </div>
                        </div>
                        {feedback.experiencePointName && (
                          <div className="text-right">
                            <p className="text-sm">{feedback.experiencePointName}</p>
                          </div>
                        )}
                      </div>
                      {(feedback.text || feedback.followupOptionText) && (
                        <div className="mt-2 pt-2 border-t">
                          {feedback.text && (
                            <p className="italic text-sm text-gray-600 dark:text-gray-400">"{feedback.text}"</p>
                          )}
                          {feedback.followupOptionText && (
                            <div className="flex items-start gap-1 mt-1.5 text-sm">
                              <span className="font-medium text-gray-700 dark:text-gray-300">Follow-up:</span>
                              <span className="text-gray-600 dark:text-gray-400">{feedback.followupOptionText}</span>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex flex-col justify-center items-center h-40 text-muted-foreground text-sm bg-gray-50 dark:bg-gray-900/50 rounded-lg border border-dashed border-gray-200 dark:border-gray-800">
                  <MessageSquare className="h-8 w-8 text-gray-400 dark:text-gray-600 mb-2" />
                  <p>No recent feedback available</p>
                </div>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
