import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Shield, CheckCircle, XCircle, AlertCircle } from 'lucide-react';

interface AuthorizedLimitSectionProps {
  control: any;
  disabled?: boolean;
  auditType: 'buy' | 'pawn' | 'price';
}

/**
 * Authorized Limit Check component for the final Authorization & Summary step
 * Only shown for buy and pawn audits (not price audits)
 */
export function AuthorizedLimitSection({ control, disabled = false, auditType }: AuthorizedLimitSectionProps) {
  // Don't show for price audits
  if (auditType === 'price') {
    return null;
  }

  // Common fail reasons for authorized limit check
  const limitFailReasons = [
    { id: 'exceeded_limit', label: 'Exceeded authorized limit' },
    { id: 'no_authorization', label: 'No authorization obtained' },
    { id: 'wrong_approver', label: 'Wrong approver used' },
    { id: 'missing_documentation', label: 'Missing authorization documentation' },
  ];

  // Helper function to render status badge
  const renderStatusBadge = (status: string) => {
    switch (status) {
      case 'pass':
        return <Badge variant="success" className="flex items-center gap-1"><CheckCircle className="h-3 w-3" />Pass</Badge>;
      case 'fail':
        return <Badge variant="destructive" className="flex items-center gap-1"><XCircle className="h-3 w-3" />Fail</Badge>;
      default:
        return <Badge variant="secondary" className="flex items-center gap-1"><AlertCircle className="h-3 w-3" />Not Assessed</Badge>;
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Shield className="h-5 w-5 text-primary" />
            <CardTitle>Authorized Limit Check</CardTitle>
          </div>
          <FormField
            control={control}
            name="authorizedLimitCheck.status"
            render={({ field }) => renderStatusBadge(field.value)}
          />
        </div>
        <CardDescription>
          Verify that the transaction was within authorized limits and properly approved
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <FormField
          control={control}
          name="authorizedLimitCheck.status"
          render={({ field }) => (
            <FormItem className="space-y-3">
              <FormLabel>Assessment</FormLabel>
              <FormControl>
                <RadioGroup
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  className="flex flex-col space-y-2"
                  disabled={disabled}
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="pass" id="limit-pass" />
                    <FormLabel htmlFor="limit-pass" className="cursor-pointer">Pass</FormLabel>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="fail" id="limit-fail" />
                    <FormLabel htmlFor="limit-fail" className="cursor-pointer">Fail</FormLabel>
                  </div>
                </RadioGroup>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Show fail reasons if status is "fail" */}
        <FormField
          control={control}
          name="authorizedLimitCheck.status"
          render={({ field }) => (
            <div>
              {field.value === 'fail' && (
                <FormField
                  control={control}
                  name="authorizedLimitCheck.failReasons"
                  render={({ field: failReasonsField }) => (
                    <FormItem>
                      <FormLabel>Fail Reasons</FormLabel>
                      <FormDescription>Select all that apply</FormDescription>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 border rounded-md p-3">
                        {limitFailReasons.map((reason) => (
                          <FormItem
                            key={reason.id}
                            className="flex flex-row items-start space-x-3 space-y-0 p-2 hover:bg-accent rounded-md"
                          >
                            <FormControl>
                              <Checkbox
                                checked={failReasonsField.value?.includes(reason.id)}
                                onCheckedChange={(checked) => {
                                  const currentReasons = failReasonsField.value || [];
                                  if (checked) {
                                    failReasonsField.onChange([...currentReasons, reason.id]);
                                  } else {
                                    failReasonsField.onChange(currentReasons.filter((r: string) => r !== reason.id));
                                  }
                                }}
                                disabled={disabled}
                              />
                            </FormControl>
                            <FormLabel className="text-sm font-normal cursor-pointer">
                              {reason.label}
                            </FormLabel>
                          </FormItem>
                        ))}
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
            </div>
          )}
        />

        <FormField
          control={control}
          name="authorizedLimitCheck.notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Notes</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Enter any additional notes about authorization limits"
                  className="min-h-[100px]"
                  {...field}
                  disabled={disabled}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </CardContent>
    </Card>
  );
}
