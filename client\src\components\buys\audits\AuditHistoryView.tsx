import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { 
  History, 
  Clock, 
  User, 
  Edit, 
  Flag, 
  CheckCircle, 
  MessageSquare,
  FileText,
  AlertTriangle
} from 'lucide-react';
import { format, formatDistanceToNow } from 'date-fns';
import { getAuditHistory } from '@/api/buyPawnAudits';
import { useToast } from '@/hooks/useToast';

interface HistoryEntry {
  _id: string;
  auditId: string;
  action: string;
  description: string;
  userId: string;
  userName: string;
  userRole: string;
  timestamp: string;
  details?: any;
}

interface AuditHistoryViewProps {
  auditId: string;
}

export function AuditHistoryView({ auditId }: AuditHistoryViewProps) {
  const { toast } = useToast();
  const [history, setHistory] = useState<HistoryEntry[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load audit history
  useEffect(() => {
    loadHistory();
  }, [auditId]);

  const loadHistory = async () => {
    try {
      setIsLoading(true);
      const response = await getAuditHistory(auditId);

      if (response.success) {
        // Transform the data to match our interface
        const transformedHistory = response.data.map((entry: any) => ({
          _id: entry._id,
          auditId,
          action: entry.action,
          description: entry.description,
          userId: entry.performedBy._id,
          userName: entry.performedBy.fullName,
          userRole: entry.performedBy.role,
          timestamp: entry.performedAt,
          details: entry.details,
        }));
        setHistory(transformedHistory);
      } else {
        throw new Error(response.error || 'Failed to load history');
      }
    } catch (error: any) {
      console.error('Failed to load audit history:', error);
      toast({
        title: 'Error',
        description: 'Failed to load audit history.',
        variant: 'destructive',
      });
      setHistory([]); // Set empty array on error
    } finally {
      setIsLoading(false);
    }
  };

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'created':
        return <FileText className="h-4 w-4 text-blue-500" />;
      case 'updated':
        return <Edit className="h-4 w-4 text-orange-500" />;
      case 'flagged':
        return <Flag className="h-4 w-4 text-red-500" />;
      case 'unflagged':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'followup_completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'comment_added':
        return <MessageSquare className="h-4 w-4 text-purple-500" />;
      default:
        return <History className="h-4 w-4 text-gray-500" />;
    }
  };

  const getActionColor = (action: string) => {
    switch (action) {
      case 'created':
        return 'bg-blue-100 text-blue-800';
      case 'updated':
        return 'bg-orange-100 text-orange-800';
      case 'flagged':
        return 'bg-red-100 text-red-800';
      case 'unflagged':
      case 'followup_completed':
        return 'bg-green-100 text-green-800';
      case 'comment_added':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'bg-red-100 text-red-800';
      case 'manager':
        return 'bg-blue-100 text-blue-800';
      case 'employee':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            Audit History
          </CardTitle>
          <CardDescription>
            Complete timeline of all actions and changes made to this audit
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8 text-muted-foreground">
              Loading audit history...
            </div>
          ) : history.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No history available for this audit.
            </div>
          ) : (
            <div className="space-y-4">
              {history.map((entry, index) => (
                <div key={entry._id} className="relative">
                  {/* Timeline line */}
                  {index < history.length - 1 && (
                    <div className="absolute left-6 top-12 w-0.5 h-16 bg-border"></div>
                  )}
                  
                  <div className="flex items-start space-x-4">
                    {/* Action icon */}
                    <div className="flex-shrink-0 w-12 h-12 bg-background border-2 border-border rounded-full flex items-center justify-center">
                      {getActionIcon(entry.action)}
                    </div>
                    
                    {/* Content */}
                    <div className="flex-1 space-y-2">
                      <div className="flex items-center space-x-2 flex-wrap">
                        <Badge 
                          variant="secondary" 
                          className={`text-xs ${getActionColor(entry.action)}`}
                        >
                          {entry.action.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </Badge>
                        <span className="text-sm font-medium">{entry.description}</span>
                      </div>
                      
                      <div className="flex items-center space-x-3 text-xs text-muted-foreground">
                        <div className="flex items-center space-x-1">
                          <Avatar className="h-5 w-5">
                            <AvatarFallback className="text-xs">
                              {getInitials(entry.userName)}
                            </AvatarFallback>
                          </Avatar>
                          <span>{entry.userName}</span>
                          <Badge 
                            variant="secondary" 
                            className={`text-xs ${getRoleColor(entry.userRole)}`}
                          >
                            {entry.userRole}
                          </Badge>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Clock className="h-3 w-3" />
                          <span title={format(new Date(entry.timestamp), 'PPpp')}>
                            {formatDistanceToNow(new Date(entry.timestamp), { addSuffix: true })}
                          </span>
                        </div>
                      </div>
                      
                      {/* Additional details */}
                      {entry.details && (
                        <div className="text-xs bg-muted rounded-md p-2 mt-2">
                          {entry.details.reason && (
                            <div><strong>Reason:</strong> {entry.details.reason}</div>
                          )}
                          {entry.details.comment && (
                            <div><strong>Comment:</strong> {entry.details.comment}</div>
                          )}
                          {entry.details.response && (
                            <div><strong>Response:</strong> {entry.details.response}</div>
                          )}
                          {entry.details.section && (
                            <div><strong>Section:</strong> {entry.details.section} → {entry.details.status}</div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* History Statistics */}
      <Card className="border-dashed">
        <CardContent className="pt-6">
          <div className="text-sm text-muted-foreground space-y-2">
            <h4 className="font-medium text-foreground">History Summary:</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs">
              <div>
                <span className="font-medium">Total Actions:</span> {history.length}
              </div>
              <div>
                <span className="font-medium">Updates:</span> {history.filter(h => h.action === 'updated').length}
              </div>
              <div>
                <span className="font-medium">Comments:</span> {history.filter(h => h.action === 'comment_added').length}
              </div>
              <div>
                <span className="font-medium">Follow-ups:</span> {history.filter(h => h.action.includes('follow')).length}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
