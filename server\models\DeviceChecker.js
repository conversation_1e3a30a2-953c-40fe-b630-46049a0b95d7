const mongoose = require('mongoose');

const deviceCheckerSchema = new mongoose.Schema({
  deviceIdentifier: {
    type: String,
    required: true,
    index: true
  },
  serviceId: {
    type: String,
    required: true
  },
  serviceName: {
    type: String,
    required: true
  },
  cost: {
    type: Number,
    default: 0
  },
  result: {
    type: Object,
    required: true
  },
  status: {
    type: String,
    enum: ['success', 'error', 'pending'],
    default: 'success'
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  username: {
    type: String,
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now,
    immutable: true,
    index: true
  }
}, {
  versionKey: false
});

// Create indexes for common queries
deviceCheckerSchema.index({ deviceIdentifier: 1, createdAt: -1 });
deviceCheckerSchema.index({ userId: 1, createdAt: -1 });

const DeviceChecker = mongoose.model('DeviceChe<PERSON>', deviceCheckerSchema, 'devicechecker');

module.exports = DeviceChecker;
