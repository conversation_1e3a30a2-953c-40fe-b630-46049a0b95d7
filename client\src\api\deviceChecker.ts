import api from './api';

export interface DeviceCheckerSettings {
  url: string;
  apiKey: string;
  enabledServices: Array<{
    serviceId: string;
    name: string;
    cost: number;
    description?: string;
    enabled: boolean;
  }>;
  accountBalance: number;
  lastUpdated: string;
}

export interface DeviceCheckerLookup {
  _id: string;
  deviceIdentifier: string;
  serviceId: string;
  serviceName: string;
  cost: number;
  result: any;
  status: 'success' | 'error' | 'pending';
  userId: {
    _id: string;
    username: string;
    email: string;
  };
  username: string;
  createdAt: string;
}

export interface PaginationData {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Description: Get device checker settings
// Endpoint: GET /api/device-checker/settings
// Request: {}
// Response: { success: boolean, settings: DeviceCheckerSettings }
export const getDeviceCheckerSettings = async (): Promise<{ success: boolean, settings: DeviceCheckerSettings }> => {
  try {
    const response = await api.get('/device-checker/settings');
    return response.data;
  } catch (error: any) {
    console.error('Error fetching device checker settings:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Description: Update device checker settings
// Endpoint: PUT /api/device-checker/settings
// Request: Partial<DeviceCheckerSettings>
// Response: { success: boolean, settings: DeviceCheckerSettings }
export const updateDeviceCheckerSettings = async (settings: Partial<DeviceCheckerSettings>): Promise<{ success: boolean, settings: DeviceCheckerSettings }> => {
  try {
    const response = await api.put('/device-checker/settings', settings);
    return response.data;
  } catch (error: any) {
    console.error('Error updating device checker settings:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Description: Verify connection to the API
// Endpoint: POST /api/device-checker/verify-connection
// Request: {}
// Response: { success: boolean, message?: string, error?: string, balance?: number }
export const verifyDeviceCheckerConnection = async (): Promise<{ success: boolean, message?: string, error?: string, balance?: number }> => {
  try {
    const response = await api.post('/device-checker/verify-connection');
    return response.data;
  } catch (error: any) {
    console.error('Error verifying device checker connection:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Description: Get account balance
// Endpoint: GET /api/device-checker/balance
// Request: {}
// Response: { success: boolean, balance?: number, error?: string }
export const getDeviceCheckerBalance = async (): Promise<{ success: boolean, balance?: number, error?: string }> => {
  try {
    const response = await api.get('/device-checker/balance');
    return response.data;
  } catch (error: any) {
    console.error('Error getting device checker balance:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Description: Get available services
// Endpoint: GET /api/device-checker/services
// Request: {}
// Response: { success: boolean, services?: Array<{ serviceId: string, name: string, cost: number, enabled: boolean }>, error?: string }
export const getDeviceCheckerServices = async (): Promise<{ success: boolean, services?: Array<{ serviceId: string, name: string, cost: number, description?: string, enabled: boolean }>, error?: string }> => {
  try {
    const response = await api.get('/device-checker/services');
    return response.data;
  } catch (error: any) {
    console.error('Error getting device checker services:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Description: Refresh services from external API
// Endpoint: POST /api/device-checker/refresh-services
// Request: {}
// Response: { success: boolean, services?: Array<{ serviceId: string, name: string, cost: number, description?: string, enabled: boolean }>, error?: string }
export const refreshDeviceCheckerServices = async (): Promise<{ success: boolean, services?: Array<{ serviceId: string, name: string, cost: number, description?: string, enabled: boolean }>, error?: string }> => {
  try {
    const response = await api.post('/device-checker/refresh-services');
    return response.data;
  } catch (error: any) {
    console.error('Error refreshing device checker services:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Description: Lookup device information
// Endpoint: POST /api/device-checker/lookup
// Request: { deviceIdentifier: string, serviceId: string, checkAgain?: boolean }
// Response: { success: boolean, result?: any, isExisting?: boolean, lookupId?: string, createdAt?: string, username?: string, error?: string }
export const lookupDevice = async (data: { deviceIdentifier: string, serviceId: string, checkAgain?: boolean }): Promise<{ success: boolean, result?: any, isExisting?: boolean, lookupId?: string, createdAt?: string, username?: string, error?: string }> => {
  try {
    const response = await api.post('/device-checker/lookup', data);
    return response.data;
  } catch (error: any) {
    console.error('Error looking up device:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Description: Get lookup history
// Endpoint: GET /api/device-checker/history
// Request: { page?: number, limit?: number, userId?: string }
// Response: { success: boolean, lookups?: DeviceCheckerLookup[], pagination?: PaginationData, error?: string }
export const getDeviceCheckerHistory = async (page = 1, limit = 10, userId?: string): Promise<{ success: boolean, lookups?: DeviceCheckerLookup[], pagination?: PaginationData, error?: string }> => {
  try {
    const params: any = { page, limit };
    if (userId) params.userId = userId;

    const response = await api.get('/device-checker/history', { params });
    return response.data;
  } catch (error: any) {
    console.error('Error getting device checker history:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};
