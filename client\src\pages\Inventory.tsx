import { useEffect, useState } from 'react';
import { getInventory, InventoryItem } from '@/api/inventory';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/useToast';
import { PlusCircle, Search, ArrowUpDown, ChevronFirst, ChevronLast, ChevronLeft, ChevronRight, AlertTriangle } from 'lucide-react';
import { InventoryItemForm } from '@/components/InventoryItemForm';
import { InventoryItemModal } from '@/components/InventoryItemModal';
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from '@/components/ui/table';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAuth } from '@/contexts/AuthContext';

export function Inventory() {
  const { toast } = useToast();
  const { user } = useAuth();
  const [items, setItems] = useState<InventoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [brandFilter, setBrandFilter] = useState('all');
  const [formOpen, setFormOpen] = useState(false);
  const [selectedItemId, setSelectedItemId] = useState<string | null>(null);
  const [itemModalOpen, setItemModalOpen] = useState(false);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(15);

  // Sorting state
  const [sortField, setSortField] = useState<keyof InventoryItem>('brand');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [secondarySortField, setSecondarySortField] = useState<keyof InventoryItem>('model');

  // Check if user can add items (admin or manager)
  const canAddItems = user?.role === 'admin' || user?.role === 'manager';

  useEffect(() => {
    fetchInventory();
  }, []);

  // Reset pagination when search term or filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, categoryFilter, brandFilter]);

  const fetchInventory = async () => {
    try {
      setLoading(true);
      const data = await getInventory();
      setItems(data.items);
    } catch (error: any) {
      toast({
        title: 'Error',
        description: `Failed to load pricing database: ${error.message}`,
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle row click to open item details modal
  const handleRowClick = (itemId: string) => {
    setSelectedItemId(itemId);
    setItemModalOpen(true);
  };

  // We're using a modal instead of navigation

  // Filter function
  const filteredItems = items.filter(item => {
    const matchesSearch =
      item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.brand?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.model?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.modelNumber?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesCategory = categoryFilter === 'all' || item.category === categoryFilter;
    const matchesBrand = brandFilter === 'all' || item.brand === brandFilter;

    return matchesSearch && matchesCategory && matchesBrand;
  });

  // Sort function
  const sortedItems = [...filteredItems].sort((a, b) => {
    let valA = a[sortField];
    let valB = b[sortField];

    // Handle dates for primary sort
    if (sortField === 'lastUpdated') {
      const dateCompare = sortDirection === 'asc'
        ? new Date(valA).getTime() - new Date(valB).getTime()
        : new Date(valB).getTime() - new Date(valA).getTime();

      // If dates are equal, use secondary sort
      if (dateCompare === 0) {
        const secondaryValA = a[secondarySortField];
        const secondaryValB = b[secondarySortField];

        if (typeof secondaryValA === 'string' && typeof secondaryValB === 'string') {
          // First check for model numbers like "iPhone 7" vs "iPhone 11"
          const baseNameA = secondaryValA.split(/\d/)[0]?.trim();
          const baseNameB = secondaryValB.split(/\d/)[0]?.trim();

          if (baseNameA === baseNameB) {
            // Extract numbers from the model names
            const numbersA = secondaryValA.match(/\d+/g) || [];
            const numbersB = secondaryValB.match(/\d+/g) || [];

            if (numbersA.length > 0 && numbersB.length > 0) {
              // Compare the first number found
              return parseInt(numbersA[0]) - parseInt(numbersB[0]);
            }
          }

          // Fall back to generation handling
          const genRegex = /\((\d+)(?:st|nd|rd|th) Gen\)/;
          const matchA = secondaryValA.match(genRegex);
          const matchB = secondaryValB.match(genRegex);

          if (matchA && matchB) {
            return parseInt(matchA[1]) - parseInt(matchB[1]);
          }

          return secondaryValA.localeCompare(secondaryValB);
        }
        return Number(secondaryValA) - Number(secondaryValB);
      }
      return dateCompare;
    }

    // Handle strings for primary sort
    if (typeof valA === 'string' && typeof valB === 'string') {
      // First check for product lines with numeric models (iPhone 7 vs iPhone 11)
      const baseNameA = valA.split(/\d/)[0]?.trim();
      const baseNameB = valB.split(/\d/)[0]?.trim();

      if (baseNameA === baseNameB) {
        // If the base names match (e.g., both are "iPhone "), compare by the numbers
        const numbersA = valA.match(/\d+/g) || [];
        const numbersB = valB.match(/\d+/g) || [];

        if (numbersA.length > 0 && numbersB.length > 0) {
          // Sort by the first number found in the string
          const numCompare = parseInt(numbersA[0]) - parseInt(numbersB[0]);
          return sortDirection === 'asc' ? numCompare : -numCompare;
        }
      }

      // Then check for generation numbers
      const genRegexPrimary = /\((\d+)(?:st|nd|rd|th) Gen\)/;
      const matchAPrimary = valA.match(genRegexPrimary);
      const matchBPrimary = valB.match(genRegexPrimary);

      if (matchAPrimary && matchBPrimary && valA.split('(')[0] === valB.split('(')[0]) {
        const genCompare = sortDirection === 'asc'
          ? parseInt(matchAPrimary[1]) - parseInt(matchBPrimary[1])
          : parseInt(matchBPrimary[1]) - parseInt(matchAPrimary[1]);
        return genCompare;
      }

      // Regular string comparison
      const stringCompare = sortDirection === 'asc'
        ? valA.localeCompare(valB)
        : valB.localeCompare(valA);

      // If primary strings are equal, use secondary sort
      if (stringCompare === 0) {
        const secondaryValA = a[secondarySortField];
        const secondaryValB = b[secondarySortField];

        if (typeof secondaryValA === 'string' && typeof secondaryValB === 'string') {
          // First check for model numbers like "iPhone 7" vs "iPhone 11"
          const secBaseNameA = secondaryValA.split(/\d/)[0]?.trim();
          const secBaseNameB = secondaryValB.split(/\d/)[0]?.trim();

          if (secBaseNameA === secBaseNameB) {
            // Extract numbers from the model names
            const numbersA = secondaryValA.match(/\d+/g) || [];
            const numbersB = secondaryValB.match(/\d+/g) || [];

            if (numbersA.length > 0 && numbersB.length > 0) {
              // Compare the first number found
              return parseInt(numbersA[0]) - parseInt(numbersB[0]);
            }
          }

          // Then check for generation numbers
          const genRegex = /\((\d+)(?:st|nd|rd|th) Gen\)/;
          const matchA = secondaryValA.match(genRegex);
          const matchB = secondaryValB.match(genRegex);

          if (matchA && matchB) {
            return parseInt(matchA[1]) - parseInt(matchB[1]);
          }

          return secondaryValA.localeCompare(secondaryValB);
        }
        return Number(secondaryValA) - Number(secondaryValB);
      }
      return stringCompare;
    }

    // Handle numbers for primary sort
    const numberCompare = sortDirection === 'asc'
      ? Number(valA) - Number(valB)
      : Number(valB) - Number(valA);

    // If primary numbers are equal, use secondary sort
    if (numberCompare === 0) {
      const secondaryValA = a[secondarySortField];
      const secondaryValB = b[secondarySortField];

      if (typeof secondaryValA === 'string' && typeof secondaryValB === 'string') {
        // First check for model numbers
        const secBaseNameA = secondaryValA.split(/\d/)[0]?.trim();
        const secBaseNameB = secondaryValB.split(/\d/)[0]?.trim();

        if (secBaseNameA === secBaseNameB) {
          // Extract numbers from the model names
          const numbersA = secondaryValA.match(/\d+/g) || [];
          const numbersB = secondaryValB.match(/\d+/g) || [];

          if (numbersA.length > 0 && numbersB.length > 0) {
            // Compare the first number found
            return parseInt(numbersA[0]) - parseInt(numbersB[0]);
          }
        }

        // Then check for generation numbers
        const genRegex = /\((\d+)(?:st|nd|rd|th) Gen\)/;
        const matchA = secondaryValA.match(genRegex);
        const matchB = secondaryValB.match(genRegex);

        if (matchA && matchB) {
          return parseInt(matchA[1]) - parseInt(matchB[1]);
        }

        return secondaryValA.localeCompare(secondaryValB);
      }
      return Number(secondaryValA) - Number(secondaryValB);
    }
    return numberCompare;
  });

  // Pagination
  const totalPages = Math.ceil(sortedItems.length / itemsPerPage);
  const paginatedItems = sortedItems.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // Handle sort toggle
  const handleSort = (field: keyof InventoryItem) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const getUpdatedStatusColor = (date: string) => {
    const lastUpdated = new Date(date);
    const now = new Date();
    const diffDays = Math.floor((now.getTime() - lastUpdated.getTime()) / (1000 * 60 * 60 * 24));

    if (diffDays <= 40) return 'success';
    if (diffDays <= 90) return 'warning';
    return 'destructive';
  };

  const handleAddSuccess = () => {
    fetchInventory();
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (value: string) => {
    setItemsPerPage(parseInt(value));
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  // Extract unique values for filters
  const uniqueCategories = [...new Set(items.map(item => item.category))];
  const uniqueBrands = [...new Set(items.map(item => item.brand).filter(brand => brand))];

  if (loading && items.length === 0) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
          <p>Loading Pricing Database...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Alert
        className="mb-4 border-yellow-500 bg-yellow-500/20 text-yellow-700 dark:text-yellow-400"
      >
        <AlertTriangle className="h-4 w-4 text-yellow-500" />
        <AlertDescription>
          <strong>Disclaimer:</strong> The prices, calculators, and information given are solely for guidance - You must still follow the 11 Buys steps. Walkers should be approved by the Shift Supervisor and logged on the Walker's Record Sheet.
        </AlertDescription>
      </Alert>

      <Card>
        <CardContent className="p-0">
          {/* Search and filters inside the table card */}
          <div className="p-4 border-b">
            <div className="grid grid-cols-1 md:grid-cols-12 gap-4 items-center">
              {/* Search bar takes 4 columns */}
              <div className="relative md:col-span-4">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search items..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-9 transition-all duration-200 border-primary/20 focus:border-primary hover:border-primary/50 h-10"
                />
              </div>

              {/* Filters take 5 columns, arranged horizontally */}
              <div className="md:col-span-5 grid grid-cols-1 md:grid-cols-2 gap-4">
                <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                  <SelectTrigger className="transition-all duration-200 border-primary/20 focus:border-primary hover:border-primary/50 h-10">
                    <SelectValue placeholder="Filter by category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {uniqueCategories.map(category => (
                      <SelectItem key={category} value={category}>{category}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={brandFilter} onValueChange={setBrandFilter}>
                  <SelectTrigger className="transition-all duration-200 border-primary/20 focus:border-primary hover:border-primary/50 h-10">
                    <SelectValue placeholder="Filter by brand" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Brands</SelectItem>
                    {uniqueBrands.map(brand => (
                      <SelectItem key={brand} value={brand}>{brand}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Buttons take 3 columns */}
              <div className="md:col-span-3 flex items-center justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchTerm('');
                    setCategoryFilter('all');
                    setBrandFilter('all');
                  }}
                >
                  Reset
                </Button>

                {canAddItems && (
                  <Button
                  variant="outline"
                  onClick={() => setFormOpen(true)}>
                    <PlusCircle className="h-4 w-4 mr-2" />
                    Add New Item
                  </Button>
                )}
              </div>
            </div>
          </div>

          <div className="rounded-md">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead onClick={() => handleSort('brand')} className="cursor-pointer">
                    Brand
                    {sortField === 'brand' && (
                      <ArrowUpDown className={`ml-2 h-4 w-4 inline ${sortDirection === 'desc' ? 'rotate-180' : ''}`} />
                    )}
                  </TableHead>
                  <TableHead onClick={() => handleSort('model')} className="cursor-pointer">
                    Model Name
                    {sortField === 'model' && (
                      <ArrowUpDown className={`ml-2 h-4 w-4 inline ${sortDirection === 'desc' ? 'rotate-180' : ''}`} />
                    )}
                  </TableHead>
                  <TableHead onClick={() => handleSort('modelNumber')} className="cursor-pointer">
                    Model Number
                    {sortField === 'modelNumber' && (
                      <ArrowUpDown className={`ml-2 h-4 w-4 inline ${sortDirection === 'desc' ? 'rotate-180' : ''}`} />
                    )}
                  </TableHead>
                  <TableHead onClick={() => handleSort('category')} className="cursor-pointer">
                    Category
                    {sortField === 'category' && (
                      <ArrowUpDown className={`ml-2 h-4 w-4 inline ${sortDirection === 'desc' ? 'rotate-180' : ''}`} />
                    )}
                  </TableHead>
                  <TableHead onClick={() => handleSort('releaseYear')} className="cursor-pointer">
                    Year
                    {sortField === 'releaseYear' && (
                      <ArrowUpDown className={`ml-2 h-4 w-4 inline ${sortDirection === 'desc' ? 'rotate-180' : ''}`} />
                    )}
                  </TableHead>
                  <TableHead onClick={() => handleSort('lastRRP')} className="cursor-pointer text-right">
                    Last RRP
                    {sortField === 'lastRRP' && (
                      <ArrowUpDown className={`ml-2 h-4 w-4 inline ${sortDirection === 'desc' ? 'rotate-180' : ''}`} />
                    )}
                  </TableHead>
                  <TableHead onClick={() => handleSort('currentPrice')} className="cursor-pointer text-right">
                    Sell Price
                    {sortField === 'currentPrice' && (
                      <ArrowUpDown className={`ml-2 h-4 w-4 inline ${sortDirection === 'desc' ? 'rotate-180' : ''}`} />
                    )}
                  </TableHead>
                  <TableHead onClick={() => handleSort('lastUpdated')} className="cursor-pointer">
                    Last Updated
                    {sortField === 'lastUpdated' && (
                      <ArrowUpDown className={`ml-2 h-4 w-4 inline ${sortDirection === 'desc' ? 'rotate-180' : ''}`} />
                    )}
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedItems.map((item) => {
                  const updatedStatusColor = getUpdatedStatusColor(item.lastUpdated);

                  return (
                    <TableRow
                      key={item._id}
                      onClick={() => handleRowClick(item._id)}
                      className="cursor-pointer hover:bg-primary/5"
                    >
                      <TableCell>{item.brand || 'N/A'}</TableCell>
                      <TableCell>{item.model || 'N/A'}</TableCell>
                      <TableCell>{item.modelNumber || 'N/A'}</TableCell>
                      <TableCell>{item.category}</TableCell>
                      <TableCell>{item.releaseYear || 'N/A'}</TableCell>
                      <TableCell className="text-right">
                        {item.lastRRP ? `$${item.lastRRP.toFixed(2)}` : 'N/A'}
                      </TableCell>
                      <TableCell className="text-right font-medium">
                        ${item.currentPrice.toFixed(2)}
                      </TableCell>
                      <TableCell>
                        <Badge variant={updatedStatusColor}>
                          {new Date(item.lastUpdated).toLocaleDateString()}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>

          <div className="flex items-center justify-between px-6 py-4 border-t">
            <div className="flex items-center text-sm text-muted-foreground">
              <p>Showing {(currentPage - 1) * itemsPerPage + 1}-{Math.min(currentPage * itemsPerPage, filteredItems.length)} of {filteredItems.length} items</p>
            </div>

            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <span className="text-sm whitespace-nowrap">
                  Page {currentPage} of {totalPages}
                </span>

                <div className="flex items-center">
                  <Button
                    variant="outline"
                    className="h-8 w-8 p-0"
                    onClick={() => handlePageChange(1)}
                    disabled={currentPage === 1}
                  >
                    <span className="sr-only">Go to first page</span>
                    <ChevronFirst className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    className="h-8 w-8 p-0"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                  >
                    <span className="sr-only">Go to previous page</span>
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    className="h-8 w-8 p-0"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                  >
                    <span className="sr-only">Go to next page</span>
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    className="h-8 w-8 p-0"
                    onClick={() => handlePageChange(totalPages)}
                    disabled={currentPage === totalPages}
                  >
                    <span className="sr-only">Go to last page</span>
                    <ChevronLast className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <p className="text-sm whitespace-nowrap">Items per page</p>
                <Select
                  value={itemsPerPage.toString()}
                  onValueChange={handleItemsPerPageChange}
                >
                  <SelectTrigger className="h-8 w-[70px]">
                    <SelectValue placeholder={itemsPerPage.toString()} />
                  </SelectTrigger>
                  <SelectContent>
                    {[15, 20, 25, 50, 100].map((size) => (
                      <SelectItem key={size} value={size.toString()}>
                        {size}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <InventoryItemForm
        open={formOpen}
        onOpenChange={setFormOpen}
        onSuccess={handleAddSuccess}
      />

      <InventoryItemModal
        open={itemModalOpen}
        onOpenChange={setItemModalOpen}
        itemId={selectedItemId}
        onItemDeleted={fetchInventory}
      />
    </div>
  );
}