/**
 * TradeMe Utilities
 *
 * Shared utilities for TradeMe API integration
 */

const OAuth = require('oauth-1.0a');
const crypto = require('crypto');

// TradeMe API endpoints
const TRADEME_API = {
  production: {
    apiBase: 'https://api.trademe.co.nz/v1',
    authorizeUrl: 'https://secure.trademe.co.nz/Oauth/Authorize',
    requestTokenUrl: 'https://secure.trademe.co.nz/Oauth/RequestToken',
    accessTokenUrl: 'https://secure.trademe.co.nz/Oauth/AccessToken'
  },
  sandbox: {
    apiBase: 'https://api.tmsandbox.co.nz/v1',
    authorizeUrl: 'https://secure.tmsandbox.co.nz/Oauth/Authorize',
    requestTokenUrl: 'https://secure.tmsandbox.co.nz/Oauth/RequestToken',
    accessTokenUrl: 'https://secure.tmsandbox.co.nz/Oauth/AccessToken'
  }
};

/**
 * Create an OAuth instance for TradeMe API
 * @param {string} environment - 'production' or 'sandbox'
 * @returns {Object} OAuth instance
 */
function getOAuth(environment) {
  // Use the appropriate keys based on the environment
  const consumerKey = environment === 'sandbox'
    ? process.env.TRADEME_SANDBOX_CONSUMER_KEY
    : process.env.TRADEME_CONSUMER_KEY;
  const consumerSecret = environment === 'sandbox'
    ? process.env.TRADEME_SANDBOX_CONSUMER_SECRET
    : process.env.TRADEME_CONSUMER_SECRET;

  // Create OAuth instance with the appropriate signature method
  // TradeMe API documentation specifies PLAINTEXT for request token and access token requests
  // and HMAC-SHA1 for API requests
  return OAuth({
    consumer: {
      key: consumerKey,
      secret: consumerSecret
    },
    signature_method: 'PLAINTEXT',
    hash_function(base_string, key) {
      // For PLAINTEXT, the signature is just the key (consumer_secret&token_secret)
      // For request token requests, it's consumer_secret&
      // For access token requests, it's consumer_secret&request_token_secret
      if (this.signature_method === 'PLAINTEXT') {
        return key;
      }
      // For HMAC-SHA1, use the standard hash function
      return crypto
        .createHmac('sha1', key)
        .update(base_string)
        .digest('base64');
    }
  });
}

/**
 * Parse TradeMe date format
 * @param {string} dateString - TradeMe date string in format '/Date(1514764800)/'
 * @returns {Date|null} JavaScript Date object or null if invalid
 */
function parseTradeMeDate(dateString) {
  if (!dateString) return null;

  try {
    // Extract the timestamp from the string
    const timestamp = dateString.match(/\/Date\((\d+)\)\//);
    if (timestamp && timestamp[1]) {
      // Convert to milliseconds and create a Date object
      return new Date(parseInt(timestamp[1]));
    }

    // If the format doesn't match, try parsing as a regular date string
    return new Date(dateString);
  } catch (error) {
    console.error('Error parsing TradeMe date:', error);
    return null;
  }
}

/**
 * Get the request token URL for the specified environment
 * @param {string} environment - 'production' or 'sandbox'
 * @returns {string} Request token URL
 */
function getRequestTokenUrl(environment) {
  if (environment !== 'production' && environment !== 'sandbox') {
    throw new Error('Invalid environment. Must be "production" or "sandbox"');
  }
  return TRADEME_API[environment].requestTokenUrl;
}

/**
 * Get the authorization URL for the specified environment
 * @param {string} environment - 'production' or 'sandbox'
 * @param {string} requestToken - OAuth request token
 * @returns {string} Authorization URL
 */
function getAuthorizationUrl(environment, requestToken) {
  if (environment !== 'production' && environment !== 'sandbox') {
    throw new Error('Invalid environment. Must be "production" or "sandbox"');
  }
  return `${TRADEME_API[environment].authorizeUrl}?oauth_token=${requestToken}`;
}

/**
 * Get the access token URL for the specified environment
 * @param {string} environment - 'production' or 'sandbox'
 * @returns {string} Access token URL
 */
function getAccessTokenUrl(environment) {
  if (environment !== 'production' && environment !== 'sandbox') {
    throw new Error('Invalid environment. Must be "production" or "sandbox"');
  }
  return TRADEME_API[environment].accessTokenUrl;
}

module.exports = {
  TRADEME_API,
  getOAuth,
  parseTradeMeDate,
  getRequestTokenUrl,
  getAuthorizationUrl,
  getAccessTokenUrl
};
