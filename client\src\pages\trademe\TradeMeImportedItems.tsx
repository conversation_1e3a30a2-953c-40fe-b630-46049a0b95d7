import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, AlertCircle, ArrowLeft, Pencil, RefreshCw } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Pagination } from '@/components/CustomPagination';
// Tabs component is not used in this file but may be needed for future enhancements
import { Dialog, DialogContent, Di<PERSON>D<PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON><PERSON>er, Di<PERSON>Title } from '@/components/ui/dialog';
import { getImportedItems, updateImportedItem, batchUpdateImportedItems } from '@/api/tradeMeImport';
import { formatCurrency, formatDate } from '@/lib/utils';
import { toast } from 'sonner';

interface ImportedItem {
  _id: string;
  title: string;
  stockCode: string;
  cost: number;
  currentPrice: number;
  buyNowPrice: number;
  status: string;
  location: string;
  locationId: string;
  isFaulty: boolean;
  faultCategories: string[];
  faultDescription: string;
  importedAt: string;
  importedBy: {
    _id: string;
    username: string;
    fullName: string;
  };
  images: string[];
  currentListingId: string;
}

interface Location {
  _id: string;
  name: string;
}

const TradeMeImportedItems: React.FC = () => {
  // Get user for permission check
  const { user } = useAuth();

  // Check if user has permission to access this page
  const hasPermission = user?.role === 'admin' || user?.role === 'manager';
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [items, setItems] = useState<ImportedItem[]>([]);
  const [locations, setLocations] = useState<Location[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 1
  });
  const [filters, setFilters] = useState({
    status: 'all',
    stockCode: '',
    title: '',
    location: ''
  });
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [editItem, setEditItem] = useState<ImportedItem | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isBatchEditDialogOpen, setIsBatchEditDialogOpen] = useState(false);
  const [batchEditData, setBatchEditData] = useState({
    locationId: '',
    isFaulty: false,
    faultCategories: [] as string[]
  });
  const [isUpdating, setIsUpdating] = useState(false);

  // Fault categories
  const faultCategoryOptions = [
    { id: 'screen', label: 'Screen' },
    { id: 'battery', label: 'Battery' },
    { id: 'camera', label: 'Camera' },
    { id: 'charging', label: 'Charging' },
    { id: 'buttons', label: 'Buttons' },
    { id: 'audio', label: 'Audio' },
    { id: 'water_damage', label: 'Water Damage' },
    { id: 'cosmetic', label: 'Cosmetic' },
    { id: 'other', label: 'Other' }
  ];

  useEffect(() => {
    fetchItems();
  }, [pagination.page, pagination.limit, filters]);

  const fetchItems = async () => {
    try {
      setIsLoading(true);
      const result = await getImportedItems(pagination.page, pagination.limit, filters);

      if (result.success) {
        setItems(result.items);
        setLocations(result.locations);
        setPagination(result.pagination);
      } else {
        setError(result.error || 'Failed to load imported items');
      }
    } catch (error: any) {
      console.error('Error loading imported items:', error);
      setError(error.message || 'Failed to load imported items');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPagination(prev => ({ ...prev, page: 1 })); // Reset to first page when filter changes
  };

  const resetFilters = () => {
    setFilters({
      status: 'all',
      stockCode: '',
      title: '',
      location: ''
    });
  };

  const handleSelectItem = (id: string) => {
    setSelectedItems(prev => {
      if (prev.includes(id)) {
        return prev.filter(itemId => itemId !== id);
      } else {
        return [...prev, id];
      }
    });
  };

  const handleSelectAll = () => {
    if (selectedItems.length === items.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(items.map(item => item._id));
    }
  };

  const openEditDialog = (item: ImportedItem) => {
    setEditItem(item);
    setIsEditDialogOpen(true);
  };

  const closeEditDialog = () => {
    setEditItem(null);
    setIsEditDialogOpen(false);
  };

  const openBatchEditDialog = () => {
    if (selectedItems.length === 0) {
      toast.error('Please select at least one item to edit');
      return;
    }
    setIsBatchEditDialogOpen(true);
  };

  const closeBatchEditDialog = () => {
    setIsBatchEditDialogOpen(false);
    setBatchEditData({
      locationId: '',
      isFaulty: false,
      faultCategories: []
    });
  };

  const handleUpdateItem = async () => {
    if (!editItem) return;

    try {
      setIsUpdating(true);
      const result = await updateImportedItem(editItem._id, editItem);

      if (result.success) {
        toast.success('Item updated successfully');
        setItems(prev => prev.map(item => item._id === editItem._id ? result.item : item));
        closeEditDialog();
      } else {
        toast.error(result.error || 'Failed to update item');
      }
    } catch (error: any) {
      console.error('Error updating item:', error);
      toast.error(error.message || 'Failed to update item');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleBatchUpdate = async () => {
    if (selectedItems.length === 0) return;

    try {
      setIsUpdating(true);

      // Only include fields that have values
      const updateData: any = {};
      if (batchEditData.locationId && batchEditData.locationId !== 'none') {
        updateData.locationId = batchEditData.locationId;
      }
      updateData.isFaulty = batchEditData.isFaulty;
      if (batchEditData.faultCategories.length > 0) updateData.faultCategories = batchEditData.faultCategories;

      const result = await batchUpdateImportedItems(selectedItems, updateData);

      if (result.success) {
        toast.success(`Updated ${result.modifiedCount} items successfully`);
        fetchItems(); // Refresh the list
        closeBatchEditDialog();
        setSelectedItems([]);
      } else {
        toast.error(result.error || 'Failed to update items');
      }
    } catch (error: any) {
      console.error('Error batch updating items:', error);
      toast.error(error.message || 'Failed to update items');
    } finally {
      setIsUpdating(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'draft':
        return <Badge variant="outline">Draft</Badge>;
      case 'queued':
        return <Badge variant="secondary">Queued</Badge>;
      case 'active':
        return <Badge variant="default">Active</Badge>;
      case 'sold':
        return <Badge variant="success">Sold</Badge>;
      case 'archived':
        return <Badge variant="outline">Archived</Badge>;
      case 'removed':
        return <Badge variant="destructive">Removed</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const handleFaultCategoryToggle = (categoryId: string) => {
    if (!editItem) return;

    setEditItem(prev => {
      if (!prev) return prev;

      const updatedCategories = prev.faultCategories.includes(categoryId)
        ? prev.faultCategories.filter(id => id !== categoryId)
        : [...prev.faultCategories, categoryId];

      return {
        ...prev,
        faultCategories: updatedCategories
      };
    });
  };

  const handleBatchFaultCategoryToggle = (categoryId: string) => {
    setBatchEditData(prev => {
      const updatedCategories = prev.faultCategories.includes(categoryId)
        ? prev.faultCategories.filter(id => id !== categoryId)
        : [...prev.faultCategories, categoryId];

      return {
        ...prev,
        faultCategories: updatedCategories
      };
    });
  };

  // Check if user has permission to access this page
  if (!hasPermission) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Imported Items</CardTitle>
          <CardDescription>Manage your imported TradeMe items</CardDescription>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Access Denied</AlertTitle>
            <AlertDescription>
              You do not have permission to access this page.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Imported Items</CardTitle>
          <CardDescription>Manage your imported TradeMe items</CardDescription>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm" onClick={() => navigate('/trademe/import')}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Import
          </Button>
          {selectedItems.length > 0 && (
            <Button variant="default" size="sm" onClick={openBatchEditDialog}>
              <Pencil className="mr-2 h-4 w-4" />
              Edit Selected ({selectedItems.length})
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {isLoading && items.length === 0 ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        ) : error ? (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        ) : (
          <div className="space-y-4">
            {/* Filters */}
            <div className="bg-muted/50 p-4 rounded-lg">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-sm font-medium">Filters</h3>
                <Button variant="ghost" size="sm" onClick={resetFilters}>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Reset
                </Button>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="filter-status">Status</Label>
                  <Select
                    value={filters.status}
                    onValueChange={(value) => handleFilterChange('status', value)}
                  >
                    <SelectTrigger id="filter-status">
                      <SelectValue placeholder="All Statuses" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="queued">Queued</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="sold">Sold</SelectItem>
                      <SelectItem value="archived">Archived</SelectItem>
                      <SelectItem value="removed">Removed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="filter-stockCode">Stock Code</Label>
                  <Input
                    id="filter-stockCode"
                    placeholder="Search by stock code"
                    value={filters.stockCode}
                    onChange={(e) => handleFilterChange('stockCode', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="filter-title">Title</Label>
                  <Input
                    id="filter-title"
                    placeholder="Search by title"
                    value={filters.title}
                    onChange={(e) => handleFilterChange('title', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="filter-location">Location</Label>
                  <Input
                    id="filter-location"
                    placeholder="Search by location"
                    value={filters.location}
                    onChange={(e) => handleFilterChange('location', e.target.value)}
                  />
                </div>
              </div>
            </div>

            {/* Items Table */}
            {items.length === 0 ? (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>No Items Found</AlertTitle>
                <AlertDescription>
                  {filters.status !== 'all' || filters.stockCode || filters.title || filters.location ? (
                    <>No imported items match your filters. Try adjusting your search criteria or import new items.</>
                  ) : (
                    <>No items have been imported yet. Go to the Import page to import your TradeMe listings.</>
                  )}
                </AlertDescription>
              </Alert>
            ) : (
              <div className="border rounded-md overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-muted">
                      <tr>
                        <th className="w-10 px-4 py-2">
                          <Checkbox
                            checked={selectedItems.length === items.length && items.length > 0}
                            onCheckedChange={handleSelectAll}
                            aria-label="Select all items"
                          />
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground">Image</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground">Title</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground">Stock Code</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground">Price</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground">Status</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground">Location</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground">Imported</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y">
                      {items.map((item) => (
                        <tr key={item._id} className="hover:bg-muted/50">
                          <td className="px-4 py-2">
                            <Checkbox
                              checked={selectedItems.includes(item._id)}
                              onCheckedChange={() => handleSelectItem(item._id)}
                              aria-label={`Select ${item.title}`}
                            />
                          </td>
                          <td className="px-4 py-2">
                            {item.images && item.images.length > 0 ? (
                              <img
                                src={item.images[0]}
                                alt={item.title}
                                className="w-12 h-12 object-cover rounded-md"
                              />
                            ) : (
                              <div className="w-12 h-12 bg-muted rounded-md flex items-center justify-center">
                                <span className="text-xs text-muted-foreground">No image</span>
                              </div>
                            )}
                          </td>
                          <td className="px-4 py-2 text-sm">
                            <div className="max-w-xs truncate">{item.title}</div>
                          </td>
                          <td className="px-4 py-2 text-sm">
                            {item.stockCode || <span className="text-muted-foreground italic">Not set</span>}
                          </td>
                          <td className="px-4 py-2 text-sm">
                            {formatCurrency(item.currentPrice)}
                            {item.buyNowPrice > 0 && (
                              <div className="text-xs text-muted-foreground">
                                Buy Now: {formatCurrency(item.buyNowPrice)}
                              </div>
                            )}
                          </td>
                          <td className="px-4 py-2">{getStatusBadge(item.status)}</td>
                          <td className="px-4 py-2 text-sm">
                            {item.location || <span className="text-muted-foreground italic">Not set</span>}
                          </td>
                          <td className="px-4 py-2 text-sm">
                            <div>{formatDate(item.importedAt)}</div>
                            <div className="text-xs text-muted-foreground">
                              by {item.importedBy?.fullName || item.importedBy?.username || 'Unknown'}
                            </div>
                          </td>
                          <td className="px-4 py-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => openEditDialog(item)}
                            >
                              <Pencil className="h-4 w-4" />
                              <span className="sr-only">Edit</span>
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* Pagination */}
            {pagination.pages > 1 && (
              <div className="flex justify-center mt-4">
                <Pagination
                  currentPage={pagination.page}
                  totalPages={pagination.pages}
                  onPageChange={handlePageChange}
                />
              </div>
            )}

            {/* Edit Item Dialog */}
            <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Edit Imported Item</DialogTitle>
                  <DialogDescription>
                    Update the details for this imported TradeMe item.
                  </DialogDescription>
                </DialogHeader>

                {editItem && (
                  <div className="grid gap-6 py-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="stockCode">Stock Code</Label>
                        <Input
                          id="stockCode"
                          value={editItem.stockCode}
                          onChange={(e) => setEditItem({ ...editItem, stockCode: e.target.value })}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="cost">Cost</Label>
                        <Input
                          id="cost"
                          type="number"
                          min="0"
                          step="0.01"
                          value={editItem.cost}
                          onChange={(e) => setEditItem({ ...editItem, cost: parseFloat(e.target.value) || 0 })}
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="location">Location</Label>
                        <Input
                          id="location"
                          value={editItem.location}
                          onChange={(e) => setEditItem({ ...editItem, location: e.target.value })}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="locationId">Location (Predefined)</Label>
                        <Select
                          value={editItem.locationId}
                          onValueChange={(value) => setEditItem({ ...editItem, locationId: value })}
                        >
                          <SelectTrigger id="locationId">
                            <SelectValue placeholder="Select a location" />
                          </SelectTrigger>
                          <SelectContent>
                            {locations.map((location) => (
                              <SelectItem key={location._id} value={location._id}>
                                {location.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="isFaulty"
                          checked={editItem.isFaulty}
                          onCheckedChange={(checked) => setEditItem({ ...editItem, isFaulty: !!checked })}
                        />
                        <Label htmlFor="isFaulty">Item is faulty</Label>
                      </div>
                    </div>

                    {editItem.isFaulty && (
                      <>
                        <div className="space-y-2">
                          <Label>Fault Categories</Label>
                          <div className="grid grid-cols-3 gap-2">
                            {faultCategoryOptions.map((category) => (
                              <div key={category.id} className="flex items-center space-x-2">
                                <Checkbox
                                  id={`fault-${category.id}`}
                                  checked={editItem.faultCategories.includes(category.id)}
                                  onCheckedChange={() => handleFaultCategoryToggle(category.id)}
                                />
                                <Label htmlFor={`fault-${category.id}`}>{category.label}</Label>
                              </div>
                            ))}
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="faultDescription">Fault Description</Label>
                          <Input
                            id="faultDescription"
                            value={editItem.faultDescription || ''}
                            onChange={(e) => setEditItem({ ...editItem, faultDescription: e.target.value })}
                          />
                        </div>
                      </>
                    )}
                  </div>
                )}

                <DialogFooter>
                  <Button variant="outline" onClick={closeEditDialog}>Cancel</Button>
                  <Button onClick={handleUpdateItem} disabled={isUpdating}>
                    {isUpdating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    Save Changes
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            {/* Batch Edit Dialog */}
            <Dialog open={isBatchEditDialogOpen} onOpenChange={setIsBatchEditDialogOpen}>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Batch Edit Items</DialogTitle>
                  <DialogDescription>
                    Update multiple items at once. These changes will apply to all {selectedItems.length} selected items.
                  </DialogDescription>
                </DialogHeader>

                <div className="grid gap-6 py-4">
                  <div className="space-y-2">
                    <Label htmlFor="batchLocationId">Location</Label>
                    <Select
                      value={batchEditData.locationId}
                      onValueChange={(value) => setBatchEditData({ ...batchEditData, locationId: value })}
                    >
                      <SelectTrigger id="batchLocationId">
                        <SelectValue placeholder="Select a location" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">No change</SelectItem>
                        {locations.map((location) => (
                          <SelectItem key={location._id} value={location._id}>
                            {location.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="batchIsFaulty"
                        checked={batchEditData.isFaulty}
                        onCheckedChange={(checked) => setBatchEditData({ ...batchEditData, isFaulty: !!checked })}
                      />
                      <Label htmlFor="batchIsFaulty">Items are faulty</Label>
                    </div>
                  </div>

                  {batchEditData.isFaulty && (
                    <div className="space-y-2">
                      <Label>Fault Categories</Label>
                      <div className="grid grid-cols-3 gap-2">
                        {faultCategoryOptions.map((category) => (
                          <div key={category.id} className="flex items-center space-x-2">
                            <Checkbox
                              id={`batch-fault-${category.id}`}
                              checked={batchEditData.faultCategories.includes(category.id)}
                              onCheckedChange={() => handleBatchFaultCategoryToggle(category.id)}
                            />
                            <Label htmlFor={`batch-fault-${category.id}`}>{category.label}</Label>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                <DialogFooter>
                  <Button variant="outline" onClick={closeBatchEditDialog}>Cancel</Button>
                  <Button onClick={handleBatchUpdate} disabled={isUpdating}>
                    {isUpdating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    Update {selectedItems.length} Items
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default TradeMeImportedItems;
