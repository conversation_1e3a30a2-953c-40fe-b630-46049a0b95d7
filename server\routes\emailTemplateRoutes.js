const express = require('express');
const router = express.Router();
const EmailTemplate = require('../models/EmailTemplate');
const { requireUser } = require('./middleware/auth');

/**
 * @route GET /api/email-templates
 * @description Get all email templates
 * @access Private (All authenticated users)
 */
router.get('/', requireUser, async (req, res) => {
  try {
    const { category } = req.query;

    // Build query
    const query = {};
    if (category) {
      query.category = category;
    }

    // Fetch templates with populated creator info
    const templates = await EmailTemplate.find(query)
      .populate('createdBy', 'username email fullName')
      .populate('updatedBy', 'username email fullName')
      .sort({ updatedAt: -1 });

    return res.status(200).json({
      success: true,
      templates
    });
  } catch (error) {
    console.error('Error fetching email templates:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route POST /api/email-templates
 * @description Create a new email template
 * @access Private (All authenticated users)
 */
router.post('/', requireUser, async (req, res) => {
  try {
    const { title, subject, body, category } = req.body;

    if (!title || !subject || !body) {
      return res.status(400).json({
        success: false,
        error: 'Title, subject, and body are required'
      });
    }

    const template = new EmailTemplate({
      title,
      subject,
      body,
      category: category || 'Other',
      createdBy: req.user._id,
      updatedBy: req.user._id
    });

    await template.save();

    // Populate user info before returning
    await template.populate('createdBy', 'username email fullName');
    await template.populate('updatedBy', 'username email fullName');

    return res.status(201).json({
      success: true,
      template,
      message: 'Email template created successfully'
    });
  } catch (error) {
    console.error('Error creating email template:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route PUT /api/email-templates/:id
 * @description Update an email template
 * @access Private (Creator or Admin/Manager)
 */
router.put('/:id', requireUser, async (req, res) => {
  try {
    const { id } = req.params;
    const { title, subject, body, category } = req.body;

    // Find the template
    const template = await EmailTemplate.findById(id);

    if (!template) {
      return res.status(404).json({
        success: false,
        error: 'Template not found'
      });
    }

    // Check permissions - only creator or admin/manager can edit
    const isCreator = template.createdBy.toString() === req.user._id.toString();
    const isAdminOrManager = ['admin', 'manager'].includes(req.user.role);

    if (!isCreator && !isAdminOrManager) {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to edit this template'
      });
    }

    // Update the template
    if (title) template.title = title;
    if (subject) template.subject = subject;
    if (body) template.body = body;
    if (category) template.category = category;
    template.updatedAt = Date.now();
    template.updatedBy = req.user._id;

    await template.save();

    // Populate user info before returning
    await template.populate('createdBy', 'username email fullName');
    await template.populate('updatedBy', 'username email fullName');

    return res.status(200).json({
      success: true,
      template,
      message: 'Email template updated successfully'
    });
  } catch (error) {
    console.error('Error updating email template:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route DELETE /api/email-templates/:id
 * @description Delete an email template
 * @access Private (Creator or Admin/Manager)
 */
router.delete('/:id', requireUser, async (req, res) => {
  try {
    const { id } = req.params;

    // Find the template
    const template = await EmailTemplate.findById(id);

    if (!template) {
      return res.status(404).json({
        success: false,
        error: 'Template not found'
      });
    }

    // Check permissions - only creator or admin/manager can delete
    const isCreator = template.createdBy.toString() === req.user._id.toString();
    const isAdminOrManager = ['admin', 'manager'].includes(req.user.role);

    if (!isCreator && !isAdminOrManager) {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to delete this template'
      });
    }

    // Delete the template
    await EmailTemplate.findByIdAndDelete(id);

    return res.status(200).json({
      success: true,
      message: 'Email template deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting email template:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = router;
