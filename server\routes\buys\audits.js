const express = require('express');
const router = express.Router();
const { ObjectId } = require('mongodb');
const { getDb } = require('../../db');
const { authenticateToken } = require('../../middleware/auth');

/**
 * @route GET /api/buys/audits
 * @desc Get all audits with optional filtering
 * @access Private
 */
router.get('/', authenticateToken, async (req, res) => {
  try {
    const db = getDb();
    const { 
      type, 
      status, 
      employeeId, 
      fromDate, 
      toDate, 
      search,
      page = 1,
      limit = 10
    } = req.query;
    
    // Build query
    const query = {};
    
    if (type && type !== 'all') {
      query.auditType = type;
    }
    
    if (status && status !== 'all') {
      query['summary.overallAssessment'] = status;
    }
    
    if (employeeId) {
      query.employeeId = employeeId;
    }
    
    // Date range filter
    if (fromDate || toDate) {
      query.createdAt = {};
      
      if (fromDate) {
        query.createdAt.$gte = new Date(fromDate);
      }
      
      if (toDate) {
        query.createdAt.$lte = new Date(toDate);
      }
    }
    
    // Search by transaction ID or employee name
    if (search) {
      query.$or = [
        { transactionId: { $regex: search, $options: 'i' } },
        { employeeName: { $regex: search, $options: 'i' } }
      ];
    }
    
    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    // Get total count for pagination
    const total = await db.collection('audits').countDocuments(query);
    
    // Get audits with pagination
    const audits = await db
      .collection('audits')
      .find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .toArray();
    
    res.json({
      audits,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    console.error('Error fetching audits:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route GET /api/buys/audits/:id
 * @desc Get audit by ID
 * @access Private
 */
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const db = getDb();
    const audit = await db.collection('audits').findOne({ _id: new ObjectId(req.params.id) });
    
    if (!audit) {
      return res.status(404).json({ message: 'Audit not found' });
    }
    
    res.json(audit);
  } catch (error) {
    console.error('Error fetching audit:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route POST /api/buys/audits
 * @desc Create a new audit
 * @access Private
 */
router.post('/', authenticateToken, async (req, res) => {
  try {
    const db = getDb();
    
    // Add metadata
    const audit = {
      ...req.body,
      createdBy: req.user.id,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    const result = await db.collection('audits').insertOne(audit);
    
    res.status(201).json({
      id: result.insertedId,
      message: 'Audit created successfully'
    });
  } catch (error) {
    console.error('Error creating audit:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route PUT /api/buys/audits/:id
 * @desc Update an existing audit
 * @access Private
 */
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const db = getDb();
    
    // Check if audit exists
    const existingAudit = await db.collection('audits').findOne({ _id: new ObjectId(req.params.id) });
    
    if (!existingAudit) {
      return res.status(404).json({ message: 'Audit not found' });
    }
    
    // Update audit
    const updatedAudit = {
      ...req.body,
      updatedBy: req.user.id,
      updatedAt: new Date(),
      createdAt: existingAudit.createdAt,
      createdBy: existingAudit.createdBy
    };
    
    await db.collection('audits').updateOne(
      { _id: new ObjectId(req.params.id) },
      { $set: updatedAudit }
    );
    
    res.json({
      id: req.params.id,
      message: 'Audit updated successfully'
    });
  } catch (error) {
    console.error('Error updating audit:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route DELETE /api/buys/audits/:id
 * @desc Delete an audit
 * @access Private
 */
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const db = getDb();
    
    // Check if audit exists
    const existingAudit = await db.collection('audits').findOne({ _id: new ObjectId(req.params.id) });
    
    if (!existingAudit) {
      return res.status(404).json({ message: 'Audit not found' });
    }
    
    // Delete audit
    await db.collection('audits').deleteOne({ _id: new ObjectId(req.params.id) });
    
    res.json({ message: 'Audit deleted successfully' });
  } catch (error) {
    console.error('Error deleting audit:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route GET /api/buys/audits/stats/summary
 * @desc Get audit statistics summary
 * @access Private
 */
router.get('/stats/summary', authenticateToken, async (req, res) => {
  try {
    const db = getDb();
    
    // Get counts by type
    const typeStats = await db.collection('audits').aggregate([
      { $group: { _id: '$auditType', count: { $sum: 1 } } }
    ]).toArray();
    
    // Get counts by status
    const statusStats = await db.collection('audits').aggregate([
      { $group: { _id: '$summary.overallAssessment', count: { $sum: 1 } } }
    ]).toArray();
    
    // Get average scores
    const scoreStats = await db.collection('audits').aggregate([
      { 
        $group: { 
          _id: null, 
          averageScore: { $avg: { $toDouble: '$summary.score' } },
          count: { $sum: 1 }
        } 
      }
    ]).toArray();
    
    // Format the response
    const stats = {
      total: scoreStats.length > 0 ? scoreStats[0].count : 0,
      averageScore: scoreStats.length > 0 ? Math.round(scoreStats[0].averageScore * 10) / 10 : 0,
      byType: typeStats.reduce((acc, stat) => {
        acc[stat._id] = stat.count;
        return acc;
      }, {}),
      byStatus: statusStats.reduce((acc, stat) => {
        acc[stat._id] = stat.count;
        return acc;
      }, {})
    };
    
    res.json(stats);
  } catch (error) {
    console.error('Error fetching audit stats:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
