import { cn } from "@/lib/utils";

interface PasswordStrengthIndicatorProps {
  password: string;
}

export function PasswordStrengthIndicator({ password }: PasswordStrengthIndicatorProps) {
  // Calculate password strength
  const getPasswordStrength = (password: string): number => {
    if (!password) return 0;
    
    let strength = 0;
    
    // Length check
    if (password.length >= 6) strength += 1;
    if (password.length >= 8) strength += 1;
    
    // Character variety checks
    if (/[A-Z]/.test(password)) strength += 1; // Has uppercase
    if (/[a-z]/.test(password)) strength += 1; // Has lowercase
    if (/[0-9]/.test(password)) strength += 1; // Has number
    if (/[^A-Za-z0-9]/.test(password)) strength += 1; // Has special character
    
    // Normalize to 0-4 scale
    return Math.min(4, Math.floor(strength / 1.5));
  };
  
  const strength = getPasswordStrength(password);
  
  const getStrengthLabel = (strength: number): string => {
    if (strength === 0) return "Too weak";
    if (strength === 1) return "Weak";
    if (strength === 2) return "Fair";
    if (strength === 3) return "Good";
    return "Strong";
  };
  
  const getStrengthColor = (strength: number): string => {
    if (strength === 0) return "bg-red-500";
    if (strength === 1) return "bg-orange-500";
    if (strength === 2) return "bg-yellow-500";
    if (strength === 3) return "bg-green-500";
    return "bg-emerald-500";
  };
  
  return (
    <div className="space-y-2">
      <div className="flex justify-between items-center">
        <div className="flex space-x-1 h-1.5 w-full">
          {[...Array(4)].map((_, index) => (
            <div
              key={index}
              className={cn(
                "h-full flex-1 rounded-full transition-colors",
                index < strength ? getStrengthColor(strength) : "bg-gray-200 dark:bg-gray-700"
              )}
            />
          ))}
        </div>
        <span className="text-xs text-muted-foreground ml-2 min-w-16 text-right">
          {password ? getStrengthLabel(strength) : ""}
        </span>
      </div>
    </div>
  );
}
