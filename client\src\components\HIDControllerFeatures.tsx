import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { HIDDeviceInfo as HIDDeviceInfoType, DualSense, Xbox, SwitchPro } from '@/utils/hidUtils';
import { Gamepad, Vibrate, Lightbulb, Zap } from 'lucide-react';

interface HIDControllerFeaturesProps {
  hidDevice: HIDDeviceInfoType | null;
}

const HIDControllerFeatures = ({ hidDevice }: HIDControllerFeaturesProps) => {
  const [leftVibration, setLeftVibration] = useState(0);
  const [rightVibration, setRightVibration] = useState(0);
  const [lightR, setLightR] = useState(0);
  const [lightG, setLightG] = useState(0);
  const [lightB, setLightB] = useState(0);
  const [triggerMode, setTriggerMode] = useState(0);
  const [triggerStart, setTriggerStart] = useState(0);
  const [triggerForce, setTriggerForce] = useState(0);
  const [activeTab, setActiveTab] = useState('vibration');
  const [supportedFeatures, setSupportedFeatures] = useState({
    vibration: false,
    lightbar: false,
    triggers: false
  });

  useEffect(() => {
    if (hidDevice) {
      // Determine supported features based on device
      const vendorId = hidDevice.vendorId;
      const productId = hidDevice.productId;

      // Sony DualSense (PS5) controller
      if (vendorId === 0x054c && (productId === 0x0ce6 || productId === 0x0df2)) {
        setSupportedFeatures({
          vibration: true,
          lightbar: true,
          triggers: true
        });
      }
      // Sony DualShock 4 (PS4) controller
      else if (vendorId === 0x054c && productId === 0x09cc) {
        setSupportedFeatures({
          vibration: true,
          lightbar: true,
          triggers: false
        });
      }
      // Xbox controller
      else if (vendorId === 0x045e) {
        setSupportedFeatures({
          vibration: true,
          lightbar: false,
          triggers: false
        });
      }
      // Nintendo Switch Pro controller
      else if (vendorId === 0x057e && productId === 0x2009) {
        setSupportedFeatures({
          vibration: true,
          lightbar: false,
          triggers: false
        });
      }
      // Nintendo Switch Joy-Con
      else if (vendorId === 0x057e && (productId === 0x2006 || productId === 0x2007)) {
        setSupportedFeatures({
          vibration: true,
          lightbar: false,
          triggers: false
        });
      }
      // Default to no supported features
      else {
        setSupportedFeatures({
          vibration: false,
          lightbar: false,
          triggers: false
        });
      }
    }
  }, [hidDevice]);

  const handleVibrationTest = async () => {
    if (!hidDevice) return;

    // Apply vibration based on controller type
    const vendorId = hidDevice.vendorId;
    const productId = hidDevice.productId;

    // Sony controllers (DualSense, DualShock 4)
    if (vendorId === 0x054c) {
      // For DualSense, use the combined state function for better reliability
      if (productId === 0x0ce6 || productId === 0x0df2) {
        await DualSense.setControllerState(hidDevice, {
          leftVibration: leftVibration,
          rightVibration: rightVibration
        });
      } else {
        await DualSense.setVibration(hidDevice, leftVibration, rightVibration);
      }
    }
    // Xbox controllers
    else if (vendorId === 0x045e) {
      await Xbox.setVibration(hidDevice, leftVibration, rightVibration);
    }
    // Nintendo Switch controllers
    else if (vendorId === 0x057e) {
      await SwitchPro.setVibration(hidDevice, leftVibration, rightVibration);
    }
  };

  const handleStopVibration = async () => {
    if (!hidDevice) return;

    const vendorId = hidDevice.vendorId;
    const productId = hidDevice.productId;

    // Sony controllers
    if (vendorId === 0x054c) {
      // For DualSense, use the combined state function for better reliability
      if (productId === 0x0ce6 || productId === 0x0df2) {
        await DualSense.setControllerState(hidDevice, {
          leftVibration: 0,
          rightVibration: 0
        });
      } else {
        await DualSense.setVibration(hidDevice, 0, 0);
      }
    }
    // Xbox controllers
    else if (vendorId === 0x045e) {
      await Xbox.setVibration(hidDevice, 0, 0);
    }
    // Nintendo Switch controllers
    else if (vendorId === 0x057e) {
      await SwitchPro.setVibration(hidDevice, 0, 0);
    }

    // Reset sliders
    setLeftVibration(0);
    setRightVibration(0);
  };

  const handleLightbarTest = async () => {
    if (!hidDevice) return;

    // Only Sony controllers support lightbar
    if (hidDevice.vendorId === 0x054c) {
      const productId = hidDevice.productId;

      // For DualSense, use the combined state function for better reliability
      if (productId === 0x0ce6 || productId === 0x0df2) {
        await DualSense.setControllerState(hidDevice, {
          lightR: lightR,
          lightG: lightG,
          lightB: lightB
        });
      } else {
        await DualSense.setLightbarColor(hidDevice, lightR, lightG, lightB);
      }
    }
  };

  const handleTriggerTest = async (trigger: 'left' | 'right') => {
    if (!hidDevice) return;

    // Only DualSense supports adaptive triggers
    if (hidDevice.vendorId === 0x054c &&
        (hidDevice.productId === 0x0ce6 || hidDevice.productId === 0x0df2)) {

      // Use the combined state function for better reliability
      if (trigger === 'left') {
        await DualSense.setControllerState(hidDevice, {
          leftTriggerMode: triggerMode,
          leftTriggerStart: triggerStart,
          leftTriggerForce: triggerForce
        });
      } else {
        await DualSense.setControllerState(hidDevice, {
          rightTriggerMode: triggerMode,
          rightTriggerStart: triggerStart,
          rightTriggerForce: triggerForce
        });
      }
    }
  };

  const handleResetTriggers = async () => {
    if (!hidDevice) return;

    // Only DualSense supports adaptive triggers
    if (hidDevice.vendorId === 0x054c &&
        (hidDevice.productId === 0x0ce6 || hidDevice.productId === 0x0df2)) {
      // Mode 0 = no resistance
      await DualSense.setControllerState(hidDevice, {
        leftTriggerMode: DualSense.TRIGGER_EFFECT_MODE.OFF,
        rightTriggerMode: DualSense.TRIGGER_EFFECT_MODE.OFF
      });
    }
  };

  if (!hidDevice) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>HID Controller Features</CardTitle>
          <CardDescription>
            Connect a controller via HID to access advanced features
          </CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center">
          <Button variant="outline" disabled>
            <Gamepad className="mr-2 h-4 w-4" />
            No HID Device Connected
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>HID Controller Features</CardTitle>
        <CardDescription>
          Test advanced features of your controller
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger
              value="vibration"
              disabled={!supportedFeatures.vibration}
            >
              <Vibrate className="mr-2 h-4 w-4" />
              Vibration
            </TabsTrigger>
            <TabsTrigger
              value="lightbar"
              disabled={!supportedFeatures.lightbar}
            >
              <Lightbulb className="mr-2 h-4 w-4" />
              Light
            </TabsTrigger>
            <TabsTrigger
              value="triggers"
              disabled={!supportedFeatures.triggers}
            >
              <Zap className="mr-2 h-4 w-4" />
              Triggers
            </TabsTrigger>
          </TabsList>

          {/* Vibration Tab */}
          <TabsContent value="vibration" className="space-y-4">
            <div className="space-y-4">
              <div>
                <h3 className="mb-2 text-sm font-medium">Left Motor</h3>
                <div className="flex items-center space-x-4">
                  <Slider
                    value={[leftVibration]}
                    max={255}
                    step={1}
                    onValueChange={(values) => setLeftVibration(values[0])}
                  />
                  <span className="w-12 text-sm">{leftVibration}</span>
                </div>
              </div>

              <div>
                <h3 className="mb-2 text-sm font-medium">Right Motor</h3>
                <div className="flex items-center space-x-4">
                  <Slider
                    value={[rightVibration]}
                    max={255}
                    step={1}
                    onValueChange={(values) => setRightVibration(values[0])}
                  />
                  <span className="w-12 text-sm">{rightVibration}</span>
                </div>
              </div>

              <div className="flex space-x-2">
                <Button onClick={handleVibrationTest}>
                  <Vibrate className="mr-2 h-4 w-4" />
                  Test Vibration
                </Button>
                <Button variant="outline" onClick={handleStopVibration}>
                  Stop
                </Button>
              </div>
            </div>
          </TabsContent>

          {/* Lightbar Tab */}
          <TabsContent value="lightbar" className="space-y-4">
            <div className="space-y-4">
              <div>
                <h3 className="mb-2 text-sm font-medium">Red</h3>
                <div className="flex items-center space-x-4">
                  <Slider
                    value={[lightR]}
                    max={255}
                    step={1}
                    onValueChange={(values) => setLightR(values[0])}
                  />
                  <span className="w-12 text-sm">{lightR}</span>
                </div>
              </div>

              <div>
                <h3 className="mb-2 text-sm font-medium">Green</h3>
                <div className="flex items-center space-x-4">
                  <Slider
                    value={[lightG]}
                    max={255}
                    step={1}
                    onValueChange={(values) => setLightG(values[0])}
                  />
                  <span className="w-12 text-sm">{lightG}</span>
                </div>
              </div>

              <div>
                <h3 className="mb-2 text-sm font-medium">Blue</h3>
                <div className="flex items-center space-x-4">
                  <Slider
                    value={[lightB]}
                    max={255}
                    step={1}
                    onValueChange={(values) => setLightB(values[0])}
                  />
                  <span className="w-12 text-sm">{lightB}</span>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <div
                  className="w-10 h-10 rounded-full border"
                  style={{ backgroundColor: `rgb(${lightR}, ${lightG}, ${lightB})` }}
                />
                <Button onClick={handleLightbarTest}>
                  <Lightbulb className="mr-2 h-4 w-4" />
                  Set Light Color
                </Button>
              </div>
            </div>
          </TabsContent>

          {/* Triggers Tab */}
          <TabsContent value="triggers" className="space-y-4">
            <div className="space-y-4">
              <div>
                <h3 className="mb-2 text-sm font-medium">Mode</h3>
                <div className="grid grid-cols-2 gap-2">
                  <Button
                    variant={triggerMode === DualSense.TRIGGER_EFFECT_MODE.OFF ? "default" : "outline"}
                    onClick={() => setTriggerMode(DualSense.TRIGGER_EFFECT_MODE.OFF)}
                  >
                    Off
                  </Button>
                  <Button
                    variant={triggerMode === DualSense.TRIGGER_EFFECT_MODE.FEEDBACK ? "default" : "outline"}
                    onClick={() => setTriggerMode(DualSense.TRIGGER_EFFECT_MODE.FEEDBACK)}
                  >
                    Feedback
                  </Button>
                  <Button
                    variant={triggerMode === DualSense.TRIGGER_EFFECT_MODE.WEAPON ? "default" : "outline"}
                    onClick={() => setTriggerMode(DualSense.TRIGGER_EFFECT_MODE.WEAPON)}
                  >
                    Weapon
                  </Button>
                  <Button
                    variant={triggerMode === DualSense.TRIGGER_EFFECT_MODE.VIBRATION ? "default" : "outline"}
                    onClick={() => setTriggerMode(DualSense.TRIGGER_EFFECT_MODE.VIBRATION)}
                  >
                    Vibration
                  </Button>
                </div>
              </div>

              <div>
                <h3 className="mb-2 text-sm font-medium">Start Position</h3>
                <div className="flex items-center space-x-4">
                  <Slider
                    value={[triggerStart]}
                    max={255}
                    step={1}
                    onValueChange={(values) => setTriggerStart(values[0])}
                  />
                  <span className="w-12 text-sm">{triggerStart}</span>
                </div>
              </div>

              <div>
                <h3 className="mb-2 text-sm font-medium">Force</h3>
                <div className="flex items-center space-x-4">
                  <Slider
                    value={[triggerForce]}
                    max={255}
                    step={1}
                    onValueChange={(values) => setTriggerForce(values[0])}
                  />
                  <span className="w-12 text-sm">{triggerForce}</span>
                </div>
              </div>

              <div className="flex space-x-2">
                <Button onClick={() => handleTriggerTest('left')}>
                  Test Left Trigger
                </Button>
                <Button onClick={() => handleTriggerTest('right')}>
                  Test Right Trigger
                </Button>
                <Button variant="outline" onClick={handleResetTriggers}>
                  Reset
                </Button>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default HIDControllerFeatures;
