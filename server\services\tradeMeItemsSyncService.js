/**
 * TradeMe Items Sync Service
 *
 * Handles synchronization between TradeMe API and our local TradeMeItems database
 */

const tradeMeService = require('./tradeMeService');
const mongoose = require('mongoose');
const TradeMeItems = require('../models/TradeMeItems');
const TradeMeItemAudit = require('../models/TradeMeItemAudit');
const TradeMeSettings = require('../models/TradeMeSettings');
const TrademeImportLog = require('../models/TrademeImportLog');
const Location = require('../models/Location');
const { downloadAndSaveImage } = require('./imageService');
const fs = require('fs');
const path = require('path');
const { parseTradeMeDate } = require('./tradeMeUtils');
const { parseShippingOptions } = require('./tradeMeShippingFix');
const axios = require('axios');

/**
 * Sync listings from TradeMe API
 * @param {Object} options - Options for the sync operation
 * @param {number} options.maxPagesPerEndpoint - Maximum number of pages to fetch per endpoint
 * @param {Object} options.syncLog - Sync log object for tracking
 * @returns {Promise<Object>} Result of the sync operation
 */
async function syncListingsFromTradeMe(options = {}) {
  try {
    // Default options
    const { maxPagesPerEndpoint = 5, syncLog = null } = options;

    // Track API calls and stats
    let apiCallCount = 0;
    const MAX_API_CALLS = 50;

    // Track sync statistics
    const stats = {
      itemsProcessed: 0,
      newItems: 0,
      updatedItems: 0,
      apiCallsMade: 0
    };

    // Get TradeMe settings
    const settings = await TradeMeSettings.findOne();
    if (!settings || !settings.connected) {
      return {
        success: false,
        error: 'No active TradeMe connection'
      };
    }

    // Get OAuth instance and API endpoints
    const oauth = tradeMeService.getOAuth(settings.environment);
    const apiEndpoints = tradeMeService.TRADEME_API[settings.environment];
    const accessToken = settings.accessToken;
    const accessTokenSecret = settings.accessSecret;

    // Helper function to make authenticated API calls
    const makeAuthenticatedRequest = async (endpoint, page = 1) => {
      apiCallCount++;
      stats.apiCallsMade++;
      console.log(`TradeMe API Call #${apiCallCount}: ${endpoint} (page ${page})`);

      // Add page parameter if specified
      const url = page > 1 ? `${apiEndpoints.apiBase}${endpoint}?page=${page}` : `${apiEndpoints.apiBase}${endpoint}`;

      // Update the syncLog if provided
      if (syncLog) {
        syncLog.apiCallsMade = stats.apiCallsMade;
        await syncLog.save();
      }

      return axios({
        method: 'get',
        url: url,
        headers: oauth.toHeader(
          oauth.authorize(
            {
              url: url,
              method: 'GET'
            },
            {
              key: accessToken,
              secret: accessTokenSecret
            }
          )
        )
      });
    };

    // Helper function to fetch all pages of results
    const fetchAllPages = async (endpoint, processFunction) => {
      let page = 1;
      let hasMorePages = true;
      const allResults = [];

      while (hasMorePages && page <= maxPagesPerEndpoint && apiCallCount < MAX_API_CALLS) {
        try {
          const response = await makeAuthenticatedRequest(endpoint, page);

          // Check if we have valid data
          if (response.data && Array.isArray(response.data.List)) {
            const results = response.data.List;

            // Process each result if a process function is provided
            if (processFunction && typeof processFunction === 'function') {
              for (const result of results) {
                await processFunction(result);
              }
            }

            // Add results to the collection
            allResults.push(...results);

            // Check if we have more pages
            hasMorePages = results.length > 0 && response.data.TotalCount > page * response.data.PageSize;

            // Increment page counter
            page++;
          } else {
            hasMorePages = false;
          }
        } catch (error) {
          console.error(`Error fetching page ${page} of ${endpoint}:`, error);
          hasMorePages = false;
        }
      }

      return allResults;
    };

    // Process active listings
    const processActiveListing = async (listing) => {
      try {
        // Check if listing already exists in our database
        let existingItem = await TradeMeItems.findOne({
          currentListingId: listing.ListingId.toString()
        });

        // If the item exists, update it
        if (existingItem) {
          // Get full listing details if needed
          let fullListing = listing;
          if (!listing.Description) {
            try {
              const detailsResponse = await tradeMeService.fetchListingFromTradeMe(listing.ListingId);
              if (detailsResponse.success) {
                fullListing = detailsResponse.listing;
              }
            } catch (detailsError) {
              console.error(`Error fetching details for listing ${listing.ListingId}:`, detailsError);
            }
          }

          // Update the item with the latest information
          existingItem.title = fullListing.Title;
          existingItem.description = fullListing.Description || existingItem.description;
          existingItem.currentPrice = fullListing.StartPrice || 0;
          existingItem.buyNowPrice = fullListing.BuyNowPrice || 0;
          existingItem.status = 'active';

          // Update listing history entry
          const historyEntry = existingItem.listingHistory.find(
            h => h.trademeListingId === listing.ListingId.toString()
          );

          if (historyEntry) {
            historyEntry.views = fullListing.ViewCount || 0;
            historyEntry.watchers = fullListing.WatchCount || 0;
            historyEntry.bids = fullListing.BidCount || 0;
          }

          // Save images locally if they don't exist
          if (fullListing.PictureHref) {
            const imageDir = path.join(__dirname, `../uploads/trademe/${existingItem._id}`);
            if (!fs.existsSync(imageDir)) {
              fs.mkdirSync(imageDir, { recursive: true });
            }

            // Check if we already have this image
            const hasImage = existingItem.images && existingItem.images.some(img =>
              img.includes(path.basename(fullListing.PictureHref))
            );

            if (!hasImage) {
              try {
                const imagePath = await downloadAndSaveImage(
                  fullListing.PictureHref,
                  `../uploads/trademe/${existingItem._id}/${path.basename(fullListing.PictureHref)}`
                );

                if (imagePath) {
                  if (!existingItem.images) {
                    existingItem.images = [];
                  }
                  existingItem.images.push(imagePath);
                }
              } catch (imageError) {
                console.error(`Error downloading image for listing ${listing.ListingId}:`, imageError);
              }
            }
          }

          // Update last synced timestamp
          existingItem.lastSyncedAt = new Date();

          // Save the updated item
          await existingItem.save();

          // Update statistics
          stats.itemsProcessed++;
          stats.updatedItems++;
        } else {
          // This is a new listing we don't have in our database
          // We'll skip it since we only want to update existing items
          // console.log(`Skipping listing ${listing.ListingId} - not found in database`);
          // Don't increment stats.itemsProcessed since we're skipping this item
        }
      } catch (error) {
        console.error(`Error processing active listing ${listing.ListingId}:`, error);
      }
    };

    // Process sold listings
    const processSoldListing = async (listing) => {
      try {
        // Check if we already have this listing in our database
        let existingItem = await TradeMeItems.findOne({
          currentListingId: listing.ListingId.toString()
        });

        if (!existingItem) {
          // Check if we have it in listing history
          existingItem = await TradeMeItems.findOne({
            'listingHistory.trademeListingId': listing.ListingId.toString()
          });
        }

        if (existingItem) {
          // Update the item status to sold
          existingItem.status = 'sold';

          // Update the listing history entry
          const historyEntry = existingItem.listingHistory.find(
            h => h.trademeListingId === listing.ListingId.toString()
          );

          if (historyEntry) {
            historyEntry.status = 'sold';
            historyEntry.soldPrice = listing.SoldPrice || listing.StartPrice;
            historyEntry.soldDate = parseTradeMeDate(listing.SoldDate);
            historyEntry.buyer = listing.Winner;
          }

          // Update last synced timestamp
          existingItem.lastSyncedAt = new Date();

          // Save the updated item
          await existingItem.save();

          // Update statistics
          stats.itemsProcessed++;
          stats.updatedItems++;
        } else {
          // This is a sold listing we don't have in our database
          // We'll skip it since we only want to update existing items
          // console.log(`Skipping sold listing ${listing.ListingId} - not found in database`);
          // Don't increment stats.itemsProcessed since we're skipping this item
        }
      } catch (error) {
        console.error(`Error processing sold listing ${listing.ListingId}:`, error);
      }
    };

    // Fetch active listings from TradeMe
    console.log('Starting to fetch active TradeMe listings...');
    await fetchAllPages('/MyTradeMe/SellingItems/All.json', processActiveListing);

    // Fetch sold listings
    console.log('Starting to fetch sold TradeMe listings...');
    await fetchAllPages('/MyTradeMe/SoldItems.json', processSoldListing);

    return {
      success: true,
      stats
    };
  } catch (error) {
    console.error('Error syncing TradeMe listings:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Sync questions from TradeMe API
 * @param {Object} options - Options for the sync operation
 * @param {number} options.maxPagesPerEndpoint - Maximum number of pages to fetch
 * @param {Object} options.syncLog - Sync log object for tracking
 * @returns {Promise<Object>} Result of the sync operation
 */
async function syncQuestionsFromTradeMe(options = {}) {
  try {
    // Default options
    const { maxPagesPerEndpoint = 3, syncLog = null } = options;

    // Track API calls and stats
    let apiCallCount = 0;
    const MAX_API_CALLS = 20;

    // Track sync statistics
    const stats = {
      itemsProcessed: 0,
      newItems: 0,
      updatedItems: 0,
      apiCallsMade: 0
    };

    // Get TradeMe settings
    const settings = await TradeMeSettings.findOne();
    if (!settings || !settings.connected) {
      return {
        success: false,
        error: 'No active TradeMe connection'
      };
    }

    // Get OAuth instance and API endpoints
    const oauth = tradeMeService.getOAuth(settings.environment);
    const apiEndpoints = tradeMeService.TRADEME_API[settings.environment];
    const accessToken = settings.accessToken;
    const accessTokenSecret = settings.accessSecret;

    // Helper function to make authenticated API calls
    const makeAuthenticatedRequest = async (endpoint, page = 1) => {
      apiCallCount++;
      stats.apiCallsMade++;
      console.log(`TradeMe API Call #${apiCallCount}: ${endpoint} (page ${page})`);

      // Add page parameter if specified
      const url = page > 1 ? `${apiEndpoints.apiBase}${endpoint}?page=${page}` : `${apiEndpoints.apiBase}${endpoint}`;

      // Update the syncLog if provided
      if (syncLog) {
        syncLog.apiCallsMade = stats.apiCallsMade;
        await syncLog.save();
      }

      return axios({
        method: 'get',
        url: url,
        headers: oauth.toHeader(
          oauth.authorize(
            {
              url: url,
              method: 'GET'
            },
            {
              key: accessToken,
              secret: accessTokenSecret
            }
          )
        )
      });
    };

    // Helper function to fetch all pages of results
    const fetchAllPages = async (endpoint) => {
      let page = 1;
      let hasMorePages = true;
      const allResults = [];

      while (hasMorePages && page <= maxPagesPerEndpoint && apiCallCount < MAX_API_CALLS) {
        try {
          const response = await makeAuthenticatedRequest(endpoint, page);

          // Check if we have valid data
          if (response.data && Array.isArray(response.data.List)) {
            const results = response.data.List;

            // Add results to the collection
            allResults.push(...results);

            // Check if we have more pages
            hasMorePages = results.length > 0 && response.data.TotalCount > page * response.data.PageSize;

            // Increment page counter
            page++;
          } else {
            hasMorePages = false;
          }
        } catch (error) {
          console.error(`Error fetching page ${page} of ${endpoint}:`, error);
          hasMorePages = false;
        }
      }

      return allResults;
    };

    // Fetch questions from TradeMe
    console.log('Starting to fetch TradeMe questions...');
    const questions = await fetchAllPages('/Listings/questions/unansweredquestions.json');

    // Process questions
    for (const question of questions) {
      try {
        // Find the item this question belongs to
        let item = await TradeMeItems.findOne({
          currentListingId: question.ListingId.toString()
        });

        if (!item) {
          // Check if we have it in listing history
          item = await TradeMeItems.findOne({
            'listingHistory.trademeListingId': question.ListingId.toString()
          });
        }

        if (item) {
          // Check if we already have this question
          const existingQuestion = item.questions.find(
            q => q.trademeQuestionId === question.QuestionId.toString()
          );

          if (existingQuestion) {
            // Update the question if needed
            if (!existingQuestion.isAnswered && question.Answer) {
              existingQuestion.answer = question.Answer;
              existingQuestion.isAnswered = true;
              existingQuestion.answerDate = parseTradeMeDate(question.AnswerDate);
              // We don't know who answered it, so we'll leave answeredBy as is
            }
          } else {
            // Add the new question
            item.questions.push({
              trademeQuestionId: question.QuestionId.toString(),
              question: question.Question,
              answer: question.Answer,
              askerName: question.AskerNickname,
              askDate: parseTradeMeDate(question.AskDate),
              answerDate: question.Answer ? parseTradeMeDate(question.AnswerDate) : null,
              isAnswered: !!question.Answer,
              originalListingId: question.ListingId.toString()
            });

            // Create audit entry for the new question
            await TradeMeItemAudit.create({
              itemId: item._id,
              trademeListingId: question.ListingId.toString(),
              action: 'question_added',
              details: `New question received: "${question.Question.substring(0, 50)}..."`,
              performedBy: '000000000000000000000000' // System user
            });
          }

          // Save the item
          await item.save();

          // Update statistics
          stats.itemsProcessed++;
          stats.updatedItems++;
        }
      } catch (error) {
        console.error(`Error processing question for listing ${question.ListingId}:`, error);
      }
    }

    return {
      success: true,
      stats
    };
  } catch (error) {
    console.error('Error syncing TradeMe questions:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Import user listings from TradeMe
 * @param {Object} options - Options for the import operation
 * @param {number} options.maxPagesPerEndpoint - Maximum number of pages to fetch per endpoint
 * @param {Object} options.user - User initiating the import
 * @returns {Promise<Object>} Result of the import operation
 */
async function importUserListings(options = {}) {
  // Create a new import log
  const importLog = new TrademeImportLog({
    status: 'pending',
    importType: 'manual',
    initiatedBy: options.user._id,
    listingType: options.type || 'active' // Save the listing type (active or sold)
  });

  await importLog.save();

  try {
    // Default options
    const { maxPagesPerEndpoint = 5, user, type = 'active' } = options;

    // Update import log status
    importLog.status = 'in_progress';
    importLog.addLog(`Starting import of TradeMe ${type} listings`);
    await importLog.save();

    // Track API calls and stats
    let apiCallCount = 0;
    const MAX_API_CALLS = 50;

    // Track import statistics
    const stats = {
      itemsProcessed: 0,
      newItems: 0,
      updatedItems: 0,
      duplicateItems: 0,
      failedItems: 0,
      apiCallsMade: 0
    };

    // Get TradeMe settings
    const settings = await TradeMeSettings.findOne();
    if (!settings || !settings.connected) {
      importLog.fail('No active TradeMe connection');
      await importLog.save();

      return {
        success: false,
        error: 'No active TradeMe connection',
        importLogId: importLog._id
      };
    }

    // Update import log with environment
    importLog.environment = settings.environment;
    await importLog.save();

    // Get OAuth instance and API endpoints
    const { TRADEME_API, getOAuth } = require('./tradeMeUtils');
    const oauth = getOAuth(settings.environment);
    const apiEndpoints = TRADEME_API[settings.environment];
    const accessToken = settings.accessToken;
    const accessTokenSecret = settings.accessSecret;

    // Helper function to make authenticated API calls
    const makeAuthenticatedRequest = async (endpoint, page = 1) => {
      apiCallCount++;
      stats.apiCallsMade++;

      // Update import log
      importLog.apiCallsMade = stats.apiCallsMade;
      importLog.addLog(`Making API call to ${endpoint} (page ${page})`);
      await importLog.save();

      // Add page parameter if specified
      const url = page > 1 ? `${apiEndpoints.apiBase}${endpoint}?page=${page}` : `${apiEndpoints.apiBase}${endpoint}`;

      return axios({
        method: 'get',
        url: url,
        headers: oauth.toHeader(
          oauth.authorize(
            {
              url: url,
              method: 'GET'
            },
            {
              key: accessToken,
              secret: accessTokenSecret
            }
          )
        )
      });
    };

    // Helper function to fetch all pages of results
    const fetchAllPages = async (endpoint) => {
      let page = 1;
      let hasMorePages = true;
      const allResults = [];

      while (hasMorePages && page <= maxPagesPerEndpoint && apiCallCount < MAX_API_CALLS) {
        try {
          const response = await makeAuthenticatedRequest(endpoint, page);

          // Check if we have valid data
          if (response.data && Array.isArray(response.data.List)) {
            const results = response.data.List;
            allResults.push(...results);

            // Check if we have more pages
            const totalCount = response.data.TotalCount || 0;
            const pageSize = response.data.PageSize || results.length;

            hasMorePages = totalCount > page * pageSize;
            page++;

            // Add a small delay to avoid hitting rate limits
            await new Promise(resolve => setTimeout(resolve, 200));
          } else {
            hasMorePages = false;
          }
        } catch (error) {
          importLog.addLog(`Error fetching page ${page} of ${endpoint}: ${error.message}`, 'error');
          await importLog.save();

          console.error(`Error fetching page ${page} of ${endpoint}:`, error);
          hasMorePages = false;
        }
      }

      return allResults;
    };

    // Helper function to get default location ID
    const getDefaultLocationId = async () => {
      try {
        // Try to find a location in the database
        const location = await Location.findOne();
        if (location) {
          return location._id;
        }

        // If no location exists, create a default one
        const defaultLocation = new Location({
          name: 'Default',
          description: 'Default location created during TradeMe import',
          createdBy: user._id
        });

        await defaultLocation.save();
        return defaultLocation._id;
      } catch (error) {
        console.error('Error getting default location ID:', error);
        throw error;
      }
    };

    // Note: fetchQuestionsForListing is defined elsewhere in this file

    // Helper function to download images
    const downloadImages = async (listing, stockCode = '') => {
      const images = [];

      // Check if we have a primary image
      if (listing.PictureHref) {
        try {
          // Try to download and save the image locally
          const fullImageUrl = listing.PictureHref.replace('/thumb/', '/full/');
          const savedImage = await downloadAndSaveImage(fullImageUrl, stockCode, listing.ListingId.toString());

          if (savedImage) {
            images.push(savedImage.original);
          }
        } catch (error) {
          console.error(`Error downloading primary image for listing ${listing.ListingId}:`, error);
          importLog.addLog(`Error downloading primary image for listing ${listing.ListingId}: ${error.message}`, 'warning');

          // Fall back to the original URL if download fails
          images.push(listing.PictureHref.replace('/thumb/', '/full/'));
        }
      }

      // Check if we have additional images
      if (listing.Photos && Array.isArray(listing.Photos)) {
        for (const photo of listing.Photos) {
          if (photo.Value && photo.Value.FullSize) {
            try {
              // Try to download and save the image locally
              const savedImage = await downloadAndSaveImage(photo.Value.FullSize, stockCode, listing.ListingId.toString());

              if (savedImage) {
                images.push(savedImage.original);
              }
            } catch (error) {
              console.error(`Error downloading additional image for listing ${listing.ListingId}:`, error);
              importLog.addLog(`Error downloading additional image for listing ${listing.ListingId}: ${error.message}`, 'warning');

              // Fall back to the original URL if download fails
              images.push(photo.Value.FullSize);
            }
          }
        }
      }

      return images;
    };

    // Helper function to extract description from listing
    const extractDescription = (listing) => {
      const possibleDescriptionFields = ['Body', 'Description', 'body', 'description'];

      for (const field of possibleDescriptionFields) {
        if (listing[field] && typeof listing[field] === 'string' && listing[field].trim() !== '') {
          return listing[field];
        }
      }

      return 'No description provided';
    };

    // Helper function to parse payment methods
    const parsePaymentMethods = (paymentMethodsData) => {
      // Default payment methods if none are provided
      const defaultMethods = ['BankDeposit', 'Cash'];

      if (!paymentMethodsData) return defaultMethods;

      try {
        // If it's already an array of strings, use it directly
        if (Array.isArray(paymentMethodsData) && typeof paymentMethodsData[0] === 'string') {
          return paymentMethodsData;
        }

        // If it's an array of objects, extract the Name property
        if (Array.isArray(paymentMethodsData) && typeof paymentMethodsData[0] === 'object') {
          return paymentMethodsData.map(method => {
            // Map TradeMe payment method names to our enum values
            const name = method.Name || '';
            if (name.includes('Bank Deposit')) return 'BankDeposit';
            if (name.includes('Credit Card')) return 'CreditCard';
            if (name.includes('Cash')) return 'Cash';
            if (name.includes('Ping')) return 'Ping';
            if (name.includes('Afterpay')) return 'Afterpay';
            return 'Other';
          }).filter(Boolean); // Remove any empty values
        }

        // If it's a string that looks like JSON, try to parse it
        if (typeof paymentMethodsData === 'string') {
          try {
            // Clean up the string if it's malformed
            let cleanedString = paymentMethodsData;
            if (cleanedString.startsWith('[\n') || cleanedString.includes('\'')) {
              // This is likely a stringified representation with escape characters
              // Try to extract the method names directly using regex
              const methodNames = [];
              const cashMatch = cleanedString.match(/Name: 'Cash'/i);
              const bankMatch = cleanedString.match(/Name: 'NZ Bank Deposit'/i);
              const pingMatch = cleanedString.match(/Name: 'Ping'/i);
              const afterpayMatch = cleanedString.match(/Name: 'Afterpay'/i);

              if (cashMatch) methodNames.push('Cash');
              if (bankMatch) methodNames.push('BankDeposit');
              if (pingMatch) methodNames.push('Ping');
              if (afterpayMatch) methodNames.push('Afterpay');

              return methodNames.length > 0 ? methodNames : defaultMethods;
            }

            // Try to parse as JSON
            const parsedData = JSON.parse(cleanedString);
            if (Array.isArray(parsedData)) {
              return parsedData.map(method => {
                const name = method.Name || '';
                if (name.includes('Bank Deposit')) return 'BankDeposit';
                if (name.includes('Credit Card')) return 'CreditCard';
                if (name.includes('Cash')) return 'Cash';
                if (name.includes('Ping')) return 'Ping';
                if (name.includes('Afterpay')) return 'Afterpay';
                return 'Other';
              }).filter(Boolean);
            }
          } catch (e) {
            console.warn('Failed to parse payment methods string:', e);
            return defaultMethods;
          }
        }

        // If we couldn't parse the payment methods, return the defaults
        return defaultMethods;
      } catch (error) {
        console.warn('Error parsing payment methods:', error);
        return defaultMethods;
      }
    };

    // Helper function to fetch full listing details
    const fetchFullListingDetails = async (listingId) => {
      try {
        const response = await makeAuthenticatedRequest(`/Listings/${listingId}.json`);
        return response.data;
      } catch (error) {
        console.error(`Error fetching full details for listing ${listingId}:`, error);
        importLog.addLog(`Error fetching full details for listing ${listingId}: ${error.message}`, 'error');
        return null;
      }
    };

    // Helper function to fetch questions for a listing
    const fetchQuestionsForListing = async (listingId) => {
      try {
        const response = await makeAuthenticatedRequest(`/Listings/${listingId}/Questions.json`);

        if (response.data && Array.isArray(response.data.List)) {
          const questions = response.data.List;
          console.log(`Fetched ${questions.length} questions for listing ${listingId}`);

          // Log the structure of the first question to help with debugging
          if (questions.length > 0) {
            console.log('Sample question structure:', JSON.stringify(questions[0], null, 2));
          }

          return questions;
        } else {
          console.log(`No questions found for listing ${listingId}`);
          return [];
        }
      } catch (error) {
        console.error(`Error fetching questions for listing ${listingId}:`, error);
        importLog.addLog(`Error fetching questions for listing ${listingId}: ${error.message}`, 'warning');
        return [];
      }
    };

    // Helper function to process a listing
    const processListing = async (listing) => {
      try {
        // Fetch full listing details first to get the SKU
        const fullListing = await fetchFullListingDetails(listing.ListingId);
        if (!fullListing) {
          stats.failedItems++;
          stats.itemsProcessed++;

          importLog.addLog(`Failed to fetch full details for listing ${listing.ListingId}`, 'error');
          importLog.updateStats(stats);
          await importLog.save();

          return {
            success: false,
            error: 'Failed to fetch full listing details'
          };
        }

        // Extract the SKU/stockCode from the listing
        const stockCode = fullListing.SKU || '';

        // First, check if we have an item with the same SKU/stockCode
        let existingItem = null;

        if (stockCode && stockCode.trim() !== '') {
          existingItem = await TradeMeItems.findOne({ stockCode: stockCode });
        }

        // If no match by SKU, check if the specific listing already exists
        if (!existingItem) {
          existingItem = await TradeMeItems.findOne({
            $or: [
              { currentListingId: listing.ListingId.toString() },
              { 'listingHistory.trademeListingId': listing.ListingId.toString() }
            ]
          });

          if (existingItem) {
            // This specific listing already exists, count as duplicate
            stats.duplicateItems++;
            stats.itemsProcessed++;

            importLog.addLog(`Listing ${listing.ListingId} already exists in the database`, 'warning');
            importLog.updateStats(stats);
            await importLog.save();

            return {
              success: false,
              error: 'Duplicate listing',
              item: existingItem
            };
          }
        }

        // If we found an existing item with the same SKU, update it with this listing
        if (existingItem && stockCode && stockCode.trim() !== '') {
          // Download images if needed
          const images = await downloadImages(fullListing, stockCode);

          // Fetch questions
          const questions = await fetchQuestionsForListing(listing.ListingId);

          // Create a new listing history entry
          const listingHistoryEntry = {
            trademeListingId: fullListing.ListingId.toString(),
            startDate: parseTradeMeDate(fullListing.StartDate),
            endDate: parseTradeMeDate(fullListing.EndDate),
            startPrice: fullListing.StartPrice || 0,
            buyNowPrice: fullListing.BuyNowPrice || 0,
            reservePrice: fullListing.ReservePrice || 0,
            views: fullListing.ViewCount || 0,
            watchers: fullListing.WatchCount || 0,
            bids: fullListing.BidCount || 0,
            status: 'active',
            createdBy: 'Imported', // For imported listings, set createdBy to 'Imported'
            answeredBy: null
          };

          // Add the new listing to the history
          existingItem.listingHistory.push(listingHistoryEntry);

          // Update current listing information
          existingItem.currentListingId = fullListing.ListingId.toString();
          existingItem.currentPrice = fullListing.StartPrice || 0;
          existingItem.buyNowPrice = fullListing.BuyNowPrice || 0;
          existingItem.reservePrice = fullListing.ReservePrice || 0;
          existingItem.status = listing.status || 'active';

          // If this is a sold item, update the buyer payment information
          if (listing.status === 'sold' && fullListing.Sales && fullListing.Sales.length > 0) {
            // Log payment information for debugging
            console.log(`Payment info for existing item ${fullListing.ListingId}:`, {
              PaymentType: fullListing.Sales[0].PaymentType,
              PaymentMethod: fullListing.Sales[0].PaymentMethod,
              HasPaidByCreditCard: fullListing.Sales[0].HasPaidByCreditCard,
              CreditCardType: fullListing.Sales[0].CreditCardType
            });

            // Create or update buyer payment info
            if (!existingItem.buyerPaymentInfo) {
              existingItem.buyerPaymentInfo = {};
            }

            // Update payment type
            let paymentType = 'None';

            // Check for PaymentDetails object first
            if (fullListing.Sales[0].PaymentDetails) {
              const paymentDetails = fullListing.Sales[0].PaymentDetails;

              // Log the payment details for debugging
              console.log(`Payment details for existing item ${fullListing.ListingId}:`, paymentDetails);

              // Extract payment type from PaymentDetails
              if (paymentDetails.PaymentType !== undefined) {
                // Map numeric payment types according to TradeMe API documentation
                switch (paymentDetails.PaymentType) {
                  case 0: paymentType = 'None'; break;
                  case 1: paymentType = 'BankDeposit'; break;
                  case 2: paymentType = 'CreditCard'; break;
                  case 4: paymentType = 'Cash'; break;
                  case 8: paymentType = 'SafeTrader'; break;
                  case 16: paymentType = 'Other'; break;
                  case 32: paymentType = 'Ping'; break;
                  case 64: paymentType = 'Afterpay'; break;
                  default: paymentType = 'None'; break;
                }
              }
            } else {
              // Fallback to checking other fields if PaymentDetails is not available
              if (fullListing.Sales[0].HasPaidByCreditCard) {
                paymentType = 'CreditCard';
              } else if (fullListing.Sales[0].PaymentType === 1 ||
                        fullListing.Sales[0].PaymentMethod === 1 ||
                        fullListing.Sales[0].PaymentMethod === 'BankDeposit' ||
                        fullListing.Sales[0].PaymentType === 'BankDeposit') {
                paymentType = 'BankDeposit';
              } else if (fullListing.Sales[0].PaymentType === 2 ||
                        fullListing.Sales[0].PaymentMethod === 2 ||
                        fullListing.Sales[0].PaymentMethod === 'CreditCard' ||
                        fullListing.Sales[0].PaymentType === 'CreditCard') {
                paymentType = 'CreditCard';
              } else if (fullListing.Sales[0].PaymentType === 4 ||
                        fullListing.Sales[0].PaymentMethod === 4 ||
                        fullListing.Sales[0].PaymentMethod === 'Cash' ||
                        fullListing.Sales[0].PaymentType === 'Cash') {
                paymentType = 'Cash';
              } else if (fullListing.Sales[0].PaymentType === 32 ||
                        fullListing.Sales[0].PaymentMethod === 32 ||
                        fullListing.Sales[0].PaymentMethod === 'Ping' ||
                        fullListing.Sales[0].PaymentType === 'Ping') {
                paymentType = 'Ping';
              } else if (fullListing.Sales[0].PaymentType === 64 ||
                        fullListing.Sales[0].PaymentMethod === 64 ||
                        fullListing.Sales[0].PaymentMethod === 'Afterpay' ||
                        fullListing.Sales[0].PaymentType === 'Afterpay') {
                paymentType = 'Afterpay';
              } else if (fullListing.Sales[0].PaymentType === 8 ||
                        fullListing.Sales[0].PaymentMethod === 8 ||
                        fullListing.Sales[0].PaymentMethod === 'SafeTrader' ||
                        fullListing.Sales[0].PaymentType === 'SafeTrader') {
                paymentType = 'SafeTrader';
              } else if (fullListing.Sales[0].PaymentType === 16 ||
                        fullListing.Sales[0].PaymentMethod === 16 ||
                        fullListing.Sales[0].PaymentMethod === 'Other' ||
                        fullListing.Sales[0].PaymentType === 'Other') {
                paymentType = 'Other';
              }
            }

            existingItem.buyerPaymentInfo.paymentType = paymentType;
            existingItem.buyerPaymentInfo.tradeMeBuyer = fullListing.Winner || fullListing.BuyerNickname || fullListing.Sales[0].Buyer?.Nickname || '';
            existingItem.buyerPaymentInfo.purchaseId = fullListing.PurchaseId || fullListing.Sales[0].PurchaseId || '';
            existingItem.buyerPaymentInfo.referenceNumber = fullListing.ReferenceNumber || fullListing.Sales[0].ReferenceNumber || '';
            existingItem.buyerPaymentInfo.selectedShipping = fullListing.Sales[0].SelectedShipping || fullListing.SelectedShipping || '';
            existingItem.buyerPaymentInfo.messageFromBuyer = fullListing.Sales[0].MessageFromBuyer || '';
            existingItem.buyerPaymentInfo.hasSellerPlacedFeedback = fullListing.Sales[0].HasSellerPlacedFeedback || false;
            existingItem.buyerPaymentInfo.hasBuyerPlacedFeedback = fullListing.Sales[0].HasBuyerPlacedFeedback || false;
            existingItem.buyerPaymentInfo.isClearance = fullListing.IsClearance || false;

            // Update payment pending status from PaymentDetails if available
            if (fullListing.Sales[0].PaymentDetails && fullListing.Sales[0].PaymentDetails.IsPaymentPending !== undefined) {
              existingItem.buyerPaymentInfo.isPaymentPending = fullListing.Sales[0].PaymentDetails.IsPaymentPending;
            } else {
              existingItem.buyerPaymentInfo.isPaymentPending = fullListing.Sales[0].IsPaymentPending || false;
            }

            // Update other payment-related fields
            // Update payment amount fields
            existingItem.buyerPaymentInfo.paymentAmount = parseFloat(
              fullListing.Sales[0].PaymentDetails?.PaymentAmount ||
              fullListing.Sales[0].PaymentAmount ||
              fullListing.Sales[0].TotalSalePrice || 0
            );
            existingItem.buyerPaymentInfo.paymentMethodFee = parseFloat(
              fullListing.Sales[0].PaymentDetails?.PaymentMethodFee ||
              fullListing.Sales[0].PaymentMethodFee || 0
            );
            existingItem.buyerPaymentInfo.gstCollected = parseFloat(
              fullListing.Sales[0].PaymentDetails?.GstCollected ||
              fullListing.Sales[0].GstCollected || 0
            );
            existingItem.buyerPaymentInfo.pingEscrowStatus =
              fullListing.Sales[0].PaymentDetails?.PingEscrowStatus ||
              fullListing.Sales[0].PingEscrowStatus || 'None';
            existingItem.buyerPaymentInfo.isPayNowPurchase = fullListing.Sales[0].IsPayNowPurchase || false;
            existingItem.buyerPaymentInfo.payNowRefundValue = parseFloat(fullListing.Sales[0].PayNowRefundValue || 0);

            // Update refunds
            if (!existingItem.buyerPaymentInfo.refunds) {
              existingItem.buyerPaymentInfo.refunds = [];
            }

            // Check PaymentDetails first for refunds
            if (fullListing.Sales[0].PaymentDetails?.RefundCollection &&
                Array.isArray(fullListing.Sales[0].PaymentDetails.RefundCollection)) {
              existingItem.buyerPaymentInfo.refunds = fullListing.Sales[0].PaymentDetails.RefundCollection.map(refund => ({
                amount: parseFloat(refund.Amount || 0),
                destination: refund.Destination || '',
                date: refund.Date ? parseTradeMeDate(refund.Date) : null
              }));
            }
            // Fallback to Sales for refunds
            else if (fullListing.Sales[0].RefundCollection &&
                    Array.isArray(fullListing.Sales[0].RefundCollection)) {
              existingItem.buyerPaymentInfo.refunds = fullListing.Sales[0].RefundCollection.map(refund => ({
                amount: parseFloat(refund.Amount || 0),
                destination: refund.Destination || '',
                date: refund.Date ? parseTradeMeDate(refund.Date) : null
              }));
            }
          }

          // Add any new images
          if (images && images.length > 0) {
            // Add only new images that don't already exist
            for (const image of images) {
              if (!existingItem.images.includes(image)) {
                existingItem.images.push(image);
              }
            }
          }

          // Add any new questions
          if (questions && questions.length > 0) {
            for (const q of questions) {
              // Check if this question already exists
              const existingQuestion = existingItem.questions.find(
                eq => eq.trademeQuestionId === (q.ListingQuestionId ? q.ListingQuestionId.toString() : '')
              );

              if (!existingQuestion) {
                // Generate a unique ID if none exists
                const questionId = q.ListingQuestionId ? q.ListingQuestionId.toString() : `generated-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;

                // Ensure question text is not empty
                const questionText = q.Comment || 'Question text not available';

                // For already answered questions, set answeredBy to 'Imported'
                const isAnswered = !!q.Answer;
                const answeredByValue = isAnswered ? 'Imported' : null;

                existingItem.questions.push({
                  trademeQuestionId: questionId,
                  question: questionText,
                  answer: q.Answer || '',
                  askerName: q.AskingMember?.Nickname || 'Anonymous',
                  askDate: q.CommentDate ? parseTradeMeDate(q.CommentDate) : new Date(),
                  answerDate: q.Answer && q.AnswerDate ? parseTradeMeDate(q.AnswerDate) : null,
                  isAnswered: isAnswered,
                  answeredBy: answeredByValue, // 'Imported' for questions already answered on TradeMe
                  originalListingId: fullListing.ListingId ? fullListing.ListingId.toString() : '',
                  comment: ''
                });
              }
            }
          }

          // Update last synced timestamp
          existingItem.lastSyncedAt = new Date();

          // Save the updated item
          await existingItem.save();

          // Create an audit entry
          const auditEntry = new TradeMeItemAudit({
            itemId: existingItem._id,
            trademeListingId: fullListing.ListingId ? fullListing.ListingId.toString() : '',
            action: 'import',
            details: {
              listingId: fullListing.ListingId ? fullListing.ListingId.toString() : '',
              title: fullListing.Title || 'Unknown',
              message: `Added new listing ${fullListing.ListingId} to existing item with SKU ${stockCode}`
            },
            performedBy: user._id
          });

          await auditEntry.save();

          // Update statistics
          stats.updatedItems++;
          stats.itemsProcessed++;

          importLog.addLog(`Added listing ${listing.ListingId} to existing item with SKU ${stockCode}`);
          importLog.updateStats(stats);
          await importLog.save();

          return {
            success: true,
            item: existingItem,
            message: `Added to existing item with SKU ${stockCode}`
          };
        }

        // We already have the full listing details from above

        // Download images - reuse the stockCode variable from above
        const images = await downloadImages(fullListing, stockCode);

        // Fetch questions
        const questions = await fetchQuestionsForListing(listing.ListingId);

        // Get default location ID
        const locationId = await getDefaultLocationId();

        // Create new item
        // Create an item object but don't save it to the database yet
        const itemData = {
          // Basic item information
          title: fullListing.Title,
          subtitle: fullListing.Subtitle || '',
          description: extractDescription(fullListing),
          stockCode: fullListing.SKU && fullListing.SKU.trim() !== '' ? fullListing.SKU : `listing-${fullListing.ListingId}`, // Use TradeMe SKU as stockCode if available, otherwise use listing ID
          cost: 0, // To be filled in by user

          // Current listing information
          currentPrice: fullListing.StartPrice || 0,
          buyNowPrice: fullListing.BuyNowPrice || 0,
          reservePrice: fullListing.ReservePrice || 0,
          category: fullListing.Category || 'General',
          categoryPath: fullListing.CategoryPath || '',

          // Current status - use the status from the listing or default to active
          status: listing.status || 'active',

          // Current TradeMe listing ID
          currentListingId: fullListing.ListingId.toString(),

          // Location information
          location: '', // To be filled in by user
          locationId: locationId,

          // Buyer information (to be filled in by user)
          buyer: null,

          // Sold information (for sold items)
          ...(listing.status === 'sold' ? {
            soldPrice: parseFloat(fullListing.SoldPrice || fullListing.StartPrice || 0),
            soldDate: parseTradeMeDate(fullListing.SoldDate || fullListing.EndDate),

            // Extract buyer delivery address if available
            ...(fullListing.Sales && fullListing.Sales.length > 0 && fullListing.Sales[0].DeliveryAddress ? {
              buyerDeliveryAddress: {
                name: fullListing.Sales[0].DeliveryAddress.Name || '',
                address1: fullListing.Sales[0].DeliveryAddress.Address1 || '',
                address2: fullListing.Sales[0].DeliveryAddress.Address2 || '',
                suburb: fullListing.Sales[0].DeliveryAddress.Suburb || '',
                city: fullListing.Sales[0].DeliveryAddress.City || '',
                postcode: fullListing.Sales[0].DeliveryAddress.Postcode || '',
                country: fullListing.Sales[0].DeliveryAddress.Country || '',
                phoneNumber: fullListing.Sales[0].DeliveryAddress.PhoneNumber || ''
              }
            } : {}),

            // Extract buyer payment information if available
            ...(fullListing.Sales && fullListing.Sales.length > 0 ? {
              buyerPaymentInfo: {
                // TradeMe buyer information
                tradeMeBuyer: fullListing.Winner || fullListing.BuyerNickname || fullListing.Sales[0].Buyer?.Nickname || '',

                // Purchase and reference numbers
                purchaseId: fullListing.PurchaseId || fullListing.Sales[0].PurchaseId || '',
                referenceNumber: fullListing.ReferenceNumber || fullListing.Sales[0].ReferenceNumber || '',

                // Selected shipping information
                selectedShipping: fullListing.Sales[0].SelectedShipping || fullListing.SelectedShipping || '',

                // Message from buyer
                messageFromBuyer: fullListing.Sales[0].MessageFromBuyer || '',

                // Feedback flags
                hasSellerPlacedFeedback: fullListing.Sales[0].HasSellerPlacedFeedback || false,
                hasBuyerPlacedFeedback: fullListing.Sales[0].HasBuyerPlacedFeedback || false,

                // Clearance flag
                isClearance: fullListing.IsClearance || false,

                // Credit card information
                hasPaidByCreditCard: fullListing.Sales[0].HasPaidByCreditCard || false,
                creditCardType: fullListing.Sales[0].CreditCardType || '',
                creditCardLastFourDigits: fullListing.Sales[0].CreditCardLastFourDigits || '',
                creditCardPaymentDate: fullListing.Sales[0].CreditCardPaymentDate ? parseTradeMeDate(fullListing.Sales[0].CreditCardPaymentDate) : null,
                isPaymentPending: fullListing.Sales[0].PaymentDetails?.IsPaymentPending || fullListing.Sales[0].IsPaymentPending || false,

                // Additional payment fields
                // Log payment information for debugging
                paymentType: (() => {
                  // Log the payment information for debugging
                  if (fullListing.Sales && fullListing.Sales.length > 0) {
                    console.log(`Payment info for listing ${fullListing.ListingId}:`, {
                      PaymentType: fullListing.Sales[0].PaymentType,
                      PaymentMethod: fullListing.Sales[0].PaymentMethod,
                      HasPaidByCreditCard: fullListing.Sales[0].HasPaidByCreditCard,
                      CreditCardType: fullListing.Sales[0].CreditCardType,
                      // Check for PaymentDetails object
                      PaymentDetails: fullListing.Sales[0].PaymentDetails,
                      // Log the full Sales object structure for debugging
                      SalesObjectKeys: fullListing.Sales[0] ? Object.keys(fullListing.Sales[0]) : []
                    });

                    // Log the full Sales object for deeper inspection
                    console.log(`Full Sales object for listing ${fullListing.ListingId}:`,
                      JSON.stringify(fullListing.Sales[0], null, 2));
                  }

                  // Check for PaymentDetails object first
                  if (fullListing.Sales[0].PaymentDetails) {
                    const paymentDetails = fullListing.Sales[0].PaymentDetails;

                    // Log the payment details for debugging
                    console.log(`Payment details for listing ${fullListing.ListingId}:`, paymentDetails);

                    // Check if IsPaymentPending is available
                    if (paymentDetails.IsPaymentPending !== undefined) {
                      console.log(`IsPaymentPending for listing ${fullListing.ListingId}:`, paymentDetails.IsPaymentPending);
                    }

                    // Extract payment type from PaymentDetails
                    if (paymentDetails.PaymentType !== undefined) {
                      // Map numeric payment types according to TradeMe API documentation
                      switch (paymentDetails.PaymentType) {
                        case 0: return 'None';
                        case 1: return 'BankDeposit';
                        case 2: return 'CreditCard';
                        case 4: return 'Cash';
                        case 8: return 'SafeTrader';
                        case 16: return 'Other';
                        case 32: return 'Ping';
                        case 64: return 'Afterpay';
                        default: return 'None';
                      }
                    }
                  }

                  // Fallback to checking other fields if PaymentDetails is not available
                  if (fullListing.Sales[0].HasPaidByCreditCard) {
                    return 'CreditCard';
                  } else if (fullListing.Sales[0].PaymentType === 1 ||
                            fullListing.Sales[0].PaymentMethod === 1 ||
                            fullListing.Sales[0].PaymentMethod === 'BankDeposit' ||
                            fullListing.Sales[0].PaymentType === 'BankDeposit') {
                    return 'BankDeposit';
                  } else if (fullListing.Sales[0].PaymentType === 2 ||
                            fullListing.Sales[0].PaymentMethod === 2 ||
                            fullListing.Sales[0].PaymentMethod === 'CreditCard' ||
                            fullListing.Sales[0].PaymentType === 'CreditCard') {
                    return 'CreditCard';
                  } else if (fullListing.Sales[0].PaymentType === 4 ||
                            fullListing.Sales[0].PaymentMethod === 4 ||
                            fullListing.Sales[0].PaymentMethod === 'Cash' ||
                            fullListing.Sales[0].PaymentType === 'Cash') {
                    return 'Cash';
                  } else if (fullListing.Sales[0].PaymentType === 32 ||
                            fullListing.Sales[0].PaymentMethod === 32 ||
                            fullListing.Sales[0].PaymentMethod === 'Ping' ||
                            fullListing.Sales[0].PaymentType === 'Ping') {
                    return 'Ping';
                  } else if (fullListing.Sales[0].PaymentType === 64 ||
                            fullListing.Sales[0].PaymentMethod === 64 ||
                            fullListing.Sales[0].PaymentMethod === 'Afterpay' ||
                            fullListing.Sales[0].PaymentType === 'Afterpay') {
                    return 'Afterpay';
                  } else if (fullListing.Sales[0].PaymentType === 8 ||
                            fullListing.Sales[0].PaymentMethod === 8 ||
                            fullListing.Sales[0].PaymentMethod === 'SafeTrader' ||
                            fullListing.Sales[0].PaymentType === 'SafeTrader') {
                    return 'SafeTrader';
                  } else if (fullListing.Sales[0].PaymentType === 16 ||
                            fullListing.Sales[0].PaymentMethod === 16 ||
                            fullListing.Sales[0].PaymentMethod === 'Other' ||
                            fullListing.Sales[0].PaymentType === 'Other') {
                    return 'Other';
                  }

                  return 'None';
                })(),
                paymentAmount: parseFloat(
                  fullListing.Sales[0].PaymentDetails?.PaymentAmount ||
                  fullListing.Sales[0].PaymentAmount ||
                  fullListing.Sales[0].TotalSalePrice || 0
                ),
                paymentMethodFee: parseFloat(
                  fullListing.Sales[0].PaymentDetails?.PaymentMethodFee ||
                  fullListing.Sales[0].PaymentMethodFee || 0
                ),
                gstCollected: parseFloat(
                  fullListing.Sales[0].PaymentDetails?.GstCollected ||
                  fullListing.Sales[0].GstCollected || 0
                ),
                pingEscrowStatus: fullListing.Sales[0].PaymentDetails?.PingEscrowStatus || fullListing.Sales[0].PingEscrowStatus || 'None',
                isPayNowPurchase: fullListing.Sales[0].IsPayNowPurchase || false,
                payNowRefundValue: parseFloat(fullListing.Sales[0].PayNowRefundValue || 0),

                // Refunds - check PaymentDetails first, then fallback to Sales
                refunds: (() => {
                  // Check PaymentDetails first
                  if (fullListing.Sales[0].PaymentDetails?.RefundCollection &&
                      Array.isArray(fullListing.Sales[0].PaymentDetails.RefundCollection)) {
                    return fullListing.Sales[0].PaymentDetails.RefundCollection.map(refund => ({
                      amount: parseFloat(refund.Amount || 0),
                      destination: refund.Destination || '',
                      date: refund.Date ? parseTradeMeDate(refund.Date) : null
                    }));
                  }

                  // Fallback to Sales
                  if (fullListing.Sales[0].RefundCollection &&
                      Array.isArray(fullListing.Sales[0].RefundCollection)) {
                    return fullListing.Sales[0].RefundCollection.map(refund => ({
                      amount: parseFloat(refund.Amount || 0),
                      destination: refund.Destination || '',
                      date: refund.Date ? parseTradeMeDate(refund.Date) : null
                    }));
                  }

                  return [];
                })()
              }
            } : {})
          } : {}),

          // Fault tracking (to be filled in by user)
          isFaulty: false,
          faultCategories: [],
          faultDescription: '',

          // Relist settings (default values)
          relistSettings: {
            initialListings: 1,
            priceReductionPercent: 2, // Default to 2% as per requirements
            autoRelist: true,
            maxRelistCount: 100, // Default to 100 as per requirements
            minPrice: fullListing.StartPrice ? fullListing.StartPrice * 0.5 : 0 // 50% of start price
          },

          // Images
          images: images,

          // Questions
          questions: questions && questions.length ? questions.map(q => {
            // Generate a unique ID if none exists
            const questionId = q.ListingQuestionId ? q.ListingQuestionId.toString() : `generated-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;

            // Ensure question text is not empty
            const questionText = q.Comment || 'Question text not available';

            // For already answered questions, set answeredBy to 'Imported'
            const isAnswered = !!q.Answer;
            const answeredByValue = isAnswered ? 'Imported' : null;

            return {
              trademeQuestionId: questionId,
              question: questionText,
              answer: q.Answer || '',
              askerName: q.AskingMember?.Nickname || 'Anonymous',
              askDate: q.CommentDate ? parseTradeMeDate(q.CommentDate) : new Date(),
              answerDate: q.Answer && q.AnswerDate ? parseTradeMeDate(q.AnswerDate) : null,
              isAnswered: isAnswered,
              answeredBy: answeredByValue, // 'Imported' for questions already answered on TradeMe
              originalListingId: fullListing.ListingId ? fullListing.ListingId.toString() : '',
              comment: ''
            };
          }) : [],

          // Notes
          notes: [],

          // Listing history
          listingHistory: [{
            trademeListingId: fullListing.ListingId.toString(),
            startDate: parseTradeMeDate(fullListing.StartDate),
            endDate: parseTradeMeDate(fullListing.EndDate),
            startPrice: fullListing.StartPrice || 0,
            buyNowPrice: fullListing.BuyNowPrice || 0,
            reservePrice: fullListing.ReservePrice || 0,
            views: fullListing.ViewCount || 0,
            watchers: fullListing.WatchCount || 0,
            bids: fullListing.BidCount || 0,
            status: listing.status || 'active',
            createdBy: 'Imported', // For imported listings, set createdBy to 'Imported'
            // Add sold information if this is a sold item
            ...(listing.status === 'sold' ? {
              soldPrice: fullListing.SoldPrice || fullListing.StartPrice || 0,
              soldDate: parseTradeMeDate(fullListing.SoldDate || fullListing.EndDate),
              buyer: fullListing.Winner || fullListing.BuyerNickname || ''
            } : {})
          }],

          // Shipping options - use our improved parser
          shippingOptions: parseShippingOptions(fullListing),

          // Payment methods
          paymentMethods: parsePaymentMethods(fullListing.PaymentMethods),

          // Other payment method description
          otherPaymentMethod: '',

          // Pickup options
          pickup: fullListing.Pickup || 'Allow',

          // TradeMe specific fields
          isNew: fullListing.IsNew || false,
          isOrNearOffer: fullListing.IsOrNearOffer || false,
          duration: fullListing.Duration || 7,
          tradeMeCategoryId: fullListing.CategoryId ? fullListing.CategoryId.toString() : '',

          // Promotional flags
          isBold: fullListing.IsBold || false,
          isFeatured: fullListing.IsFeatured || false,
          hasGallery: fullListing.HasGallery || false,
          isHighlighted: fullListing.IsHighlighted || false,

          // Quantity
          quantity: fullListing.Quantity || 1,

          // Environment
          environment: settings.environment,

          // Metadata
          createdBy: 'Imported', // For imported listings, set createdBy to 'Imported'

          // Import information
          importedAt: new Date(),
          importedBy: user._id,
          lastSyncedAt: new Date()
        };

        // Add the item data to the import log's fetchedItems array
        importLog.fetchedItems.push(itemData);

        // Update statistics
        stats.newItems++;
        stats.itemsProcessed++;

        importLog.addLog(`Successfully imported listing ${listing.ListingId}`);
        importLog.updateStats(stats);
        await importLog.save();

        return {
          success: true,
          item: itemData
        };
      } catch (error) {
        console.error(`Error processing listing ${listing.ListingId}:`, error);

        stats.failedItems++;
        stats.itemsProcessed++;

        importLog.addLog(`Error processing listing ${listing.ListingId}: ${error.message}`, 'error');
        importLog.updateStats(stats);
        await importLog.save();

        return {
          success: false,
          error: error.message
        };
      }
    };

    // Determine which endpoint to use based on the type
    const endpoint = type === 'active'
      ? '/MyTradeMe/SellingItems/All.json'
      : '/MyTradeMe/SoldItems/Last45Days.json';

    // Fetch listings from TradeMe
    importLog.addLog(`Fetching ${type} listings from TradeMe`);
    await importLog.save();

    const listings = await fetchAllPages(endpoint);

    importLog.addLog(`Found ${listings.length} ${type} listings`);
    await importLog.save();

    // Process each listing
    const results = [];
    for (const listing of listings) {
      // Set the status based on the type
      if (listing) {
        listing.status = type === 'active' ? 'active' : 'sold';
      }
      const result = await processListing(listing);
      results.push(result);
    }

    // Save the import log with all the fetched items
    importLog.complete(true);
    importLog.updateStats(stats);
    importLog.addLog('Import completed successfully');
    await importLog.save();

    return {
      success: true,
      stats,
      itemCount: importLog.fetchedItems.length,
      importLogId: importLog._id
    };
  } catch (error) {
    console.error('Error importing TradeMe listings:', error);

    importLog.fail(error);
    importLog.addLog(`Import failed: ${error.message}`, 'error');
    await importLog.save();

    return {
      success: false,
      error: error.message,
      importLogId: importLog._id
    };
  }
}

module.exports = {
  syncListingsFromTradeMe,
  syncQuestionsFromTradeMe,
  importUserListings
};
