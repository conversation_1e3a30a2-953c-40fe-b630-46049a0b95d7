import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
// import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import {
  getDeviceCheckerServices,
  lookupDevice,
  getDeviceCheckerHistory,
  getDeviceCheckerBalance,
  DeviceCheckerLookup
} from '@/api/deviceChecker';
import { useToast } from '@/hooks/useToast';
// import { useAuth } from '@/contexts/AuthContext';
import {
  Loader2,
  AlertCircle,
  CheckCircle,
  XCircle,
  Search,
  RefreshCw,
  Clock,
  DollarSign,
  // InfoIcon
} from 'lucide-react';
import { PreviousLookupModal } from '@/components/PreviousLookupModal';
import { format } from 'date-fns';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious
} from '@/components/ui/pagination';

export function DeviceChecker() {
  const [deviceIdentifier, setDeviceIdentifier] = useState('');
  const [selectedService, setSelectedService] = useState('');
  const [services, setServices] = useState<Array<{
    serviceId: string;
    name: string;
    cost: number;
    description?: string;
    enabled: boolean;
  }>>([]);
  const [lookupResult, setLookupResult] = useState<any>(null);
  const [selectedLookupDetails, setSelectedLookupDetails] = useState<DeviceCheckerLookup | null>(null);
  // We'll use showPreviousLookupModal instead of a separate isExistingLookup state
  const [existingLookupDate, setExistingLookupDate] = useState<string | null>(null);
  const [showPreviousLookupModal, setShowPreviousLookupModal] = useState(false);
  const [existingLookupInfo, setExistingLookupInfo] = useState<{
    serviceName: string;
    cost: number;
    username: string;
  }>({ serviceName: '', cost: 0, username: '' });
  const [loading, setLoading] = useState(false);
  const [loadingServices, setLoadingServices] = useState(true);
  const [loadingHistory, setLoadingHistory] = useState(true);
  const [lookupHistory, setLookupHistory] = useState<DeviceCheckerLookup[]>([]);
  const [historyPage, setHistoryPage] = useState(1);
  const [historyTotalPages, setHistoryTotalPages] = useState(1);
  const [accountBalance, setAccountBalance] = useState<number | null>(null);
  const [loadingBalance, setLoadingBalance] = useState(false);
  const { toast } = useToast();


  useEffect(() => {
    fetchServices();
    fetchHistory();
    fetchBalance();
  }, []);

  useEffect(() => {
    fetchHistory();
  }, [historyPage]);

  const fetchServices = async () => {
    try {
      setLoadingServices(true);
      const response = await getDeviceCheckerServices();
      if (response.success && response.services) {
        // Filter only enabled services and sort alphabetically
        const enabledServices = response.services
          .filter(service => service.enabled)
          .sort((a, b) => a.name.localeCompare(b.name));

        setServices(enabledServices);

        // Set the first service as selected if available
        if (enabledServices.length > 0 && !selectedService) {
          setSelectedService(enabledServices[0].serviceId);
        }
      } else {
        toast({
          title: "Error",
          description: response.error || "Failed to fetch services",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to fetch services",
        variant: "destructive",
      });
    } finally {
      setLoadingServices(false);
    }
  };

  const fetchHistory = async () => {
    try {
      setLoadingHistory(true);
      // Fetch history for all users by not specifying a userId
      const response = await getDeviceCheckerHistory(historyPage, 10);
      if (response.success && response.lookups) {
        setLookupHistory(response.lookups);
        if (response.pagination) {
          setHistoryTotalPages(response.pagination.totalPages);
        }
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to fetch lookup history",
        variant: "destructive",
      });
    } finally {
      setLoadingHistory(false);
    }
  };

  const fetchBalance = async () => {
    try {
      setLoadingBalance(true);
      const response = await getDeviceCheckerBalance();
      if (response.success) {
        setAccountBalance(response.balance || 0);
      }
    } catch (error: any) {
      console.error('Error fetching balance:', error);
    } finally {
      setLoadingBalance(false);
    }
  };

  const handleLookup = async (checkAgain = false) => {
    if (!deviceIdentifier.trim()) {
      toast({
        title: "Error",
        description: "Please enter a device identifier",
        variant: "destructive",
      });
      return;
    }

    if (!selectedService) {
      toast({
        title: "Error",
        description: "Please select a service",
        variant: "destructive",
      });
      return;
    }

    try {
      setLoading(true);
      setLookupResult(null);
      setExistingLookupDate(null);
      setShowPreviousLookupModal(false);

      const response = await lookupDevice({
        deviceIdentifier: deviceIdentifier.trim(),
        serviceId: selectedService,
        checkAgain
      });

      if (response.success) {
        setLookupResult(response.result);

        // Handle existing lookup
        if (response.isExisting && !checkAgain) {
          if (response.createdAt) {
            setExistingLookupDate(response.createdAt);
          }

          // Find the service name for the existing lookup
          const service = services.find(s => s.serviceId === selectedService);

          // Set existing lookup info for the modal
          setExistingLookupInfo({
            serviceName: service?.name || 'Unknown Service',
            cost: service?.cost || 0,
            username: lookupHistory[0]?.username || 'Unknown User' // Use the username from history as fallback
          });

          // Show the modal
          setShowPreviousLookupModal(true);
          return; // Don't proceed with showing results yet
        }

        // Refresh history and balance after a successful lookup
        fetchHistory();
        fetchBalance();
      } else {
        toast({
          title: "Error",
          description: response.error || "Failed to lookup device",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to lookup device",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const renderLookupResult = () => {
    if (!lookupResult) return null;

    const isSuccess = lookupResult.success === true;

    return (
      <div className="mt-6 space-y-4">
        <Card className={isSuccess ? "border-green-500/50" : "border-red-500/50"}>
          <CardHeader className={isSuccess ? "bg-green-500/10" : "bg-red-500/10"}>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center">
                {isSuccess ? (
                  <CheckCircle className="mr-2 h-5 w-5 text-green-500" />
                ) : (
                  <XCircle className="mr-2 h-5 w-5 text-red-500" />
                )}
                {isSuccess ? "Lookup Successful" : "Lookup Failed"}
              </CardTitle>
              <Badge variant={isSuccess ? "success" : "destructive"}>
                {isSuccess ? "SUCCESS" : "ERROR"}
              </Badge>
            </div>
            <CardDescription>
              {lookupResult.message || (isSuccess ? "Device information retrieved successfully" : "Failed to retrieve device information")}
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-6">
            <div className="space-y-4">
              {isSuccess && lookupResult.response && (
                <div
                  className="bg-muted p-4 rounded-md text-sm"
                  dangerouslySetInnerHTML={{ __html: lookupResult.response }}
                />
              )}

              {!isSuccess && lookupResult.error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Error</AlertTitle>
                  <AlertDescription>{lookupResult.error}</AlertDescription>
                </Alert>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  const renderHistoryItem = (lookup: DeviceCheckerLookup) => {
    const isSuccess = lookup.status === 'success' && lookup.result?.success === true;
    const isSelected = selectedLookupDetails?._id === lookup._id;

    return (
      <div key={lookup._id} className="mb-2">
        <div
          className={`flex items-center justify-between p-2 rounded-md cursor-pointer hover:bg-muted/50 ${isSelected ? 'bg-muted' : ''}`}
          onClick={() => {
            // Toggle selection
            if (isSelected) {
              setSelectedLookupDetails(null);
            } else {
              setSelectedLookupDetails(lookup);
              setDeviceIdentifier(lookup.deviceIdentifier);
              setSelectedService(lookup.serviceId);
              setLookupResult(lookup.result);
              setExistingLookupDate(lookup.createdAt);

              // Set existing lookup info for the modal
              setExistingLookupInfo({
                serviceName: lookup.serviceName,
                cost: lookup.cost,
                username: lookup.username
              });
            }
          }}
        >
          <div className="flex items-center space-x-2 overflow-hidden">
            {isSuccess ? (
              <CheckCircle className="h-4 w-4 flex-shrink-0 text-green-500" />
            ) : (
              <XCircle className="h-4 w-4 flex-shrink-0 text-red-500" />
            )}
            <span className="text-sm font-medium truncate">{lookup.deviceIdentifier}</span>
          </div>
          <div className="flex items-center space-x-3">
            <span className="text-xs text-muted-foreground">
              {format(new Date(lookup.createdAt), 'MMM d, h:mm a')}
            </span>
            <Badge variant={isSuccess ? "success" : "destructive"} className="text-xs">
              {isSuccess ? "SUCCESS" : "ERROR"}
            </Badge>
          </div>
        </div>

        {/* Expanded details when selected */}
        {isSelected && (
          <div className="mt-1 mb-3 ml-6 p-3 text-sm border-l-2 border-muted bg-muted/20 rounded-r-md">
            <div className="grid grid-cols-2 gap-2">
              <div className="text-muted-foreground">Service:</div>
              <div>{lookup.serviceName}</div>

              <div className="text-muted-foreground">Cost:</div>
              <div>${lookup.cost.toFixed(2)}</div>

              <div className="text-muted-foreground">Date:</div>
              <div>{format(new Date(lookup.createdAt), 'MMM d, yyyy h:mm a')}</div>

              <div className="text-muted-foreground">User:</div>
              <div>{lookup.username}</div>
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Previous Lookup Modal */}
      <PreviousLookupModal
        isOpen={showPreviousLookupModal}
        onClose={() => setShowPreviousLookupModal(false)}
        onCheckAgain={() => {
          setShowPreviousLookupModal(false);
          handleLookup(true);
        }}
        existingLookupDate={existingLookupDate}
        serviceName={existingLookupInfo.serviceName}
        cost={existingLookupInfo.cost}
        username={existingLookupInfo.username}
        loading={loading}
      />
      <div className="border-b">
        <div className="flex h-16 items-center px-4">
          <h1 className="text-2xl font-bold">Device Checker</h1>
          {accountBalance !== null && (
            <div className="ml-auto flex items-center">
              <Badge variant="outline" className="flex items-center gap-1">
                <DollarSign className="h-3 w-3" />
                Balance: ${accountBalance.toFixed(2)}
                {loadingBalance && <Loader2 className="ml-1 h-3 w-3 animate-spin" />}
              </Badge>
              <Button
                variant="ghost"
                size="icon"
                className="ml-2"
                onClick={fetchBalance}
                disabled={loadingBalance}
              >
                <RefreshCw className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-10 gap-6">
        <div className="md:col-span-6 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Device Lookup</CardTitle>
              <CardDescription>
                Enter a device identifier (IMEI or Serial Number) to check its status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="deviceIdentifier">IMEI or Serial Number</Label>
                  <Input
                    id="deviceIdentifier"
                    placeholder="Enter IMEI or Serial Number"
                    value={deviceIdentifier}
                    onChange={(e) => setDeviceIdentifier(e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="service">Service</Label>
                  {loadingServices ? (
                    <div className="flex items-center space-x-2">
                      <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                      <span className="text-sm text-muted-foreground">Loading services...</span>
                    </div>
                  ) : services.length === 0 ? (
                    <Alert variant="destructive">
                      <AlertCircle className="h-4 w-4" />
                      <AlertTitle>No services available</AlertTitle>
                      <AlertDescription>
                        No enabled services found. Please contact an administrator to enable services.
                      </AlertDescription>
                    </Alert>
                  ) : (
                    <div className="space-y-2">
                      <Select value={selectedService} onValueChange={setSelectedService}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a service" />
                        </SelectTrigger>
                        <SelectContent>
                          {/* Sort services alphabetically by name */}
                          {[...services]
                            .sort((a, b) => a.name.localeCompare(b.name))
                            .map((service) => (
                              <SelectItem key={service.serviceId} value={service.serviceId}>
                                {service.name} (${service.cost.toFixed(2)})
                              </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>

                      {/* Display description below the dropdown */}
                      {(() => {
                        const selectedServiceData = selectedService ? services.find(s => s.serviceId === selectedService) : null;
                        return selectedServiceData?.description ? (
                          <div
                            className="mt-2 text-sm text-muted-foreground"
                            dangerouslySetInnerHTML={{ __html: selectedServiceData.description }}
                          />
                        ) : null;
                      })()}
                    </div>
                  )}
                </div>

                <Button
                  onClick={() => handleLookup(false)}
                  disabled={loading || loadingServices || services.length === 0 || !deviceIdentifier.trim() || !selectedService}
                  className="w-full"
                >
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Checking...
                    </>
                  ) : (
                    <>
                      <Search className="mr-2 h-4 w-4" />
                      Check Device
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>

          {renderLookupResult()}
        </div>

        <div className="md:col-span-4 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Shared Lookup History</CardTitle>
              <CardDescription>
                Recent device lookups from all users
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loadingHistory ? (
                <div className="flex justify-center items-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : lookupHistory.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Clock className="mx-auto h-8 w-8 mb-2 opacity-50" />
                  <p>No lookup history found</p>
                </div>
              ) : (
                <div>
                  <div className="space-y-1 mb-4">
                    {lookupHistory.map(renderHistoryItem)}
                  </div>

                  {historyTotalPages > 1 && (
                    <Pagination>
                      <PaginationContent>
                        <PaginationItem>
                          <PaginationPrevious
                            href="#"
                            onClick={(e) => {
                              e.preventDefault();
                              if (historyPage > 1) {
                                setHistoryPage(p => Math.max(1, p - 1));
                              }
                            }}
                            className={historyPage === 1 ? 'pointer-events-none opacity-50' : ''}
                          />
                        </PaginationItem>

                        {Array.from({ length: Math.min(5, historyTotalPages) }, (_, i) => {
                          const pageNum = i + 1;
                          return (
                            <PaginationItem key={pageNum}>
                              <PaginationLink
                                isActive={pageNum === historyPage}
                                onClick={() => setHistoryPage(pageNum)}
                              >
                                {pageNum}
                              </PaginationLink>
                            </PaginationItem>
                          );
                        })}

                        {historyTotalPages > 5 && (
                          <>
                            <PaginationItem>
                              <PaginationEllipsis />
                            </PaginationItem>
                            <PaginationItem>
                              <PaginationLink
                                isActive={historyTotalPages === historyPage}
                                onClick={() => setHistoryPage(historyTotalPages)}
                              >
                                {historyTotalPages}
                              </PaginationLink>
                            </PaginationItem>
                          </>
                        )}

                        <PaginationItem>
                          <PaginationNext
                            href="#"
                            onClick={(e) => {
                              e.preventDefault();
                              if (historyPage < historyTotalPages) {
                                setHistoryPage(p => Math.min(historyTotalPages, p + 1));
                              }
                            }}
                            className={historyPage === historyTotalPages ? 'pointer-events-none opacity-50' : ''}
                          />
                        </PaginationItem>
                      </PaginationContent>
                    </Pagination>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

export default DeviceChecker;
