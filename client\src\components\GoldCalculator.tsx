import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import useMetalPrices from '@/hooks/useMetalPrices';
import { Loader2 } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';
import { useToast } from '@/components/ui/use-toast';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import useAuth from '@/hooks/useAuth';

// Metal purity table data
const goldPurityTable = [
  { name: "Gold 6K", purity: 0.25 },
  { name: "Gold 8K", purity: 0.333 },
  { name: "Gold 9K", purity: 0.375 },
  { name: "Gold 10K", purity: 0.417 },
  { name: "Gold 12K", purity: 0.5 },
  { name: "Gold 14K", purity: 0.585 },
  { name: "Gold 18K", purity: 0.75 },
  { name: "Gold 21K", purity: 0.875 },
  { name: "Gold 22K", purity: 0.916 },
  { name: "Gold 24K", purity: 0.999 }
];

// Sorted alphabetically
const metalPurityTable = [
  ...goldPurityTable,
  { name: "Palladium", purity: 0.95 },
  { name: "Platinum", purity: 0.95 },
  { name: "Silver Sterling", purity: 0.925 },
  { name: "Silver", purity: 0.999 }
];

const GoldCalculator = () => {
  const { metalPrices, loading: metalPricesLoading } = useMetalPrices();
  const { user } = useAuth();
  const { toast } = useToast();

  // Weight Calculator State
  const [weightCalcState, setWeightCalcState] = useState({
    metal: "Gold",
    purity: "0.999",
    weight: "",
    unit: "g",
    result: null
  });

  // MHJ Calculator State
  const [mhjCalcState, setMhjCalcState] = useState({
    mhjGoldPrice: "",
    weight: "",
    purity: "0.999",
    unit: "g",
    result: null
  });

  const handleWeightCalcChange = (e) => {
    const { name, value } = e.target;
    setWeightCalcState({
      ...weightCalcState,
      [name]: value
    });
  };

  const handleWeightCalcSelectChange = (name, value) => {
    setWeightCalcState({
      ...weightCalcState,
      [name]: value
    });
  };

  const handleMhjCalcChange = (e) => {
    const { name, value } = e.target;
    setMhjCalcState({
      ...mhjCalcState,
      [name]: value
    });
  };

  const handleMhjCalcSelectChange = (name, value) => {
    setMhjCalcState({
      ...mhjCalcState,
      [name]: value
    });
  };

  const calculateWeightValue = () => {
    try {
      if (!weightCalcState.weight || !weightCalcState.purity) {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Please enter both weight and purity"
        });
        return;
      }

      const metalPrice = metalPrices.find(p => p.metal.toLowerCase() === weightCalcState.metal.toLowerCase());
      
      if (!metalPrice) {
        toast({
          variant: "destructive",
          title: "Error",
          description: `Price for ${weightCalcState.metal} not found`
        });
        return;
      }

      let weight = parseFloat(weightCalcState.weight);
      
      // Convert to grams if needed
      if (weightCalcState.unit === "oz") {
        weight = weight * 31.1035; // Troy ounce to grams
      } else if (weightCalcState.unit === "dwt") {
        weight = weight * 1.55517; // Pennyweight to grams
      }

      const purity = parseFloat(weightCalcState.purity);
      const pricePerGram = metalPrice.pricePerOunce / 31.1035; // Convert price per ounce to price per gram
      
      const value = weight * purity * pricePerGram;
      
      setWeightCalcState({
        ...weightCalcState,
        result: value
      });
    } catch (error) {
      console.error("Error calculating value:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to calculate value"
      });
    }
  };

  const calculateMhjValue = () => {
    try {
      if (!mhjCalcState.mhjGoldPrice || !mhjCalcState.weight || !mhjCalcState.purity) {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Please fill in all fields"
        });
        return;
      }

      let weight = parseFloat(mhjCalcState.weight);
      
      // Convert to grams if needed
      if (mhjCalcState.unit === "oz") {
        weight = weight * 31.1035; // Troy ounce to grams
      } else if (mhjCalcState.unit === "dwt") {
        weight = weight * 1.55517; // Pennyweight to grams
      }

      const purity = parseFloat(mhjCalcState.purity);
      const mhjPricePerGram = parseFloat(mhjCalcState.mhjGoldPrice);
      
      const value = weight * purity * mhjPricePerGram;
      
      setMhjCalcState({
        ...mhjCalcState,
        result: value
      });
    } catch (error) {
      console.error("Error calculating MHJ value:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to calculate MHJ value"
      });
    }
  };

  return (
    <div className="container mx-auto p-4">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Current Prices Section */}
        <div>
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Current Metal Prices</CardTitle>
              <CardDescription>Last updated prices for precious metals</CardDescription>
            </CardHeader>
            <CardContent>
              {metalPricesLoading ? (
                <div className="flex justify-center items-center h-40">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
              ) : (
                <div className="space-y-4">
                  {metalPrices.map((price) => (
                    <div key={price._id} className="flex justify-between border-b pb-2">
                      <span className="font-semibold">{price.metal}</span>
                      <span>{formatCurrency(price.pricePerOunce)} / oz</span>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Metal Purity Reference</CardTitle>
              <CardDescription>Common metals and their purity values</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Metal</TableHead>
                    <TableHead className="text-right">Purity</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {metalPurityTable.map((metal) => (
                    <TableRow key={metal.name}>
                      <TableCell>{metal.name}</TableCell>
                      <TableCell className="text-right">{metal.purity}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </div>

        {/* Calculators Section */}
        <div>
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Weight Calculator</CardTitle>
              <CardDescription>Calculate value based on weight and current prices</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="metal">Metal</Label>
                    <Select 
                      value={weightCalcState.metal}
                      onValueChange={(value) => handleWeightCalcSelectChange("metal", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select Metal" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Gold">Gold</SelectItem>
                        <SelectItem value="Silver">Silver</SelectItem>
                        <SelectItem value="Platinum">Platinum</SelectItem>
                        <SelectItem value="Palladium">Palladium</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="purity">Purity</Label>
                    <Input 
                      id="purity" 
                      name="purity" 
                      placeholder="e.g. 0.999" 
                      value={weightCalcState.purity}
                      onChange={handleWeightCalcChange}
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="weight">Weight</Label>
                    <Input 
                      id="weight" 
                      name="weight" 
                      type="number" 
                      placeholder="Enter weight" 
                      value={weightCalcState.weight}
                      onChange={handleWeightCalcChange}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="unit">Unit</Label>
                    <Select 
                      value={weightCalcState.unit}
                      onValueChange={(value) => handleWeightCalcSelectChange("unit", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select Unit" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="g">Grams (g)</SelectItem>
                        <SelectItem value="oz">Troy Ounces (oz)</SelectItem>
                        <SelectItem value="dwt">Pennyweight (dwt)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <Button className="w-full" onClick={calculateWeightValue}>Calculate</Button>
                {weightCalcState.result !== null && (
                  <div className="mt-4 p-4 border rounded-md bg-muted">
                    <p className="text-lg">Estimated Value: <span className="font-bold">{formatCurrency(weightCalcState.result)}</span></p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>MHJ Calculator</CardTitle>
              <CardDescription>Calculate value based on MHJ price per gram</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="mhjGoldPrice">MHJ Price Per Gram</Label>
                  <Input 
                    id="mhjGoldPrice" 
                    name="mhjGoldPrice" 
                    type="number" 
                    placeholder="Enter MHJ price per gram" 
                    value={mhjCalcState.mhjGoldPrice}
                    onChange={handleMhjCalcChange}
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="mhjWeight">Weight</Label>
                    <Input 
                      id="mhjWeight" 
                      name="weight" 
                      type="number" 
                      placeholder="Enter weight" 
                      value={mhjCalcState.weight}
                      onChange={handleMhjCalcChange}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="mhjUnit">Unit</Label>
                    <Select 
                      value={mhjCalcState.unit}
                      onValueChange={(value) => handleMhjCalcSelectChange("unit", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select Unit" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="g">Grams (g)</SelectItem>
                        <SelectItem value="oz">Troy Ounces (oz)</SelectItem>
                        <SelectItem value="dwt">Pennyweight (dwt)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="mhjPurity">Purity</Label>
                  <Input 
                    id="mhjPurity" 
                    name="purity" 
                    placeholder="e.g. 0.999" 
                    value={mhjCalcState.purity}
                    onChange={handleMhjCalcChange}
                  />
                </div>
                <Button className="w-full" onClick={calculateMhjValue}>Calculate</Button>
                {mhjCalcState.result !== null && (
                  <div className="mt-4 p-4 border rounded-md bg-muted">
                    <p className="text-lg">Estimated Value: <span className="font-bold">{formatCurrency(mhjCalcState.result)}</span></p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default GoldCalculator;