import { useState, useEffect, useRef } from 'react';
import { toast } from 'sonner';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Loader2, Eye, RefreshCw, ShoppingCart, Store, ListFilter } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { PaginationControls } from '@/components/ui/pagination-controls';
import {
  getFeedback,
  markFeedbackAsRead,
  markAllFeedbackAsRead,
  getFeedbackStats,
  TradeMeFeedbackItem,
  FeedbackStats
} from '@/api/tradeMeFeedback';

interface FeedbackResponse {
  success: boolean;
  feedback: TradeMeFeedbackItem[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
  counts: {
    positive: number;
    neutral: number;
    negative: number;
  };
  error?: string;
}

export function TradeMeFeedback() {
  const [activeTab, setActiveTab] = useState<'received' | 'given' | 'all'>('all');
  const [activeRating, setActiveRating] = useState<'Positive' | 'Neutral' | 'Negative' | null>(null);
  const [feedback, setFeedback] = useState<TradeMeFeedbackItem[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [stats, setStats] = useState<FeedbackStats | null>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const feedbackTimersRef = useRef<Record<string, NodeJS.Timeout>>({});
  const [counts, setCounts] = useState({
    positive: 0,
    neutral: 0,
    negative: 0
  });
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    limit: 20,
    pages: 0
  });

  const fetchFeedback = async (type: 'received' | 'given' | 'all' = activeTab, page: number = 1, rating: 'Positive' | 'Neutral' | 'Negative' | null = activeRating, customLimit?: number) => {
    try {
      setLoading(true);

      // Build options
      const options: any = {
        type,
        page,
        limit: customLimit || pagination.limit
      };

      console.log(`Client: Fetching feedback with limit=${options.limit}`);

      // Add rating filter if selected
      if (rating) {
        options.rating = rating;
      }

      const response: FeedbackResponse = await getFeedback(options);

      if (response.success) {
        setFeedback(response.feedback);
        setPagination(response.pagination);
        setCounts(response.counts);
      } else {
        toast.error('Error', {
          description: response.error || 'Failed to fetch feedback'
        });
      }
    } catch (error: any) {
      toast.error('Error', {
        description: error.message || 'Failed to fetch feedback'
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      setLoading(true);
      const response = await getFeedbackStats();

      if (response.success) {
        setStats(response.stats);
      } else {
        console.error('Failed to fetch feedback stats:', response.error);
      }
    } catch (error) {
      console.error('Error fetching feedback stats:', error);
    } finally {
      setLoading(false);
    }
  };

  // Function to mark feedback as read after it's been visible for 3 seconds
  const markFeedbackAsReadAfterDelay = (feedbackId: string) => {
    // Clear any existing timer for this feedback
    if (feedbackTimersRef.current[feedbackId]) {
      clearTimeout(feedbackTimersRef.current[feedbackId]);
    }

    // Set a new timer
    feedbackTimersRef.current[feedbackId] = setTimeout(async () => {
      try {
        // Check if the feedback is already read
        const feedbackItem = feedback.find(item => item.feedbackId === feedbackId);
        if (feedbackItem && !feedbackItem.isRead) {
          const response = await markFeedbackAsRead(feedbackId);

          if (response.success) {
            // Update the local state
            setFeedback(prevFeedback =>
              prevFeedback.map(item =>
                item.feedbackId === feedbackId
                  ? { ...item, isRead: true }
                  : item
              )
            );
            console.log(`Feedback ${feedbackId} automatically marked as read after 3 seconds`);
          }
        }
      } catch (error) {
        console.error(`Error auto-marking feedback ${feedbackId} as read:`, error);
      }
    }, 3000); // 3 seconds delay
  };

  const handleTabChange = (value: 'received' | 'given' | 'all') => {
    setActiveTab(value);
    fetchFeedback(value, 1, activeRating);
  };

  const handleRatingFilter = (rating: 'Positive' | 'Neutral' | 'Negative' | null) => {
    // Toggle off if clicking the same rating
    const newRating = rating === activeRating ? null : rating;
    setActiveRating(newRating);
    fetchFeedback(activeTab, 1, newRating);
  };

  const handlePageChange = (page: number) => {
    fetchFeedback(activeTab, page, activeRating);
  };

  const handleLimitChange = (limit: number) => {
    console.log(`Client: Changing limit to ${limit}`);
    // First update the pagination state
    setPagination(prev => ({ ...prev, limit, page: 1 }));
    // Then fetch with the new limit - pass it explicitly to ensure it's used
    setTimeout(() => {
      fetchFeedback(activeTab, 1, activeRating, limit);
    }, 0);
  };

  const handleRefresh = () => {
    fetchFeedback(activeTab, 1, activeRating);
    fetchStats();
  };

  const handleMarkAllAsRead = async () => {
    try {
      const response = await markAllFeedbackAsRead();

      if (response.success) {
        // Update all feedback items to be read
        setFeedback(prevFeedback =>
          prevFeedback.map(item => ({ ...item, isRead: true }))
        );

        toast.success('All feedback marked as seen');
      } else {
        toast.error('Error', {
          description: response.error || 'Failed to mark all feedback as seen'
        });
      }
    } catch (error: any) {
      toast.error('Error', {
        description: error.message || 'Failed to mark all feedback as seen'
      });
    }
  };



  // Set up the IntersectionObserver to detect when feedback items are visible
  useEffect(() => {
    // Create a new IntersectionObserver
    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const feedbackId = entry.target.getAttribute('data-feedback-id');
          if (feedbackId) {
            if (entry.isIntersecting) {
              // Feedback item is visible, start the timer to mark it as read
              markFeedbackAsReadAfterDelay(feedbackId);
            } else {
              // Feedback item is no longer visible, clear the timer
              if (feedbackTimersRef.current[feedbackId]) {
                clearTimeout(feedbackTimersRef.current[feedbackId]);
                delete feedbackTimersRef.current[feedbackId];
              }
            }
          }
        });
      },
      {
        root: null, // Use the viewport as the root
        rootMargin: '0px',
        threshold: 0.5 // At least 50% of the item must be visible
      }
    );

    // Clean up the observer when the component unmounts
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }

      // Clear all timers
      Object.values(feedbackTimersRef.current).forEach(timer => {
        clearTimeout(timer);
      });
      feedbackTimersRef.current = {};
    };
  }, []);

  // Observe feedback items when they are rendered
  useEffect(() => {
    // First, disconnect any existing observations
    if (observerRef.current) {
      observerRef.current.disconnect();
    }

    // Then observe all feedback items
    const feedbackRows = document.querySelectorAll('[data-feedback-id]');
    feedbackRows.forEach(row => {
      if (observerRef.current) {
        observerRef.current.observe(row);
      }
    });
  }, [feedback]);

  // Initial data fetch
  useEffect(() => {
    fetchFeedback();
    fetchStats();
  }, []);

  return (
    <div className="space-y-6">
      {/* Feedback Stats Card */}
      {stats && (
        <Card>
          <CardHeader className="pb-2">
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>Feedback Summary</CardTitle>
                <CardDescription>
                  Overall feedback statistics for your TradeMe account
                </CardDescription>
              </div>
              <Button variant="outline" size="sm" onClick={handleRefresh}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="mt-4 mb-6"></div>
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-4">
              <div className="p-4 bg-muted/30 rounded-lg text-center">
                <div className="text-2xl font-bold">{stats.totalCount}</div>
                <div className="text-sm text-muted-foreground">Total Feedback</div>
              </div>
              <div className="p-4 bg-green-100 dark:bg-green-900/20 rounded-lg text-center">
                <div className="text-2xl font-bold text-green-600 dark:text-green-500">
                  {Number(stats.positiveFeedbackPercentage).toFixed(4)}%
                </div>
                <div className="text-sm text-muted-foreground">Positive %</div>
              </div>
              <div className="p-4 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg text-center">
                <div className="text-2xl font-bold text-yellow-600 dark:text-yellow-500">{stats.totalPositive}</div>
                <div className="text-sm text-muted-foreground">Positive</div>
              </div>
              <div className="p-4 bg-blue-100 dark:bg-blue-900/20 rounded-lg text-center">
                <div className="text-2xl font-bold text-blue-600 dark:text-blue-500">{stats.totalNeutral}</div>
                <div className="text-sm text-muted-foreground">Neutral</div>
              </div>
              <div className="p-4 bg-red-100 dark:bg-red-900/20 rounded-lg text-center">
                <div className="text-2xl font-bold text-red-600 dark:text-red-500">{stats.totalNegative}</div>
                <div className="text-sm text-muted-foreground">Negative</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Feedback List Card */}
      <Card>
        <CardHeader className="pb-2">
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>TradeMe Feedback</CardTitle>
              <CardDescription>View and manage your TradeMe feedback</CardDescription>
            </div>
            <Button variant="ghost" size="sm" onClick={handleMarkAllAsRead}>
              <Eye className="h-4 w-4 mr-2" />
              Mark All as Seen
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={(value) => handleTabChange(value as 'received' | 'given' | 'all')}>
            <div className="flex justify-between items-center mb-4">
              <TabsList>
              <TabsTrigger value="all" className="flex items-center gap-1">
                  <ListFilter className="h-4 w-4" />
                  All Feedback
                </TabsTrigger>
                <TabsTrigger value="given" className="flex items-center gap-1">
                  <Store className="h-4 w-4" />
                  As Seller
                </TabsTrigger>
                <TabsTrigger value="received" className="flex items-center gap-1">
                  <ShoppingCart className="h-4 w-4" />
                  As Buyer
                </TabsTrigger>
              </TabsList>

              <div className="flex gap-2">
                <Button
                  variant={activeRating === 'Positive' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleRatingFilter('Positive')}
                  className={activeRating === 'Positive' ? 'bg-yellow-500' : ''}
                >
                  <img src="/img/trademe/happy-48.png" alt="Positive" className="h-5 w-5 mr-1" />
                  Positive ({counts.positive})
                </Button>
                <Button
                  variant={activeRating === 'Neutral' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleRatingFilter('Neutral')}
                  className={activeRating === 'Neutral' ? 'bg-blue-600' : ''}
                >
                  <img src="/img/trademe/neutral-48.png" alt="Neutral" className="h-5 w-5 mr-1" />
                  Neutral ({counts.neutral})
                </Button>
                <Button
                  variant={activeRating === 'Negative' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleRatingFilter('Negative')}
                  className={activeRating === 'Negative' ? 'bg-red-600' : ''}
                >
                  <img src="/img/trademe/angry-48.png" alt="Negative" className="h-5 w-5 mr-1" />
                  Negative ({counts.negative})
                </Button>
              </div>
            </div>

            <TabsContent value={activeTab} className="space-y-4">
              {loading ? (
                <div className="flex justify-center items-center h-64">
                  <Loader2 className="h-8 w-8 animate-spin" />
                  <span className="ml-2">Loading feedback...</span>
                </div>
              ) : feedback.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  No feedback found.
                </div>
              ) : (
                <>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[80px]">Rating</TableHead>
                        <TableHead className="w-[120px]">Listing ID</TableHead>
                        <TableHead className="w-[150px]">{activeTab === 'given' ? 'To' : 'From'}</TableHead>
                        <TableHead className="w-[150px]">Date</TableHead>
                        <TableHead>Comment</TableHead>
                        <TableHead className="w-[120px]">Type</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {feedback.map((item) => (
                        <TableRow
                          key={item._id}
                          data-feedback-id={item.feedbackId}
                          className={!item.isRead ? 'bg-blue-50 dark:bg-blue-900/20' : ''}
                        >
                          <TableCell>
                            <div className="flex justify-center">
                              {item.rating === 'Positive' && (
                                <img src="/img/trademe/happy-48.png" alt="Positive" className="h-6 w-6" title="Positive" />
                              )}
                              {item.rating === 'Neutral' && (
                                <img src="/img/trademe/neutral-48.png" alt="Neutral" className="h-6 w-6" title="Neutral" />
                              )}
                              {item.rating === 'Negative' && (
                                <img src="/img/trademe/angry-48.png" alt="Negative" className="h-6 w-6" title="Negative" />
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <a
                              href={`/trademe/listing/${item.listingId}`}
                              className="text-blue-600 hover:underline"
                            >
                              {item.listingId}
                            </a>
                          </TableCell>
                          <TableCell>{item.submittedBy}</TableCell>
                          <TableCell className="whitespace-nowrap">
                            {formatDistanceToNow(new Date(item.submittedDate), { addSuffix: true })}
                          </TableCell>
                          <TableCell className="relative">
                            <div className="whitespace-normal pr-6 pb-4">
                              {item.comment}
                            </div>
                            {item.isRead && (
                              <div className="absolute bottom-2 right-2">
                                <Eye className="h-3 w-3 text-gray-400" />
                              </div>
                            )}
                          </TableCell>
                          <TableCell className="whitespace-nowrap">
                            {item.feedbackType === 'Seller' ? (
                              <Badge variant="outline" className="bg-green-50 text-green-800">
                                <ShoppingCart className="h-3 w-3 mr-1" /> As Buyer
                              </Badge>
                            ) : (
                              <Badge variant="outline" className="bg-blue-50 text-blue-800">
                                <Store className="h-3 w-3 mr-1" /> As Seller
                              </Badge>
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>

                  <div className="mt-4">
                    {pagination.pages > 1 && (
                      <PaginationControls
                        currentPage={pagination.page}
                        totalPages={pagination.pages}
                        onPageChange={handlePageChange}
                        onLimitChange={handleLimitChange}
                        limit={pagination.limit}
                        totalItems={pagination.total}
                      />
                    )}
                  </div>
                </>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}

export default TradeMeFeedback;
