const mongoose = require('mongoose');

const metalPriceSettingsSchema = new mongoose.Schema({
  minBuyPercentage: {
    type: Number,
    required: true,
    default: 70,
    min: 0,
    max: 100
  },
  maxBuyPercentage: {
    type: Number,
    required: true,
    default: 80,
    min: 0,
    max: 100
  },
  meltPercentage: {
    type: Number,
    required: true,
    default: 115,
    min: 0
  },
  mhjMinPercentage: {
    type: Number,
    required: true,
    default: 25,
    min: 0,
    max: 100
  },
  mhjMaxPercentage: {
    type: Number,
    required: true,
    default: 35,
    min: 0,
    max: 100
  },
  apiKey: {
    type: String
  },
  updateFrequencyMinutes: {
    type: Number,
    required: true,
    default: 15,
    min: 5
  },
  lastUpdated: {
    type: Date,
    default: Date.now
  }
}, {
  versionKey: false
});

// There should only be one settings document
metalPriceSettingsSchema.statics.getSettings = async function() {
  let settings = await this.findOne();
  if (!settings) {
    settings = await this.create({});
  }
  return settings;
};

const MetalPriceSettings = mongoose.model('MetalPriceSettings', metalPriceSettingsSchema);

module.exports = MetalPriceSettings;