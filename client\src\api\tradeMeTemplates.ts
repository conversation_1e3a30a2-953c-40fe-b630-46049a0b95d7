import api from './api';

export interface TradeMeTemplate {
  _id: string;
  type: 'question' | 'footer' | 'shipping' | 'withdraw';
  title: string;
  content: string;
  price?: number;
  method?: string;
  isDefault?: boolean;
  createdBy: {
    _id: string;
    username: string;
    fullName: string;
  };
  createdAt: string;
  updatedAt: string;
  updatedBy?: {
    _id: string;
    username: string;
    fullName: string;
  };
}

/**
 * Get templates with optional filtering
 * @param type - Template type (question, footer, shipping, withdraw)
 * @param search - Search term
 * @returns Promise with templates
 */
export const getTemplates = async (
  type?: string,
  search?: string
) => {
  try {
    const params = new URLSearchParams();
    if (type) params.append('type', type);
    if (search) params.append('search', search);

    const response = await api.get(`/trademe/templates?${params.toString()}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching templates:', error);
    throw error;
  }
};

/**
 * Get a template by ID
 * @param id - Template ID
 * @returns Promise with template
 */
export const getTemplateById = async (id: string) => {
  try {
    const response = await api.get(`/trademe/templates/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching template:', error);
    throw error;
  }
};

/**
 * Create a new template
 * @param templateData - Template data
 * @returns Promise with created template
 */
export const createTemplate = async (templateData: Partial<TradeMeTemplate>) => {
  try {
    const response = await api.post('/trademe/templates', templateData);
    return response.data;
  } catch (error) {
    console.error('Error creating template:', error);
    throw error;
  }
};

/**
 * Update a template
 * @param id - Template ID
 * @param templateData - Updated template data
 * @returns Promise with updated template
 */
export const updateTemplate = async (
  id: string,
  templateData: Partial<TradeMeTemplate>
) => {
  try {
    const response = await api.put(`/trademe/templates/${id}`, templateData);
    return response.data;
  } catch (error) {
    console.error('Error updating template:', error);
    throw error;
  }
};

/**
 * Delete a template
 * @param id - Template ID
 * @returns Promise with result
 */
export const deleteTemplate = async (id: string) => {
  try {
    const response = await api.delete(`/trademe/templates/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error deleting template:', error);
    throw error;
  }
};

/**
 * Set a template as default for its type
 * @param id - Template ID
 * @returns Promise with result
 */
export const setDefaultTemplate = async (id: string) => {
  try {
    const response = await api.put(`/trademe/templates/${id}/default`);
    return response.data;
  } catch (error) {
    console.error('Error setting default template:', error);
    throw error;
  }
};

