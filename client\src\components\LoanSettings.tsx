import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Loader2, DollarSign, Save, CheckCircle, Target, CreditCard } from 'lucide-react';
import { useToast } from '@/hooks/useToast';
import { getLoanSettings, updateLoanSettings, LoanSettings as LoanSettingsType } from '@/api/loanApplications';

export function LoanSettings() {
  const [settings, setSettings] = useState<LoanSettingsType | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [submissionBonus, setSubmissionBonus] = useState<number>(0);
  const [fundedBonus, setFundedBonus] = useState<number>(0);
  const [weeklySubmissionGoal, setWeeklySubmissionGoal] = useState<number>(10);
  const [weeklyFundedGoal, setWeeklyFundedGoal] = useState<number>(5);
  const [showSuccess, setShowSuccess] = useState(false);

  const { toast } = useToast();

  // Load settings on initial render
  useEffect(() => {
    fetchSettings();
  }, []);

  // Fetch loan settings
  const fetchSettings = async () => {
    setLoading(true);
    try {
      const response = await getLoanSettings();

      if (response.success) {
        setSettings(response.data);
        setSubmissionBonus(response.data.submissionBonus);
        setFundedBonus(response.data.fundedBonus);
        setWeeklySubmissionGoal(response.data.weeklySubmissionGoal || 10);
        setWeeklyFundedGoal(response.data.weeklyFundedGoal || 5);
      } else {
        toast({
          title: 'Error',
          description: 'Failed to load loan settings',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error fetching loan settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to load loan settings',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Save settings
  const saveSettings = async () => {
    setSaving(true);
    try {
      const response = await updateLoanSettings({
        submissionBonus,
        fundedBonus,
        weeklySubmissionGoal,
        weeklyFundedGoal,
      });

      if (response.success) {
        setSettings(response.data);
        setShowSuccess(true);
        setTimeout(() => setShowSuccess(false), 3000);

        toast({
          title: 'Success',
          description: 'Loan settings updated successfully',
        });
      } else {
        toast({
          title: 'Error',
          description: 'Failed to update loan settings',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error updating loan settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to update loan settings',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Loan Application Settings</CardTitle>
        <CardDescription>Configure bonus amounts and weekly goals for loan applications</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {loading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        ) : (
          <>
            <div className="space-y-6">
              {/* Bonus Settings Section */}
              <div>
                <h3 className="text-lg font-medium mb-4">Bonus Settings</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Submission Bonus */}
                  <div className="space-y-2">
                    <Label htmlFor="submissionBonus">Submission Bonus ($)</Label>
                    <div className="relative">
                      <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                      <Input
                        id="submissionBonus"
                        type="number"
                        min="0"
                        step="0.01"
                        value={submissionBonus}
                        onChange={(e) => setSubmissionBonus(parseFloat(e.target.value) || 0)}
                        className="pl-9"
                      />
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Bonus amount for each submitted loan application
                    </p>
                  </div>

                  {/* Funded Bonus */}
                  <div className="space-y-2">
                    <Label htmlFor="fundedBonus">Funded Bonus ($)</Label>
                    <div className="relative">
                      <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                      <Input
                        id="fundedBonus"
                        type="number"
                        min="0"
                        step="0.01"
                        value={fundedBonus}
                        onChange={(e) => setFundedBonus(parseFloat(e.target.value) || 0)}
                        className="pl-9"
                      />
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Additional bonus amount for each funded loan application
                    </p>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Weekly Goals Section */}
              <div>
                <h3 className="text-lg font-medium mb-4">Weekly Goals</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Set weekly targets for loan submissions and funding. Goals reset every Monday.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Weekly Submission Goal */}
                  <div className="space-y-2">
                    <Label htmlFor="weeklySubmissionGoal">Weekly Submission Goal</Label>
                    <div className="relative">
                      <Target className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                      <Input
                        id="weeklySubmissionGoal"
                        type="number"
                        min="0"
                        step="1"
                        value={weeklySubmissionGoal}
                        onChange={(e) => setWeeklySubmissionGoal(parseInt(e.target.value) || 0)}
                        className="pl-9"
                      />
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Target number of loan applications to be submitted each week
                    </p>
                  </div>

                  {/* Weekly Funded Goal */}
                  <div className="space-y-2">
                    <Label htmlFor="weeklyFundedGoal">Weekly Funded Goal</Label>
                    <div className="relative">
                      <CreditCard className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                      <Input
                        id="weeklyFundedGoal"
                        type="number"
                        min="0"
                        step="1"
                        value={weeklyFundedGoal}
                        onChange={(e) => setWeeklyFundedGoal(parseInt(e.target.value) || 0)}
                        className="pl-9"
                      />
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Target number of loan applications to be funded each week
                    </p>
                  </div>
                </div>
              </div>

              <Separator />

              {settings && (
                <div className="text-sm text-muted-foreground">
                  <p>Last updated: {formatDate(settings.updatedAt)}</p>
                  <p>Updated by: {settings.updatedBy.fullName}</p>
                </div>
              )}
            </div>

            <div className="flex justify-end">
              <Button onClick={saveSettings} disabled={saving}>
                {saving ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : showSuccess ? (
                  <CheckCircle className="mr-2 h-4 w-4" />
                ) : (
                  <Save className="mr-2 h-4 w-4" />
                )}
                {saving ? 'Saving...' : showSuccess ? 'Saved!' : 'Save Settings'}
              </Button>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}
