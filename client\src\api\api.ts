import axios from 'axios';
import { refreshTokenApi } from './auth';
import { getCsrfToken, fetchCsrfToken } from '@/utils/csrfUtils';

// Global auth failure handler - will be set by AuthContext
let globalAuthFailureHandler: (() => void) | null = null;

export const setGlobalAuthFailureHandler = (handler: () => void) => {
  globalAuthFailureHandler = handler;
};

// Create axios instance
const api = axios.create({
  baseURL: '/api',
  headers: {
    'Content-Type': 'application/json'
  },
  withCredentials: true // This ensures cookies are sent with requests
});

// Add CSRF token to every request that needs it
api.interceptors.request.use(config => {
  // Skip CSRF token for auth endpoints
  const skipCsrfPaths = [
    '/auth/login',
    '/auth/register',
    '/auth/refresh',
    '/auth/setup-pin',
    '/auth/remove-pin',
    '/auth/pin-status',
    '/csrf-token'
  ];

  const path = config.url || '';
  const shouldSkipCsrf = skipCsrfPaths.some(skipPath => path.startsWith(skipPath));

  // Only add CSRF token for non-GET requests that aren't in the skip list
  if (!shouldSkipCsrf &&
      config.method &&
      ['post', 'put', 'delete', 'patch'].includes(config.method.toLowerCase())) {
    const token = getCsrfToken();
    if (token) {
      // Add the token to both headers that might be used by the server
      config.headers['X-CSRF-Token'] = token;
      config.headers['CSRF-Token'] = token;

      // Log the token being sent (for debugging)
      console.debug(`Adding CSRF token to ${config.method?.toUpperCase()} ${path}`);
    } else {
      // If we don't have a token, try to fetch one
      console.warn(`No CSRF token available for ${config.method?.toUpperCase()} ${path}, fetching one...`);
      // We'll fetch asynchronously but won't wait for it
      // The request will proceed without a token and might fail
      // But the next request should have the token
      fetchCsrfToken().then(token => {
        if (token) {
          console.log(`CSRF token fetched for future requests: ${token.substring(0, 5)}...`);
        }
      });
    }
  }
  return config;
});

let isRefreshing = false;
let failedQueue: any[] = [];

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(prom => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });
  failedQueue = [];
};

api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    // Handle CSRF token errors
    if (error.response?.status === 403 &&
        (error.response?.data?.error === 'invalid csrf token' ||
         error.response?.data?.message === 'invalid csrf token' ||
         error.response?.data?.message?.includes('CSRF'))) {
      console.warn('CSRF token validation failed, fetching new token...', {
        url: originalRequest.url,
        method: originalRequest.method,
        headers: originalRequest.headers
      });

      // Try to fetch a new CSRF token using our utility function
      try {
        const newToken = await fetchCsrfToken();
        console.log('New CSRF token:', newToken ? 'Found' : 'Not found');

        if (newToken && originalRequest.headers) {
          // Update the token in the original request
          originalRequest.headers['X-CSRF-Token'] = newToken;
          originalRequest.headers['CSRF-Token'] = newToken;
          console.log('Updated request with new CSRF token');
        } else {
          console.warn('No CSRF token available after fetch, request may fail again');
        }

        // Retry the original request
        return api(originalRequest);
      } catch (csrfError) {
        console.error('Failed to refresh CSRF token:', csrfError);
      }
    }

    if (error.response?.status === 401 && !originalRequest._retry) {
      if (isRefreshing) {
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        })
          .then((token) => {
            originalRequest.headers['Authorization'] = `Bearer ${token}`;
            return api(originalRequest);
          })
          .catch((err) => Promise.reject(err));
      }

      originalRequest._retry = true;
      isRefreshing = true;

      const refreshToken = localStorage.getItem('refreshToken');
      if (!refreshToken) {
        // No refresh token available - trigger auth failure
        if (globalAuthFailureHandler) {
          globalAuthFailureHandler();
        }
        return Promise.reject(error);
      }

      try {
        const response = await refreshTokenApi(refreshToken);
        const { accessToken, refreshToken: newRefreshToken } = response.data;

        localStorage.setItem('accessToken', accessToken);
        localStorage.setItem('refreshToken', newRefreshToken);
        api.defaults.headers.common['Authorization'] = `Bearer ${accessToken}`;

        processQueue(null, accessToken);
        return api(originalRequest);
      } catch (refreshError) {
        processQueue(refreshError, null);
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');

        // Refresh token failed - trigger auth failure
        if (globalAuthFailureHandler) {
          globalAuthFailureHandler();
        }

        return Promise.reject(refreshError);
      } finally {
        isRefreshing = false;
      }
    }

    return Promise.reject(error);
  }
);

export default api;
