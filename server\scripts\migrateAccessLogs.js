/**
 * Migration script to update existing access logs with new fields
 * 
 * This script adds the following fields to existing logs:
 * - requestType (page, api, asset)
 * - responseTime (default 0)
 * - referrer (default empty string)
 * - fullUrl (constructed from path)
 * 
 * Run with: node scripts/migrateAccessLogs.js
 */

const mongoose = require('mongoose');
const { connectDB } = require('../config/database');
const AccessLog = require('../models/AccessLog');

async function migrateAccessLogs() {
  try {
    // Connect to database
    await connectDB();
    console.log('Connected to database');

    // Get count of logs without requestType
    const count = await AccessLog.countDocuments({ requestType: { $exists: false } });
    console.log(`Found ${count} logs without requestType field`);

    if (count === 0) {
      console.log('No logs to migrate');
      process.exit(0);
    }

    // Process logs in batches to avoid memory issues
    const batchSize = 1000;
    let processed = 0;

    while (processed < count) {
      // Get batch of logs
      const logs = await AccessLog.find({ requestType: { $exists: false } })
        .limit(batchSize);

      console.log(`Processing batch of ${logs.length} logs`);

      // Update each log
      for (const log of logs) {
        // Determine request type based on path
        let requestType = 'unknown';
        if (log.path.startsWith('/api/')) {
          requestType = 'api';
        } else if (log.path.match(/\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot)$/i)) {
          requestType = 'asset';
        } else {
          requestType = 'page';
        }

        // Construct full URL (best effort)
        const fullUrl = `http://localhost${log.path}`;

        // Update the log
        log.requestType = requestType;
        log.responseTime = 0; // Default value
        log.referrer = ''; // Default value
        log.fullUrl = fullUrl;

        await log.save();
      }

      processed += logs.length;
      console.log(`Processed ${processed} of ${count} logs`);
    }

    console.log('Migration completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error during migration:', error);
    process.exit(1);
  }
}

migrateAccessLogs();
