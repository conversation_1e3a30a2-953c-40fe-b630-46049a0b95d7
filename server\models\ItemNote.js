const mongoose = require('mongoose');

const itemNoteSchema = new mongoose.Schema({
  itemId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Inventory',
    required: true,
    index: true,
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  content: {
    type: String,
    required: true,
  },
  createdAt: {
    type: Date,
    default: Date.now,
    immutable: true,
  },
}, {
  versionKey: false,
});

// Create indexes for common queries
itemNoteSchema.index({ createdAt: -1 });

const ItemNote = mongoose.model('ItemNote', itemNoteSchema);

module.exports = ItemNote;