import api from './api';

export interface BannedItem {
  _id: string;
  title: string;
  description: string;
  category: 'banned' | 'restricted';
  createdBy: {
    _id: string;
    username: string;
    fullName: string;
  };
  updatedBy?: {
    _id: string;
    username: string;
    fullName: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface BannedItemInput {
  title: string;
  description: string;
  category: 'banned' | 'restricted';
}

// Get all banned items with optional filtering
export const getBannedItems = async (category?: string, search?: string) => {
  try {
    const params = new URLSearchParams();
    if (category && category !== 'all') {
      params.append('category', category);
    }
    if (search) {
      params.append('search', search);
    }

    const response = await api.get(`/banned-items?${params.toString()}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching banned items:', error);
    throw error;
  }
};

// Get a single banned item by ID
export const getBannedItem = async (id: string) => {
  try {
    const response = await api.get(`/banned-items/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching banned item ${id}:`, error);
    throw error;
  }
};

// Create a new banned item
export const createBannedItem = async (item: BannedItemInput) => {
  try {
    const response = await api.post('/banned-items', item);
    return response.data;
  } catch (error) {
    console.error('Error creating banned item:', error);
    throw error;
  }
};

// Update an existing banned item
export const updateBannedItem = async (id: string, item: BannedItemInput) => {
  try {
    const response = await api.put(`/banned-items/${id}`, item);
    return response.data;
  } catch (error) {
    console.error(`Error updating banned item ${id}:`, error);
    throw error;
  }
};

// Delete a banned item
export const deleteBannedItem = async (id: string) => {
  try {
    const response = await api.delete(`/banned-items/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error deleting banned item ${id}:`, error);
    throw error;
  }
};
