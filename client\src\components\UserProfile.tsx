import { useAuth } from "@/contexts/AuthContext";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

export function UserProfile() {
  const { user } = useAuth();

  if (!user) {
    return <div>Loading user information...</div>;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>User Profile</CardTitle>
        <CardDescription>Your account information</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <h3 className="text-sm font-medium">Email</h3>
            <p>{user.email}</p>
          </div>
          {user.username && (
            <div>
              <h3 className="text-sm font-medium">Username</h3>
              <p>{user.username}</p>
            </div>
          )}
          <div>
            <h3 className="text-sm font-medium">Role</h3>
            <Badge variant="outline" className="mt-1">{user.role}</Badge>
          </div>
          <div>
            <h3 className="text-sm font-medium">Location</h3>
            {user.location ? (
              <p>{user.location.name}</p>
            ) : (
              <p className="text-gray-500">No location assigned</p>
            )}
          </div>
          <div>
            <h3 className="text-sm font-medium">Last Login</h3>
            <p>{new Date(user.lastLoginAt).toLocaleString()}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}