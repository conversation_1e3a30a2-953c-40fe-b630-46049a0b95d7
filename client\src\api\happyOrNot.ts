import api from './api';

export interface HappyOrNotSettings {
  apiKey: string;
  experiencePointName: string;
  weeklyFeedbackGoal: number;
  happinessScoreGoal: number;
  lastSynced: string | null;
  lastUpdated: string;
  updatedBy: string | null;
}

export interface HappyOrNotGoalSettings {
  weeklyFeedbackGoal: number;
  happinessScoreGoal: number;
  lastSynced: string | null;
  lastUpdated: string;
}

export interface HappyOrNotFeedback {
  feedbackId: string;
  buttonIndex: number;
  localTime: string;
  experiencePointId: string;
  experiencePointName: string;
  surveyId: string;
  smileyId: string;
  smileyType: string;
  followupQuestionId?: string;
  followupOptionId?: string;
  followupOptionText?: string;
  text?: string;
  textInEnglish?: string;
  misuse: boolean;
  spam?: boolean;
  importedAt: string;
}

export interface HappyOrNotExperiencePoint {
  experiencePointId: number;
  name: string;
  parent: number;
  type: string;
}

export interface HappyOrNotFeedbackData {
  totalCount: number;
  buttonCounts: Array<{
    _id: number;
    count: number;
  }>;
  happinessScore: number;
  timeSeriesData: Array<{
    date: string;
    total: number;
    [key: number]: number;
  }>;
  comparison: {
    previousPeriod: {
      totalCount: number;
      buttonCounts: Array<{
        _id: number;
        count: number;
      }>;
      happinessScore: number;
      change: number;
      scoreChange: number;
    };
    lastYear: {
      totalCount: number;
      buttonCounts: Array<{
        _id: number;
        count: number;
      }>;
      happinessScore: number;
      change: number;
      scoreChange: number;
    };
  };
  recentFeedbacks: HappyOrNotFeedback[];
}

// Description: Get Happy or Not settings
// Endpoint: GET /api/happy-or-not/settings
// Request: {}
// Response: { success: true, data: HappyOrNotSettings }
export const getHappyOrNotSettings = async (): Promise<{ success: boolean, data: HappyOrNotSettings }> => {
  try {
    const response = await api.get('/happy-or-not/settings');
    return response.data;
  } catch (error: any) {
    console.error('Error fetching Happy or Not settings:', error);
    throw new Error(error?.response?.data?.message || error.message);
  }
};

// Description: Get Happy or Not goal settings (accessible to all users)
// Endpoint: GET /api/happy-or-not/goals
// Request: {}
// Response: { success: true, data: HappyOrNotGoalSettings }
export const getHappyOrNotGoalSettings = async (): Promise<{ success: boolean, data: HappyOrNotGoalSettings }> => {
  try {
    const response = await api.get('/happy-or-not/goals');
    return response.data;
  } catch (error: any) {
    console.error('Error fetching Happy or Not goal settings:', error);
    throw new Error(error?.response?.data?.message || error.message);
  }
};

// Description: Update Happy or Not settings
// Endpoint: PUT /api/happy-or-not/settings
// Request: { apiKey?: string, experiencePointName?: string }
// Response: { success: true, data: HappyOrNotSettings }
export const updateHappyOrNotSettings = async (settings: Partial<HappyOrNotSettings>): Promise<{ success: boolean, data: HappyOrNotSettings }> => {
  try {
    const response = await api.put('/happy-or-not/settings', settings);
    return response.data;
  } catch (error: any) {
    console.error('Error updating Happy or Not settings:', error);
    throw new Error(error?.response?.data?.message || error.message);
  }
};

// Description: Get Happy or Not experience points
// Endpoint: GET /api/happy-or-not/experience-points
// Request: {}
// Response: { success: true, data: HappyOrNotExperiencePoint[] }
export const getHappyOrNotExperiencePoints = async (): Promise<{ success: boolean, data: HappyOrNotExperiencePoint[] }> => {
  try {
    const response = await api.get('/happy-or-not/experience-points');
    return response.data;
  } catch (error: any) {
    console.error('Error fetching Happy or Not experience points:', error);
    throw new Error(error?.response?.data?.message || error.message);
  }
};

// Description: Get Happy or Not feedback data
// Endpoint: GET /api/happy-or-not/feedback
// Request: { startDate?: string, endDate?: string, experiencePointId?: string, groupBy?: string }
// Response: { success: true, data: HappyOrNotFeedbackData }
export const getHappyOrNotFeedbackData = async (
  options: {
    startDate?: string;
    endDate?: string;
    experiencePointId?: string;
    groupBy?: 'day' | 'week' | 'month';
  } = {}
): Promise<{ success: boolean, data: HappyOrNotFeedbackData }> => {
  try {
    const response = await api.get('/happy-or-not/feedback', { params: options });
    return response.data;
  } catch (error: any) {
    console.error('Error fetching Happy or Not feedback data:', error);
    throw new Error(error?.response?.data?.message || error.message);
  }
};

// Description: Sync Happy or Not feedback data
// Endpoint: POST /api/happy-or-not/sync
// Request: { startDate?: string, endDate?: string, experiencePointId?: string }
// Response: { success: true, data: { message: string, results: object } }
export const syncHappyOrNotFeedback = async (
  options: {
    startDate?: string;
    endDate?: string;
    experiencePointId?: string;
  } = {}
): Promise<{ success: boolean, data: any }> => {
  try {
    const response = await api.post('/happy-or-not/sync', options);
    return response.data;
  } catch (error: any) {
    console.error('Error syncing Happy or Not feedback:', error);
    throw new Error(error?.response?.data?.message || error.message);
  }
};

// Description: Sync all historical Happy or Not feedback data
// Endpoint: POST /api/happy-or-not/sync-all-historical
// Request: {}
// Response: { success: true, message: string }
export const syncAllHistoricalHappyOrNotFeedback = async (): Promise<{ success: boolean, message: string }> => {
  try {
    const response = await api.post('/happy-or-not/sync-all-historical');
    return response.data;
  } catch (error: any) {
    console.error('Error syncing all historical Happy or Not feedback:', error);
    throw new Error(error?.response?.data?.message || error.message);
  }
};

// Description: Get available years with feedback data
// Endpoint: GET /api/happy-or-not/available-years
// Request: {}
// Response: { success: true, data: number[] }
export const getHappyOrNotAvailableYears = async (): Promise<{ success: boolean, data: number[] }> => {
  try {
    const response = await api.get('/happy-or-not/available-years');
    return response.data;
  } catch (error: any) {
    console.error('Error fetching Happy or Not available years:', error);
    throw new Error(error?.response?.data?.message || error.message);
  }
};

// Interface for follow-up response data
export interface FollowUpResponseItem {
  option: string;
  count: number;
  percentage: number | string;
}

export interface FollowUpResponseData {
  responses: FollowUpResponseItem[];
  totalResponses: number;
  feedbackType: 'positive' | 'negative' | 'all';
  startDate: string;
  endDate: string;
}

// Description: Get follow-up response data
// Endpoint: GET /api/happy-or-not/follow-up-responses
// Request: { startDate?: string, endDate?: string, experiencePointId?: string, feedbackType?: 'positive' | 'negative' | 'all' }
// Response: { success: true, data: FollowUpResponseData }
export const getHappyOrNotFollowUpResponses = async (
  options: {
    startDate?: string;
    endDate?: string;
    experiencePointId?: string;
    feedbackType?: 'positive' | 'negative' | 'all';
  } = {}
): Promise<{ success: boolean, data: FollowUpResponseData }> => {
  try {
    const response = await api.get('/happy-or-not/follow-up-responses', { params: options });
    return response.data;
  } catch (error: any) {
    console.error('Error fetching Happy or Not follow-up responses:', error);
    throw new Error(error?.response?.data?.message || error.message);
  }
};

export interface PaginatedFeedbackOptions {
  startDate?: string;
  endDate?: string;
  experiencePointId?: string;
  buttonIndex?: number | string;
  hasText?: boolean;
  hasFollowUp?: boolean;
  searchText?: string;
  page?: number;
  limit?: number;
  sortField?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedFeedbackResponse {
  feedbacks: HappyOrNotFeedback[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
}

// Description: Get paginated Happy or Not feedback with search and filtering
// Endpoint: GET /api/happy-or-not/feedback/paginated
// Request: PaginatedFeedbackOptions
// Response: { success: true, data: PaginatedFeedbackResponse }
export const getPaginatedHappyOrNotFeedback = async (
  options: PaginatedFeedbackOptions = {}
): Promise<{ success: boolean, data: PaginatedFeedbackResponse }> => {
  try {
    console.log('Fetching paginated feedback with options:', options);

    // Make the actual API call to get real data from the database
    const response = await api.get('/happy-or-not/feedback/paginated', { params: options });
    return response.data;
  } catch (error: any) {
    console.error('Error fetching paginated Happy or Not feedback:', error);
    throw new Error(error?.response?.data?.message || error.message);
  }
};
