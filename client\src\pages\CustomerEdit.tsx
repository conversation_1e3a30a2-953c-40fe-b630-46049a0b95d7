import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { getCustomer, updateCustomer } from "@/api/customers";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import { Loader2Icon } from "lucide-react";

const formSchema = z.object({
  name: z.string().min(1, "Name is required"),
  phone: z.string().optional(),
  email: z.string().email("Invalid email").optional().or(z.literal("")),
  address: z.string().optional(),
  notes: z.string().optional(),
  type: z.enum(["customer", "supplier", "partner"]),
});

type FormValues = z.infer<typeof formSchema>;

type Customer = FormValues & {
  _id: string;
  createdAt: string;
  updatedAt: string;
};

export default function CustomerEdit() {
  const { id } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      phone: "",
      email: "",
      address: "",
      notes: "",
      type: "customer",
    },
  });

  const fetchCustomer = async () => {
    try {
      if (!id) return;
      setLoading(true);
      console.log("Fetching customer with ID:", id);
      const response = await getCustomer(id);
      console.log("API response:", response); // Log the full response

      // Check if we have data
      if (!response || !response.data || !response.data.customer) {
        console.error("Invalid response format:", response);
        toast({
          variant: "destructive",
          title: "Error",
          description: "Received invalid customer data format",
        });
        return;
      }

      const customerData = response.data.customer;
      console.log("Customer data extracted:", customerData);
      setCustomer(customerData);

      console.log("Setting form values:", {
        name: customerData.name || "",
        phone: customerData.phone || "",
        email: customerData.email || "",
        address: customerData.address || "",
        notes: customerData.notes || "",
        type: customerData.type || "customer",
      });

      // Reset form with data
      form.reset({
        name: customerData.name || "",
        phone: customerData.phone || "",
        email: customerData.email || "",
        address: customerData.address || "",
        notes: customerData.notes || "",
        type: customerData.type || "customer",
      });

      // Check form values after reset
      console.log("Form values after reset:", form.getValues());
    } catch (err) {
      console.error("Error fetching customer:", err);
      toast({
        variant: "destructive",
        title: "Error",
        description: err.message || "Failed to load customer data",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCustomer();
  }, [id]);

  const onSubmit = async (data: FormValues) => {
    try {
      if (!id) return;
      setSubmitting(true);
      await updateCustomer(id, data);
      toast({
        title: "Success",
        description: "Contact updated successfully",
      });
      navigate("/address-book");
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to update contact",
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Right before the return statement in CustomerEdit.tsx
  console.log("Current form values during render:", form.getValues());
  console.log("Current customer state:", customer);

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Edit Contact</h1>
        <Button
          variant="outline"
          onClick={() => navigate("/address-book")}
          disabled={loading || submitting}
        >
          Cancel
        </Button>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2Icon className="h-8 w-8 animate-spin" />
        </div>
      ) : (
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter phone number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter email" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Type</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select contact type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="customer">Customer</SelectItem>
                        <SelectItem value="supplier">Supplier</SelectItem>
                        <SelectItem value="partner">Partner</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Address</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter address" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter notes"
                      className="min-h-32"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end">
              <Button type="submit" disabled={submitting}>
                {submitting && (
                  <Loader2Icon className="mr-2 h-4 w-4 animate-spin" />
                )}
                Save Changes
              </Button>
            </div>
          </form>
        </Form>
      )}
    </div>
  );
}