const express = require('express');
const router = express.Router();
const ContactService = require('../services/contactService');
const { requireUser } = require('./middleware/auth');

// Get all contacts with pagination
router.get('/', requireUser, async (req, res) => {
  try {
    // Extract pagination parameters from query string
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const search = req.query.search || '';
    const type = req.query.type || null;

    console.log(`Requesting contacts - page: ${page}, limit: ${limit}, search term: ${search ? `"${search}"` : 'none'}, type: ${type || 'all'}`);

    const result = await ContactService.getAll(page, limit, search, type);

    res.status(200).json(result);
  } catch (error) {
    console.error(`Error fetching contacts: ${error.stack}`);
    res.status(500).json({ error: error.message });
  }
});

// Get a single contact by ID
router.get('/:id', requireUser, async (req, res) => {
  try {
    const contact = await ContactService.getById(req.params.id);
    res.status(200).json({ contact });
  } catch (error) {
    console.error(`Error fetching contact: ${error.stack}`);
    if (error.message === 'Contact not found') {
      return res.status(404).json({ error: error.message });
    }
    res.status(500).json({ error: error.message });
  }
});

// Create a new contact
router.post('/', requireUser, async (req, res) => {
  try {
    const contact = await ContactService.create(req.body);
    res.status(201).json({
      success: true,
      message: 'Contact created successfully',
      contact
    });
  } catch (error) {
    console.error(`Error creating contact: ${error.stack}`);
    if (error.message.includes('already exists')) {
      return res.status(400).json({ error: error.message });
    }
    res.status(500).json({ error: error.message });
  }
});

// Update an existing contact
router.put('/:id', requireUser, async (req, res) => {
  try {
    const contact = await ContactService.update(req.params.id, req.body);
    res.status(200).json({
      success: true,
      message: 'Contact updated successfully',
      contact
    });
  } catch (error) {
    console.error(`Error updating contact: ${error.stack}`);
    if (error.message === 'Contact not found') {
      return res.status(404).json({ error: error.message });
    }
    if (error.message.includes('already exists')) {
      return res.status(400).json({ error: error.message });
    }
    res.status(500).json({ error: error.message });
  }
});

// Delete a contact
router.delete('/:id', requireUser, async (req, res) => {
  try {
    await ContactService.delete(req.params.id);
    res.status(200).json({
      success: true,
      message: 'Contact deleted successfully'
    });
  } catch (error) {
    console.error(`Error deleting contact: ${error.stack}`);
    if (error.message === 'Contact not found') {
      return res.status(404).json({ error: error.message });
    }
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;