{"name": "vite_client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "npm run dev", "dev": "vite --host", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-menubar": "^1.1.2", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.2.6", "@tanstack/react-table": "^8.21.3", "axios": "^1.8.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.5.1", "framer-motion": "^12.5.0", "input-otp": "^1.4.1", "lucide-react": "^0.460.0", "next-themes": "^0.4.3", "react": "^18.3.1", "react-day-picker": "^9.7.0", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.55.0", "react-query": "^3.39.3", "react-resizable-panels": "^2.1.7", "react-router-dom": "^7.0.1", "recharts": "^2.15.1", "sonner": "^1.7.4", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.1", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "^9.13.0", "@types/node": "^22.9.1", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.20", "eslint": "^9.13.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.11.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.15", "typescript": "~5.6.2", "typescript-eslint": "^8.11.0", "vite": "^6.3.2"}, "optionalDependencies": {"@esbuild/win32-arm64": "0.21.5", "@esbuild/win32-ia32": "0.21.5", "@esbuild/win32-x64": "0.21.5", "@rollup/rollup-win32-arm64-msvc": "4.35.0", "@rollup/rollup-win32-ia32-msvc": "4.35.0", "@rollup/rollup-win32-x64-msvc": "4.35.0"}, "overrides": {"glob": "^9.0.0", "rimraf": "^4.0.0", "inflight": "npm:lru-cache@^7.0.0"}}