import { useState } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Download, 
  FileText, 
  FileSpreadsheet, 
  Printer,
  X
} from 'lucide-react';
import { useToast } from '@/hooks/useToast';

interface AuditExportModalProps {
  audit: any;
  isOpen: boolean;
  onClose: () => void;
}

export function AuditExportModal({ audit, isOpen, onClose }: AuditExportModalProps) {
  const { toast } = useToast();
  const [exportFormat, setExportFormat] = useState('pdf');
  const [isExporting, setIsExporting] = useState(false);
  const [selectedSections, setSelectedSections] = useState({
    basicInfo: true,
    transactionDetails: true,
    items: true,
    assessments: true,
    pricing: true,
    comments: false,
    history: false,
  });

  const exportSections = [
    { id: 'basicInfo', label: 'Basic Information', description: 'Audit type, dates, employee details' },
    { id: 'transactionDetails', label: 'Transaction Details', description: 'Transaction ID, amount, dates' },
    { id: 'items', label: 'Items', description: 'Item details and pricing information' },
    { id: 'assessments', label: 'Assessments', description: 'All assessment sections and results' },
    { id: 'pricing', label: 'Pricing Analysis', description: 'Pricing assessment and overpayment details' },
    { id: 'comments', label: 'Comments', description: 'All comments and discussions' },
    { id: 'history', label: 'Audit History', description: 'Complete audit timeline and changes' },
  ];

  const exportFormats = [
    { id: 'pdf', label: 'PDF Report', icon: FileText, description: 'Formatted report suitable for printing and sharing' },
    { id: 'excel', label: 'Excel Spreadsheet', icon: FileSpreadsheet, description: 'Data export for analysis and record keeping' },
    { id: 'print', label: 'Print Preview', icon: Printer, description: 'Open print-friendly view in new window' },
  ];

  const handleSectionToggle = (sectionId: string, checked: boolean) => {
    setSelectedSections(prev => ({
      ...prev,
      [sectionId]: checked
    }));
  };

  const handleExport = async () => {
    try {
      setIsExporting(true);
      
      const selectedSectionsList = Object.entries(selectedSections)
        .filter(([_, selected]) => selected)
        .map(([section, _]) => section);

      if (selectedSectionsList.length === 0) {
        toast({
          title: 'No Sections Selected',
          description: 'Please select at least one section to export.',
          variant: 'destructive',
        });
        return;
      }

      // TODO: Implement actual export functionality
      // This would call different export APIs based on format
      switch (exportFormat) {
        case 'pdf':
          // await exportAuditToPDF(audit._id, selectedSectionsList);
          break;
        case 'excel':
          // await exportAuditToExcel(audit._id, selectedSectionsList);
          break;
        case 'print':
          // Open print view in new window
          const printUrl = `/buys/audits/${audit._id}/print?sections=${selectedSectionsList.join(',')}`;
          window.open(printUrl, '_blank');
          break;
      }

      // Mock success for now
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast({
        title: 'Export Successful',
        description: `Audit has been exported as ${exportFormat.toUpperCase()}.`,
      });
      
      onClose();
    } catch (error: any) {
      toast({
        title: 'Export Failed',
        description: 'Failed to export audit. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsExporting(false);
    }
  };

  const getSelectedIcon = () => {
    const format = exportFormats.find(f => f.id === exportFormat);
    return format ? <format.icon className="h-4 w-4" /> : <Download className="h-4 w-4" />;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Export Audit
          </DialogTitle>
          <DialogDescription>
            Export audit details in your preferred format. Select the sections you want to include.
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className="flex-1 pr-4">
          <div className="space-y-6">
          {/* Audit Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Audit Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-muted-foreground">Transaction ID</p>
                  <p className="font-medium">{audit.transactionId}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Audit Type</p>
                  <p className="font-medium capitalize">{audit.auditType}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Employee</p>
                  <p className="font-medium">{audit.employeeName}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Amount</p>
                  <p className="font-medium">${audit.amount}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Export Format Selection */}
          <Card>
            <CardHeader>
              <CardTitle>Export Format</CardTitle>
              <CardDescription>
                Choose how you want to export the audit data
              </CardDescription>
            </CardHeader>
            <CardContent>
              <RadioGroup
                value={exportFormat}
                onValueChange={setExportFormat}
                className="space-y-3"
              >
                {exportFormats.map((format) => (
                  <div key={format.id} className="flex items-start space-x-3 p-3 border rounded-lg hover:bg-accent">
                    <RadioGroupItem value={format.id} id={format.id} className="mt-1" />
                    <div className="flex-1">
                      <Label htmlFor={format.id} className="flex items-center gap-2 cursor-pointer">
                        <format.icon className="h-4 w-4" />
                        <span className="font-medium">{format.label}</span>
                      </Label>
                      <p className="text-sm text-muted-foreground mt-1">
                        {format.description}
                      </p>
                    </div>
                  </div>
                ))}
              </RadioGroup>
            </CardContent>
          </Card>

          {/* Section Selection */}
          <Card>
            <CardHeader>
              <CardTitle>Sections to Include</CardTitle>
              <CardDescription>
                Select which sections of the audit to include in the export
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {exportSections.map((section) => (
                  <div key={section.id} className="flex items-start space-x-3 p-2 hover:bg-accent rounded-md">
                    <Checkbox
                      id={section.id}
                      checked={selectedSections[section.id as keyof typeof selectedSections]}
                      onCheckedChange={(checked) => handleSectionToggle(section.id, checked as boolean)}
                    />
                    <div className="flex-1">
                      <Label htmlFor={section.id} className="cursor-pointer font-medium">
                        {section.label}
                      </Label>
                      <p className="text-sm text-muted-foreground">
                        {section.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Export Summary */}
          <Card className="border-dashed">
            <CardContent className="pt-6">
              <div className="text-sm text-muted-foreground space-y-2">
                <h4 className="font-medium text-foreground">Export Summary:</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <span className="font-medium">Format:</span> {exportFormats.find(f => f.id === exportFormat)?.label}
                  </div>
                  <div>
                    <span className="font-medium">Sections:</span> {Object.values(selectedSections).filter(Boolean).length} selected
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          </div>
        </ScrollArea>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-2 pt-4 border-t">
          <Button variant="outline" onClick={onClose} disabled={isExporting}>
            <X className="h-4 w-4 mr-2" />
            Cancel
          </Button>
          <Button onClick={handleExport} disabled={isExporting}>
            {getSelectedIcon()}
            <span className="ml-2">
              {isExporting ? 'Exporting...' : 
               exportFormat === 'print' ? 'Open Print View' : 
               `Export ${exportFormat.toUpperCase()}`}
            </span>
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
