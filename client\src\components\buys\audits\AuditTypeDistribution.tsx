import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>xis,
  <PERSON>A<PERSON>s,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Cell
} from 'recharts';

interface AuditTypeData {
  type: string;
  compliant: number;
  minorNonCompliant: number;
  majorNonCompliant: number;
  total: number;
}

interface AuditTypeDistributionProps {
  data: AuditTypeData[];
}

/**
 * Chart component to display distribution of audit types and their compliance status
 */
export function AuditTypeDistribution({ data }: AuditTypeDistributionProps) {
  // Handle empty data
  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-full">
        <p className="text-muted-foreground">No audit type distribution data available</p>
      </div>
    );
  }

  // Format the type names for display
  const formattedData = data.map(item => ({
    ...item,
    type: item.type ? item.type.charAt(0).toUpperCase() + item.type.slice(1) : 'Unknown'
  }));

  // Custom tooltip to show all values
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border border-border p-3 rounded-md shadow-md">
          <p className="font-medium">{label} Audits</p>
          <div className="space-y-1 mt-2">
            <p className="text-sm flex items-center">
              <span className="w-3 h-3 rounded-full bg-green-500 inline-block mr-2"></span>
              Compliant: {payload[0].value}
            </p>
            <p className="text-sm flex items-center">
              <span className="w-3 h-3 rounded-full bg-yellow-500 inline-block mr-2"></span>
              Minor Non-Compliant: {payload[1].value}
            </p>
            <p className="text-sm flex items-center">
              <span className="w-3 h-3 rounded-full bg-red-500 inline-block mr-2"></span>
              Major Non-Compliant: {payload[2].value}
            </p>
            <p className="text-sm text-muted-foreground mt-1 border-t pt-1">
              Total: {payload[0].value + payload[1].value + payload[2].value}
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <ResponsiveContainer width="100%" height="100%">
      <BarChart
        data={formattedData}
        margin={{
          top: 20,
          right: 30,
          left: 20,
          bottom: 5,
        }}
      >
        <CartesianGrid strokeDasharray="3 3" opacity={0.1} />
        <XAxis
          dataKey="type"
          tick={{ fill: 'hsl(var(--muted-foreground))' }}
        />
        <YAxis
          tick={{ fill: 'hsl(var(--muted-foreground))' }}
          label={{
            value: 'Number of Audits',
            angle: -90,
            position: 'insideLeft',
            style: { fill: 'hsl(var(--muted-foreground))' }
          }}
        />
        <Tooltip content={<CustomTooltip />} />
        <Legend />
        <Bar
          dataKey="compliant"
          name="Compliant"
          stackId="a"
          fill="rgb(34, 197, 94)"
        />
        <Bar
          dataKey="minorNonCompliant"
          name="Minor Non-Compliant"
          stackId="a"
          fill="rgb(234, 179, 8)"
        />
        <Bar
          dataKey="majorNonCompliant"
          name="Major Non-Compliant"
          stackId="a"
          fill="rgb(239, 68, 68)"
        />
      </BarChart>
    </ResponsiveContainer>
  );
}
