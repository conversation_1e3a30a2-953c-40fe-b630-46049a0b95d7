const mongoose = require('mongoose');

const loanApplicationSchema = new mongoose.Schema({
  // Loan details
  loanId: {
    type: String,
    required: true,
    trim: true,
  },
  loanAmount: {
    type: Number,
    required: true,
    min: 0,
  },
  customerId: {
    type: String,
    required: true,
    trim: true,
  },
  customerName: {
    type: String,
    required: true,
    trim: true,
  },
  status: {
    type: String,
    enum: ['awaitingDecision', 'approved', 'approvedPaid', 'declined', 'cancelled'],
    required: true,
    default: 'awaitingDecision',
  },

  // Dates
  submittedDate: {
    type: Date,
    required: true,
  },
  createdAt: {
    type: Date,
    default: Date.now,
    immutable: true,
  },

  // User information
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  submittedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },

  // Additional metadata
  notes: {
    type: String,
    default: '',
  },
}, {
  timestamps: true,
  versionKey: false,
});

// Create indexes for better query performance
loanApplicationSchema.index({ submittedDate: -1 });
loanApplicationSchema.index({ createdBy: 1 });
loanApplicationSchema.index({ submittedBy: 1 });
loanApplicationSchema.index({ status: 1 });

const LoanApplication = mongoose.model('LoanApplication', loanApplicationSchema);

module.exports = LoanApplication;
