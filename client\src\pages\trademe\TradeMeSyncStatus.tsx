import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Loader2, <PERSON>freshC<PERSON>, CheckCircle, XCircle, Clock,
  MessageSquare, ShoppingBag, ListFilter, LayoutGrid
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { getStatus as getSyncStatus, getLogs, triggerSync as runManualSync } from '@/api/tradeMeSync';

interface SyncLog {
  _id: string;
  syncType: 'listings' | 'questions' | 'feedback' | 'categories';
  startTime: string;
  endTime: string;
  success: boolean;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  itemsProcessed: number;
  newItems: number;
  updatedItems: number;
  apiCallsMade: number;
  totalItems?: number;
  totalPages?: number;
  currentPage?: number;
  maxPagesPerEndpoint?: number;
  error?: string;
  createdAt?: string;
  updatedAt?: string;
}

interface SyncStatus {
  isSyncing: boolean;
  lastSyncTime: string | null;
  syncLogs: SyncLog[];
}

const TradeMeSyncStatus = () => {
  const [status, setStatus] = useState<SyncStatus | null>({ isSyncing: false, lastSyncTime: null, syncLogs: [] });
  const [syncLogs, setSyncLogs] = useState<SyncLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [syncing, setSyncing] = useState(false);
  const [syncingType, setSyncingType] = useState<'listings' | 'questions' | 'feedback' | 'categories' | null>(null);

  const fetchStatus = async () => {
    try {
      setLoading(true);
      const response = await getSyncStatus();
      setStatus(response.status);

      // Fetch sync logs separately
      const logsResponse = await getLogs({ limit: 20 });
      if (logsResponse.success && logsResponse.logs) {
        setSyncLogs(logsResponse.logs);
      }
    } catch (error: any) {
      toast.error('Error', {
        description: error.message || 'Failed to fetch sync status'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleManualSync = async (syncType: 'listings' | 'questions' | 'feedback' | 'categories' = 'listings') => {
    try {
      setSyncing(true);
      setSyncingType(syncType);

      // Custom message for feedback sync
      if (syncType === 'feedback') {
        toast.info('Starting Feedback Sync', {
          description: 'Starting manual feedback sync with TradeMe. This will retrieve ALL feedback (up to 20,000 items, including historical items) and update statistics. This may take several minutes for accounts with large feedback history.'
        });
      } else {
        toast.info('Starting Sync', {
          description: `Starting manual ${syncType} sync with TradeMe...`
        });
      }

      // Set an extremely high page limit for feedback to ensure we get ALL feedback (up to 20,000 items)
      const maxPages = syncType === 'feedback' ? 1000 : 5;

      const result = await runManualSync({
        syncType,
        maxPagesPerEndpoint: maxPages
      });

      if (result.success) {
        if (syncType === 'feedback') {
          toast.success('Feedback Sync Started', {
            description: 'Feedback sync started successfully. ALL feedback (up to 20,000 items, including historical items) and statistics will be updated in the background. This may take several minutes to complete for accounts with large feedback history.'
          });
        } else {
          toast.success('Sync Started', {
            description: `${syncType} sync started successfully. It will continue in the background.`
          });
        }
      } else {
        toast.error('Sync Failed', {
          description: result.error || 'Failed to start sync with TradeMe'
        });
      }

      // Refresh the status after a short delay
      setTimeout(fetchStatus, 2000);

      // For feedback sync, refresh again after a longer delay to see updated results
      if (syncType === 'feedback') {
        setTimeout(fetchStatus, 10000);
        // Refresh again after an even longer delay for large feedback syncs
        setTimeout(fetchStatus, 30000);
      }
    } catch (error: any) {
      toast.error('Error', {
        description: error.message || 'Failed to run manual sync'
      });
    } finally {
      setSyncing(false);
      setSyncingType(null);
    }
  };

  useEffect(() => {
    fetchStatus();

    // Refresh status every minute
    const interval = setInterval(() => {
      fetchStatus();
    }, 60000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="container py-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <h1 className="text-2xl font-bold">TradeMe Sync Status</h1>
        <Button
          variant="outline"
          onClick={fetchStatus}
          disabled={loading}
          className="self-end md:self-auto"
        >
          {loading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <RefreshCw className="h-4 w-4" />
          )}
          <span className="ml-2">Refresh Status</span>
        </Button>
      </div>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Sync Actions</CardTitle>
          <CardDescription>Manually trigger TradeMe sync operations</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center">
            <div className="grid grid-cols-2 gap-3 w-full max-w-xl">
              {/* Feedback Sync Button */}
              <Button
                onClick={() => handleManualSync('feedback')}
                disabled={syncing || (status?.isSyncing ?? false)}
                variant="default"
                className="bg-green-600 hover:bg-green-700 h-16"
                size="lg"
              >
                {syncing && syncingType === 'feedback' ? (
                  <div className="flex flex-col items-center justify-center">
                    <Loader2 className="h-5 w-5 animate-spin mb-1" />
                    <span className="text-xs">Syncing...</span>
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center">
                    <ListFilter className="h-5 w-5 mb-1" />
                    <span className="text-xs">Feedback</span>
                  </div>
                )}
              </Button>

              {/* Listings Sync Button */}
              <Button
                onClick={() => handleManualSync('listings')}
                disabled={syncing || (status?.isSyncing ?? false)}
                variant="default"
                className="bg-blue-600 hover:bg-blue-700 h-16"
                size="lg"
              >
                {syncing && syncingType === 'listings' ? (
                  <div className="flex flex-col items-center justify-center">
                    <Loader2 className="h-5 w-5 animate-spin mb-1" />
                    <span className="text-xs">Syncing...</span>
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center">
                    <ShoppingBag className="h-5 w-5 mb-1" />
                    <span className="text-xs">Listings</span>
                  </div>
                )}
              </Button>

              {/* Questions Sync Button */}
              <Button
                onClick={() => handleManualSync('questions')}
                disabled={syncing || (status?.isSyncing ?? false)}
                variant="default"
                className="bg-purple-600 hover:bg-purple-700 h-16"
                size="lg"
              >
                {syncing && syncingType === 'questions' ? (
                  <div className="flex flex-col items-center justify-center">
                    <Loader2 className="h-5 w-5 animate-spin mb-1" />
                    <span className="text-xs">Syncing...</span>
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center">
                    <MessageSquare className="h-5 w-5 mb-1" />
                    <span className="text-xs">Questions</span>
                  </div>
                )}
              </Button>

              {/* Categories Sync Button */}
              <Button
                onClick={() => handleManualSync('categories')}
                disabled={syncing || (status?.isSyncing ?? false)}
                variant="default"
                className="bg-orange-600 hover:bg-orange-700 h-16"
                size="lg"
              >
                {syncing && syncingType === 'categories' ? (
                  <div className="flex flex-col items-center justify-center">
                    <Loader2 className="h-5 w-5 animate-spin mb-1" />
                    <span className="text-xs">Syncing...</span>
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center">
                    <LayoutGrid className="h-5 w-5 mb-1" />
                    <span className="text-xs">Categories</span>
                  </div>
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading sync status...</span>
        </div>
      ) : status ? (
        <div className="grid gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Current Status</CardTitle>
              <CardDescription>Current status of the TradeMe sync service</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center">
                  <div className="mr-2">Sync Status:</div>
                  {status?.isSyncing ? (
                    <Badge variant="outline" className="bg-blue-100 text-blue-800">
                      <Loader2 className="h-3 w-3 animate-spin mr-1" />
                      Syncing
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="bg-green-100 text-green-800">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Idle
                    </Badge>
                  )}
                </div>
                <div>
                  <div className="mr-2">Last Sync:</div>
                  {status?.lastSyncTime ? (
                    <span className="text-sm text-gray-600">
                      {new Date(status?.lastSyncTime || '').toLocaleString()}
                      ({formatDistanceToNow(new Date(status?.lastSyncTime || ''), { addSuffix: true })})
                    </span>
                  ) : (
                    <span className="text-sm text-gray-600">Never</span>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Sync History</CardTitle>
                  <CardDescription>Recent TradeMe sync operations</CardDescription>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={async () => {
                    try {
                      const logsResponse = await getLogs({ limit: 20 });
                      if (logsResponse.success && logsResponse.logs) {
                        setSyncLogs(logsResponse.logs);
                        toast.success('Sync logs refreshed');
                      }
                    } catch (error: any) {
                      toast.error('Error', {
                        description: error.message || 'Failed to refresh sync logs'
                      });
                    }
                  }}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh Logs
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {!syncLogs || syncLogs.length === 0 ? (
                <div className="text-center py-4 text-gray-500">No sync logs available</div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Sync Type</TableHead>
                      <TableHead>Start Time</TableHead>
                      <TableHead>Duration</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Items Processed</TableHead>
                      <TableHead>New Items</TableHead>
                      <TableHead>Updated Items</TableHead>
                      <TableHead>API Calls</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {syncLogs.map((log) => (
                      <TableRow
                        key={log._id}
                        className={log.syncType === 'feedback' ? 'bg-green-50 dark:bg-green-900/10' : ''}
                      >
                        <TableCell>
                          <Badge
                            variant="outline"
                            className={
                              log.syncType === 'feedback'
                                ? 'bg-green-100 text-green-800'
                                : log.syncType === 'listings'
                                ? 'bg-blue-100 text-blue-800'
                                : log.syncType === 'questions'
                                ? 'bg-purple-100 text-purple-800'
                                : 'bg-orange-100 text-orange-800'
                            }
                          >
                            {log.syncType?.charAt(0).toUpperCase() + log.syncType?.slice(1) || 'Unknown'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {new Date(log.startTime).toLocaleString()}
                        </TableCell>
                        <TableCell>
                          {log.endTime ? (
                            `${Math.round((new Date(log.endTime).getTime() - new Date(log.startTime).getTime()) / 1000)}s`
                          ) : (
                            'In progress'
                          )}
                        </TableCell>
                        <TableCell>
                          {log.status === 'completed' || log.success === true ? (
                            <Badge variant="outline" className="bg-green-100 text-green-800">
                              <CheckCircle className="h-3 w-3 mr-1" />
                              Success
                            </Badge>
                          ) : log.status === 'failed' || log.success === false ? (
                            <Badge variant="outline" className="bg-red-100 text-red-800">
                              <XCircle className="h-3 w-3 mr-1" />
                              Failed
                            </Badge>
                          ) : log.status === 'in_progress' ? (
                            <Badge variant="outline" className="bg-yellow-100 text-yellow-800">
                              <Loader2 className="h-3 w-3 animate-spin mr-1" />
                              In Progress
                            </Badge>
                          ) : (
                            <Badge variant="outline" className="bg-blue-100 text-blue-800">
                              <Clock className="h-3 w-3 mr-1" />
                              Pending
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell>{log.itemsProcessed}</TableCell>
                        <TableCell>{log.newItems}</TableCell>
                        <TableCell>{log.updatedItems}</TableCell>
                        <TableCell>{log.apiCallsMade}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </div>
      ) : (
        <div className="text-center py-8 text-gray-500">
          Failed to load sync status. Please try again.
        </div>
      )}
    </div>
  );
};

export default TradeMeSyncStatus;
