import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { updateCurrentUser, checkUsernameAvailability } from "@/api/user";
import { useToast } from "@/hooks/useToast";

interface ProfileEditModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const formSchema = z.object({
  fullName: z.string().min(2, "Full name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
  username: z.string().min(3, "Username must be at least 3 characters").optional().nullable(),
});

export function ProfileEditModal({ open, onOpenChange }: ProfileEditModalProps) {
  const { user, refreshUserData } = useAuth();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isCheckingUsername, setIsCheckingUsername] = useState(false);
  const [isUsernameAvailable, setIsUsernameAvailable] = useState(true);

  // Initialize form with user data
  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      fullName: user?.fullName || "",
      email: user?.email || "",
      username: user?.username || "",
    },
  });

  // Reset form when user data changes or modal opens
  useEffect(() => {
    if (user && open) {
      form.reset({
        fullName: user.fullName || "",
        email: user.email || "",
        username: user.username || "",
      });
    }
  }, [user, form, open]);

  // Check username availability
  const checkUsername = async (username: string) => {
    if (!username || username === user?.username) {
      setIsUsernameAvailable(true);
      return true;
    }

    setIsCheckingUsername(true);
    try {
      const available = await checkUsernameAvailability(username);
      setIsUsernameAvailable(available);
      return available;
    } catch (error) {
      console.error("Error checking username:", error);
      return false;
    } finally {
      setIsCheckingUsername(false);
    }
  };

  // Watch username changes
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === "username" && value.username !== user?.username) {
        const timeoutId = setTimeout(() => {
          checkUsername(value.username);
        }, 500);

        return () => clearTimeout(timeoutId);
      }
    });
    return () => subscription.unsubscribe();
  }, [form, user]);

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    // Verify username availability before submitting
    if (values.username && values.username !== user?.username) {
      const available = await checkUsername(values.username);
      if (!available) {
        toast({
          title: "Error",
          description: "Username is already taken",
          variant: "destructive",
        });
        return;
      }
    }

    setIsLoading(true);
    try {
      const submitData = {
        ...values,
        username: values.username || null,
      };

      await updateCurrentUser(submitData);
      await refreshUserData();

      toast({
        title: "Success",
        description: "Profile updated successfully",
      });

      onOpenChange(false);
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to update profile",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit Profile</DialogTitle>
          <DialogDescription>
            Update your account information
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="fullName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Full Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter your full name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter your email" type="email" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="username"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Username</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="Choose a username" 
                      {...field} 
                      value={field.value || ""}
                    />
                  </FormControl>
                  <FormDescription>
                    {isCheckingUsername 
                      ? "Checking username availability..." 
                      : field.value && field.value !== user?.username
                        ? isUsernameAvailable 
                          ? "Username is available" 
                          : "Username is already taken"
                        : "Your unique username for the system"}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter className="pt-4">
              <Button 
                type="submit" 
                disabled={isLoading || (!!form.getValues().username && !isUsernameAvailable)}
              >
                {isLoading ? "Saving..." : "Save Changes"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
