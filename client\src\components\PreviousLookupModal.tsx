import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { formatDistanceToNow, format } from 'date-fns';
import { AlertCircle, RefreshCw, Loader2 } from 'lucide-react';

interface PreviousLookupModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCheckAgain: () => void;
  existingLookupDate: string | null;
  serviceName: string;
  cost: number;
  username: string;
  loading: boolean;
}

export function PreviousLookupModal({
  isOpen,
  onClose,
  onCheckAgain,
  existingLookupDate,
  serviceName,
  cost,
  username,
  loading
}: PreviousLookupModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <AlertCircle className="h-5 w-5 text-blue-500 mr-2" />
            Previous Lookup Found
          </DialogTitle>
          <DialogDescription>
            This device was previously checked {existingLookupDate && formatDistanceToNow(new Date(existingLookupDate), { addSuffix: true })}.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-3 py-4">
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div className="text-muted-foreground">Service:</div>
            <div className="font-medium">{serviceName}</div>
            
            <div className="text-muted-foreground">Cost:</div>
            <div className="font-medium">${cost.toFixed(2)}</div>
            
            <div className="text-muted-foreground">Date:</div>
            <div className="font-medium">{existingLookupDate && format(new Date(existingLookupDate), 'MMM d, yyyy h:mm a')}</div>
            
            <div className="text-muted-foreground">User:</div>
            <div className="font-medium">{username}</div>
          </div>
        </div>
        
        <DialogFooter className="flex flex-row justify-between sm:justify-between">
          <Button variant="outline" onClick={onClose}>
            Use Existing Result
          </Button>
          <Button onClick={onCheckAgain} disabled={loading}>
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Checking...
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                Check Again
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default PreviousLookupModal;
