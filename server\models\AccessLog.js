const mongoose = require('mongoose');

const accessLogSchema = new mongoose.Schema({
  timestamp: {
    type: Date,
    default: Date.now,
    required: true
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  username: {
    type: String,
    default: 'unauthenticated'
  },
  fullName: {
    type: String,
    default: ''
  },
  userRole: {
    type: String,
    default: 'none'
  },
  ipAddress: {
    type: String,
    required: true
  },
  method: {
    type: String,
    required: true
  },
  path: {
    type: String,
    required: true
  },
  queryParams: {
    type: Object,
    default: {}
  },
  userAgent: {
    type: String,
    default: 'unknown'
  },
  statusCode: {
    type: Number,
    default: 200
  },
  requestType: {
    type: String,
    enum: ['page', 'api', 'asset', 'auth', 'user', 'unknown'],
    default: 'unknown'
  },
  responseTime: {
    type: Number,
    default: 0
  },
  referrer: {
    type: String,
    default: ''
  },
  pageName: {
    type: String,
    default: ''
  },
  fullUrl: {
    type: String,
    default: ''
  }
}, {
  versionKey: false
});

// Index for faster queries
accessLogSchema.index({ timestamp: -1 });
accessLogSchema.index({ userId: 1 });
accessLogSchema.index({ ipAddress: 1 });
accessLogSchema.index({ path: 1 });
accessLogSchema.index({ statusCode: 1 });
accessLogSchema.index({ requestType: 1 });
accessLogSchema.index({ responseTime: 1 });

const AccessLog = mongoose.model('AccessLog', accessLogSchema);

module.exports = AccessLog;
