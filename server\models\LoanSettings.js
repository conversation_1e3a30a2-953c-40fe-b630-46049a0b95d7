const mongoose = require('mongoose');

const loanSettingsSchema = new mongoose.Schema({
  // Bonus amounts
  submissionBonus: {
    type: Number,
    required: true,
    default: 0,
    min: 0,
  },
  fundedBonus: {
    type: Number,
    required: true,
    default: 0,
    min: 0,
  },

  // Weekly goals
  weeklySubmissionGoal: {
    type: Number,
    default: 10,
    min: 0,
  },
  weeklyFundedGoal: {
    type: Number,
    default: 5,
    min: 0,
  },

  // Last updated timestamp
  updatedAt: {
    type: Date,
    default: Date.now,
  },

  // Who last updated the settings
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
}, {
  versionKey: false,
});

const LoanSettings = mongoose.model('LoanSettings', loanSettingsSchema);

module.exports = LoanSettings;
