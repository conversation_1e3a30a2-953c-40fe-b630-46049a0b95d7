import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Area,
  ComposedChart
} from 'recharts';

interface ComplianceTrend {
  date: string;
  compliantRate: number;
  minorNonCompliantRate: number;
  majorNonCompliantRate: number;
  totalAudits: number;
}

interface AuditComplianceChartProps {
  data: ComplianceTrend[];
}

/**
 * Chart component to display compliance trends over time
 */
export function AuditComplianceChart({ data }: AuditComplianceChartProps) {
  // Handle empty data
  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-full">
        <p className="text-muted-foreground">No compliance data available</p>
      </div>
    );
  }

  // Format the date for display
  const formattedData = data.map(item => ({
    ...item,
    date: new Date(item.date).toLocaleDateString('en-GB', {
      day: '2-digit',
      month: 'short'
    })
  }));

  // Custom tooltip to show all values
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border border-border p-3 rounded-md shadow-md">
          <p className="font-medium">{label}</p>
          <div className="space-y-1 mt-2">
            <p className="text-sm flex items-center">
              <span className="w-3 h-3 rounded-full bg-green-500 inline-block mr-2"></span>
              Compliant: {payload[0].value}%
            </p>
            <p className="text-sm flex items-center">
              <span className="w-3 h-3 rounded-full bg-yellow-500 inline-block mr-2"></span>
              Minor Non-Compliant: {payload[1].value}%
            </p>
            <p className="text-sm flex items-center">
              <span className="w-3 h-3 rounded-full bg-red-500 inline-block mr-2"></span>
              Major Non-Compliant: {payload[2].value}%
            </p>
            <p className="text-sm text-muted-foreground mt-1">
              Total Audits: {payload[3].value}
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <ResponsiveContainer width="100%" height="100%">
      <ComposedChart
        data={formattedData}
        margin={{
          top: 5,
          right: 30,
          left: 20,
          bottom: 5,
        }}
      >
        <CartesianGrid strokeDasharray="3 3" opacity={0.1} />
        <XAxis
          dataKey="date"
          tick={{ fill: 'hsl(var(--muted-foreground))' }}
        />
        <YAxis
          yAxisId="left"
          tick={{ fill: 'hsl(var(--muted-foreground))' }}
          domain={[0, 100]}
          label={{
            value: 'Percentage (%)',
            angle: -90,
            position: 'insideLeft',
            style: { fill: 'hsl(var(--muted-foreground))' }
          }}
        />
        <YAxis
          yAxisId="right"
          orientation="right"
          tick={{ fill: 'hsl(var(--muted-foreground))' }}
          label={{
            value: 'Total Audits',
            angle: 90,
            position: 'insideRight',
            style: { fill: 'hsl(var(--muted-foreground))' }
          }}
        />
        <Tooltip content={<CustomTooltip />} />
        <Legend />
        <Area
          type="monotone"
          dataKey="compliantRate"
          name="Compliant"
          fill="rgba(34, 197, 94, 0.2)"
          stroke="rgb(34, 197, 94)"
          yAxisId="left"
          stackId="1"
        />
        <Area
          type="monotone"
          dataKey="minorNonCompliantRate"
          name="Minor Non-Compliant"
          fill="rgba(234, 179, 8, 0.2)"
          stroke="rgb(234, 179, 8)"
          yAxisId="left"
          stackId="1"
        />
        <Area
          type="monotone"
          dataKey="majorNonCompliantRate"
          name="Major Non-Compliant"
          fill="rgba(239, 68, 68, 0.2)"
          stroke="rgb(239, 68, 68)"
          yAxisId="left"
          stackId="1"
        />
        <Line
          type="monotone"
          dataKey="totalAudits"
          name="Total Audits"
          stroke="hsl(var(--primary))"
          yAxisId="right"
          strokeWidth={2}
        />
      </ComposedChart>
    </ResponsiveContainer>
  );
}
