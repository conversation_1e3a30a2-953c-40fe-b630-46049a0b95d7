const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Section Assessment Schema
const sectionAssessmentSchema = new Schema({
  status: {
    type: String,
    enum: ['pass', 'fail', 'not_assessed'],
    default: 'not_assessed'
  },
  failReasons: {
    type: [String],
    default: []
  },
  notes: {
    type: String
  }
}, { _id: false });

// Pricing Assessment Schema
const pricingAssessmentSchema = new Schema({
  status: {
    type: String,
    enum: ['pass', 'fail', 'not_assessed'],
    default: 'not_assessed'
  },
  failReasons: {
    type: [String],
    default: []
  },
  suggestedPrice: {
    type: String
  },
  costPrice: {
    type: String
  },
  ticketPrice: {
    type: String
  },
  // Price audit specific fields
  transactionType: {
    type: String,
    enum: ['buy', 'pawn'],
    required: function() {
      return this.parent().auditType === 'price';
    }
  },
  overpaymentReason: {
    type: String,
    enum: ['paid_over_ghost_price', 'paid_over_gold_calculator', 'insufficient_research', 'other'],
    required: function() {
      return this.parent().auditType === 'price';
    }
  },
  customOverpaymentReason: {
    type: String,
    required: function() {
      return this.parent().auditType === 'price' && this.overpaymentReason === 'other';
    }
  },
  notes: {
    type: String
  }
}, { _id: false });

// Responsible Lending Assessment Schema
const responsibleLendingAssessmentSchema = new Schema({
  status: {
    type: String,
    enum: ['pass', 'fail', 'not_assessed'],
    default: 'not_assessed'
  },
  polText: {
    type: String
  },
  failReasons: {
    type: [String],
    default: []
  },
  notes: {
    type: String
  }
}, { _id: false });

// Essential Item Check Schema
const essentialItemCheckSchema = new Schema({
  status: {
    type: String,
    enum: ['pass', 'fail', 'not_assessed', 'not_applicable'],
    default: 'not_assessed'
  },
  category: {
    type: String
  },
  hasEssentialItems: {
    type: Boolean,
    default: false
  },
  isEssential: {
    type: Boolean,
    default: false
  },
  potentiallyEssentialChecked: {
    type: Boolean,
    default: false
  },
  potentiallyEssentialText: {
    type: String
  },
  compliance: {
    type: String,
    enum: ['compliant', 'non_compliant', 'not_assessed', 'not_applicable'],
    default: 'not_assessed'
  },
  reasonNotEssential: {
    type: String
  },
  notes: {
    type: String
  }
}, { _id: false });

// Audit Schema
const buyPawnAuditSchema = new Schema({
  // Basic information
  auditType: {
    type: String,
    enum: ['buy', 'pawn', 'price'],
    required: true
  },
  transactionId: {
    type: String,
    required: true
  },
  employeeId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  employeeName: {
    type: String,
    required: true
  },
  amount: {
    type: Number,
    required: true
  },
  transactionDate: {
    type: Date,
    required: true
  },
  // Legacy fields for backward compatibility
  itemDescription: {
    type: String
  },
  stockcode: {
    type: String
  },
  brand: {
    type: String
  },
  // New multi-item structure
  items: [{
    stockcode: {
      type: String,
      required: true
    },
    brand: {
      type: String,
      required: true
    },
    description: {
      type: String,
      required: true
    },
    cost: {
      type: Number,
      required: true
    }
  }],
  
  // Audit metadata
  auditedBy: {
    _id: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    fullName: {
      type: String,
      required: true
    }
  },
  auditDate: {
    type: Date,
    default: Date.now
  },
  status: {
    type: String,
    enum: ['draft', 'completed', 'flagged', 'resolved'],
    default: 'draft'
  },
  
  // Transaction section (for buy and pawn)
  dataEntryQuality: {
    type: sectionAssessmentSchema,
    default: () => ({})
  },
  itemConditionCheck: {
    type: sectionAssessmentSchema,
    default: () => ({})
  },
  
  // Pricing section (for all types)
  pricing: {
    type: pricingAssessmentSchema,
    default: () => ({})
  },
  authorizedLimitCheck: {
    type: sectionAssessmentSchema,
    default: () => ({})
  },
  
  // Responsible lending section (for pawn only)
  polSuitability: {
    type: responsibleLendingAssessmentSchema,
    default: () => ({})
  },
  customerUnderstanding: {
    type: sectionAssessmentSchema,
    default: () => ({})
  },
  vulnerableCustomer: {
    type: sectionAssessmentSchema,
    default: () => ({})
  },
  essentialItemCheck: {
    type: essentialItemCheckSchema,
    default: () => ({})
  },
  
  // Summary section
  overallCompliance: {
    type: String,
    enum: ['compliant', 'minor_non_compliant', 'major_non_compliant', 'not_assessed'],
    default: 'not_assessed'
  },
  overallScore: {
    type: Number,
    default: 0
  },
  auditNotes: {
    type: String
  },
  
  // Follow-up
  flaggedForFollowup: {
    type: Boolean,
    default: false
  },
  flagReason: {
    type: String
  },
  followedUp: {
    type: Boolean,
    default: false
  },
  followupResponse: {
    type: String
  },
  followupNotes: {
    type: String
  },
  flagResolvedAt: {
    type: Date
  },
  flagResolvedBy: {
    _id: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    fullName: {
      type: String
    }
  },

  // Comments
  comments: [{
    _id: {
      type: Schema.Types.ObjectId,
      default: () => new mongoose.Types.ObjectId()
    },
    comment: {
      type: String,
      required: true
    },
    addedBy: {
      _id: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        required: true
      },
      fullName: {
        type: String,
        required: true
      },
      role: {
        type: String,
        required: true
      }
    },
    addedAt: {
      type: Date,
      default: Date.now
    }
  }],

  // History/Activity Log
  history: [{
    _id: {
      type: Schema.Types.ObjectId,
      default: () => new mongoose.Types.ObjectId()
    },
    action: {
      type: String,
      required: true,
      enum: ['created', 'updated', 'flagged', 'unflagged', 'followup_completed', 'comment_added']
    },
    description: {
      type: String,
      required: true
    },
    details: {
      type: Schema.Types.Mixed
    },
    performedBy: {
      _id: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        required: true
      },
      fullName: {
        type: String,
        required: true
      },
      role: {
        type: String,
        required: true
      }
    },
    performedAt: {
      type: Date,
      default: Date.now
    }
  }],

  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Add text index for search
buyPawnAuditSchema.index({
  transactionId: 'text',
  itemDescription: 'text',
  employeeName: 'text'
});

module.exports = mongoose.model('BuyPawnAudit', buyPawnAuditSchema);
