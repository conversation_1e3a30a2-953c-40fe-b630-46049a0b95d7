import React, { useState, useEffect } from 'react';

interface TradeMeIconProps {
  name: string;
  className?: string;
  style?: React.CSSProperties;
}

export const TradeMeIcon: React.FC<TradeMeIconProps> = ({ name, className = '', style = {} }) => {
  const [cssLoaded, setCssLoaded] = useState(false);

  // Load the TradeMe icons CSS
  useEffect(() => {
    const linkId = 'trademe-icons-css';

    if (!document.getElementById(linkId)) {
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = '/img/trademe/tmicons/tmicons.css';
      link.id = linkId;

      link.onload = () => setCssLoaded(true);
      link.onerror = () => console.error('Failed to load TradeMe icons CSS');

      document.head.appendChild(link);
    } else {
      setCssLoaded(true);
    }

    return () => {
      // Cleanup is optional since we want to keep the CSS loaded
    };
  }, []);

  // Render the icon with a fallback
  return (
    <i className={`tmicon-${name} ${className}`} style={style}>
      {!cssLoaded && name === 'watchlist' && (
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z" />
        </svg>
      )}
    </i>
  );
};

export const TradeMeWatchlistIcon: React.FC<Omit<TradeMeIconProps, 'name'>> = (props) => {
  return <TradeMeIcon name="watchlist" {...props} />;
};

export const TradeMeWatchlistAddIcon: React.FC<Omit<TradeMeIconProps, 'name'>> = (props) => {
  return <TradeMeIcon name="watchlist-add" {...props} />;
};

export const TradeMeWatchlistRemoveIcon: React.FC<Omit<TradeMeIconProps, 'name'>> = (props) => {
  return <TradeMeIcon name="watchlist-remove" {...props} />;
};

export const TradeMeWatchlistTickIcon: React.FC<Omit<TradeMeIconProps, 'name'>> = (props) => {
  return <TradeMeIcon name="watchlist-tick" {...props} />;
};
