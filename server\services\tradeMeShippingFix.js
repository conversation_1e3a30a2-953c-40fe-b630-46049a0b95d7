/**
 * TradeMe Shipping Fix
 *
 * This module provides functions to properly extract and format shipping options
 * from TradeMe API responses.
 */

/**
 * Parse shipping options from TradeMe API response
 * @param {Object} listing - The listing object from TradeMe API
 * @returns {Array} Array of shipping option objects
 */
function parseShippingOptions(listing) {
  if (!listing) return [];

  const shippingOptions = [];

  // Check if there's a Shipping object
  if (listing.Shipping) {
    // Add the main shipping option
    shippingOptions.push({
      type: 'Custom',
      price: parseFloat(listing.Shipping.Price) || 0,
      method: listing.Shipping.Method || 'Standard',
      name: listing.Shipping.Description || listing.Shipping.Method || 'Standard Shipping'
    });
  }

  // Check if there are additional shipping options
  if (listing.ShippingOptions && Array.isArray(listing.ShippingOptions)) {

    listing.ShippingOptions.forEach(option => {
      // For TradeMe API, shipping options can have different structures
      // Try to extract the name from various possible fields
      let name = 'Additional Shipping';

      // Use Method field directly if available (as shown in the log)
      if (option.Method) {
        name = option.Method;
      } else if (option.Description) {
        name = option.Description;
      } else if (option.Name) {
        name = option.Name;
      } else if (option.SelectedShipping) {
        name = option.SelectedShipping;
      } else if (option.Type) {
        // Map numeric types to human-readable names
        const typeMap = {
          '1': 'North Island',
          '2': 'South Island',
          '3': 'Rural Delivery',
          '4': 'North Island Rural',
          '5': 'South Island Rural',
          '6': 'Pick-up Only'
        };
        name = typeMap[option.Type] || `Type ${option.Type}`;
      }

      shippingOptions.push({
        type: 'Custom',
        price: parseFloat(option.Price) || 0,
        method: option.Type || option.Method || 'Additional',
        name: name
      });
    });
  }

  // Check if there's a SelectedShipping field (for sold items)
  if (listing.SelectedShipping && !shippingOptions.some(opt => opt.name === listing.SelectedShipping)) {
    shippingOptions.push({
      type: 'Custom',
      price: parseFloat(listing.ShippingPrice) || 0,
      method: 'Selected',
      name: listing.SelectedShipping
    });
  }

  // For sold items, check the Sales array for shipping information
  if (listing.Sales && Array.isArray(listing.Sales) && listing.Sales.length > 0) {
    const sale = listing.Sales[0];

    if (sale.SelectedShipping && !shippingOptions.some(opt => opt.name === sale.SelectedShipping)) {
      shippingOptions.push({
        type: 'Custom',
        price: parseFloat(sale.ShippingPrice) || 0,
        method: 'Selected',
        name: sale.SelectedShipping
      });
    }

    // Check for ShippingType field which might contain a numeric code
    if (sale.ShippingType && !isNaN(sale.ShippingType)) {
      const typeMap = {
        '1': 'North Island',
        '2': 'South Island',
        '3': 'Rural Delivery',
        '4': 'North Island Rural',
        '5': 'South Island Rural',
        '6': 'Pick-up Only'
      };

      const shippingName = typeMap[sale.ShippingType] || `Type ${sale.ShippingType}`;

      if (!shippingOptions.some(opt => opt.name === shippingName)) {
        shippingOptions.push({
          type: 'Custom',
          price: parseFloat(sale.ShippingPrice) || 0,
          method: sale.ShippingType,
          name: shippingName
        });
      }
    }
  }

  return shippingOptions;
}

module.exports = {
  parseShippingOptions
};
