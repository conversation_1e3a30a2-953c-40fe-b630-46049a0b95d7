const { Service } = require('node-windows');
const path = require('path');

// Create a new service object
const svc = new Service({
  name: 'GH0ST Application',
  script: path.join(__dirname, 'server.js')
});

// Listen for the "uninstall" event
svc.on('uninstall', function() {
  console.log('Service uninstalled successfully!');
});

// Listen for the "error" event
svc.on('error', function(err) {
  console.error('Service error:', err);
});

console.log('Uninstalling the service...');
svc.uninstall();
