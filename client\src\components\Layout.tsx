import { Outlet } from "react-router-dom";
import { GhostSidebar } from "./GhostSidebar";
import { Toaster } from "./ui/toaster";
import { SidebarInset, SidebarProvider } from "./ui/sidebar";
import { ScrollArea } from "./ui/scroll-area";

export function Layout() {
  return (
    <SidebarProvider className="h-screen w-screen overflow-hidden">
      <GhostSidebar />
      <SidebarInset className="overflow-hidden">
        <ScrollArea className="h-screen w-full">
          <div className="flex-1 flex-col p-8 md:p-8">
            <div className="mx-auto w-full max-w-6xl">
              <Outlet />
            </div>
          </div>
        </ScrollArea>
      </SidebarInset>
      <Toaster />
    </SidebarProvider>
  );
}

export default Layout;