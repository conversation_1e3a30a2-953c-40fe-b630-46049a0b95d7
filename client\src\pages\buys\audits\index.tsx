import { useState, useEffect } from 'react';
import { Routes, Route, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, Plus, Search, FileText, Edit, Trash2 } from 'lucide-react';
import { useToast } from '@/hooks/useToast';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import { AuditDetails } from './AuditDetails';
import NewAuditForm from './NewAuditForm';
import { AuditDashboard } from '@/components/buys/audits/AuditDashboard';
import { FlaggedAudits } from '@/components/buys/audits/FlaggedAudits';
import { AuditReporting } from '@/components/buys/audits/AuditReporting';
import { AuditList } from '@/components/buys/audits/AuditList';
import { StaffPerformance } from '@/components/buys/audits/StaffPerformance';
import { AuditMetrics } from '@/components/buys/audits/AuditMetrics';
import { AuditExports } from '@/components/buys/audits/AuditExports';

// Mock data for audits
const mockAudits = [
  {
    id: '1',
    auditType: 'buy',
    transactionId: 'BUY-2025-001',
    employeeName: 'John Smith',
    date: '2025-04-28',
    overallAssessment: 'pass',
    score: 85,
  },
  {
    id: '2',
    auditType: 'pawn',
    transactionId: 'PAWN-2025-002',
    employeeName: 'Jane Doe',
    date: '2025-04-27',
    overallAssessment: 'fail',
    score: 45,
  },
  {
    id: '3',
    auditType: 'price',
    transactionId: 'PRICE-2025-003',
    employeeName: 'Mike Johnson',
    date: '2025-04-26',
    overallAssessment: 'partial',
    score: 70,
  },
  {
    id: '4',
    auditType: 'buy',
    transactionId: 'BUY-2025-004',
    employeeName: 'Sarah Williams',
    date: '2025-04-25',
    overallAssessment: 'pass',
    score: 90,
  },
  {
    id: '5',
    auditType: 'pawn',
    transactionId: 'PAWN-2025-005',
    employeeName: 'David Brown',
    date: '2025-04-24',
    overallAssessment: 'partial',
    score: 65,
  },
];

/**
 * This component handles routing for the Buy/Pawn Audit feature
 */
export function BuyPawnAudits() {
  return (
    <Routes>
      {/* Main routes */}
      <Route index element={<AuditDashboard />} />
      <Route path="list" element={<AuditList />} />
      <Route path="new" element={<NewAuditForm />} />
      <Route path="flagged" element={<FlaggedAudits />} />
      <Route path="reporting" element={<AuditReporting />} />
      <Route path="staff/*" element={<StaffPerformance />} />
      <Route path="metrics" element={<AuditMetrics />} />
      <Route path="exports" element={<AuditExports />} />

      {/* Detail routes */}
      <Route path=":id" element={<AuditDetails />} />
      <Route path=":id/edit" element={<div>Edit Audit Page (Not Implemented)</div>} />
    </Routes>
  );
}

function AuditListPage() {
  const [audits, setAudits] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const navigate = useNavigate();
  const { toast } = useToast();

  // Fetch audits from API
  useEffect(() => {
    const fetchAudits = async () => {
      try {
        setLoading(true);
        // In a real application, you would fetch from your API
        // const response = await fetch('/api/buys/audits');
        // const data = await response.json();

        // Using mock data for now
        setTimeout(() => {
          setAudits(mockAudits);
          setTotalPages(Math.ceil(mockAudits.length / 10));
          setLoading(false);
        }, 500);
      } catch (error) {
        console.error('Error fetching audits:', error);
        toast({
          title: 'Error fetching audits',
          description: 'There was an error fetching the audits. Please try again.',
          variant: 'destructive',
        });
        setLoading(false);
      }
    };

    fetchAudits();
  }, [toast]);

  // Filter audits based on search term and filters
  const filteredAudits = audits.filter((audit) => {
    const matchesSearch =
      searchTerm === '' ||
      audit.transactionId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      audit.employeeName.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesType = filterType === 'all' || audit.auditType === filterType;

    const matchesStatus = filterStatus === 'all' || audit.overallAssessment === filterStatus;

    return matchesSearch && matchesType && matchesStatus;
  });

  // Handle audit deletion
  const handleDeleteAudit = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this audit?')) {
      try {
        // In a real application, you would call your API
        // await fetch(`/api/buys/audits/${id}`, {
        //   method: 'DELETE',
        // });

        // Using mock data for now
        setAudits(audits.filter((audit) => audit.id !== id));

        toast({
          title: 'Audit deleted',
          description: 'The audit has been deleted successfully.',
          variant: 'default',
        });
      } catch (error) {
        console.error('Error deleting audit:', error);
        toast({
          title: 'Error deleting audit',
          description: 'There was an error deleting the audit. Please try again.',
          variant: 'destructive',
        });
      }
    }
  };

  // Get status badge color
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pass':
        return <Badge className="bg-green-500">Pass</Badge>;
      case 'fail':
        return <Badge className="bg-red-500">Fail</Badge>;
      case 'partial':
        return <Badge className="bg-yellow-500">Partial</Badge>;
      default:
        return <Badge>Unknown</Badge>;
    }
  };

  // Get audit type display name
  const getAuditTypeDisplay = (type: string) => {
    switch (type) {
      case 'buy':
        return 'Buy Deal';
      case 'pawn':
        return 'Pawn Loan';
      case 'price':
        return 'Price';
      default:
        return 'Unknown';
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Audits</h1>
        <Button onClick={() => navigate('/buys/audits/create')}>
          <Plus className="mr-2 h-4 w-4" />
          Create New Audit
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Audit List</CardTitle>
          <CardDescription>
            View and manage all audits
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1 relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by transaction ID or employee name"
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="flex gap-2">
              <Select value={filterType} onValueChange={setFilterType}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="buy">Buy Deal</SelectItem>
                  <SelectItem value="pawn">Pawn Loan</SelectItem>
                  <SelectItem value="price">Price</SelectItem>
                </SelectContent>
              </Select>

              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="pass">Pass</SelectItem>
                  <SelectItem value="fail">Fail</SelectItem>
                  <SelectItem value="partial">Partial</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {loading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : filteredAudits.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No audits found. Try adjusting your filters or create a new audit.
            </div>
          ) : (
            <>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Transaction ID</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Employee</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Score</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredAudits.map((audit) => (
                      <TableRow key={audit.id}>
                        <TableCell className="font-medium">{audit.transactionId}</TableCell>
                        <TableCell>{getAuditTypeDisplay(audit.auditType)}</TableCell>
                        <TableCell>{audit.employeeName}</TableCell>
                        <TableCell>{formatDate(audit.date)}</TableCell>
                        <TableCell>{getStatusBadge(audit.overallAssessment)}</TableCell>
                        <TableCell>{audit.score}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => navigate(`/buys/audits/${audit.id}`)}
                              title="View"
                            >
                              <FileText className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => navigate(`/buys/audits/${audit.id}/edit`)}
                              title="Edit"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleDeleteAudit(audit.id)}
                              title="Delete"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              <Pagination className="mt-4">
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      className={currentPage === 1 ? 'pointer-events-none opacity-50' : ''}
                    />
                  </PaginationItem>
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                    <PaginationItem key={page}>
                      <PaginationLink
                        onClick={() => setCurrentPage(page)}
                        isActive={currentPage === page}
                      >
                        {page}
                      </PaginationLink>
                    </PaginationItem>
                  ))}
                  <PaginationItem>
                    <PaginationNext
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      className={currentPage === totalPages ? 'pointer-events-none opacity-50' : ''}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
