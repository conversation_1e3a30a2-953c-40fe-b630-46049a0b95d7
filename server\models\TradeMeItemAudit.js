const mongoose = require('mongoose');

/**
 * TradeMeItemAudit Schema
 * Tracks all changes to TradeMe items for audit purposes
 */
const tradeMeItemAuditSchema = new mongoose.Schema({
  // Reference to the TradeMe item
  itemId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'TradeMeItems',
    required: true,
    index: true
  },

  // Current TradeMe listing ID (if applicable)
  trademeListingId: {
    type: String,
    index: true
  },

  // Type of action performed
  action: {
    type: String,
    required: true,
    enum: [
      'create',              // Item created
      'update',              // General update
      'status_change',       // Primary status changed
      'sold_status_change',  // Secondary sold status changed
      'price_change',        // Price changed
      'list',                // Listed on TradeMe
      'relist',              // Relisted on TradeMe
      'withdraw',            // Withdrawn from TradeMe
      'question_added',      // Question received
      'question_answered',   // Question answered
      'image_added',         // Image added
      'image_removed',       // Image removed
      'comment_added',       // Internal comment added
      'archive',             // Item archived
      'import'               // Item imported from TradeMe
    ]
  },

  // Details of the change
  details: {
    type: mongoose.Schema.Types.Mixed,
    required: true
  },

  // Previous values (for changes)
  previousValues: {
    type: mongoose.Schema.Types.Mixed
  },

  // New values (for changes)
  newValues: {
    type: mongoose.Schema.Types.Mixed
  },

  // User who performed the action
  performedBy: {
    type: mongoose.Schema.Types.Mixed, // Can be ObjectId (for User) or String (for 'Imported')
    ref: 'User',
    required: true,
    get: v => v === 'Imported' ? v : v
  },

  // Timestamp
  timestamp: {
    type: Date,
    default: Date.now,
    immutable: true
  }
}, {
  versionKey: false,
  toJSON: { getters: true },
  toObject: { getters: true }
});

// Indexes for efficient querying
tradeMeItemAuditSchema.index({ itemId: 1, timestamp: -1 });
tradeMeItemAuditSchema.index({ performedBy: 1, timestamp: -1 });
tradeMeItemAuditSchema.index({ action: 1, timestamp: -1 });
tradeMeItemAuditSchema.index({ trademeListingId: 1, timestamp: -1 });

const TradeMeItemAudit = mongoose.model('TradeMeItemAudit', tradeMeItemAuditSchema);

module.exports = TradeMeItemAudit;
