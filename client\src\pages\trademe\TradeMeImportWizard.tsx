import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, AlertCircle, ArrowLeft, CheckCircle2, Save, ChevronLeft, ChevronRight } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import ImportItemForm from '@/components/trademe/ImportItemForm';
import { getImportLog, getImportLogItems, saveImportedItem, skipImportedItem } from '@/api/tradeMeImport';
import { toast } from 'sonner';

// Interface for progress data
interface ImportProgress {
  currentItemIndex: number;
  completedItems: string[];
  skippedItems: string[];
  timestamp: number;
}

const TradeMeImportWizard = () => {
  // We still need useAuth for the protected route
  useAuth();
  const navigate = useNavigate();
  const { logId } = useParams<{ logId: string }>();

  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [importLog, setImportLog] = useState<any>(null);

  const [items, setItems] = useState<any[]>([]);

  const [currentItemIndex, setCurrentItemIndex] = useState(0);
  const [completedItems, setCompletedItems] = useState<string[]>([]);
  const [skippedItems, setSkippedItems] = useState<string[]>([]);

  // Helper function to save progress to localStorage
  const saveProgress = () => {
    if (!logId || items.length === 0) return;

    // Determine import type based on the import log's listingType or the first item's status
    const importType = importLog?.listingType || (items[0]?.status === 'sold' ? 'sold' : 'active');

    const progress: ImportProgress = {
      currentItemIndex,
      completedItems,
      skippedItems,
      timestamp: Date.now(),
      importType,
      totalItems: items.length
    };

    localStorage.setItem(`trademe-import-progress-${logId}`, JSON.stringify(progress));
    console.log('Progress saved:', progress);
  };

  // Helper function to load progress from localStorage
  const loadProgress = () => {
    if (!logId) return false;

    try {
      const savedProgress = localStorage.getItem(`trademe-import-progress-${logId}`);
      if (!savedProgress) return false;

      const progress: ImportProgress = JSON.parse(savedProgress);

      // Check if progress is recent (within 7 days)
      const isRecent = (Date.now() - progress.timestamp) < 7 * 24 * 60 * 60 * 1000;
      if (!isRecent) {
        localStorage.removeItem(`trademe-import-progress-${logId}`);
        return false;
      }

      // Restore progress
      setCurrentItemIndex(progress.currentItemIndex);
      setCompletedItems(progress.completedItems);
      setSkippedItems(progress.skippedItems);

      console.log('Progress restored:', progress);
      return true;
    } catch (error) {
      console.error('Error loading progress:', error);
      return false;
    }
  };

  // Load import log and items
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Load import log
        if (logId) {
          const logResult = await getImportLog(logId);
          if (logResult.success) {
            setImportLog(logResult.log);
          } else {
            throw new Error(logResult.error || 'Failed to load import log');
          }

          // Load items from the import log
          const itemsResult = await getImportLogItems(logId);
          if (itemsResult.success) {
            // Filter out items that have already been processed (saved or skipped)
            const pendingItems = itemsResult.items.filter((item: any) => item.status === 'pending');
            setItems(pendingItems);
          } else {
            throw new Error(itemsResult.error || 'Failed to load items from import log');
          }

          // Try to load saved progress after items are loaded
          setTimeout(() => {
            const progressLoaded = loadProgress();
            if (progressLoaded) {
              toast.info('Progress restored from your last session');
            }
            setIsLoading(false);
          }, 100);
        } else {
          throw new Error('No import log ID provided');
        }
      } catch (error: any) {
        console.error('Error loading import wizard data:', error);
        setError(error.message || 'Failed to load data');
        toast.error(`Error: ${error.message}`);
        setIsLoading(false);
      }
    };

    loadData();
  }, [logId]);

  // Save progress when it changes
  useEffect(() => {
    if (items.length > 0 && !isLoading) {
      saveProgress();
    }
  }, [currentItemIndex, completedItems, skippedItems, items]);

  // Handle item save
  const handleSaveItem = async (updatedItem: any) => {
    try {
      // Save the item to the database
      const currentItem = items[currentItemIndex];
      const result = await saveImportedItem(logId!, currentItem.index, updatedItem);

      if (result.success) {
        setCompletedItems(prev => [...prev, currentItem.index]);
        toast.success('Item saved successfully');

        // Move to next item
        if (currentItemIndex < items.length - 1) {
          setCurrentItemIndex(prev => prev + 1);
        } else {
          // All items completed
          // Remove progress from localStorage
          if (logId) {
            localStorage.removeItem(`trademe-import-progress-${logId}`);
            console.log('Import completed, progress removed from localStorage');
          }
          toast.success('All items have been updated!');
          navigate('/trademe/selling');
        }
      } else {
        toast.error(`Failed to save item: ${result.error}`);
      }
    } catch (error: any) {
      console.error('Error saving item:', error);
      toast.error(`Error saving item: ${error.message}`);
    }
  };

  // Handle skip
  const handleSkipItem = async () => {
    try {
      // Mark the item as skipped
      const currentItem = items[currentItemIndex];
      const result = await skipImportedItem(logId!, currentItem.index);

      if (result.success) {
        setSkippedItems(prev => [...prev, currentItem.index]);
        toast.info('Item skipped');

        // Move to next item
        if (currentItemIndex < items.length - 1) {
          setCurrentItemIndex(prev => prev + 1);
        } else {
          // All items completed or skipped
          // Remove progress from localStorage
          if (logId) {
            localStorage.removeItem(`trademe-import-progress-${logId}`);
            console.log('Import completed, progress removed from localStorage');
          }
          toast.success('Import wizard completed!');
          navigate('/trademe/selling');
        }
      } else {
        toast.error(`Failed to skip item: ${result.error}`);
      }
    } catch (error: any) {
      console.error('Error skipping item:', error);
      toast.error(`Error skipping item: ${error.message}`);
    }
  };

  // Calculate progress
  const progress = items.length > 0
    ? Math.round(((completedItems.length + skippedItems.length) / items.length) * 100)
    : 0;

  // Current item
  const currentItem = items[currentItemIndex];

  return (
    <div className="space-y-6  mx-auto px-4">
      <div className="flex justify-between items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Import Wizard</h1>
          {importLog && (
            <div className="text-muted-foreground text-sm mt-1 flex items-center gap-2">
              <span>Import batch: <Badge variant="outline">{importLog.name}</Badge></span>
              <Badge variant="outline" className={importLog.listingType === 'sold' ? 'bg-amber-500/10 text-amber-600' : 'bg-green-500/10 text-green-600'}>
                {importLog.listingType === 'sold' ? 'Sold Items' : 'Active Listings'}
              </Badge>
            </div>
          )}
        </div>
        <Button variant="outline" onClick={() => navigate('/trademe/import')}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Imports
        </Button>
      </div>

      <Separator className="my-4" />

      {isLoading ? (
        <Card className="min-h-[300px] flex items-center justify-center">
          <CardContent className="flex flex-col items-center gap-4 py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <p className="text-muted-foreground">Loading import items...</p>
          </CardContent>
        </Card>
      ) : error ? (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      ) : items.length === 0 ? (
        <Card>
          <CardContent className="py-12 flex flex-col items-center text-center">
            <CheckCircle2 className="h-12 w-12 text-green-500 mb-4" />
            <h3 className="text-xl font-medium mb-2">No Items Need Updating</h3>
            <p className="text-muted-foreground mb-6 max-w-md">
              All imported items have the required information.
            </p>
            <Button onClick={() => navigate('/trademe/items')}>
              View All Items
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-6">
          <Card>
            <CardContent className="py-4">
              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Progress</span>
                  <span className="font-medium">{progress}%</span>
                </div>
                <Progress value={progress} className="h-2" />
                <div className="flex justify-between items-center text-sm">
                  <div className="flex gap-4">
                    <span className="text-green-600">
                      <CheckCircle2 className="inline h-4 w-4 mr-1" />
                      {completedItems.length} completed
                    </span>
                    <span className="text-yellow-600">
                      <AlertCircle className="inline h-4 w-4 mr-1" />
                      {skippedItems.length} skipped
                    </span>
                  </div>
                  <span className="text-muted-foreground">
                    Item {currentItemIndex + 1} of {items.length}
                  </span>
                </div>
                <div className="flex justify-between pt-2">
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleSkipItem}
                      disabled={currentItemIndex >= items.length - 1}
                    >
                      Skip
                    </Button>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            saveProgress();
                            toast.success('Progress saved');
                          }}
                          className="text-xs"
                        >
                          <Save className="w-3 h-3 mr-1" />
                          Save
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        Save your current progress
                      </TooltipContent>
                    </Tooltip>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentItemIndex(prev => Math.max(0, prev - 1))}
                      disabled={currentItemIndex === 0}
                    >
                      <ChevronLeft className="h-4 w-4 mr-1" />
                      Previous
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentItemIndex(prev => Math.min(items.length - 1, prev + 1))}
                      disabled={currentItemIndex >= items.length - 1}
                    >
                      Next
                      <ChevronRight className="h-4 w-4 ml-1" />
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {currentItem && (
            <ImportItemForm
              item={currentItem}
              locations={[]}
              onSave={handleSaveItem}
              onSkip={handleSkipItem}
            />
          )}
        </div>
      )}
    </div>
  );
};

export default TradeMeImportWizard;
