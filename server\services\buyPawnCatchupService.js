/**
 * Buy/Pawn Catchup Service
 * 
 * Handles operations related to buy/pawn staff catch-up notes, including:
 * - Creating and updating catch-up notes
 * - Retrieving and filtering catch-up notes
 */

const BuyPawnCatchup = require('../models/BuyPawnCatchup');
const BuyPawnAudit = require('../models/BuyPawnAudit');
const mongoose = require('mongoose');

/**
 * Create a new catch-up note for an audit
 * @param {string} auditId - The audit ID
 * @param {Object} catchupData - The catch-up data
 * @param {Object} user - The user creating the catch-up note
 * @returns {Promise<Object>} - The created catch-up note
 */
const createCatchup = async (auditId, catchupData, user) => {
  try {
    // Check if audit exists
    const auditResult = await BuyPawnAudit.findById(auditId).populate('dealId');
    if (!auditResult) {
      return { success: false, error: 'Audit not found' };
    }
    
    // Create new catch-up note
    const catchup = new BuyPawnCatchup({
      auditId,
      dealId: auditResult.dealId._id,
      transactionId: auditResult.transactionId,
      employee: catchupData.employee,
      conductedBy: user._id,
      date: catchupData.date || new Date(),
      findingsDiscussed: catchupData.findingsDiscussed,
      commentsBefore: catchupData.commentsBefore,
      discussionPoints: catchupData.discussionPoints,
      employeeResponse: catchupData.employeeResponse,
      agreedActions: catchupData.agreedActions,
      commentsAfter: catchupData.commentsAfter,
      status: 'draft'
    });
    
    await catchup.save();
    
    return { success: true, data: catchup };
  } catch (error) {
    console.error('Error creating buy/pawn catchup note:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get a catch-up note by ID
 * @param {string} id - The catch-up note ID
 * @returns {Promise<Object>} - The catch-up note
 */
const getCatchupById = async (id) => {
  try {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return { success: false, error: 'Invalid catch-up note ID' };
    }
    
    const catchup = await BuyPawnCatchup.findById(id)
      .populate('auditId')
      .populate('dealId')
      .populate('employee', 'username fullName')
      .populate('conductedBy', 'username fullName');
    
    if (!catchup) {
      return { success: false, error: 'Catch-up note not found' };
    }
    
    return { success: true, data: catchup };
  } catch (error) {
    console.error('Error getting buy/pawn catchup note by ID:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get catch-up notes by audit ID
 * @param {string} auditId - The audit ID
 * @returns {Promise<Object>} - The catch-up notes
 */
const getCatchupsByAuditId = async (auditId) => {
  try {
    if (!mongoose.Types.ObjectId.isValid(auditId)) {
      return { success: false, error: 'Invalid audit ID' };
    }
    
    const catchups = await BuyPawnCatchup.find({ auditId })
      .populate('employee', 'username fullName')
      .populate('conductedBy', 'username fullName')
      .sort({ date: -1 });
    
    return { success: true, data: catchups };
  } catch (error) {
    console.error('Error getting buy/pawn catchup notes by audit ID:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get catch-up notes by employee ID
 * @param {string} employeeId - The employee ID
 * @returns {Promise<Object>} - The catch-up notes
 */
const getCatchupsByEmployeeId = async (employeeId) => {
  try {
    if (!mongoose.Types.ObjectId.isValid(employeeId)) {
      return { success: false, error: 'Invalid employee ID' };
    }
    
    const catchups = await BuyPawnCatchup.find({ employee: employeeId })
      .populate('auditId')
      .populate('dealId')
      .populate('conductedBy', 'username fullName')
      .sort({ date: -1 });
    
    return { success: true, data: catchups };
  } catch (error) {
    console.error('Error getting buy/pawn catchup notes by employee ID:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Update a catch-up note
 * @param {string} id - The catch-up note ID
 * @param {Object} updateData - The data to update
 * @param {Object} user - The user updating the catch-up note
 * @returns {Promise<Object>} - The updated catch-up note
 */
const updateCatchup = async (id, updateData, user) => {
  try {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return { success: false, error: 'Invalid catch-up note ID' };
    }
    
    // Get the catch-up note
    const catchup = await BuyPawnCatchup.findById(id);
    if (!catchup) {
      return { success: false, error: 'Catch-up note not found' };
    }
    
    // Check if catch-up note is already finalized
    if (catchup.status === 'finalized' && updateData.status !== 'finalized') {
      return { success: false, error: 'Cannot update a finalized catch-up note' };
    }
    
    // Update catch-up note fields
    Object.keys(updateData).forEach(key => {
      if (key !== '_id' && key !== 'auditId' && key !== 'dealId' && key !== 'transactionId') {
        catchup[key] = updateData[key];
      }
    });
    
    // If finalizing the catch-up note, set finalizedAt
    if (updateData.status === 'finalized' && catchup.status !== 'finalized') {
      catchup.finalizedAt = new Date();
    }
    
    await catchup.save();
    
    return { success: true, data: catchup };
  } catch (error) {
    console.error('Error updating buy/pawn catchup note:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get catch-up notes with filtering and pagination
 * @param {Object} options - The filter options
 * @returns {Promise<Object>} - The catch-up notes and pagination info
 */
const getCatchups = async (options) => {
  try {
    const {
      status,
      employee,
      conductedBy,
      startDate,
      endDate,
      page = 1,
      limit = 20,
      sort = 'date',
      sortDirection = 'desc'
    } = options;
    
    // Build query
    const query = {};
    
    // Filter by status
    if (status) {
      query.status = status;
    }
    
    // Filter by employee
    if (employee) {
      query.employee = employee;
    }
    
    // Filter by conductor
    if (conductedBy) {
      query.conductedBy = conductedBy;
    }
    
    // Filter by date range
    if (startDate || endDate) {
      query.date = {};
      if (startDate) {
        query.date.$gte = new Date(startDate);
      }
      if (endDate) {
        query.date.$lte = new Date(endDate);
      }
    }
    
    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    // Build sort
    const sortObj = {};
    sortObj[sort] = sortDirection === 'asc' ? 1 : -1;
    
    // Get total count
    const total = await BuyPawnCatchup.countDocuments(query);
    
    // Get catch-up notes
    const catchups = await BuyPawnCatchup.find(query)
      .populate('auditId')
      .populate('dealId')
      .populate('employee', 'username fullName')
      .populate('conductedBy', 'username fullName')
      .sort(sortObj)
      .skip(skip)
      .limit(parseInt(limit));
    
    return {
      success: true,
      data: catchups,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / parseInt(limit))
      }
    };
  } catch (error) {
    console.error('Error getting buy/pawn catchup notes:', error);
    return { success: false, error: error.message };
  }
};

module.exports = {
  createCatchup,
  getCatchupById,
  getCatchupsByAuditId,
  getCatchupsByEmployeeId,
  updateCatchup,
  getCatchups
};
