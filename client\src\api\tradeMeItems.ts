import api from './api';

export type ListingStatus = 'draft' | 'queued' | 'active' | 'sold' | 'archived' | 'unsold';

export type SaleStatus =
  | 'instant_payment_received'
  | 'bank_payment_received'
  | 'email_sent'
  | 'awaiting_pickup_paid'
  | 'awaiting_pickup_unpaid'
  | 'awaiting_packaging'
  | 'awaiting_packaging_paid'
  | 'awaiting_return_package'
  | 'awaiting_payment'
  | 'refunded'
  | 'cancelled'
  | 'no_status'
  | null;

export const getSaleStatusLabel = (status: SaleStatus): string => {
  const labels: Record<NonNullable<SaleStatus>, string> = {
    instant_payment_received: 'Instant Payment Received',
    bank_payment_received: 'Bank Payment Received',
    email_sent: 'Email Sent',
    awaiting_pickup_paid: 'Awaiting In-store Pickup (Paid)',
    awaiting_pickup_unpaid: 'Awaiting In-store Pickup (Unpaid)',
    awaiting_packaging: 'Awaiting Packaging',
    awaiting_packaging_paid: 'Awaiting Packaging (Paid)',
    awaiting_return_package: 'Awaiting Return Package',
    awaiting_payment: 'Awaiting Payment',
    refunded: 'Refunded',
    cancelled: 'Sale Cancelled',
    no_status: 'No Status'
  };
  return status ? labels[status] : 'No Sale Status';
};

export interface TradeMeItem {
  _id: string;
  title: string;
  subtitle?: string;
  description: string;
  currentPrice: number;
  reservePrice?: number;
  buyNowPrice?: number;
  allowOffers?: boolean;
  authenticatedBidders?: boolean;
  status: ListingStatus;
  soldStatus: SaleStatus;
  soldStatusUpdatedAt?: string;
  soldStatusUpdatedBy?: {
    _id: string;
    username: string;
    fullName?: string;
  };
  location: string;
  locationId: string;
  stockCode?: string;
  category: string;
  categoryPath?: string;
  isNew?: boolean;
  images: string[];
  currentListingId?: string;
  cost?: number;

  // Buyer payment information
  buyerPaymentInfo?: {
    tradeMeBuyer?: string;
    purchaseId?: string;
    referenceNumber?: string;
    selectedShipping?: string;
    messageFromBuyer?: string;
    hasSellerPlacedFeedback?: boolean;
    hasBuyerPlacedFeedback?: boolean;
    isClearance?: boolean;
    hasPaidByCreditCard?: boolean;
    creditCardType?: string;
    creditCardLastFourDigits?: string;
    creditCardPaymentDate?: string;
    isPaymentPending?: boolean;
    paymentType?: string;
    paymentAmount?: any; // Using any to handle Decimal128
    paymentMethodFee?: any; // Using any to handle Decimal128
    gstCollected?: any; // Using any to handle Decimal128
    pingEscrowStatus?: string;
    isPayNowPurchase?: boolean;
    payNowRefundValue?: any; // Using any to handle Decimal128
    refunds?: Array<{
      amount: any; // Using any to handle Decimal128
      destination?: string;
      date?: string;
    }>;
  };

  // Listing history
  listingHistory: Array<{
    _id: string;
    trademeListingId: string;
    startDate: string;
    endDate: string;
    startPrice: number;
    buyNowPrice?: number;
    views: number;
    watchers: number;
    bids: number;
    status: 'active' | 'sold' | 'unsold' | 'archived';
    soldPrice?: number;
    soldDate?: string;
    buyer?: string;
    createdBy: {
      _id: string;
      username: string;
      fullName?: string;
    };
  }>;

  // Questions
  questions: Array<{
    _id: string;
    trademeQuestionId: string;
    question: string;
    answer?: string;
    askerName: string;
    askDate: string;
    answerDate?: string;
    isAnswered: boolean;
    answeredBy?: {
      _id: string;
      username: string;
      fullName?: string;
    };
    originalListingId: string;
  }>;

  // Notes
  notes: Array<{
    _id: string;
    text: string;
    createdBy: {
      _id: string;
      username: string;
      fullName?: string;
    };
    createdAt: string;
  }>;

  // Fault tracking
  isFaulty?: boolean;
  faultCategory?: string;
  faultDescription?: string;
  faultReportedBy?: {
    _id: string;
    username: string;
    fullName?: string;
  };
  faultReportedAt?: string;

  // Relisting rules
  relistingRules?: {
    autoRelist: boolean;
    maxRelistCount?: number;
    priceReduction?: number;
    minPrice?: number;
  };

  // Physical location tracking
  physicalLocation?: string;

  // Shipping options
  shippingOptions?: Array<{
    name: string;
    cost?: number;
    price?: any; // Using any to handle Decimal128
    details?: string;
    type?: string;
    method?: string;
  }>;

  // Payment methods
  paymentMethods?: {
    ping?: boolean;
    afterpay?: boolean;
    cash?: boolean;
    bankDeposit?: boolean;
    other?: boolean;
    otherDetails?: string;
  };

  // Promotional flags
  isBold?: boolean;
  isFeatured?: boolean;
  hasGallery?: boolean;
  isHighlighted?: boolean;

  // Was price (for clearance items)
  wasPrice?: number;

  // Embedded content (YouTube videos, etc.)
  embeddedContent?: {
    youTubeVideoKey?: string;
    vimeoVideoKey?: string;
  };

  // Geographic location
  geographicLocation?: {
    latitude?: number;
    longitude?: number;
    accuracy?: 'None' | 'Address' | 'Street' | 'Suburb';
  };

  // Metadata
  createdBy: {
    _id: string;
    username: string;
    fullName?: string;
  };
  createdAt: string;
  updatedAt: string;
  updatedBy?: {
    _id: string;
    username: string;
    fullName?: string;
  };
}

export interface TradeMeItemCreateData {
  title: string;
  subtitle?: string;
  description: string;
  currentPrice: number;
  reservePrice?: number;
  buyNowPrice?: number;
  allowOffers?: boolean;
  authenticatedBidders?: boolean;
  category: string;
  categoryPath?: string;
  location: string;
  locationId: string;
  stockCode?: string;
  isNew?: boolean;
  images?: string[];

  // Shipping options
  shippingOptions?: Array<{
    name: string;
    cost?: number;
    price?: any; // Using any to handle Decimal128
    details?: string;
    type?: string;
    method?: string;
  }>;

  // Payment methods
  paymentMethods?: {
    ping?: boolean;
    afterpay?: boolean;
    cash?: boolean;
    bankDeposit?: boolean;
    other?: boolean;
    otherDetails?: string;
  };

  // Promotional flags
  isBold?: boolean;
  isFeatured?: boolean;
  hasGallery?: boolean;
  isHighlighted?: boolean;

  // Was price (for clearance items)
  wasPrice?: number;

  // Embedded content (YouTube videos, etc.)
  embeddedContent?: {
    youTubeVideoKey?: string;
    vimeoVideoKey?: string;
  };

  // Geographic location
  geographicLocation?: {
    latitude?: number;
    longitude?: number;
    accuracy?: 'None' | 'Address' | 'Street' | 'Suburb';
  };

  // Relisting rules
  relistingRules?: {
    autoRelist: boolean;
    maxRelistCount?: number;
    priceReduction?: number;
    minPrice?: number;
  };

  // Physical location tracking
  physicalLocation?: string;

  // Status
  status?: 'draft' | 'queued';
}

export interface PaginationData {
  total: number;
  page: number;
  pages: number;
  limit: number;
}

/**
 * Get TradeMe items with filtering and pagination
 * @param params - Filter and pagination parameters
 * @returns Promise with items and pagination data
 */
export const getItems = async (params?: {
  status?: ListingStatus,
  location?: string,
  page?: number,
  limit?: number,
  sync?: boolean,
  search?: string,
  filter?: string,
  sort?: string,
  days?: number,
  soldStatus?: SaleStatus
}) => {
  try {
    const queryParams = new URLSearchParams();

    if (params?.status) {
      queryParams.append('status', params.status);
    }

    if (params?.location) {
      queryParams.append('location', params.location);
    }

    if (params?.page) {
      queryParams.append('page', params.page.toString());
    }

    if (params?.limit) {
      queryParams.append('limit', params.limit.toString());
    }

    // Always sync on first load
    queryParams.append('sync', (params?.sync === true || !params).toString());

    // Add search parameter if provided
    if (params?.search) {
      queryParams.append('search', params.search);
    }

    // Add filter parameter if provided
    if (params?.filter) {
      queryParams.append('filter', params.filter);
    }

    // Add sort parameter if provided
    if (params?.sort) {
      queryParams.append('sort', params.sort);
    }

    // Add days parameter if provided
    if (params?.days) {
      queryParams.append('days', params.days.toString());
    }

    // Add soldStatus parameter if provided
    if (params?.soldStatus !== undefined) {
      queryParams.append('soldStatus', params.soldStatus === null ? 'null' : params.soldStatus);
    }

    const queryString = queryParams.toString();
    const url = `/trademe/items${queryString ? `?${queryString}` : ''}`;

    const response = await api.get(url);
    return response.data;
  } catch (error: any) {
    console.error('TradeMe API - Error in getItems:', error);
    console.error('TradeMe API - Error details:', error?.response?.data);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

/**
 * Get a single TradeMe item by ID
 * @param id - Item ID
 * @returns Promise with item
 */
export const getItemById = async (id: string) => {
  try {
    const response = await api.get(`/trademe/items/${id}`);
    return response.data;
  } catch (error: any) {
    console.error('TradeMe API - Error in getItemById:', error);
    console.error('TradeMe API - Error details:', error?.response?.data);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

/**
 * Create a new TradeMe item
 * @param data - Item data or FormData for file uploads
 * @returns Promise with created item
 */
export const createItem = async (data: TradeMeItemCreateData | FormData) => {
  try {
    const headers = data instanceof FormData ?
      { 'Content-Type': 'multipart/form-data' } :
      { 'Content-Type': 'application/json' };

    const response = await api.post('/trademe/items', data, { headers });
    return response.data;
  } catch (error: any) {
    console.error('TradeMe API - Error in createItem:', error);
    console.error('TradeMe API - Error details:', error?.response?.data);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

/**
 * Update a TradeMe item
 * @param id - Item ID
 * @param data - Updated item data or FormData for file uploads
 * @returns Promise with updated item
 */
export const updateItem = async (id: string, data: Partial<TradeMeItemCreateData> | FormData) => {
  try {
    const headers = data instanceof FormData ?
      { 'Content-Type': 'multipart/form-data' } :
      { 'Content-Type': 'application/json' };

    const response = await api.put(`/trademe/items/${id}`, data, { headers });
    return response.data;
  } catch (error: any) {
    console.error('TradeMe API - Error in updateItem:', error);
    console.error('TradeMe API - Error details:', error?.response?.data);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

/**
 * List an item on TradeMe
 * @param id - Item ID
 * @returns Promise with result
 */
export const listItemOnTradeMe = async (id: string) => {
  try {
    const response = await api.post(`/trademe/items/${id}/list`);
    return response.data;
  } catch (error: any) {
    console.error('TradeMe API - Error in listItemOnTradeMe:', error);
    console.error('TradeMe API - Error details:', error?.response?.data);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

/**
 * Relist an item on TradeMe
 * @param id - Item ID
 * @returns Promise with result
 */
export const relistItemOnTradeMe = async (id: string) => {
  try {
    const response = await api.post(`/trademe/items/${id}/relist`);
    return response.data;
  } catch (error: any) {
    console.error('TradeMe API - Error in relistItemOnTradeMe:', error);
    console.error('TradeMe API - Error details:', error?.response?.data);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

/**
 * Archive an item
 * @param id - Item ID
 * @returns Promise with result
 */
export const archiveItem = async (id: string) => {
  try {
    const response = await api.post(`/trademe/items/${id}/archive`);
    return response.data;
  } catch (error: any) {
    console.error('TradeMe API - Error in archiveItem:', error);
    console.error('TradeMe API - Error details:', error?.response?.data);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

/**
 * Get audit history for an item
 * @param id - Item ID
 * @returns Promise with audit history
 */
export const getItemAuditHistory = async (id: string) => {
  try {
    const response = await api.get(`/trademe/items/${id}/audit`);
    return response.data;
  } catch (error: any) {
    console.error('TradeMe API - Error in getItemAuditHistory:', error);
    console.error('TradeMe API - Error details:', error?.response?.data);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

/**
 * Answer a question
 * @param itemId - Item ID
 * @param questionId - Question ID
 * @param answer - Answer text
 * @returns Promise with result
 */
export const answerQuestion = async (itemId: string, questionId: string, answer: string) => {
  try {
    const response = await api.post(`/trademe/items/${itemId}/questions/${questionId}/answer`, { answer });
    return response.data;
  } catch (error: any) {
    console.error('TradeMe API - Error in answerQuestion:', error);
    console.error('TradeMe API - Error details:', error?.response?.data);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

/**
 * Add a note to an item
 * @param itemId - Item ID
 * @param text - Note text
 * @returns Promise with result
 */
export const addNote = async (itemId: string, text: string) => {
  try {
    const response = await api.post(`/trademe/items/${itemId}/notes`, { text });
    return response.data;
  } catch (error: any) {
    console.error('TradeMe API - Error in addNote:', error);
    console.error('TradeMe API - Error details:', error?.response?.data);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

/**
 * Get notes for an item
 * @param itemId - Item ID
 * @returns Promise with notes
 */
export const getNotes = async (itemId: string) => {
  try {
    const response = await api.get(`/trademe/items/${itemId}/notes`);
    return response.data;
  } catch (error: any) {
    console.error('TradeMe API - Error in getNotes:', error);
    console.error('TradeMe API - Error details:', error?.response?.data);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

/**
 * Delete a note from an item
 * @param itemId - Item ID
 * @param noteId - Note ID
 * @returns Promise with result
 */
export const deleteNote = async (itemId: string, noteId: string) => {
  try {
    const response = await api.delete(`/trademe/items/${itemId}/notes/${noteId}`);
    return response.data;
  } catch (error: any) {
    console.error('TradeMe API - Error in deleteNote:', error);
    console.error('TradeMe API - Error details:', error?.response?.data);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

/**
 * Update a listing's sold status
 * @param id - Item ID
 * @param status - Sale status
 * @returns Promise with result
 */
export const updateSaleStatus = async (id: string, status: SaleStatus) => {
  try {
    const response = await api.put(`/trademe/items/${id}/sale-status`, { status });
    return response.data;
  } catch (error: any) {
    console.error('TradeMe API - Error in updateSaleStatus:', error);
    console.error('TradeMe API - Error details:', error?.response?.data);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Legacy API functions for backward compatibility

/**
 * @deprecated Use getItems instead
 */
export const getListings = async (params?: any) => {
  console.warn('Deprecated: Use getItems instead');
  return getItems(params);
};

/**
 * @deprecated Use getItemById instead
 */
export const getListing = async (id: string) => {
  console.warn('Deprecated: Use getItemById instead');
  return getItemById(id);
};

/**
 * @deprecated Use createItem instead
 */
export const createListing = async (data: any) => {
  console.warn('Deprecated: Use createItem instead');
  return createItem(data);
};

/**
 * @deprecated Use updateItem instead
 */
export const updateListing = async (id: string, data: any) => {
  console.warn('Deprecated: Use updateItem instead');
  return updateItem(id, data);
};
