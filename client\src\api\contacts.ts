import api from './api';
import { Customer, PaginationData } from './customers';

// Rename Customer type to Contact but keep the same structure
export type Contact = Customer;
export type { PaginationData };

export interface ContactsResponse {
  contacts: Contact[];
  pagination: PaginationData;
}

// Description: Get contacts list with pagination
// Endpoint: GET /api/address-book
// Request: { page?: number, limit?: number, search?: string, type?: string }
// Response: { contacts: Contact[], pagination: { total: number, page: number, limit: number, totalPages: number } }
export const getContacts = async (page = 1, limit = 10, search = '', type?: string) => {
  try {
    const response = await api.get('/address-book', {
      params: { page, limit, search, type }
    });
    // Transform response from customers to contacts format
    return {
      contacts: response.data.customers,
      pagination: response.data.pagination
    };
  } catch (error) {
    console.error(error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Description: Create a new contact
// Endpoint: POST /api/address-book
// Request: { name: string, email?: string, phone?: string, address?: string, notes?: string, type?: string }
// Response: { success: boolean, message: string, contact: Contact }
export const createContact = async (contactData: {
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  notes?: string;
  type?: string;
}) => {
  try {
    const response = await api.post('/address-book', contactData);
    // Transform response from customer to contact format
    return {
      ...response.data,
      contact: response.data.customer
    };
  } catch (error) {
    console.error(error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Description: Get a single contact
// Endpoint: GET /api/address-book/:id
// Request: {}
// Response: { contact: Contact }
export const getContact = async (id: string) => {
  try {
    const response = await api.get(`/address-book/${id}`);
    // Transform response from customer to contact format
    return {
      contact: response.data.customer
    };
  } catch (error) {
    console.error(error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Description: Update a contact
// Endpoint: PUT /api/address-book/:id
// Request: Partial<Contact>
// Response: { success: boolean, message: string, contact: Contact }
export const updateContact = async (id: string, contactData: Partial<Omit<Contact, '_id'>>) => {
  try {
    const response = await api.put(`/address-book/${id}`, contactData);
    // Transform response from customer to contact format
    return {
      ...response.data,
      contact: response.data.customer
    };
  } catch (error) {
    console.error(error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Description: Delete a contact
// Endpoint: DELETE /api/address-book/:id
// Request: {}
// Response: { success: boolean, message: string }
export const deleteContact = async (id: string) => {
  try {
    const response = await api.delete(`/address-book/${id}`);
    return response.data;
  } catch (error) {
    console.error(error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};