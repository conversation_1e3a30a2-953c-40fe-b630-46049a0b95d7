import { Link } from 'react-router-dom';
import { useState, useEffect } from 'react';
import { TradeMeItem as TradeItem, SaleStatus, getSaleStatusLabel } from '@/api/tradeMeItems';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { TradeMeWithdrawModal } from '@/components/trademe/TradeMeWithdrawModal';
import {
  ShoppingBag,
  Eye,
  Clock,
  MapPin,
  DollarSign,
  MessageSquare,
  RefreshCw,
  Copy,
  User,
  Edit,
  XCircle
} from 'lucide-react';
import { formatCurrency, formatDateTime, calculateTimeLeft } from '@/lib/utils';
import { toast } from 'sonner';
import { TradeMeWatchlistIcon } from './TradeMeIcons';


interface TradeMeListingCardProps {
  listing: TradeItem;
  type: 'selling' | 'sold' | 'unsold';
  onStatusChange?: (id: string, status: SaleStatus) => Promise<void>;
  onRelist?: () => void;
  onEdit?: (id: string) => void;
  onWithdraw?: (id: string, reason: string) => Promise<void>;
}

export function TradeMeListingCard({ listing, type, onRelist, onEdit, onWithdraw }: TradeMeListingCardProps) {
  const [withdrawDialogOpen, setWithdrawDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [timeLeft, setTimeLeft] = useState<ReturnType<typeof calculateTimeLeft> | null>(null);
  // Get the appropriate status color based on the status
  const getSaleStatusColor = (status: SaleStatus) => {
    switch (status) {
      case 'instant_payment_received':
      case 'bank_payment_received':
        return 'text-green-500';
      case 'awaiting_payment':
        return 'text-red-500';
      case 'awaiting_pickup_paid':
      case 'awaiting_pickup_unpaid':
      case 'awaiting_packaging':
      case 'awaiting_packaging_paid':
      case 'awaiting_return_package':
        return 'text-orange-500';
      case 'email_sent':
        return 'text-blue-500';
      case 'refunded':
        return 'text-purple-500';
      case 'cancelled':
        return 'text-gray-500';
      case 'no_status':
      case null:
        return 'text-gray-400';
      default:
        return 'text-gray-400';
    }
  };

  // Format the end date for display
  const formatDateDisplay = (dateString: string | null | undefined) => {
    if (!dateString) return 'No end date';

    const date = new Date(dateString);
    return date.toLocaleDateString('en-NZ', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Helper function to copy text to clipboard
  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text).then(
      () => {
        toast.success('Copied!', {
          description: `${label} copied to clipboard`,
          duration: 2000,
        });
      },
      (err) => {
        console.error('Could not copy text: ', err);
        toast.error('Error', {
          description: 'Failed to copy to clipboard',
          duration: 2000,
        });
      }
    );
  };

  // Get current listing data from history
  const getCurrentListingData = () => {
    if (!listing.listingHistory || listing.listingHistory.length === 0) {
      return {
        trademeListingId: listing.currentListingId || 'N/A',
        views: 0,
        watchers: 0,
        bids: 0,
        endDate: null,
        startPrice: 0,
        buyNowPrice: 0
      };
    }

    // Get the most recent listing (first in the array)
    const currentListing = listing.listingHistory[0];
    return {
      trademeListingId: currentListing.trademeListingId || listing.currentListingId || 'N/A',
      views: currentListing.views || 0,
      watchers: currentListing.watchers || 0,
      bids: currentListing.bids || 0,
      endDate: currentListing.endDate,
      startPrice: currentListing.startPrice || 0,
      buyNowPrice: currentListing.buyNowPrice || 0
    };
  };

  const currentListingData = getCurrentListingData();

  // Set up a timer to update the time left every second
  useEffect(() => {
    if (!currentListingData.endDate || type !== 'selling') return;

    const endDate = new Date(currentListingData.endDate);

    // Initial calculation
    setTimeLeft(calculateTimeLeft(endDate));

    // Set up interval to update every second
    const intervalId = setInterval(() => {
      const newTimeLeft = calculateTimeLeft(endDate);
      setTimeLeft(newTimeLeft);

      // Clear interval if auction has ended
      if (newTimeLeft.total <= 0) {
        clearInterval(intervalId);
      }
    }, 1000);

    // Clean up interval on unmount
    return () => clearInterval(intervalId);
  }, [currentListingData.endDate, type]);

  // Format the time left string
  const getTimeLeftString = () => {
    if (!timeLeft) return '';

    if (timeLeft.days > 0) {
      return `${timeLeft.days}d ${timeLeft.hours}h left`;
    } else if (timeLeft.hours > 0) {
      return `${timeLeft.hours}h ${timeLeft.minutes}m left`;
    } else if (timeLeft.minutes > 0) {
      return `${timeLeft.minutes}m ${timeLeft.seconds}s left`;
    } else {
      return `${timeLeft.seconds}s left`;
    }
  };

  // Calculate and display time left until end date
  const renderTimeLeft = () => {
    if (!currentListingData.endDate) return null;

    // If timeLeft hasn't been calculated yet, calculate it now
    if (!timeLeft) {
      const endDate = new Date(currentListingData.endDate);
      const calculatedTimeLeft = calculateTimeLeft(endDate);

      if (calculatedTimeLeft.total <= 0) {
        return <div className="text-xs text-muted-foreground">Ended</div>;
      }
    } else if (timeLeft.total <= 0) {
      return <div className="text-xs text-muted-foreground">Ended</div>;
    }

    // Determine if the auction is ending soon (< 1 hour)
    const isEndingSoon = timeLeft && timeLeft.total > 0 && timeLeft.days === 0 && timeLeft.hours === 0;

    return (
      <div className="flex flex-col">
        <div className="text-sm">{formatDateDisplay(currentListingData.endDate)}</div>
        <div className={`text-xs font-medium ${isEndingSoon ? 'text-red-500' : 'text-muted-foreground'}`}>
          {getTimeLeftString()}
        </div>
      </div>
    );
  };


  // Render the appropriate badge based on listing type
  const renderStatusBadge = () => {
    switch (type) {
      case 'selling':
        return <Badge className="ml-2">Active</Badge>;
      case 'sold':
        return <Badge variant="success">Sold</Badge>;
      case 'unsold':
        return <Badge variant="outline">Unsold</Badge>;
      default:
        return null;
    }
  };

  // Handle withdraw submission
  const handleWithdrawSubmit = async (reason: string) => {
    if (onWithdraw) {
      try {
        setIsSubmitting(true);
        await onWithdraw(listing._id, reason);
        toast.success('Success', {
          description: 'Listing has been withdrawn',
          duration: 2000,
        });
        setWithdrawDialogOpen(false);
      } catch (error: any) {
        toast.error('Error', {
          description: error.message || 'Failed to withdraw listing',
          duration: 2000,
        });
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  return (
    <>
      <Link to={`/trademe/listing/${listing._id}`} className="block">
        <Card className="overflow-hidden hover:bg-accent/50 cursor-pointer transition-colors">
          <div className="flex flex-col md:flex-row">
            {/* Image container with gradient overlay */}
          <div className="relative w-full md:w-72 min-h-[200px]">
            {listing.images && listing.images[0] ? (
              <img
                src={listing.images[0]}
                alt={listing.title}
                className="absolute inset-0 w-full h-full object-cover"
                onError={(e) => {
                  // If the image fails to load, show the placeholder
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                  target.parentElement?.classList.add('flex', 'items-center', 'justify-center');
                  const placeholder = document.createElement('div');
                  placeholder.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-8 w-8 text-muted-foreground"><path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path><polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline><line x1="12" y1="22.08" x2="12" y2="12"></line></svg>';
                  target.parentElement?.appendChild(placeholder);
                }}
              />
            ) : (
              <div className="absolute inset-0 flex items-center justify-center bg-muted">
                <ShoppingBag className="h-8 w-8 text-muted-foreground" />
              </div>
            )}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-transparent to-background/95"></div>
          </div>

          <div className="flex-1 p-6">
            <div className="flex items-start justify-between">
              <div>
                <h3 className="text-lg font-semibold">{listing.title}</h3>
                <div className="text-xs text-muted-foreground mt-1">
                  <div className="flex items-center space-x-2">
                    {listing.stockCode && (
                      <span className="cursor-pointer hover:text-primary" onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        copyToClipboard(listing.stockCode || '', 'Stock Code');
                      }}>
                        SKU: {listing.stockCode}
                        <Copy className="inline-block ml-1 h-3 w-3" />
                      </span>
                    )}
                    <span className="cursor-pointer hover:text-primary" onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      copyToClipboard(currentListingData.trademeListingId || '', 'Listing ID');
                    }}>
                      Listing: {currentListingData.trademeListingId}
                      <Copy className="inline-block ml-1 h-3 w-3" />
                    </span>
                    <span className="flex items-center">
                      <MapPin className="h-3 w-3 mr-1 text-muted-foreground" />
                      <span>{listing.location || 'Unknown'}</span>
                    </span>
                  </div>
                </div>
              </div>
              {renderStatusBadge()}
            </div>

            <div className="mt-4 space-y-4">
              {/* Price, Cost, and End Date in a 3-column grid */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Price information */}
                <div className="flex flex-col">
                  <div className="flex items-center mb-1">
                    <DollarSign className="h-4 w-4 mr-2 text-muted-foreground" />
                    <span className="text-sm font-medium">Current Price</span>
                  </div>
                  {currentListingData.bids > 0 ? (
                    <div className="font-semibold">
                      {formatCurrency(listing.currentPrice)} ({currentListingData.bids} bid{currentListingData.bids !== 1 ? 's' : ''})
                    </div>
                  ) : (
                    <div className="font-semibold">
                      {formatCurrency(listing.currentPrice)} <span className="text-muted-foreground font-normal">No bids</span>
                    </div>
                  )}
                  {listing.reservePrice && listing.reservePrice > 0 && (
                    <div className="text-xs text-muted-foreground">
                      Reserve: {formatCurrency(listing.reservePrice)}
                    </div>
                  )}
                  {currentListingData.buyNowPrice > 0 && (
                    <div className="text-xs text-muted-foreground">
                      Buy Now: {formatCurrency(currentListingData.buyNowPrice)}
                    </div>
                  )}
                </div>

                {/* Cost price information */}
                <div className="flex flex-col">
                  <div className="flex items-center mb-1">
                    <User className="h-4 w-4 mr-2 text-muted-foreground" />
                    <span className="text-sm font-medium">Cost & Buyer</span>
                  </div>
                  <div className="font-semibold">
                    {formatCurrency(listing.cost || 0)}
                  </div>
                  {type === 'sold' && listing.buyerPaymentInfo?.tradeMeBuyer && (
                    <div className="text-xs text-muted-foreground">
                      Buyer: {listing.buyerPaymentInfo.tradeMeBuyer}
                    </div>
                  )}
                  {type === 'sold' && listing.buyerPaymentInfo?.paymentType && (
                    <div className="text-xs text-muted-foreground">
                      Payment: {listing.buyerPaymentInfo.paymentType}
                    </div>
                  )}
                </div>

                {/* End date / Time left */}
                <div className="flex flex-col">
                  <div className="flex items-center mb-1">
                    <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
                    <span className="text-sm font-medium">End Date</span>
                  </div>
                  {type === 'selling' ? (
                    renderTimeLeft()
                  ) : (
                    <span>{formatDateDisplay(currentListingData.endDate)}</span>
                  )}
                </div>
              </div>

              {/* Stats - views, watchers, bids */}
              <div className="flex items-center space-x-4">
                <div className="flex items-center">
                  <Eye className="h-4 w-4 mr-1 text-muted-foreground" />
                  <span className="text-sm">{currentListingData.views}</span>
                </div>
                <div className="flex items-center">
                  <TradeMeWatchlistIcon className="text-muted-foreground mr-1" style={{ fontSize: '14px', display: 'flex', alignItems: 'center' }} />
                  <span className="text-sm">{currentListingData.watchers}</span>
                </div>
                <div className="flex items-center">
                  <DollarSign className="h-4 w-4 mr-1 text-muted-foreground" />
                  <span className="text-sm">{currentListingData.bids}</span>
                </div>
                {listing.questions && listing.questions.length > 0 && (
                  <div className="flex items-center">
                    <MessageSquare className="h-4 w-4 mr-1 text-muted-foreground" />
                    <span className="text-sm">{listing.questions.length}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Bottom row - status display for sold items */}
            {type === 'sold' && (
              <div className="mt-4 flex justify-between items-center">
                {/* Status display */}
                {listing.soldStatus ? (
                  <div className="flex items-center gap-2">
                    <div className={`font-medium ${getSaleStatusColor(listing.soldStatus)}`}>
                      {getSaleStatusLabel(listing.soldStatus)}
                    </div>
                    {listing.soldStatusUpdatedAt && (
                      <div className="text-xs text-muted-foreground flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        <span>Updated {formatDateTime(listing.soldStatusUpdatedAt)}</span>
                        {listing.soldStatusUpdatedBy && (
                          <span>by {listing.soldStatusUpdatedBy.username}</span>
                        )}
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-muted-foreground">No status set</div>
                )}
              </div>
            )}

            {/* Bottom row - action buttons */}
            <div className="mt-4 flex justify-end gap-2">
              {/* Edit button for all items */}
              {onEdit && (
                <Button
                  variant="outline"
                  size="sm"
                  className="gap-1"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    onEdit(listing._id);
                  }}
                >
                  <Edit className="h-3 w-3" />
                  Edit
                </Button>
              )}

              {/* Withdraw button for active items */}
              {type === 'selling' && onWithdraw && (
                <Button
                  variant="outline"
                  size="sm"
                  className="gap-1 text-destructive hover:text-destructive"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    setWithdrawDialogOpen(true);
                  }}
                >
                  <XCircle className="h-3 w-3" />
                  Withdraw
                </Button>
              )}

              {/* Relist button for unsold items */}
              {type === 'unsold' && onRelist && (
                <Button
                  variant="outline"
                  size="sm"
                  className="gap-1"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    onRelist();
                  }}
                >
                  <RefreshCw className="h-3 w-3" />
                  Relist
                </Button>
              )}
            </div>
          </div>
        </div>
      </Card>
    </Link>

    {/* Withdraw Modal */}
    <TradeMeWithdrawModal
      open={withdrawDialogOpen}
      onOpenChange={setWithdrawDialogOpen}
      onWithdraw={handleWithdrawSubmit}
      isSubmitting={isSubmitting}
    />
    </>
  );
}
