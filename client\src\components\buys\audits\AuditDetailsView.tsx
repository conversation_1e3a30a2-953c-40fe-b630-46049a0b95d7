import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  Package, 
  DollarSign, 
  User, 
  Calendar,
  FileText,
  Edit,
  Flag,
  MessageSquare,
  History,
  Download
} from 'lucide-react';
import { format } from 'date-fns';
import { useAuth } from '@/contexts/AuthContext';
import { AuditAssessmentsView } from './AuditAssessmentsView';
import { AuditCommentsView } from './AuditCommentsView';
import { AuditHistoryView } from './AuditHistoryView';
import { AuditExportModal } from './AuditExportModal';

interface AuditDetailsViewProps {
  audit: any;
  onEdit?: () => void;
  onFlag?: () => void;
  onUnflag?: () => void;
  onCompleteFollowup?: () => void;
  onAddComment?: () => void;
  onExport?: () => void;
}

export function AuditDetailsView({
  audit,
  onEdit,
  onFlag,
  onUnflag,
  onCompleteFollowup,
  onAddComment,
  onExport
}: AuditDetailsViewProps) {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [exportModalOpen, setExportModalOpen] = useState(false);

  // Check if user can edit (manager or admin only)
  const canEdit = user?.role === 'manager' || user?.role === 'admin';

  // Helper function to render status badge
  const renderStatusBadge = (status: string) => {
    switch (status) {
      case 'pass':
      case 'compliant':
        return <Badge variant="success" className="flex items-center gap-1"><CheckCircle className="h-3 w-3" />Pass</Badge>;
      case 'fail':
      case 'minor_non_compliant':
      case 'major_non_compliant':
        return <Badge variant="destructive" className="flex items-center gap-1"><XCircle className="h-3 w-3" />Fail</Badge>;
      default:
        return <Badge variant="secondary" className="flex items-center gap-1"><AlertCircle className="h-3 w-3" />Not Assessed</Badge>;
    }
  };

  // Helper function to format date
  const formatDate = (dateString: string | Date) => {
    if (!dateString) return 'Not specified';
    const date = new Date(dateString);
    return format(date, 'PPP');
  };

  // Helper function to format currency
  const formatCurrency = (amount: number | string) => {
    const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    return isNaN(numericAmount) ? amount : `$${numericAmount.toFixed(2)}`;
  };

  return (
    <div className="space-y-6">
      {/* Header with Actions */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-2xl">Audit Details</CardTitle>
              <CardDescription>
                {audit.auditType.charAt(0).toUpperCase() + audit.auditType.slice(1)} audit for transaction {audit.transactionId}
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              {canEdit && (
                <Button variant="outline" size="sm" onClick={onEdit}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </Button>
              )}
              
              {audit.flaggedForFollowup && !audit.followedUp ? (
                <Button variant="outline" size="sm" onClick={onCompleteFollowup}>
                  <Flag className="h-4 w-4 mr-2" />
                  Complete Follow-up
                </Button>
              ) : audit.flaggedForFollowup && audit.followedUp ? (
                canEdit && (
                  <Button variant="outline" size="sm" onClick={onUnflag}>
                    <Flag className="h-4 w-4 mr-2" />
                    Unflag
                  </Button>
                )
              ) : (
                canEdit && (
                  <Button variant="outline" size="sm" onClick={onFlag}>
                    <Flag className="h-4 w-4 mr-2" />
                    Flag for Follow-up
                  </Button>
                )
              )}
              
              <Button variant="outline" size="sm" onClick={onAddComment}>
                <MessageSquare className="h-4 w-4 mr-2" />
                Add Comment
              </Button>
              
              <Button variant="outline" size="sm" onClick={() => setExportModalOpen(true)}>
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="transaction">Transaction</TabsTrigger>
          <TabsTrigger value="items">Items</TabsTrigger>
          <TabsTrigger value="assessments">Assessments</TabsTrigger>
          <TabsTrigger value="comments">Comments</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Basic Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground">Transaction ID</p>
                    <p className="font-medium">{audit.transactionId}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Audit Type</p>
                    <Badge variant="outline" className="capitalize">{audit.auditType}</Badge>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Employee</p>
                    <p className="font-medium">{audit.employeeName}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Amount</p>
                    <p className="font-medium">{formatCurrency(audit.amount)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Transaction Date</p>
                    <p className="font-medium">{formatDate(audit.transactionDate)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Audit Date</p>
                    <p className="font-medium">{formatDate(audit.auditDate)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Audit Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5" />
                  Audit Status
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Overall Compliance</span>
                    {renderStatusBadge(audit.overallCompliance)}
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Overall Score</span>
                    <Badge variant="outline">{audit.overallScore || 0}%</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Status</span>
                    <Badge variant={audit.status === 'completed' ? 'success' : 'secondary'}>
                      {audit.status}
                    </Badge>
                  </div>
                  {audit.flaggedForFollowup && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Follow-up Status</span>
                      <Badge variant={audit.followedUp ? 'success' : 'destructive'}>
                        {audit.followedUp ? 'Completed' : 'Pending'}
                      </Badge>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Auditor Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Auditor Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">Audited By</p>
                  <p className="font-medium">{audit.auditedBy?.fullName || 'Unknown'}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Created At</p>
                  <p className="font-medium">{formatDate(audit.createdAt)}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Last Updated</p>
                  <p className="font-medium">{formatDate(audit.updatedAt)}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Audit Notes */}
          {audit.auditNotes && (
            <Card>
              <CardHeader>
                <CardTitle>Audit Notes</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm">{audit.auditNotes}</p>
              </CardContent>
            </Card>
          )}

          {/* Follow-up Information */}
          {audit.flaggedForFollowup && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Flag className="h-5 w-5" />
                  Follow-up Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {audit.flagReason && (
                  <div>
                    <p className="text-sm text-muted-foreground">Flag Reason</p>
                    <p className="text-sm">{audit.flagReason}</p>
                  </div>
                )}
                {audit.followupResponse && (
                  <div>
                    <p className="text-sm text-muted-foreground">Follow-up Response</p>
                    <p className="text-sm">{audit.followupResponse}</p>
                  </div>
                )}
                {audit.followupDate && (
                  <div>
                    <p className="text-sm text-muted-foreground">Follow-up Date</p>
                    <p className="text-sm">{formatDate(audit.followupDate)}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Transaction Tab */}
        <TabsContent value="transaction" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Transaction Details</CardTitle>
              <CardDescription>
                Detailed information about the transaction being audited
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">Transaction ID</p>
                  <p className="font-medium text-lg">{audit.transactionId}</p>
                </div>
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">Transaction Type</p>
                  <Badge variant="outline" className="capitalize">{audit.auditType}</Badge>
                </div>
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">
                    {audit.auditType === 'pawn' ? 'Loan Officer' : 'Buyer'}
                  </p>
                  <p className="font-medium">{audit.employeeName}</p>
                </div>
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">
                    {audit.auditType === 'pawn' ? 'Loan Amount' : 'Purchase Cost'}
                  </p>
                  <p className="font-medium text-lg">{formatCurrency(audit.amount)}</p>
                </div>
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">Transaction Date</p>
                  <p className="font-medium">{formatDate(audit.transactionDate)}</p>
                </div>
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">Audit Date</p>
                  <p className="font-medium">{formatDate(audit.auditDate)}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Items Tab */}
        <TabsContent value="items" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Items in Transaction
              </CardTitle>
              <CardDescription>
                Detailed information about each item in this transaction
              </CardDescription>
            </CardHeader>
            <CardContent>
              {audit.items && audit.items.length > 0 ? (
                <div className="space-y-4">
                  {audit.items.map((item: any, index: number) => (
                    <Card key={index} className="border-l-4 border-l-primary">
                      <CardHeader>
                        <CardTitle className="text-lg">
                          Item {index + 1}: {item.brand} - {item.description}
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                          <div>
                            <p className="text-sm text-muted-foreground">Stockcode</p>
                            <p className="font-medium">{item.stockcode}</p>
                          </div>
                          <div>
                            <p className="text-sm text-muted-foreground">Brand</p>
                            <p className="font-medium">{item.brand}</p>
                          </div>
                          <div>
                            <p className="text-sm text-muted-foreground">Description</p>
                            <p className="font-medium">{item.description}</p>
                          </div>
                          <div>
                            <p className="text-sm text-muted-foreground">Cost</p>
                            <p className="font-medium">{formatCurrency(item.cost)}</p>
                          </div>
                        </div>
                        
                        {/* Item Pricing Information */}
                        {item.pricing && (
                          <>
                            <Separator className="my-4" />
                            <div>
                              <h4 className="font-medium mb-3">Pricing Information</h4>
                              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                {item.pricing.suggestedPrice && (
                                  <div>
                                    <p className="text-sm text-muted-foreground">Suggested Price</p>
                                    <p className="font-medium">{formatCurrency(item.pricing.suggestedPrice)}</p>
                                  </div>
                                )}
                                {item.pricing.costPrice && (
                                  <div>
                                    <p className="text-sm text-muted-foreground">Cost Price</p>
                                    <p className="font-medium">{formatCurrency(item.pricing.costPrice)}</p>
                                  </div>
                                )}
                                {item.pricing.ticketPrice && (
                                  <div>
                                    <p className="text-sm text-muted-foreground">Ticket Price</p>
                                    <p className="font-medium">{formatCurrency(item.pricing.ticketPrice)}</p>
                                  </div>
                                )}
                              </div>
                              {item.pricing.notes && (
                                <div className="mt-3">
                                  <p className="text-sm text-muted-foreground">Notes</p>
                                  <p className="text-sm">{item.pricing.notes}</p>
                                </div>
                              )}
                            </div>
                          </>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  No item details available for this audit.
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Assessments Tab */}
        <TabsContent value="assessments">
          <AuditAssessmentsView audit={audit} />
        </TabsContent>

        {/* Comments Tab */}
        <TabsContent value="comments">
          <AuditCommentsView
            auditId={audit._id}
            onCommentAdded={() => {
              // Refresh audit data or handle comment added
              console.log('Comment added');
            }}
          />
        </TabsContent>

        {/* History Tab */}
        <TabsContent value="history">
          <AuditHistoryView auditId={audit._id} />
        </TabsContent>
      </Tabs>

      {/* Export Modal */}
      <AuditExportModal
        audit={audit}
        isOpen={exportModalOpen}
        onClose={() => setExportModalOpen(false)}
      />
    </div>
  );
}
