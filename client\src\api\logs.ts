import api from './api';

export interface AccessLog {
  _id: string;
  timestamp: string;
  userId: string | null;
  username: string;
  fullName: string;
  userRole: string;
  ipAddress: string;
  method: string;
  path: string;
  fullUrl: string;
  queryParams: Record<string, any>;
  userAgent: string;
  statusCode: number;
  requestType: 'page' | 'api' | 'asset' | 'unknown';
  responseTime: number;
  referrer: string;
  pageName: string;
}

export interface LogsResponse {
  success: boolean;
  logs: AccessLog[];
  pagination: {
    total: number;
    page: number;
    pages: number;
    limit: number;
  };
}

export interface LogsStatistics {
  totalRequests: number;
  uniqueUsers: number;
  avgResponseTime: number;
  errorRate: number;
  requestsByType: Array<{ label: string; value: number; color: string }>;
  requestsByStatus: Array<{ label: string; value: number; color: string }>;
  requestsOverTime: Array<{ label: string; value: number }>;
}

export interface LogsStatsResponse {
  success: boolean;
  stats: LogsStatistics;
}

export interface LogsQueryParams {
  page?: number;
  limit?: number;
  username?: string;
  userId?: string;
  path?: string;
  method?: string;
  ipAddress?: string;
  startDate?: string;
  endDate?: string;
  userRole?: string;
  statusCode?: number;
  statusType?: 'success' | 'redirect' | 'client-error' | 'server-error' | 'error';
  requestType?: 'page' | 'api' | 'asset' | 'unknown';
  minResponseTime?: number;
  maxResponseTime?: number;
  referrer?: string;
}

// Description: Get access logs with filtering and pagination
// Endpoint: GET /api/access-logs
// Response: { success: boolean, logs: AccessLog[], pagination: { total, page, pages, limit } }
export const getLogs = async (params: LogsQueryParams = {}): Promise<LogsResponse> => {
  try {
    const response = await api.get('/access-logs', { params });
    return response.data;
  } catch (error: any) {
    console.error('Error fetching access logs:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Description: Get access logs statistics for dashboard
// Endpoint: GET /api/access-logs/stats
// Response: { success: boolean, stats: LogsStatistics }
export const getLogsStats = async (): Promise<LogsStatsResponse> => {
  try {
    console.log('Fetching stats from server...');
    const response = await api.get('/access-logs/stats');
    console.log('Stats response:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('Error fetching access logs statistics:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};

// Description: Clear old access logs
// Endpoint: DELETE /api/access-logs/clear
// Request: { days: number }
// Response: { success: boolean, message: string }
export const clearOldLogs = async (days: number = 30): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await api.delete('/access-logs/clear', { data: { days } });
    return response.data;
  } catch (error: any) {
    console.error('Error clearing access logs:', error);
    throw new Error(error?.response?.data?.error || error.message);
  }
};
