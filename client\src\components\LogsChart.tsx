import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';

// Simple bar chart component
interface BarChartProps {
  data: {
    label: string;
    value: number;
    color?: string;
  }[];
  title: string;
  className?: string;
  maxValue?: number;
}

export function LogsBarChart({ data, title, className, maxValue }: BarChartProps) {
  // Calculate the maximum value if not provided
  const calculatedMax = maxValue || Math.max(...data.map(item => item.value), 1);
  
  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          {data.map((item, index) => (
            <div key={index} className="space-y-1">
              <div className="flex items-center justify-between text-xs">
                <span>{item.label}</span>
                <span className="font-medium">{item.value}</span>
              </div>
              <div className="h-2 w-full rounded-full bg-secondary">
                <div
                  className={cn(
                    "h-full rounded-full",
                    item.color || "bg-primary"
                  )}
                  style={{
                    width: `${Math.min(100, (item.value / calculatedMax) * 100)}%`,
                  }}
                />
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

// Simple line chart component (visual representation only)
interface LineChartProps {
  data: {
    label: string;
    value: number;
  }[];
  title: string;
  className?: string;
}

export function LogsLineChart({ data, title, className }: LineChartProps) {
  // Find the max value for scaling
  const maxValue = Math.max(...data.map(item => item.value), 1);
  
  // Calculate points for the SVG path
  const points = data.map((item, index) => {
    const x = (index / (data.length - 1)) * 100;
    const y = 100 - (item.value / maxValue) * 100;
    return `${x},${y}`;
  }).join(' ');
  
  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-[120px] w-full">
          <svg width="100%" height="100%" viewBox="0 0 100 100" preserveAspectRatio="none">
            {/* Grid lines */}
            <line x1="0" y1="25" x2="100" y2="25" stroke="#e5e7eb" strokeWidth="0.5" />
            <line x1="0" y1="50" x2="100" y2="50" stroke="#e5e7eb" strokeWidth="0.5" />
            <line x1="0" y1="75" x2="100" y2="75" stroke="#e5e7eb" strokeWidth="0.5" />
            
            {/* Line chart */}
            <polyline
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              points={points}
            />
            
            {/* Area under the line */}
            <polyline
              fill="rgba(59, 130, 246, 0.1)"
              stroke="none"
              points={`0,100 ${points} 100,100`}
            />
            
            {/* Data points */}
            {data.map((item, index) => {
              const x = (index / (data.length - 1)) * 100;
              const y = 100 - (item.value / maxValue) * 100;
              return (
                <circle
                  key={index}
                  cx={x}
                  cy={y}
                  r="1.5"
                  fill="currentColor"
                />
              );
            })}
          </svg>
        </div>
        
        {/* X-axis labels */}
        <div className="flex justify-between mt-2 text-xs text-muted-foreground">
          {data.map((item, index) => (
            <div key={index} className={cn(
              index === 0 ? "text-left" : "",
              index === data.length - 1 ? "text-right" : "",
              "truncate max-w-[50px]"
            )}>
              {item.label}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
