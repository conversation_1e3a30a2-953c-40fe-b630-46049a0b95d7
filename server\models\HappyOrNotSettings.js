const mongoose = require('mongoose');

const happyOrNotSettingsSchema = new mongoose.Schema({
  apiKey: {
    type: String,
    default: ''
  },
  experiencePointName: {
    type: String,
    default: ''
  },
  weeklyFeedbackGoal: {
    type: Number,
    default: 100,
    min: 0
  },
  happinessScoreGoal: {
    type: Number,
    default: 80,
    min: 0,
    max: 100
  },
  lastSynced: {
    type: Date,
    default: null
  },
  // Sync configuration settings
  autoSyncEnabled: {
    type: Boolean,
    default: true
  },
  autoSyncFrequencyMinutes: {
    type: Number,
    default: 60, // Default to every hour
    min: 5, // Minimum 5 minutes
    max: 1440 // Maximum 24 hours (1440 minutes)
  },
  lastPeriodicSync: {
    type: Date,
    default: null
  },
  lastUpdated: {
    type: Date,
    default: Date.now
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  }
}, {
  versionKey: false
});

// There should only be one settings document
happyOrNotSettingsSchema.statics.getSettings = async function() {
  let settings = await this.findOne();
  if (!settings) {
    settings = await this.create({});
  }
  return settings;
};

// Mask API key for client-side display
happyOrNotSettingsSchema.methods.maskApiKey = function() {
  if (!this.apiKey) return '';

  // If the key already contains 'X', it's already masked
  if (this.apiKey.includes('X')) return this.apiKey;

  // Get the last 4 characters
  const last4 = this.apiKey.slice(-4);

  // Replace all characters except hyphens with 'X'
  let maskedPart = '';
  for (let i = 0; i < this.apiKey.length - 4; i++) {
    maskedPart += this.apiKey[i] === '-' ? '-' : 'X';
  }

  return maskedPart + last4;
};

const HappyOrNotSettings = mongoose.model('HappyOrNotSettings', happyOrNotSettingsSchema);

module.exports = HappyOrNotSettings;
