const mongoose = require('mongoose');

const happyOrNotFeedbackSchema = new mongoose.Schema({
  // Core feedback data
  feedbackId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  buttonIndex: {
    type: Number,
    required: true,
    min: 0,
    max: 10 // 0-3 for 4-button scale, 0-4 for 5-button scale, 0-10 for NPS scale
  },
  localTime: {
    type: Date,
    required: true,
    index: true
  },
  
  // Experience point information
  experiencePointId: {
    type: String,
    required: true,
    index: true
  },
  experiencePointName: {
    type: String,
    required: true
  },
  
  // Survey information
  surveyId: {
    type: String,
    required: true
  },
  
  // Device information
  smileyId: {
    type: String,
    required: true
  },
  smileyType: {
    type: String,
    enum: ['SMILEY_TERMINAL', 'SMILEY_TOUCH', 'SMILEY_DIGITAL'],
    required: true
  },
  
  // Optional feedback data
  followupQuestionId: String,
  followupOptionId: String,
  followupOptionText: String,
  
  // Text feedback
  text: String,
  textInEnglish: String,
  
  // Flags
  misuse: {
    type: Boolean,
    default: false
  },
  spam: {
    type: Boolean,
    default: false
  },
  
  // Metadata
  importedAt: {
    type: Date,
    default: Date.now
  }
}, {
  versionKey: false
});

// Create indexes for efficient querying
happyOrNotFeedbackSchema.index({ localTime: -1 });
happyOrNotFeedbackSchema.index({ experiencePointId: 1, localTime: -1 });
happyOrNotFeedbackSchema.index({ buttonIndex: 1, localTime: -1 });

const HappyOrNotFeedback = mongoose.model('HappyOrNotFeedback', happyOrNotFeedbackSchema);

module.exports = HappyOrNotFeedback;
