const express = require('express');
const router = express.Router();
const happyOrNotService = require('../services/happyOrNotService');
const { requireUser } = require('./middleware/auth');

/**
 * @route GET /api/happy-or-not/settings
 * @description Get Happy or Not settings
 * @access Private (Admin, Manager)
 */
router.get('/settings', requireUser, async (req, res) => {
  try {
    // Check if user has permission (admin or manager)
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to view Happy or Not settings'
      });
    }

    const result = await happyOrNotService.getSettings();
    if (result.success) {
      return res.status(200).json({ success: true, data: result.settings });
    } else {
      return res.status(400).json({ success: false, message: result.error });
    }
  } catch (error) {
    console.error('Error getting Happy or Not settings:', error);
    return res.status(500).json({
      success: false,
      message: 'An error occurred while retrieving settings'
    });
  }
});

/**
 * @route GET /api/happy-or-not/goals
 * @description Get Happy or Not goal settings (accessible to all users)
 * @access Private
 */
router.get('/goals', requireUser, async (req, res) => {
  try {
    const result = await happyOrNotService.getGoalSettings();
    if (result.success) {
      return res.status(200).json({ success: true, data: result.goals });
    } else {
      return res.status(400).json({ success: false, message: result.error });
    }
  } catch (error) {
    console.error('Error getting Happy or Not goal settings:', error);
    return res.status(500).json({
      success: false,
      message: 'An error occurred while retrieving goal settings'
    });
  }
});

/**
 * @route PUT /api/happy-or-not/settings
 * @description Update Happy or Not settings
 * @access Private (Admin, Manager)
 */
router.put('/settings', requireUser, async (req, res) => {
  try {
    // Check if user has permission (admin or manager)
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to update Happy or Not settings'
      });
    }

    const result = await happyOrNotService.updateSettings(req.body, req.user);
    if (result.success) {
      return res.status(200).json({ success: true, data: result.settings });
    } else {
      return res.status(400).json({ success: false, message: result.error });
    }
  } catch (error) {
    console.error('Error updating Happy or Not settings:', error);
    return res.status(500).json({
      success: false,
      message: 'An error occurred while updating settings'
    });
  }
});

/**
 * @route GET /api/happy-or-not/experience-points
 * @description Get Happy or Not experience points
 * @access Private (Admin, Manager)
 */
router.get('/experience-points', requireUser, async (req, res) => {
  try {
    // Check if user has permission (admin or manager)
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to view Happy or Not experience points'
      });
    }

    const result = await happyOrNotService.getExperiencePoints();
    if (result.success) {
      return res.status(200).json({ success: true, data: result.experiencePoints });
    } else {
      return res.status(400).json({ success: false, message: result.error });
    }
  } catch (error) {
    console.error('Error getting Happy or Not experience points:', error);
    return res.status(500).json({
      success: false,
      message: 'An error occurred while retrieving experience points'
    });
  }
});

/**
 * @route GET /api/happy-or-not/feedback
 * @description Get Happy or Not feedback data
 * @access Private
 */
router.get('/feedback', requireUser, async (req, res) => {
  try {
    const { startDate, endDate, experiencePointId, groupBy } = req.query;

    const result = await happyOrNotService.getFeedbackData({
      startDate,
      endDate,
      experiencePointId,
      groupBy
    });

    if (result.success) {
      return res.status(200).json({ success: true, data: result.data });
    } else {
      return res.status(400).json({ success: false, message: result.error });
    }
  } catch (error) {
    console.error('Error getting Happy or Not feedback data:', error);
    return res.status(500).json({
      success: false,
      message: 'An error occurred while retrieving feedback data'
    });
  }
});

/**
 * @route POST /api/happy-or-not/sync
 * @description Sync Happy or Not feedback data
 * @access Private (Admin, Manager)
 */
router.post('/sync', requireUser, async (req, res) => {
  try {
    // Check if user has permission (admin or manager)
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to sync Happy or Not data'
      });
    }

    // Get parameters from request body
    const { startDate, endDate, experiencePointId } = req.body;

    // Use the syncFeedback method with the provided parameters
    const result = await happyOrNotService.syncFeedback({
      startDate,
      endDate,
      experiencePointId
    });

    if (result.success) {
      return res.status(200).json({ success: true, data: result });
    } else {
      return res.status(400).json({ success: false, message: result.error });
    }
  } catch (error) {
    console.error('Error syncing Happy or Not data:', error);
    return res.status(500).json({
      success: false,
      message: 'An error occurred while syncing data'
    });
  }
});

/**
 * @route POST /api/happy-or-not/sync-all-historical
 * @description Sync all historical Happy or Not feedback data
 * @access Private (Admin, Manager)
 */
router.post('/sync-all-historical', requireUser, async (req, res) => {
  try {
    // Check if user has permission (admin or manager)
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to sync Happy or Not data'
      });
    }

    // This is a long-running operation, so we'll return immediately
    // and let the sync run in the background
    res.status(202).json({
      success: true,
      message: 'Historical data sync started. This may take several minutes to complete. Check the server logs for progress updates.'
    });

    // Run the sync in the background
    happyOrNotService.syncAllHistoricalData()
      .then(result => {
        if (result.success) {
          console.log('Historical data sync completed successfully:', result.stats);
        } else {
          console.error('Historical data sync failed:', result.error);

          // Log detailed error information for debugging
          if (result.validationErrors) {
            console.error('Validation errors:', result.validationErrors);
          }
        }
      })
      .catch(error => {
        console.error('Error in historical data sync:', error);

        // Log detailed error information for debugging
        if (error.response?.data) {
          console.error('API response data:', error.response.data);
        }
      });
  } catch (error) {
    console.error('Error starting historical data sync:', error);
    return res.status(500).json({
      success: false,
      message: 'An error occurred while starting historical data sync'
    });
  }
});

/**
 * @route GET /api/happy-or-not/feedback/paginated
 * @description Get paginated Happy or Not feedback data with search and filtering
 * @access Private
 */
router.get('/feedback/paginated', requireUser, async (req, res) => {
  try {
    const {
      startDate,
      endDate,
      experiencePointId,
      buttonIndex,
      hasText,
      hasFollowUp,
      searchText,
      page,
      limit,
      sortField,
      sortOrder
    } = req.query;

    const result = await happyOrNotService.getPaginatedFeedback({
      startDate,
      endDate,
      experiencePointId,
      buttonIndex,
      hasText,
      hasFollowUp,
      searchText,
      page,
      limit,
      sortField,
      sortOrder
    });

    if (result.success) {
      return res.status(200).json({ success: true, data: result.data });
    } else {
      return res.status(400).json({ success: false, message: result.error });
    }
  } catch (error) {
    console.error('Error getting paginated Happy or Not feedback data:', error);
    return res.status(500).json({
      success: false,
      message: 'An error occurred while retrieving paginated feedback data'
    });
  }
});

/**
 * @route GET /api/happy-or-not/available-years
 * @description Get years for which feedback data is available
 * @access Private
 */
router.get('/available-years', requireUser, async (req, res) => {
  try {
    const result = await happyOrNotService.getAvailableYears();

    if (result.success) {
      return res.status(200).json({ success: true, data: result.years });
    } else {
      return res.status(400).json({ success: false, message: result.error });
    }
  } catch (error) {
    console.error('Error getting available years:', error);
    return res.status(500).json({
      success: false,
      message: 'An error occurred while retrieving available years'
    });
  }
});

/**
 * @route GET /api/happy-or-not/follow-up-responses
 * @description Get aggregated follow-up response data
 * @access Private
 */
router.get('/follow-up-responses', requireUser, async (req, res) => {
  try {
    const { startDate, endDate, experiencePointId, feedbackType } = req.query;

    const result = await happyOrNotService.getFollowUpResponseData({
      startDate,
      endDate,
      experiencePointId,
      feedbackType
    });

    if (result.success) {
      return res.status(200).json({ success: true, data: result.data });
    } else {
      return res.status(400).json({ success: false, message: result.error });
    }
  } catch (error) {
    console.error('Error getting follow-up response data:', error);
    return res.status(500).json({
      success: false,
      message: 'An error occurred while retrieving follow-up response data'
    });
  }
});

module.exports = router;
