import { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Loader2, ArrowLeft, Edit, Printer, Download } from 'lucide-react';
import { useToast } from '@/hooks/useToast';
import { getAuditById, exportAuditData } from '@/api/buyPawnAudits';

// Mock audit data
const mockAudit = {
  id: '1',
  auditType: 'buy',
  transactionId: 'BUY-2025-001',
  transactionDate: '2025-04-28',
  transactionAmount: '250.00',
  employeeName: '<PERSON>',
  employeeId: 'EMP001',
  createdAt: '2025-04-29T10:30:00Z',
  updatedAt: '2025-04-29T14:15:00Z',

  // Transaction assessment
  dataEntryQuality: {
    status: 'pass',
    failReasons: [],
    notes: 'All data was entered correctly and completely.',
  },

  itemConditionCheck: {
    status: 'pass',
    failReasons: [],
    notes: 'Item condition was accurately assessed and documented.',
  },

  // Pricing assessment
  pricing: {
    status: 'pass',
    failReasons: [],
    suggestedPrice: '',
    notes: 'Pricing was appropriate for the item.',
  },

  // Authorized limit check
  authorizedLimitCheck: {
    status: 'pass',
    failReasons: [],
    notes: 'Transaction was within authorized limits.',
  },

  // Summary
  summary: {
    overallAssessment: 'pass',
    score: '85',
    strengths: 'Good customer service. Accurate data entry. Appropriate pricing.',
    areasForImprovement: 'Could improve documentation of item condition.',
    recommendations: 'Continue to maintain high standards. Consider additional training on item condition assessment.',
    additionalNotes: '',
    followUpRequired: 'no',
    followUpNotes: '',
  },
};

export default function AuditDetailsPage() {
  const [audit, setAudit] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const { id } = useParams();
  const { toast } = useToast();

  // Fetch audit data
  useEffect(() => {
    const fetchAudit = async () => {
      if (!id) return;

      try {
        setLoading(true);

        // Fetch the audit data from the API
        const result = await getAuditById(id);

        if (result.success) {
          setAudit(result.data);
        } else {
          toast({
            title: 'Error',
            description: result.error || 'Failed to load audit details.',
            variant: 'destructive',
          });
        }
      } catch (error: any) {
        console.error('Error fetching audit:', error);
        toast({
          title: 'Error fetching audit',
          description: error.message || 'There was an error fetching the audit details. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchAudit();
  }, [id, toast]);

  // Handle print
  const handlePrint = () => {
    window.print();
  };

  // Handle download as PDF
  const handleDownload = async () => {
    if (!id) return;

    try {
      toast({
        title: 'Export Started',
        description: 'Generating PDF report...',
      });

      // Call the export function
      await exportAuditData('pdf', {
        type: 'audit_detail',
        auditId: id,
        includeDetails: true,
      });

      toast({
        title: 'Export Complete',
        description: 'PDF report has been downloaded.',
      });
    } catch (error: any) {
      console.error('Error downloading PDF:', error);
      toast({
        title: 'Export Failed',
        description: error.message || 'Failed to generate PDF report.',
        variant: 'destructive',
      });
    }
  };

  // Get status badge color
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pass':
        return <Badge className="bg-green-500">Pass</Badge>;
      case 'fail':
        return <Badge className="bg-red-500">Fail</Badge>;
      case 'partial':
        return <Badge className="bg-yellow-500">Partial Pass</Badge>;
      case 'not_assessed':
        return <Badge className="bg-gray-500">Not Assessed</Badge>;
      default:
        return <Badge>Unknown</Badge>;
    }
  };

  // Get audit type display name
  const getAuditTypeDisplay = (type: string) => {
    switch (type) {
      case 'buy':
        return 'Buy Deal Audit';
      case 'pawn':
        return 'Pawn Loan Audit';
      case 'price':
        return 'Price Audit';
      default:
        return 'Unknown Audit';
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (loading) {
    return (
      <div className="container mx-auto py-6 flex justify-center items-center min-h-[60vh]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!audit) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex justify-between items-center mb-6">
          <Button variant="outline" onClick={() => navigate('/buys/audits')}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Audits
          </Button>
        </div>
        <Card>
          <CardContent className="py-12">
            <div className="text-center">
              <h2 className="text-2xl font-bold mb-2">Audit Not Found</h2>
              <p className="text-muted-foreground mb-6">
                The audit you are looking for does not exist or has been deleted.
              </p>
              <Button onClick={() => navigate('/buys/audits')}>
                Return to Audit List
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <Button variant="outline" onClick={() => navigate('/buys/audits')}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Audits
        </Button>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => navigate(`/buys/audits/${id}/edit`)}>
            <Edit className="mr-2 h-4 w-4" />
            Edit
          </Button>
          <Button variant="outline" onClick={handlePrint}>
            <Printer className="mr-2 h-4 w-4" />
            Print
          </Button>
          <Button variant="outline" onClick={handleDownload}>
            <Download className="mr-2 h-4 w-4" />
            Download PDF
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle>{getAuditTypeDisplay(audit.auditType)}</CardTitle>
              <CardDescription>
                Transaction ID: {audit.transactionId}
              </CardDescription>
            </div>
            <div className="text-right">
              <div className="flex items-center gap-2 mb-1">
                <span className="text-sm font-medium">Status:</span>
                {getStatusBadge(audit.summary.overallAssessment)}
              </div>
              <div className="text-sm text-muted-foreground">
                Score: {audit.summary.score}/100
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-1">Transaction Details</h3>
              <div className="space-y-1">
                <p><span className="font-medium">Date:</span> {formatDate(audit.transactionDate)}</p>
                <p><span className="font-medium">Amount:</span> ${audit.transactionAmount}</p>
                <p><span className="font-medium">Employee:</span> {audit.employeeName}</p>
              </div>
            </div>
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-1">Audit Information</h3>
              <div className="space-y-1">
                <p><span className="font-medium">Created:</span> {formatDate(audit.createdAt)}</p>
                <p><span className="font-medium">Last Updated:</span> {formatDate(audit.updatedAt)}</p>
                <p><span className="font-medium">Audit ID:</span> {audit.id}</p>
              </div>
            </div>
          </div>

          <Separator className="my-6" />

          <Tabs defaultValue="summary">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="summary">Summary</TabsTrigger>
              <TabsTrigger value="transaction">Transaction</TabsTrigger>
              <TabsTrigger value="pricing">Pricing</TabsTrigger>
              {audit.auditType === 'pawn' && (
                <TabsTrigger value="responsible-lending">Responsible Lending</TabsTrigger>
              )}
            </TabsList>

            <TabsContent value="summary" className="space-y-6 mt-6">
              <div>
                <h3 className="text-lg font-medium mb-2">Overall Assessment</h3>
                <div className="flex items-center gap-2 mb-4">
                  <span>Status:</span>
                  {getStatusBadge(audit.summary.overallAssessment)}
                  <span className="ml-4">Score: {audit.summary.score}/100</span>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Strengths</h3>
                <p className="text-muted-foreground whitespace-pre-line">
                  {audit.summary.strengths || 'No strengths specified.'}
                </p>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Areas for Improvement</h3>
                <p className="text-muted-foreground whitespace-pre-line">
                  {audit.summary.areasForImprovement || 'No areas for improvement specified.'}
                </p>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Recommendations</h3>
                <p className="text-muted-foreground whitespace-pre-line">
                  {audit.summary.recommendations || 'No recommendations specified.'}
                </p>
              </div>

              {audit.summary.additionalNotes && (
                <div>
                  <h3 className="text-lg font-medium mb-2">Additional Notes</h3>
                  <p className="text-muted-foreground whitespace-pre-line">
                    {audit.summary.additionalNotes}
                  </p>
                </div>
              )}

              <div>
                <h3 className="text-lg font-medium mb-2">Follow-up Required</h3>
                <p className="mb-2">
                  {audit.summary.followUpRequired === 'yes' ? 'Yes' : 'No'}
                </p>
                {audit.summary.followUpRequired === 'yes' && audit.summary.followUpNotes && (
                  <p className="text-muted-foreground whitespace-pre-line">
                    {audit.summary.followUpNotes}
                  </p>
                )}
              </div>
            </TabsContent>

            <TabsContent value="transaction" className="space-y-6 mt-6">
              <div>
                <h3 className="text-lg font-medium mb-2">Data Entry Quality</h3>
                <div className="flex items-center gap-2 mb-2">
                  <span>Status:</span>
                  {getStatusBadge(audit.dataEntryQuality.status)}
                </div>
                {audit.dataEntryQuality.failReasons && audit.dataEntryQuality.failReasons.length > 0 && (
                  <div className="mb-2">
                    <h4 className="text-sm font-medium mb-1">Fail Reasons:</h4>
                    <ul className="list-disc list-inside text-muted-foreground">
                      {audit.dataEntryQuality.failReasons.map((reason: string) => (
                        <li key={reason}>{reason.replace(/_/g, ' ')}</li>
                      ))}
                    </ul>
                  </div>
                )}
                {audit.dataEntryQuality.notes && (
                  <div>
                    <h4 className="text-sm font-medium mb-1">Notes:</h4>
                    <p className="text-muted-foreground whitespace-pre-line">
                      {audit.dataEntryQuality.notes}
                    </p>
                  </div>
                )}
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Item Condition Check</h3>
                <div className="flex items-center gap-2 mb-2">
                  <span>Status:</span>
                  {getStatusBadge(audit.itemConditionCheck.status)}
                </div>
                {audit.itemConditionCheck.failReasons && audit.itemConditionCheck.failReasons.length > 0 && (
                  <div className="mb-2">
                    <h4 className="text-sm font-medium mb-1">Fail Reasons:</h4>
                    <ul className="list-disc list-inside text-muted-foreground">
                      {audit.itemConditionCheck.failReasons.map((reason: string) => (
                        <li key={reason}>{reason.replace(/_/g, ' ')}</li>
                      ))}
                    </ul>
                  </div>
                )}
                {audit.itemConditionCheck.notes && (
                  <div>
                    <h4 className="text-sm font-medium mb-1">Notes:</h4>
                    <p className="text-muted-foreground whitespace-pre-line">
                      {audit.itemConditionCheck.notes}
                    </p>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="pricing" className="space-y-6 mt-6">
              <div>
                <h3 className="text-lg font-medium mb-2">Pricing</h3>
                <div className="flex items-center gap-2 mb-2">
                  <span>Status:</span>
                  {getStatusBadge(audit.pricing.status)}
                </div>
                {audit.pricing.failReasons && audit.pricing.failReasons.length > 0 && (
                  <div className="mb-2">
                    <h4 className="text-sm font-medium mb-1">Fail Reasons:</h4>
                    <ul className="list-disc list-inside text-muted-foreground">
                      {audit.pricing.failReasons.map((reason: string) => (
                        <li key={reason}>{reason.replace(/_/g, ' ')}</li>
                      ))}
                    </ul>
                  </div>
                )}
                {audit.pricing.suggestedPrice && (
                  <div className="mb-2">
                    <h4 className="text-sm font-medium mb-1">Suggested Price:</h4>
                    <p className="text-muted-foreground">${audit.pricing.suggestedPrice}</p>
                  </div>
                )}
                {audit.pricing.notes && (
                  <div>
                    <h4 className="text-sm font-medium mb-1">Notes:</h4>
                    <p className="text-muted-foreground whitespace-pre-line">
                      {audit.pricing.notes}
                    </p>
                  </div>
                )}
              </div>

              {audit.auditType !== 'price' && audit.authorizedLimitCheck && (
                <div>
                  <h3 className="text-lg font-medium mb-2">Authorized Limit Check</h3>
                  <div className="flex items-center gap-2 mb-2">
                    <span>Status:</span>
                    {getStatusBadge(audit.authorizedLimitCheck.status)}
                  </div>
                  {audit.authorizedLimitCheck.failReasons && audit.authorizedLimitCheck.failReasons.length > 0 && (
                    <div className="mb-2">
                      <h4 className="text-sm font-medium mb-1">Fail Reasons:</h4>
                      <ul className="list-disc list-inside text-muted-foreground">
                        {audit.authorizedLimitCheck.failReasons.map((reason: string) => (
                          <li key={reason}>{reason.replace(/_/g, ' ')}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                  {audit.authorizedLimitCheck.notes && (
                    <div>
                      <h4 className="text-sm font-medium mb-1">Notes:</h4>
                      <p className="text-muted-foreground whitespace-pre-line">
                        {audit.authorizedLimitCheck.notes}
                      </p>
                    </div>
                  )}
                </div>
              )}
            </TabsContent>

            {audit.auditType === 'pawn' && (
              <TabsContent value="responsible-lending" className="space-y-6 mt-6">
                {/* This section would display responsible lending assessment details */}
                <div className="text-center py-8 text-muted-foreground">
                  Responsible lending assessment details would be displayed here for pawn audits.
                </div>
              </TabsContent>
            )}
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
