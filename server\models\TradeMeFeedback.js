const mongoose = require('mongoose');

/**
 * Schema for TradeMe feedback from a member
 * Based on the TradeMe API response from /Member/{memberId}/Feedback/{filter}.json
 */
const tradeMeFeedbackSchema = new mongoose.Schema({
  // Core feedback identification
  feedbackId: {
    type: String,
    required: true,
    unique: true
  },

  // Listing information
  listingId: {
    type: String,
    required: true
  },
  listingTitle: {
    type: String,
    required: true
  },

  // Feedback type and content
  feedbackType: {
    type: String,
    enum: ['Buyer', 'Seller'], // Whether we're the buyer or seller in this transaction
    required: true
  },
  rating: {
    type: String,
    enum: ['Positive', 'Neutral', 'Negative'],
    required: true
  },
  comment: {
    type: String
  },

  // Feedback metadata
  submittedBy: {
    type: String,
    required: true
  },
  submittedDate: {
    type: Date,
    required: true
  },
  response: {
    type: String
  },
  responseDate: {
    type: Date
  },

  // Additional TradeMe API fields
  isSeller: {
    type: Boolean,
    default: false
  },
  buyNowPrice: {
    type: mongoose.Schema.Types.Decimal128
  },
  isAnOffer: {
    type: Boolean,
    default: false
  },
  maximumBidAmount: {
    type: mongoose.Schema.Types.Decimal128
  },
  offerPrice: {
    type: mongoose.Schema.Types.Decimal128
  },
  hasReturnedFeedback: {
    type: Boolean,
    default: false
  },
  isEdited: {
    type: Boolean,
    default: false
  },
  sellerId: {
    type: String
  },
  purchaseId: {
    type: String
  },

  // Feedback submitter details
  feedbackFrom: {
    memberId: {
      type: Number
    },
    nickname: {
      type: String
    },
    dateAddressVerified: {
      type: Date
    },
    dateJoined: {
      type: Date
    },
    uniqueNegative: {
      type: Number
    },
    uniquePositive: {
      type: Number
    },
    feedbackCount: {
      type: Number
    },
    isAddressVerified: {
      type: Boolean
    },
    isDealer: {
      type: Boolean
    },
    isAuthenticated: {
      type: Boolean
    }
  },

  // Application-specific fields
  isRead: {
    type: Boolean,
    default: false
  }
}, {
  timestamps: true
});

/**
 * Schema for TradeMe feedback statistics
 * Stores the overall feedback statistics for the connected account
 */
const tradeMeFeedbackStatsSchema = new mongoose.Schema({
  // There will only be one document in this collection
  accountId: {
    type: String,
    required: true,
    unique: true
  },

  // Feedback counts
  totalCount: {
    type: Number,
    default: 0
  },
  totalPositive: {
    type: Number,
    default: 0
  },
  uniquePositive: {
    type: Number,
    default: 0
  },
  totalNeutral: {
    type: Number,
    default: 0
  },
  totalNegative: {
    type: Number,
    default: 0
  },
  uniqueNegative: {
    type: Number,
    default: 0
  },

  // Member information
  nickname: {
    type: String
  },
  lastLoggedIn: {
    type: Date
  },
  dateJoined: {
    type: Date
  },
  twoMonthListingCount: {
    type: Number,
    default: 0
  },
  isAuthenticated: {
    type: Boolean,
    default: false
  },

  // Last updated timestamp
  lastUpdated: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

const TradeMeFeedback = mongoose.model('TradeMeFeedback', tradeMeFeedbackSchema);
const TradeMeFeedbackStats = mongoose.model('TradeMeFeedbackStats', tradeMeFeedbackStatsSchema);

module.exports = {
  TradeMeFeedback,
  TradeMeFeedbackStats
};
